<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.lds</groupId>
    <artifactId>one-planning-project</artifactId>
    <version>5.1.1-SNAPSHOT</version>
  </parent>
  <artifactId>one-planning-service</artifactId>
  <properties>
    <jmeter.rampup>1</jmeter.rampup>
    <jmeter.httpDomain>************</jmeter.httpDomain>
    <jmeter.num_threads>1</jmeter.num_threads>
    <maven.compiler.version>3.8.1</maven.compiler.version>
    <skipPerformanceTests>true</skipPerformanceTests>
    <java.version>1.8</java.version>
    <liquibase.version>3.6.3</liquibase.version>
    <jmeter.loops>1</jmeter.loops>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <jmeter.result.html.dir1>${project.build.directory}\jmeter\html1</jmeter.result.html.dir1>
    <jmeter.result.html.dir>${project.build.directory}\jmeter\html</jmeter.result.html.dir>
    <ReportName>UserTestReport</ReportName>
    <jmeter.profile>dev</jmeter.profile>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <jmeter.result.jtl.dir>${project.build.directory}\jmeter\results</jmeter.result.jtl.dir>
    <db.profile>dev</db.profile>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>2.2.10</version>
    </dependency>
    <dependency>
      <groupId>com.lds</groupId>
      <artifactId>ldx-wts-biz-interface</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds</groupId>
      <artifactId>basic-interface</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds</groupId>
      <artifactId>iot-interface</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds</groupId>
      <artifactId>security-service-interface</artifactId>
    </dependency>

    <dependency>
      <groupId>com.zendesk</groupId>
      <artifactId>mysql-binlog-connector-java</artifactId>
      <version>0.25.0</version>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
      <version>3.3.1</version>
    </dependency>

    <dependency>
      <groupId>io.minio</groupId>
      <artifactId>minio</artifactId>
      <version>8.5.10</version>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
      <version>1.11.523</version>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-cloudfront</artifactId>
      <version>1.11.523</version>
    </dependency>
    <dependency>
      <groupId>net.coobird</groupId>
      <artifactId>thumbnailator</artifactId>
      <version>0.4.20</version>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>log4j-over-slf4j</artifactId>
      <version>1.7.30</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-kubernetes</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-kubernetes-ribbon</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-kubernetes-config</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-logger-starter-operate</artifactId>
    </dependency>
    <dependency>
      <groupId>io.opentracing</groupId>
      <artifactId>opentracing-noop</artifactId>
      <version>0.33.0</version>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-logger</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-i18n-common-code</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-i18n-starter-exception-message</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-website-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.xerial.snappy/snappy-java -->
    <dependency>
      <groupId>org.xerial.snappy</groupId>
      <artifactId>snappy-java</artifactId>
      <version>1.1.10.5</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-sleuth</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-config</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-gson</artifactId>
      <version>11.8</version>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-jackson</artifactId>
      <version>12.1</version> <!-- 如果你使用 Jackson 进行 JSON 序列化/反序列化 -->
    </dependency>
    <dependency>
      <groupId>org.codehaus.jackson</groupId>
      <artifactId>jackson-mapper-asl</artifactId>
      <version>1.9.12</version>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-freemarker</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-consul-discovery</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-consul-starter</artifactId>
    </dependency>
    <!--spring 分布式会话-->
    <dependency>
      <groupId>org.springframework.session</groupId>
      <artifactId>spring-session-data-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.jolokia</groupId>
      <artifactId>jolokia-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-contract-verifier</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.skywalking</groupId>
      <artifactId>apm-toolkit-logback-1.x</artifactId>
      <version>6.1.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.skywalking</groupId>
      <artifactId>apm-toolkit-opentracing</artifactId>
      <version>6.1.0</version>
    </dependency>
    <dependency>
      <groupId>com.googlecode.guava-osgi</groupId>
      <artifactId>guava-osgi</artifactId>
      <version>11.0.1</version>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit</artifactId>
      <version>4.8.0.201706111038-r</version>
    </dependency>
    <dependency>
      <groupId>ma.glasnost.orika</groupId>
      <artifactId>orika-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-liquibase-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-auth-starter-auth</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-auth-starter-context</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-auth-starter-permission</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds</groupId>
      <artifactId>biz-support</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-mq-starter-stream</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-cache-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-amqp</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-job-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-models</artifactId>
      <version>1.5.22</version>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign.form</groupId>
      <artifactId>feign-form</artifactId>
    </dependency>
    <dependency>
      <groupId>org.gavaghan</groupId>
      <artifactId>geodesy</artifactId>
      <version>1.1.3</version>
    </dependency>
    <dependency>
      <groupId>com.belerweb</groupId>
      <artifactId>pinyin4j</artifactId>
      <version>2.5.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.jsqlparser</groupId>
      <artifactId>jsqlparser</artifactId>
      <version>1.4</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-mail</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>weixin-java-miniapp</artifactId>
      <version>4.4.0</version>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>weixin-java-mp</artifactId>
      <version>4.4.0</version>
    </dependency>
    <dependency>
      <groupId>com.easemob.im</groupId>
      <artifactId>im-sdk-core</artifactId>
      <version>0.8.2</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jms</artifactId>
      <version>5.2.12.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.lds.mqtt</groupId>
      <artifactId>mqtt-connector-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.whvcse</groupId>
      <artifactId>easy-captcha</artifactId>
      <version>1.6.2</version>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>amazon-sqs-java-messaging-lib</artifactId>
      <version>1.0.4</version>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
      <version>3.4.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
    </dependency>

    <!-- EasyExcel -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>3.2.0</version> <!-- 请根据需要选择最新版本 -->
    </dependency>

    <!-- Commons IO (用于文件上传) -->
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/com.oracle.database.jdbc/ojdbc8 -->
    <dependency>
      <groupId>com.oracle.database.jdbc</groupId>
      <artifactId>ojdbc8</artifactId>
      <version>23.7.0.25.01</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/com.oracle.database.messaging/aqapi -->
    <dependency>
      <groupId>com.oracle.database.messaging</groupId>
      <artifactId>aqapi</artifactId>
      <version>19.3.0.0</version>
    </dependency>

    <dependency>
      <groupId>javax.jms</groupId>
      <artifactId>javax.jms-api</artifactId>
      <version>2.0.1</version>
    </dependency>

    <dependency>
      <groupId>com.lds.coral</groupId>
      <artifactId>coral-mq-rocketmq</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
    </dependency>
  </dependencies>
  <build>
    <resources>
      <resource>
        <filtering>false</filtering>
        <directory>${project.basedir}/src/main/resources</directory>
        <includes>
          <include>**/*.xml</include>
          <include>**/*.xlsx</include>
        </includes>
      </resource>
      <resource>
        <filtering>true</filtering>
        <directory>${project.basedir}/src/main/resources</directory>
        <excludes>
          <exclude>**/*.xml</exclude>
          <exclude>**/*.xlsx</exclude>
          <exclude>**/*.png</exclude>
        </excludes>
      </resource>
    </resources>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
              <goal>build-info</goal>
            </goals>
          </execution>
        </executions>
        <!-- 可选: 明确指定主类 -->
        <configuration>
          <mainClass>com.lds.oneplanning.OnePlanningServiceApplication</mainClass> <!-- 用你的主类替换 -->
        </configuration>
      </plugin>


      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>xml-maven-plugin</artifactId>
        <version>1.0</version>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>transform</goal>
            </goals>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>net.sf.saxon</groupId>
            <artifactId>saxon</artifactId>
            <version>8.7</version>
          </dependency>
        </dependencies>
        <configuration>
          <transformationSets>
            <transformationSet>
              <dir>${jmeter.result.jtl.dir}</dir>
              <stylesheet>src/test/resources/jmeter-results-detail-report_21.xsl</stylesheet>
              <outputDir>${jmeter.result.html.dir}</outputDir>
              <fileMappers>
                <fileMapper implementation="org.codehaus.plexus.components.io.filemappers.FileExtensionMapper">
                  <targetExtension>html</targetExtension>
                </fileMapper>
              </fileMappers>
            </transformationSet>
            <transformationSet>
              <dir>${jmeter.result.jtl.dir}</dir>
              <stylesheet>src/test/resources/jmeter.results.shanhe.me.xsl</stylesheet>
              <outputDir>${jmeter.result.html.dir1}</outputDir>
              <fileMappers>
                <fileMapper implementation="org.codehaus.plexus.components.io.filemappers.FileExtensionMapper">
                  <targetExtension>html</targetExtension>
                </fileMapper>
              </fileMappers>
            </transformationSet>
          </transformationSets>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-maven-plugin</artifactId>
        <version>${liquibase.version}</version>
        <configuration>
          <changeLogFile>${basedir}/src/main/resources/liquibase/master.xml</changeLogFile>
          <promptOnNonLocalDatabase>false</promptOnNonLocalDatabase>
          <propertyFile>${basedir}/src/main/resources/liquibase/config/${db.profile}.properties</propertyFile>
        </configuration>
      </plugin>

    </plugins>
  </build>
</project>
