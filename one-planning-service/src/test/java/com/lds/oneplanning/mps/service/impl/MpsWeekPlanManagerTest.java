package com.lds.oneplanning.mps.service.impl;

import com.google.common.collect.Lists;
import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.mps.date.HalfWeekMap;
import com.lds.oneplanning.mps.date.WeekMap;
import com.lds.oneplanning.mps.date.YearWeekMap;
import com.lds.oneplanning.mps.enums.WeekTypeEnum;
import com.lds.oneplanning.mps.utils.MpsDateUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

class MpsWeekPlanManagerTest extends Junit5BaseTest {

    @Autowired
    private MpsWeekPlanManager mpsWeekPlanManager;

    @Test
    void getAllDatesFromCurrentDate() {
        // 示例：从当前日期开始推3周的所有日期列表
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.DECEMBER, 4); // 设置起始日期
        Date currentDate = calendar.getTime(); // 获取当前日期

        int weeksToPush = 6; // 往后推的周数
        YearWeekMap result = MpsDateUtil.getAllDatesFromCurrentDate(currentDate, weeksToPush);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 打印结果
        for (Map.Entry<Integer, WeekMap> yearEntry : result.getYearMap().entrySet()) {
            System.out.println("Year: " + yearEntry.getKey());
            for (Map.Entry<Integer, HalfWeekMap> weekEntry : yearEntry.getValue().getWeekMap().entrySet()) {
                System.out.println("  Week: " + weekEntry.getKey());
                for (Map.Entry<Integer, List<Date>> halfWeekEntry : weekEntry.getValue().getHalfWeekMap().entrySet()) {
                    System.out.println("    Half Week: " + WeekTypeEnum.getByCode(halfWeekEntry.getKey()).getName());
                    for (Date date : halfWeekEntry.getValue()) {
                        System.out.println("      Date: " + sdf.format(date));
                    }
                }
            }
        }
    }

    @Test
    void listByBizIds(){
        Calendar calendar = Calendar.getInstance();
        calendar.set(2025, Calendar.JANUARY, 1); // 设置起始日期
        Date currentDate = calendar.getTime(); // 获取当前日期
        mpsWeekPlanManager.listByBizIds(Lists.newArrayList("bizId"), currentDate, 5);
    }
}