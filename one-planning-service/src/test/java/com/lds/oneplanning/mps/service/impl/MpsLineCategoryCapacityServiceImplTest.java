package com.lds.oneplanning.mps.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.basedata.entity.LineCapacity;
import com.lds.oneplanning.basedata.service.ILineCapacityService;
import com.lds.oneplanning.mps.utils.ScheduleCalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Slf4j
class MpsLineCategoryCapacityServiceImplTest extends Junit5BaseTest {

    @Autowired
    private ILineCapacityService lineCapacityService;

    @Test
    void getByCodeAndDates() {
        List<LocalDate> scheduleDateList = Lists.newArrayList(LocalDate.of(2025, 2, 5),
                LocalDate.of(2025, 2, 6));
        Map<LocalDate, LineCapacity> mpsLineCategoryCapacityMap = lineCapacityService.getByCodeAndDates(1,"022",null, scheduleDateList);
        log.info("getByCodeAndDates:{}", JSON.toJSONString(mpsLineCategoryCapacityMap));
        Float aFloat = ScheduleCalculateUtil.calculateMpsWaitingOrderHour(mpsLineCategoryCapacityMap);
        log.info("calculateWaitingOrderHour:{}", aFloat);
    }
}