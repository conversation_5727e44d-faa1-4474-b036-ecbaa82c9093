package com.lds.oneplanning.mps.service;

import com.lds.oneplanning.Junit5BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class IMpsWeekPlanServiceTest extends Junit5BaseTest {

    @Autowired
    private IMpsWeekPlanService mpsWeekPlanService;

    @Test
    void listByBizIds() {
        /*Map<String, List<MpsWeekPlanVo>> mpsWeekPlanVoMap = mpsWeekPlanService.listByBizIds(Arrays.asList("bizId"));
        System.out.println(JSONUtil.toJsonStr(mpsWeekPlanVoMap));*/
    }
}