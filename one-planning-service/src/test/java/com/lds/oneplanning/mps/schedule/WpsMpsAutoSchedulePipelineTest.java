package com.lds.oneplanning.mps.schedule;

import com.alibaba.fastjson.JSON;
import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.service.MpsExcelService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
class WpsMpsAutoSchedulePipelineTest extends Junit5BaseTest {

    @Autowired
    private MpsAutoSchedulePipeline mpsAutoSchedulePipeline;

    @Autowired
    private MpsExcelService mpsExcelService;

    @Test
    void execute() {
        Long userId = 1367766865382349738L;
        // LocalDate 转Date
        Date startDate = LocalDateTimeUtil.localDateToDate(LocalDate.now().minusDays(30));
        Date endDate = new Date();
        List<MpsRowData> mpsRowDataList = mpsExcelService.getBody(userId, startDate, endDate);
        mpsRowDataList.forEach(mpsRowData -> {
            mpsRowData.setOrderPcsQty(10000000);
            mpsRowData.setProductId("10000001");
        });
        Map<String, MpsRowData> orderMap = mpsRowDataList.stream().collect(Collectors.toMap(MpsRowData::getOrderNo, mpsRowData -> mpsRowData,(t, t2) -> t2));
        MpsAutoScheduleContext context = new MpsAutoScheduleContext(orderMap);
        context.setUserId(userId);
        context.setCurrentDate(new Date());
        context.setWeeksToPush(12);
        mpsAutoSchedulePipeline.execute(context);
        log.info("AutoSchedulePipelineTest execute success, result: {}.", JSON.toJSONString(orderMap));
    }
}