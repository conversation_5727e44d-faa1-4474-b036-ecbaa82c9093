package com.lds.oneplanning.po;


import com.lds.oneplanning.po.config.PoCallOffOrderProperties;
import com.lds.oneplanning.po.domain.entity.DemandOrder;
import com.lds.oneplanning.po.enums.TransportType;
import com.lds.oneplanning.po.service.CallOffCalculationService;
import com.lds.oneplanning.po.strategy.LockDateStrategy;
import com.lds.oneplanning.po.strategy.LockDateStrategyFactory;
import com.lds.oneplanning.po.strategy.impl.JITLockDateStrategy;
import com.lds.oneplanning.po.strategy.impl.PO2LockDateStrategy;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

public class CreateVirtualDemandTest {
    @Mock
    private PoCallOffOrderProperties properties;
    private CallOffCalculationService callOffCalculationService;
    private final AtomicInteger id = new AtomicInteger(1);

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        properties.setDebugFlag(1);
        List<LockDateStrategy> lockDateStrategies = Lists.newArrayList(new JITLockDateStrategy(properties), new PO2LockDateStrategy(properties));
        LockDateStrategyFactory lockDateStrategyFactory = new LockDateStrategyFactory(lockDateStrategies);
        lockDateStrategyFactory.init();
        callOffCalculationService = new CallOffCalculationService(lockDateStrategyFactory);
    }

    /**
     * 测试差额为负数的情况
     */
    @Test
    void testCreateVirtualDemand_NegativeDifference() throws Exception {
        List<DemandOrder> demandOrders = new ArrayList<>();
        List<DemandOrder> dailyDemands = new ArrayList<>();

        DemandOrder order = DemandOrder.builder()
                .orderNo("ORDER001")
                .materialId("MAT001")
                .supplier("SUP001")
                .factoryCode("FACTORY001")
                .transportType(TransportType.PO2)
                .transportTime(5)
                .build();

        dailyDemands.add(order);

        LocalDate currentProcessDate = LocalDate.of(2025, 4, 1);
        LocalDate lastDemandDate = LocalDate.of(2025, 4, 5);
        int difference = -3; // 负数差额

        // 调用私有方法
        LocalDate result = invokeCreateVirtualDemand(demandOrders, dailyDemands, currentProcessDate, difference, lastDemandDate);

        // 验证是否新增了虚拟需求单
        assertEquals(1, demandOrders.size());
        DemandOrder virtualOrder = demandOrders.get(0);
        assertEquals(currentProcessDate.plusDays(1), virtualOrder.getDemandDate());
        assertEquals(difference, virtualOrder.getDemandQuantity());
    }

    /**
     * 测试差额为正数且能完全抹平
     */
    @Test
    void testCreateVirtualDemand_PositiveDifference_FullyCovered() throws Exception {
        List<DemandOrder> demandOrders = new ArrayList<>();
        List<DemandOrder> dailyDemands = new ArrayList<>();

        DemandOrder d1 = mockDemandOrder(5);
        DemandOrder d2 = mockDemandOrder(3);
        dailyDemands.addAll(Arrays.asList(d1, d2));

        LocalDate currentProcessDate = LocalDate.of(2025, 4, 1);
        LocalDate lastDemandDate = LocalDate.of(2025, 4, 5);
        int difference = 6; // 锁定数量 = 8 - 6 = 2

        LocalDate result = invokeCreateVirtualDemand(demandOrders, dailyDemands, currentProcessDate, difference, lastDemandDate);

        // 验证两个需求都被抹平
        verify(d1).setDemandQuantity(3); // 5 - 2 = 3
        verify(d2).setDemandQuantity(0); // 3 - 2 >= 0 => 0

        // 无剩余需求，不会调用 moveToNextDay
        assertEquals(lastDemandDate, result);
    }

    /**
     * 测试差额为正数且能完全抹平
     */
    @Test
    void testCreateVirtualDemand_PositiveDifference_FullyCovered2() throws Exception {
        List<DemandOrder> demandOrders = new ArrayList<>();
        List<DemandOrder> dailyDemands = new ArrayList<>();

        DemandOrder d1 = DemandOrder.builder().orderNo("1").demandQuantity(5).build();
        DemandOrder d2 = DemandOrder.builder().orderNo("1").demandQuantity(3).build();
        dailyDemands.addAll(Arrays.asList(d1, d2));

        int lockedQuantity = 2;

        int totalDemand = dailyDemands.stream()
                .mapToInt(DemandOrder::getDemandQuantity)
                .sum();

        int difference = totalDemand - lockedQuantity;

        LocalDate currentProcessDate = LocalDate.of(2025, 4, 1);
        LocalDate lastDemandDate = LocalDate.of(2025, 4, 5);

        LocalDate result = invokeCreateVirtualDemand(demandOrders, dailyDemands, currentProcessDate, difference, lastDemandDate);

        // 验证两个需求都被抹平
        assertEquals(5, d1.getDemandQuantity());
        assertEquals(1, d2.getDemandQuantity());
    }

    /**
     * 测试差额为正数但不能完全抹平
     */
    @Test
    void testCreateVirtualDemand_PositiveDifference_PartiallyCovered() throws Exception {
        List<DemandOrder> demandOrders = new ArrayList<>();
        List<DemandOrder> dailyDemands = new ArrayList<>();

        DemandOrder d1 = DemandOrder.builder().orderNo("1").demandQuantity(2).build();
        DemandOrder d2 = DemandOrder.builder().orderNo("2").demandQuantity(3).build();
        dailyDemands.addAll(Arrays.asList(d1, d2));

        int lockedQuantity = 10;

        int totalDemand = dailyDemands.stream()
                .mapToInt(DemandOrder::getDemandQuantity)
                .sum();

        int difference = totalDemand - lockedQuantity;

        LocalDate currentProcessDate = LocalDate.of(2025, 4, 1);
        LocalDate lastDemandDate = LocalDate.of(2025, 4, 5);

        LocalDate result = invokeCreateVirtualDemand(demandOrders, dailyDemands, currentProcessDate, difference, lastDemandDate);

        // 验证两个需求都被抹平
        assertEquals(1, demandOrders.size());
        assertEquals(-5, demandOrders.get(0).getDemandQuantity());
    }

    /**
     * 测试合并差异后为正数的情况
     */
    @Test
    void testCreateVirtualDemand_ConsolidationPositive() throws Exception {
        List<DemandOrder> demandOrders = new ArrayList<>();
        List<DemandOrder> dailyDemands = new ArrayList<>();

        DemandOrder d1 = DemandOrder.builder().orderNo("1").demandQuantity(1).build();
        DemandOrder d2 = DemandOrder.builder().orderNo("2").demandQuantity(4).build();
        DemandOrder d3 = DemandOrder.builder().orderNo("3").demandQuantity(10).build();
        dailyDemands.addAll(Arrays.asList(d1, d2, d3));

        int lockedQuantity = 10;

        int totalDemand = dailyDemands.stream()
                .mapToInt(DemandOrder::getDemandQuantity)
                .sum();

        int difference = totalDemand - lockedQuantity;

        LocalDate currentProcessDate = LocalDate.of(2025, 4, 1);
        LocalDate lastDemandDate = LocalDate.of(2025, 4, 5);

        LocalDate result = invokeCreateVirtualDemand(demandOrders, dailyDemands, currentProcessDate, difference, lastDemandDate);

        // 验证两个需求都被抹平
        assertEquals(1, demandOrders.size());
        assertEquals(5, demandOrders.get(0).getDemandQuantity());
    }


    // ------------------ 辅助方法 ------------------

    private DemandOrder mockDemandOrder(int quantity) {
        DemandOrder mock = mock(DemandOrder.class);
        when(mock.getDemandQuantity()).thenReturn(quantity);
        doAnswer(invocation -> {
            int newQty = invocation.getArgument(0);
            when(mock.getDemandQuantity()).thenReturn(newQty);
            return null;
        }).when(mock).setDemandQuantity(anyInt());
        return mock;
    }

    private LocalDate invokeCreateVirtualDemand(
            List<DemandOrder> demandOrders,
            List<DemandOrder> dailyDemands,
            LocalDate currentProcessDate,
            int difference,
            LocalDate lastDemandDate) throws Exception {

        java.lang.reflect.Method method = CallOffCalculationService.class.getDeclaredMethod(
                "createVirtualDemand",
                List.class,
                List.class,
                LocalDate.class,
                int.class,
                LocalDate.class
        );
        method.setAccessible(true);
        return (LocalDate) method.invoke(callOffCalculationService, demandOrders, dailyDemands, currentProcessDate, difference, lastDemandDate);
    }
}
