package com.lds.oneplanning.po;

import com.alibaba.fastjson.JSON;
import com.lds.oneplanning.po.config.PoCallOffOrderProperties;
import com.lds.oneplanning.po.domain.entity.CallOffOrder;
import com.lds.oneplanning.po.domain.entity.DemandOrder;
import com.lds.oneplanning.po.enums.TransportType;
import com.lds.oneplanning.po.service.CallOffCalculationService;
import com.lds.oneplanning.po.strategy.LockDateStrategy;
import com.lds.oneplanning.po.strategy.LockDateStrategyFactory;
import com.lds.oneplanning.po.strategy.impl.JITLockDateStrategy;
import com.lds.oneplanning.po.strategy.impl.PO2LockDateStrategy;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;


@DisplayName("叫料单计算测试")
@Slf4j
public class CallOffCalculationTest {
    @Mock
    private PoCallOffOrderProperties properties;
    private CallOffCalculationService service;
    private final AtomicInteger id = new AtomicInteger(1);

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        properties.setDebugFlag(1);
        List<LockDateStrategy> lockDateStrategies = Lists.newArrayList(new JITLockDateStrategy(properties), new PO2LockDateStrategy(properties));
        LockDateStrategyFactory lockDateStrategyFactory = new LockDateStrategyFactory(lockDateStrategies);
        lockDateStrategyFactory.init();
        service = new CallOffCalculationService(lockDateStrategyFactory);
    }

    @DisplayName("策略工厂测试")
    @ParameterizedTest(name = "策略工厂测试: {0}")
    @ValueSource(strings = {"JIT", "PO2"})
    @SneakyThrows
    public void initTest(String value) {
        TransportType transportType = service.getLockDateStrategyFactory()
                .getStrategy(TransportType.valueOf(value))
                .getTransportType();
        assertEquals(TransportType.valueOf(value), transportType);
    }

    public static Stream<Arguments> lockDaysTestCases() {
        LocalDate testCurrentDate = LocalDate.of(2025, 6, 10);
        return Stream.of(
                Arguments.of(-3, TransportType.JIT, testCurrentDate, true, 1),
                Arguments.of(0, TransportType.JIT, testCurrentDate, true, 1),
                Arguments.of(3, TransportType.JIT, testCurrentDate, true, 1),

                Arguments.of(-1, TransportType.PO2, testCurrentDate, true, 3),
                Arguments.of(0, TransportType.PO2, testCurrentDate, true, 3),
                Arguments.of(1, TransportType.PO2, testCurrentDate, true, 3),
                Arguments.of(2, TransportType.PO2, testCurrentDate, true, 3),
                Arguments.of(6, TransportType.PO2, testCurrentDate, true, 3)
        );
    }

    @DisplayName("需求时间测试")
    @ParameterizedTest(name = "需求时间: {0}，类型：{1}")
    @MethodSource("lockDaysTestCases")
    @SneakyThrows
    public void lockDaysTest(int offset, TransportType transportType, LocalDate currentDate, boolean expectedResult, int transportTime) {
        LocalDate demandDate = currentDate.plusDays(offset);
        log.info("测试: {}, demandDate: {}, currentDate: {}, expectedResult: {}", offset, demandDate, currentDate, expectedResult);
        //6.12号一个物料需求
        DemandOrder demandOrder = buildDemand(transportType, demandDate, 100, transportTime);
        List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);
        List<CallOffOrder> callOffOrders = Lists.newArrayList();
        callOffOrders = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);
        printResult(demandOrders, callOffOrders);
    }


    @DisplayName("需求数量测试")
    @ParameterizedTest(name = "需求数量: {0}，类型：{1}")
    @MethodSource("lockDaysTestCases")
    @SneakyThrows
    public void demandNumTest(int offset, TransportType transportType, LocalDate currentDate, boolean expectedResult, int transportTime) {
        LocalDate demandDate = currentDate.plusDays(offset);
        log.info("测试: {}, demandDate: {}, currentDate: {}, expectedResult: {}", offset, demandDate, currentDate, expectedResult);
        //6.12号一个物料需求
        List<DemandOrder> demandOrders = Lists.newArrayList(
                buildDemand(transportType, demandDate, 100, transportTime),
                buildDemand(transportType, demandDate.plusDays(1), 200, transportTime),
                buildDemand(transportType, demandDate.plusDays(1), 300, transportTime)
        );
        List<CallOffOrder> callOffOrders = Lists.newArrayList();
        callOffOrders = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);
        printResult(demandOrders, callOffOrders);
    }

    @DisplayName("需求数量测试")
    @Test
    @SneakyThrows
    public void lockNumTest() {
        LocalDate currentDate = LocalDate.of(2025, 6, 10);
        TransportType transportType = TransportType.PO2;
        Integer transportTime = 1;

        List<CallOffOrder> callOffOrders = Lists.newArrayList(
                buildCallOff(transportType, currentDate, 100, 50, transportTime),
                buildCallOff(transportType, currentDate.plusDays(1), 100, 0, transportTime),
                buildCallOff(transportType, currentDate.plusDays(2), 100, 0, transportTime),
                buildCallOff(transportType, currentDate.plusDays(3), 100, 0, transportTime)
        );

        //6.12号物料需求
        currentDate = currentDate.plusDays(1);
        List<DemandOrder> demandOrders = Lists.newArrayList(
                buildDemand(transportType, currentDate, 120, transportTime),
                buildDemand(transportType, currentDate.plusDays(1), 100, transportTime),
                buildDemand(transportType, currentDate.plusDays(2), 100, transportTime)
        );
        List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);
        printResult(demandOrders, callOffOrders, result);
    }

    //锁定场景
    //1、

    private void printResult(List<DemandOrder> demandOrders, List<CallOffOrder> callOffOrders, List<CallOffOrder> result) {
        System.out.println(JSON.toJSONString(demandOrders));
        System.out.println("----------------------------------");
        System.out.println(JSON.toJSONString(callOffOrders));
        System.out.println("----------------------------------");
        System.out.println(JSON.toJSONString(result));
    }

    private void printResult(List<DemandOrder> demandOrders, List<CallOffOrder> callOffOrders) {
        System.out.println(JSON.toJSONString(demandOrders));
        System.out.println("----------------------------------");
        System.out.println(JSON.toJSONString(callOffOrders));
    }

    private DemandOrder buildDemand(TransportType type,
                                    LocalDate demandDate,
                                    Integer quantity,
                                    Integer transportTime) {
        String mid = "3000";
        DemandOrder order = new CallOffOrder();
        order.setTransportType(type);
        order.setFactoryCode("1000");
        order.setOrderNo(type.name() + id.getAndIncrement());
        order.setDemandDate(demandDate);
        order.setMaterialId(mid);
        order.setMaterialName(type.name() + ":" + mid);
        order.setSupplier("2000");
        order.setDemandQuantity(quantity);
        order.setTransportTime(transportTime);
        return order;
    }

    private CallOffOrder buildCallOff(TransportType type,
                                      LocalDate callOffDate,
                                      Integer quantity,
                                      Integer receivedQuantity,
                                      Integer transportTime
    ) {
        String mid = "3000";
        CallOffOrder order = new CallOffOrder();
        order.setTransportType(type);
        order.setFactoryCode("1000");
        order.setOrderNo(type.name() + id.getAndIncrement());
        order.setDemandDate(callOffDate);
        order.setMaterialId(mid);
        order.setMaterialName(type.name() + ":" + mid);
        order.setSupplier("2000");
        order.setDemandQuantity(quantity);
        order.setReceivedQuantity(receivedQuantity);
        order.setTransportTime(transportTime);

        order.setCallOffDate(callOffDate);
        order.setCallOffQuantity(quantity);
        return order;
    }


}
