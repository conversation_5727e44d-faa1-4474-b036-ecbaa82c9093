package com.lds.oneplanning.po;

import com.google.common.collect.Lists;
import com.lds.oneplanning.po.domain.entity.CallOffOrder;
import com.lds.oneplanning.po.domain.entity.DemandOrder;
import com.lds.oneplanning.po.enums.TransportType;
import com.lds.oneplanning.po.service.CallOffCalculationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@DisplayName("CallOffCalculationService Tests")
@SpringBootTest
class CursorTest {

    @Mock
    private CallOffCalculationService service;
    private LocalDate currentDate;

    @BeforeEach
    void setUp() {
        // 设置一个固定的当前日期
        currentDate = LocalDate.of(2024, 3, 1);
    }

    // 辅助方法：创建需求单
    private DemandOrder createDemandOrder(String orderNo, LocalDate demandDate, int quantity,
                                          String transportType, Integer transportTime) {
        return DemandOrder.builder()
                .orderNo(orderNo)
                .demandDate(demandDate)
                .actualDemandDate(demandDate)
                .materialId("M1")
                .supplier("S1")
                .factoryCode("F1")
                .demandQuantity(quantity)
                .transportType(TransportType.valueOf(transportType.toUpperCase()))
                .transportTime(transportTime)
                .build();
    }

    // 辅助方法：创建叫料单
    private CallOffOrder createCallOffOrder(String orderNo, LocalDate callOffDate, int quantity,
                                            boolean locked, int receivedQuantity) {
        return CallOffOrder.builder()
                .orderNo(orderNo)
                .callOffDate(callOffDate)
                .callOffQuantity(quantity)
                .demandDate(callOffDate)
                .materialId("M1")
                .supplier("S1")
                .factoryCode("F1")
                .locked(locked)
                .receivedQuantity(receivedQuantity)
                .build();
    }

    @Nested
    @DisplayName("A. Locking Logic Calculation Tests")
    class LockingLogicTests {

        @ParameterizedTest(name = "PO2 Lock Test: {0}")
        @MethodSource("com.lds.oneplanning.po.ParameterSources#providePO2LockTestCases")
        @DisplayName("PO2 Lock Date Calculation Tests")
        void testPO2LockDateCalculation(String testName, int transportTime, LocalDate expectedLockDate) {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", transportTime);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder callOff = result.get(0);
            assertThat(callOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(callOff.getCallOffQuantity()).isEqualTo(100);
            assertThat(callOff.getLocked()).isTrue();
        }


        @ParameterizedTest(name = "JIT Lock Test: {0}")
        @MethodSource("com.lds.oneplanning.po.ParameterSources#provideJITLockTestCases")
        @DisplayName("JIT Lock Date Calculation Tests")
        void testJITLockDateCalculation(String testName, int transportTime, boolean shouldLock) {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("JIT-001", currentDate, 100, "JIT", transportTime);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder callOff = result.get(0);
            assertThat(callOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(callOff.getCallOffQuantity()).isEqualTo(100);
            assertThat(callOff.getLocked()).isEqualTo(shouldLock);
        }


    }

    @Nested
    @DisplayName("B1. Initialization and Basic Process Tests")
    class InitializationTests {

        @Test
        @DisplayName("TC_MC_001: No Demand Orders")
        void testNoDemandOrders() {
            // 准备测试数据
            List<DemandOrder> demandOrders = new ArrayList<>();
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("TC_MC_002: Single Demand Order, No History")
        void testSingleDemandOrderNoHistory() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder callOff = result.get(0);
            assertThat(callOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(callOff.getCallOffQuantity()).isEqualTo(100);
            assertThat(callOff.getLocked()).isTrue();
            assertThat(callOff.getOrderNo()).isEqualTo("PO2-001");
        }

        @Test
        @DisplayName("TC_MC_003: Different Groups Process Independently")
        void testDifferentGroupsProcessIndependently() {
            // 准备测试数据
            DemandOrder demandOrder1 = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            demandOrder1.setFactoryCode("F1");
            demandOrder1.setSupplier("S1");
            demandOrder1.setMaterialId("M1");

            DemandOrder demandOrder2 = createDemandOrder("JIT-001", currentDate, 50, "JIT", 6);
            demandOrder2.setFactoryCode("F2");
            demandOrder2.setSupplier("S2");
            demandOrder2.setMaterialId("M2");

            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder1, demandOrder2);
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(2);

            // 验证PO2组
            CallOffOrder po2CallOff = result.stream()
                    .filter(c -> c.getOrderNo().equals("PO2-001"))
                    .findFirst()
                    .orElseThrow(() -> new AssertionError("PO2 CallOff not found"));
            assertThat(po2CallOff.getCallOffQuantity()).isEqualTo(100);
            assertThat(po2CallOff.getLocked()).isTrue();

            // 验证JIT组
            CallOffOrder jitCallOff = result.stream()
                    .filter(c -> c.getOrderNo().equals("JIT-001"))
                    .findFirst()
                    .orElseThrow(() -> new AssertionError("JIT CallOff not found"));
            assertThat(jitCallOff.getCallOffQuantity()).isEqualTo(50);
            assertThat(jitCallOff.getLocked()).isFalse();
        }

        @Test
        @DisplayName("TC_MC_004: Multiple Demands in Same Group")
        void testMultipleDemandsInSameGroup() {
            // 准备测试数据
            DemandOrder demandOrder1 = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            DemandOrder demandOrder2 = createDemandOrder("PO2-002", currentDate, 50, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder1, demandOrder2);
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder callOff = result.get(0);
            assertThat(callOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(callOff.getCallOffQuantity()).isEqualTo(150); // 100 + 50
            assertThat(callOff.getLocked()).isTrue();
        }
    }

    @Nested
    @DisplayName("B2. Previous Day Call-off Processing Tests")
    class PreviousDayCallOffTests {

        @Test
        @DisplayName("TC_MC_005: Previous Day Partial Receipt")
        void testPreviousDayPartialReceipt() {
            // 准备测试数据
            LocalDate previousDay = currentDate.minusDays(1);
            CallOffOrder previousCallOff = createCallOffOrder("PO2-001", previousDay, 100, true, 70);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(previousCallOff);
            List<DemandOrder> demandOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder newCallOff = result.get(0);
            assertThat(newCallOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(30); // 100 - 70
            assertThat(newCallOff.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_006: Previous Day No Receipt")
        void testPreviousDayNoReceipt() {
            // 准备测试数据
            LocalDate previousDay = currentDate.minusDays(1);
            CallOffOrder previousCallOff = createCallOffOrder("PO2-001", previousDay, 100, true, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(previousCallOff);
            List<DemandOrder> demandOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder newCallOff = result.get(0);
            assertThat(newCallOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(100); // 全部未收货
            assertThat(newCallOff.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_007: Previous Day Full Receipt")
        void testPreviousDayFullReceipt() {
            // 准备测试数据
            LocalDate previousDay = currentDate.minusDays(1);
            CallOffOrder previousCallOff = createCallOffOrder("PO2-001", previousDay, 100, true, 100);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(previousCallOff);
            List<DemandOrder> demandOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).isEmpty(); // 全部收货，不需要生成新的叫料单
        }

        @Test
        @DisplayName("TC_MC_008: No Previous Day Call-off")
        void testNoPreviousDayCallOff() {
            // 准备测试数据
            List<CallOffOrder> callOffOrders = new ArrayList<>();
            List<DemandOrder> demandOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).isEmpty(); // 无昨日叫料单，无需求单，结果为空
        }

        @Test
        @DisplayName("Previous Day Call-off with Multiple Groups")
        void testPreviousDayCallOffWithMultipleGroups() {
            // 准备测试数据
            LocalDate previousDay = currentDate.minusDays(1);

            // 第一组：部分收货
            CallOffOrder callOff1 = createCallOffOrder("PO2-001", previousDay, 100, true, 70);
            callOff1.setFactoryCode("F1");
            callOff1.setSupplier("S1");
            callOff1.setMaterialId("M1");

            // 第二组：全部未收货
            CallOffOrder callOff2 = createCallOffOrder("PO2-002", previousDay, 50, true, 0);
            callOff2.setFactoryCode("F2");
            callOff2.setSupplier("S2");
            callOff2.setMaterialId("M2");

            List<CallOffOrder> callOffOrders = Lists.newArrayList(callOff1, callOff2);
            List<DemandOrder> demandOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(2);

            // 验证第一组
            CallOffOrder newCallOff1 = result.stream()
                    .filter(c -> c.getFactoryCode().equals("F1"))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(newCallOff1.getCallOffQuantity()).isEqualTo(30); // 100 - 70

            // 验证第二组
            CallOffOrder newCallOff2 = result.stream()
                    .filter(c -> c.getFactoryCode().equals("F2"))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(newCallOff2.getCallOffQuantity()).isEqualTo(50); // 全部未收货
        }
    }

    @Nested
    @DisplayName("B3. Locked and Non-locked Call-off Processing Tests")
    class LockedCallOffTests {

        @Test
        @DisplayName("TC_MC_009: PO2 Locked Call-off with Increased Demand")
        void testPO2LockedCallOffWithIncreasedDemand() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            CallOffOrder existingCallOff = createCallOffOrder("PO2-001", currentDate, 80, true, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(existingCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder newCallOff = result.get(0);
            assertThat(newCallOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(80); // 保持原有数量
            assertThat(newCallOff.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_010: PO2 Locked Call-off with Decreased Demand")
        void testPO2LockedCallOffWithDecreasedDemand() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 70, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            CallOffOrder existingCallOff = createCallOffOrder("PO2-001", currentDate, 100, true, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(existingCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder newCallOff = result.get(0);
            assertThat(newCallOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(100); // 保持原有数量
            assertThat(newCallOff.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_011: PO2 Non-locked Call-off")
        void testPO2NonLockedCallOff() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            CallOffOrder existingCallOff = createCallOffOrder("PO2-001", currentDate, 50, false, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(existingCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder newCallOff = result.get(0);
            assertThat(newCallOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(100); // 更新为新需求数量
            assertThat(newCallOff.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_012: JIT Locked Call-off")
        void testJITLockedCallOff() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("JIT-001", currentDate, 100, "JIT", 13);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            CallOffOrder existingCallOff = createCallOffOrder("JIT-001", currentDate, 100, true, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(existingCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder newCallOff = result.get(0);
            assertThat(newCallOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(100); // 保持原有数量
            assertThat(newCallOff.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_013: JIT Non-locked Call-off")
        void testJITNonLockedCallOff() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("JIT-001", currentDate, 100, "JIT", 6);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            CallOffOrder existingCallOff = createCallOffOrder("JIT-001", currentDate, 50, false, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(existingCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder newCallOff = result.get(0);
            assertThat(newCallOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(100); // 更新为新需求数量
            assertThat(newCallOff.getLocked()).isFalse();
        }

        @Test
        @DisplayName("TC_MC_014: No Demand with Locked Call-off")
        void testNoDemandWithLockedCallOff() {
            // 准备测试数据
            List<DemandOrder> demandOrders = new ArrayList<>();

            CallOffOrder existingCallOff = createCallOffOrder("PO2-001", currentDate, 50, true, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(existingCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder newCallOff = result.get(0);
            assertThat(newCallOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(50); // 保持原有数量
            assertThat(newCallOff.getLocked()).isTrue();
        }
    }

    @Nested
    @DisplayName("B4. Daily Iteration and Virtual Demand Tests")
    class DailyIterationTests {

        @Test
        @DisplayName("TC_MC_015: Single Day Demand, No Difference")
        void testSingleDayDemandNoDifference() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder callOff = result.get(0);
            assertThat(callOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(callOff.getCallOffQuantity()).isEqualTo(100);
            assertThat(callOff.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_016: Multiple Days Demand, No Cross-day Difference")
        void testMultipleDaysDemandNoCrossDayDifference() {
            // 准备测试数据
            DemandOrder demandOrder1 = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            DemandOrder demandOrder2 = createDemandOrder("PO2-002", currentDate.plusDays(1), 50, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder1, demandOrder2);
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(2);

            // 验证第一天的叫料单
            CallOffOrder callOff1 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(callOff1.getCallOffQuantity()).isEqualTo(100);
            assertThat(callOff1.getLocked()).isTrue();

            // 验证第二天的叫料单
            CallOffOrder callOff2 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate.plusDays(1)))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(callOff2.getCallOffQuantity()).isEqualTo(50);
            assertThat(callOff2.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_017: Cross-day Positive Difference")
        void testCrossDayPositiveDifference() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            CallOffOrder existingCallOff = createCallOffOrder("PO2-001", currentDate, 80, true, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(existingCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(2);

            // 验证第一天的叫料单
            CallOffOrder callOff1 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(callOff1.getCallOffQuantity()).isEqualTo(80);
            assertThat(callOff1.getLocked()).isTrue();

            // 验证第二天的叫料单（虚拟需求）
            CallOffOrder callOff2 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate.plusDays(1)))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(callOff2.getCallOffQuantity()).isEqualTo(20);
            assertThat(callOff2.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_018: Cross-day Negative Difference")
        void testCrossDayNegativeDifference() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 80, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            CallOffOrder existingCallOff = createCallOffOrder("PO2-001", currentDate, 100, true, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(existingCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(2);

            // 验证第一天的叫料单
            CallOffOrder callOff1 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(callOff1.getCallOffQuantity()).isEqualTo(100);
            assertThat(callOff1.getLocked()).isTrue();

            // 验证第二天的叫料单（虚拟需求）
            CallOffOrder callOff2 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate.plusDays(1)))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(callOff2.getCallOffQuantity()).isEqualTo(-20);
            assertThat(callOff2.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_019: Loop Termination - No More Demands")
        void testLoopTerminationNoMoreDemands() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder callOff = result.get(0);
            assertThat(callOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(callOff.getCallOffQuantity()).isEqualTo(100);
            assertThat(callOff.getLocked()).isTrue();
        }

        @Test
        @DisplayName("TC_MC_020: Virtual Demand Fields Correctness")
        void testVirtualDemandFieldsCorrectness() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            CallOffOrder existingCallOff = createCallOffOrder("PO2-001", currentDate, 80, true, 0);
            List<CallOffOrder> callOffOrders = Lists.newArrayList(existingCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(2);

            // 验证虚拟需求生成的叫料单字段
            CallOffOrder virtualCallOff = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate.plusDays(1)))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);

            assertThat(virtualCallOff.getOrderNo()).isEqualTo("PO2-001");
            assertThat(virtualCallOff.getDemandDate()).isEqualTo(currentDate.plusDays(1));
            assertThat(virtualCallOff.getMaterialId()).isEqualTo("M1");
            assertThat(virtualCallOff.getSupplier()).isEqualTo("S1");
            assertThat(virtualCallOff.getFactoryCode()).isEqualTo("F1");
            assertThat(virtualCallOff.getCallOffQuantity()).isEqualTo(20);
            assertThat(virtualCallOff.getLocked()).isTrue();
            assertThat(virtualCallOff.getReceivedQuantity()).isEqualTo(0);
        }

        @Test
        @DisplayName("TC_MC_021: New Call-off Fields Correctness")
        void testNewCallOffFieldsCorrectness() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);
            List<CallOffOrder> callOffOrders = new ArrayList<>();

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(1);
            CallOffOrder newCallOff = result.get(0);

            assertThat(newCallOff.getCallOffDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(100);
            assertThat(newCallOff.getOrderNo()).isEqualTo("PO2-001");
            assertThat(newCallOff.getDemandDate()).isEqualTo(currentDate);
            assertThat(newCallOff.getMaterialId()).isEqualTo("M1");
            assertThat(newCallOff.getSupplier()).isEqualTo("S1");
            assertThat(newCallOff.getFactoryCode()).isEqualTo("F1");
            assertThat(newCallOff.getLocked()).isTrue();
            assertThat(newCallOff.getReceivedQuantity()).isEqualTo(0);
        }
    }

    @Nested
    @DisplayName("D. Additional Edge Cases and Complex Scenarios")
    class AdditionalEdgeCaseTests {

        @Test
        @DisplayName("Multiple Days Virtual Demand Chain")
        void testMultipleDaysVirtualDemandChain() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            // 第一天的叫料单
            CallOffOrder day1CallOff = createCallOffOrder("PO2-001", currentDate, 40, true, 0);
            // 第二天的叫料单
            CallOffOrder day2CallOff = createCallOffOrder("PO2-001", currentDate.plusDays(1), 30, true, 0);
            // 第三天的叫料单
            CallOffOrder day3CallOff = createCallOffOrder("PO2-001", currentDate.plusDays(2), 20, true, 0);

            List<CallOffOrder> callOffOrders = Lists.newArrayList(day1CallOff, day2CallOff, day3CallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(4); // 3个原有叫料单 + 1个新的虚拟需求叫料单

            // 验证第一天的叫料单
            CallOffOrder resultDay1 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(resultDay1.getCallOffQuantity()).isEqualTo(40);
            assertThat(resultDay1.getLocked()).isTrue();

            // 验证第二天的叫料单
            CallOffOrder resultDay2 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate.plusDays(1)))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(resultDay2.getCallOffQuantity()).isEqualTo(30);
            assertThat(resultDay2.getLocked()).isTrue();

            // 验证第三天的叫料单
            CallOffOrder resultDay3 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate.plusDays(2)))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(resultDay3.getCallOffQuantity()).isEqualTo(20);
            assertThat(resultDay3.getLocked()).isTrue();

            // 验证第四天的虚拟需求叫料单
            CallOffOrder resultDay4 = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate.plusDays(3)))
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(resultDay4.getCallOffQuantity()).isEqualTo(10);
            assertThat(resultDay4.getLocked()).isTrue();
        }

        @Test
        @DisplayName("Multiple Locked Call-off Priority")
        void testMultipleLockedCallOffPriority() {
            // 准备测试数据
            DemandOrder demandOrder = createDemandOrder("PO2-001", currentDate, 100, "PO2", 1);
            List<DemandOrder> demandOrders = Lists.newArrayList(demandOrder);

            // 同一天有多个锁定叫料单
            CallOffOrder lockedCallOff1 = createCallOffOrder("PO2-001", currentDate, 40, true, 0);
            CallOffOrder lockedCallOff2 = createCallOffOrder("PO2-001", currentDate, 30, true, 0);
            CallOffOrder nonLockedCallOff = createCallOffOrder("PO2-001", currentDate, 20, false, 0);

            List<CallOffOrder> callOffOrders = Lists.newArrayList(lockedCallOff1, lockedCallOff2, nonLockedCallOff);

            // 执行测试
            List<CallOffOrder> result = service.calculateCallOffPlan(demandOrders, callOffOrders, currentDate);

            // 验证结果
            assertThat(result).hasSize(2); // 2个锁定叫料单 + 1个新的叫料单

            // 验证锁定叫料单保持不变
            assertThat(result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate) && c.getLocked())
                    .mapToInt(CallOffOrder::getCallOffQuantity)
                    .sum())
                    .isEqualTo(70); // 40 + 30

            // 验证新的叫料单
            CallOffOrder newCallOff = result.stream()
                    .filter(c -> c.getCallOffDate().equals(currentDate) && !c.getLocked())
                    .findFirst()
                    .orElseThrow(RuntimeException::new);
            assertThat(newCallOff.getCallOffQuantity()).isEqualTo(30); // 100 - 70
        }

        @Test
        @DisplayName("Null Input Handling")
        void testNullInputHandling() {
            // 测试null需求单列表
            assertThatThrownBy(() ->
                    service.calculateCallOffPlan(null, new ArrayList<>(), currentDate))
                    .isInstanceOf(IllegalArgumentException.class);

            // 测试null叫料单列表
            assertThatThrownBy(() ->
                    service.calculateCallOffPlan(new ArrayList<>(), null, currentDate))
                    .isInstanceOf(IllegalArgumentException.class);

            // 测试null当前日期
            assertThatThrownBy(() ->
                    service.calculateCallOffPlan(new ArrayList<>(), new ArrayList<>(), null))
                    .isInstanceOf(IllegalArgumentException.class);

            // 测试需求单中包含null
            List<DemandOrder> demandOrdersWithNullDTO = new ArrayList<>();
            demandOrdersWithNullDTO.add(null);
            assertThatThrownBy(() ->
                    service.calculateCallOffPlan(demandOrdersWithNullDTO, new ArrayList<>(), currentDate))
                    .isInstanceOf(IllegalArgumentException.class);

            // 测试叫料单中包含null
            List<CallOffOrder> callOffOrdersWithNullDTO = new ArrayList<>();
            callOffOrdersWithNullDTO.add(null);
            assertThatThrownBy(() ->
                    service.calculateCallOffPlan(new ArrayList<>(), callOffOrdersWithNullDTO, currentDate))
                    .isInstanceOf(IllegalArgumentException.class);
        }
    }
} 