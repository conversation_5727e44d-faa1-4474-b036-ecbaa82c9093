package com.lds.oneplanning.po;

import com.lds.oneplanning.po.enums.TransportType;
import org.junit.jupiter.params.provider.Arguments;

import java.time.LocalDate;
import java.util.stream.Stream;

public class ParameterSources {
    public static Stream<Arguments> provideLockingTestCases() {
        LocalDate testCurrentDate = LocalDate.of(2024, 6, 15);
        return Stream.of(
                Arguments.of("锁定日期为null", null, testCurrentDate, false),
                Arguments.of("锁定日期早于当前日期", testCurrentDate.minusDays(1), testCurrentDate, true),
                Arguments.of("锁定日期等于当前日期", testCurrentDate, testCurrentDate, true),
                Arguments.of("锁定日期晚于当前日期", testCurrentDate.plusDays(1), testCurrentDate, false)
        );
    }

    public static Stream<Arguments> provideTransportTypeTestCases() {
        return Stream.of(
                Arguments.of(TransportType.PO2, 1),
                Arguments.of(TransportType.PO2, 5),
                Arguments.of(TransportType.PO2, 10),
                Arguments.of(TransportType.JIT, 6),
                Arguments.of(TransportType.JIT, 12),
                Arguments.of(TransportType.JIT, 24)
        );
    }

    public static Stream<Arguments> provideDateTestCases() {
        LocalDate testCurrentDate = LocalDate.of(2024, 6, 15);
        return Stream.of(
                Arguments.of("需求日期早于当前日期", testCurrentDate.minusDays(1), testCurrentDate, false),
                Arguments.of("需求日期等于当前日期", testCurrentDate, testCurrentDate, true),
                Arguments.of("需求日期晚于当前日期", testCurrentDate.plusDays(1), testCurrentDate, true),
                Arguments.of("需求日期远晚于当前日期", testCurrentDate.plusDays(30), testCurrentDate, true)
        );
    }

    public static Stream<Arguments> providePO2LockTestCases() {
        LocalDate baseDate = LocalDate.of(2024, 3, 1);
        return Stream.of(
                Arguments.of("TC_LK_001: Transport time 0 days", 0, baseDate.plusDays(1)),
                Arguments.of("TC_LK_002: Transport time 3 days", 3, baseDate.plusDays(4))
        );
    }

    private static Stream<Arguments> provideJITLockTestCases() {
        return Stream.of(
                Arguments.of("TC_LK_003: Transport time < 12 hours", 6, false),
                Arguments.of("TC_LK_004: Transport time = 12 hours", 12, false),
                Arguments.of("TC_LK_005: Transport time > 12 hours", 13, true),
                Arguments.of("TC_LK_006: Transport time = 0 hours", 0, false)
        );
    }
}
