package com.lds.oneplanning.wps.service.impl;

import com.google.common.collect.Lists;
import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.enums.OrderWarningLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;

@Slf4j
class WpsOrderPlanWarningServiceImplTest extends Junit5BaseTest {

    @Autowired
    private WpsOrderPlanWarningServiceImpl wpsOrderPlanWarningService;

    @Test
    void batchSaveUnHandlerWarning() {
        List<WpsOrderPlanWarning> wpsOrderPlanWarningList = Lists.newArrayList();
        IntStream.rangeClosed(1, 10).forEach(i -> {
            WpsOrderPlanWarning wpsOrderPlanWarning = buildWpsOrderPlanWarning(1L, "orderNo" + i, OrderWarningLevelEnum.WARNING, LocalDate.now());
            wpsOrderPlanWarningList.add(wpsOrderPlanWarning);
        });
        IntStream.rangeClosed(11, 15).forEach(i -> {
            WpsOrderPlanWarning wpsOrderPlanWarning = buildWpsOrderPlanWarning(1L, "orderNo" + i, OrderWarningLevelEnum.ALARM, LocalDate.now());
            wpsOrderPlanWarningList.add(wpsOrderPlanWarning);
        });
        wpsOrderPlanWarningService.batchSaveUnHandlerWarning(wpsOrderPlanWarningList);
    }

    private WpsOrderPlanWarning buildWpsOrderPlanWarning(Long userId, String orderNo, OrderWarningLevelEnum orderWarningLevelEnum, LocalDate scheduleDate) {
        WpsOrderPlanWarning wpsOrderPlanWarning = new WpsOrderPlanWarning();
        wpsOrderPlanWarning.setOrderNo(orderNo);
        wpsOrderPlanWarning.setWarningCategory("default");
        wpsOrderPlanWarning.setWarningType("schedule_not_match");
        wpsOrderPlanWarning.setWarningLevel(orderWarningLevelEnum.getLevel());
        wpsOrderPlanWarning.setScheduleDate(scheduleDate);
        wpsOrderPlanWarning.setTriggerTime(new Date());
        wpsOrderPlanWarning.setCreateBy(userId);
        wpsOrderPlanWarning.setUpdateBy(userId);
        return wpsOrderPlanWarning;
    }
}