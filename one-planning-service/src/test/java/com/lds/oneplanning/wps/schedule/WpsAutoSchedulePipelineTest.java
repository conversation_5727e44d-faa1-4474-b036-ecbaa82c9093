package com.lds.oneplanning.wps.schedule;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.WpsExcelService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Slf4j
class WpsAutoSchedulePipelineTest extends Junit5BaseTest {

    @Autowired
    private WpsAutoSchedulePipeline wpsAutoSchedulePipeline;

    @Autowired
    private WpsExcelService wpsExcelService;

    @Test
    void execute() {
        Long userId = 1367766865382349738L;
        // LocalDate 转Date
        Date startDate = LocalDateTimeUtil.localDateToDate(LocalDate.now().minusDays(30));
        Date endDate = new Date();
        List<WpsRowData> wpsRowDataList = wpsExcelService.getBody(userId, WpsConstants.DATA_SOURCE_AUTO, startDate, endDate,"3301",true, Maps.newHashMap());
        wpsRowDataList.forEach(wpsRowData -> {
            wpsRowData.setOrderPcsQty(10000000);
            wpsRowData.setProductId("10000001");
            wpsRowData.set_startProductPeriod(LocalDate.of(2025, 2, 1));
            wpsRowData.set_endProductPeriod(LocalDate.now().plusDays(10));
        });
        WpsAutoScheduleContext context = new WpsAutoScheduleContext(wpsRowDataList);
        context.setUserId(userId);
        context.setCurrentDate(LocalDate.of(2025, 2, 1));
        context.setWeeksToPush(12);
        wpsAutoSchedulePipeline.
                execute(context);
        log.info("AutoSchedulePipelineTest execute success, result: {}.", JSON.toJSONString(context.getOrderDailyScheduleDataMap()));
    }
}