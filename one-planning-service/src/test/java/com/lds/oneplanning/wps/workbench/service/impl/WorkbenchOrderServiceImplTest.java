package com.lds.oneplanning.wps.workbench.service.impl;

import com.alibaba.fastjson.JSON;
import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.wps.workbench.resp.OrderStatusResp;
import com.lds.oneplanning.wps.workbench.service.IWorkbenchOrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
class WorkbenchOrderServiceImplTest extends Junit5BaseTest {

    @Autowired
    private IWorkbenchOrderService workbenchOrderService;

    @Test
    void listOrderStatus() {
        List<OrderStatusResp> orderStatusRespList = workbenchOrderService.listOrderStatus(1367766865382353155L);
        log.info("orderStatusRspList={}.", JSON.toJSONString(orderStatusRespList));
    }
}