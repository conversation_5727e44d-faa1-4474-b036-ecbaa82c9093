package com.lds.oneplanning.wps.service.facade.impl;

import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.wps.service.facade.IWpsSyncLineProductUphService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class WpsSyncLineProductUphServiceImplTest extends Junit5BaseTest {

    @Autowired
    private IWpsSyncLineProductUphService wpsSyncLineProductUphService;

    @Test
    void syncLineProductUph() {
        wpsSyncLineProductUphService.syncLineProductUph();
    }
}