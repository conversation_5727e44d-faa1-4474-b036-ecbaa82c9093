package com.lds.oneplanning;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ClassUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.helper.ColumnHeaderBuildHelper;
import com.lds.oneplanning.wps.utils.MockDataGenerator;
import com.lds.oneplanning.wps.vo.InProductionAbnormalVO;
import com.lds.oneplanning.wps.vo.TableColumn;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

public class SimpleTest {
    @Test
    @SneakyThrows
    public void columnHeaderBuildTest() {
        List<TableColumn> columns = ColumnHeaderBuildHelper.build(InProductionAbnormalVO.class);
        System.out.println(JSON.toJSONString(columns, true));
    }

    @Test
    @SneakyThrows
    public void mockData() {
        List<InProductionAbnormalVO> vo = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
//            vo.add(MockDataGenerator.mockInProductionAbnormalVO());
            vo.add(MockDataGenerator.mockAny(InProductionAbnormalVO.class));
        }
//        System.out.println(JSON.toJSONString(vo, true));

        for (int i = 0; i < 5; i++) {
            System.out.println(MockDataGenerator.randomLocalDate());
        }
    }

    @Test
    @SneakyThrows
    public void loadAllHeader() {
        //扫描com.lds.oneplanning.wps.vo包下所有带有@TableHeader注解的类
        Set<Class<?>> classes = ClassUtil.scanPackageByAnnotation("com.lds.oneplanning.wps.vo", TableHeader.class);
        for (Class<?> clazz : classes) {
            if (clazz.isAnnotationPresent(TableHeader.class)) {
                List<TableColumn> columns = ColumnHeaderBuildHelper.build(clazz);
                System.out.println(JSON.toJSONString(columns, true));
            }
        }
    }

    @Test
    @SneakyThrows
    public void descWarningType() {
        JSONObject json = new JSONObject();
        for (WpsOrderWarningTypeEnum value : WpsOrderWarningTypeEnum.values()) {
            json.put(value.getCode(), value.getName());
        }
        System.out.println(JSON.toJSONString(json, true));

    }


    @Test
    @SneakyThrows
    public void printMockData() {


    }




    @Test
    @SneakyThrows
    public void isTomorrowTest() {
        // 获取当前时间
        LocalDate now = LocalDate.now();
        // 获取明天的日期
        LocalDate tomorrow = now.plusDays(1);

        for (int i = 0; i < 30; i++) {
            Date date = MockDataGenerator.randomDate();
            System.out.println(DateUtil.formatDate(date) + " - " + isSomeDay(tomorrow, date));
        }

        //获取第4-14天
        LocalDate start = now.plusDays(3);
        LocalDate end = now.plusDays(13);
        for (int i = 0; i < 30; i++) {
            Date date = MockDataGenerator.randomDate();
            System.out.println(start + " - " + end + ": " + DateUtil.formatDate(date) + " - " + isBetween(start, end, date));
        }
    }

    /**
     * 判断给定的日期是否与基准日期相同。
     *
     * @param baseDate 基准日期
     * @param date     需要判断的日期
     * @return 如果传入的日期与基准日期相同，则返回true；否则返回false
     */
    private boolean isSomeDay(LocalDate baseDate, Date date) {
        // 将传入的日期转换为LocalDate
        LocalDate targetDate = LocalDateTimeUtil.dateToLocalDate(date);
        // 比较两个日期是否相等
        return baseDate.equals(targetDate);
    }

    /**
     * 判断给定日期是否在指定的两个日期之间（包括边界日期）。
     *
     * @param start 起始日期（包括此日期）
     * @param end   结束日期（包括此日期）
     * @param date  待判断的日期
     * @return 如果日期在起始日期和结束日期之间，则返回true；否则返回false
     */
    private boolean isBetween(LocalDate start, LocalDate end, Date date) {
        // 将传入的日期转换为LocalDate
        LocalDate targetDate = LocalDateTimeUtil.dateToLocalDate(date);
        return !targetDate.isBefore(start) && !targetDate.isAfter(end);
    }


    @Test
    @SneakyThrows
    public void printLog() {
        String log =
                "执行工作台预警handler异常, warningType:ATP_EXCEPTION java.lang.NullPointerException: null at java.util.HashMap.merge(HashMap.java:1226) at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320) at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169) at java.util.HashMap$EntrySpliterator.forEachRemaining(HashMap.java:1723) at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) at com.lds.oneplanning.common.service.impl.BasicUserServiceImpl.batchGetLoginNamesByPoGroups(BasicUserServiceImpl.java:71) at com.lds.oneplanning.wps.service.impl.WarningMaterialAtpAbnormalShortageServiceImpl.getShortageAssignee(WarningMaterialAtpAbnormalShortageServiceImpl.java:339) at com.lds.oneplanning.wps.service.impl.WarningMaterialAtpAbnormalShortageServiceImpl.createTodo(WarningMaterialAtpAbnormalShortageServiceImpl.java:239) at com.lds.oneplanning.wps.service.impl.WarningMaterialAtpAbnormalShortageServiceImpl.createTodoList(WarningMaterialAtpAbnormalShortageServiceImpl.java:321) at com.lds.oneplanning.wps.service.impl.WarningMaterialAtpAbnormalShortageServiceImpl$$FastClassBySpringCGLIB$$79c1067a.invoke(<generated>) at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687) at com.lds.oneplanning.wps.service.impl.WarningMaterialAtpAbnormalShortageServiceImpl$$EnhancerBySpringCGLIB$$276b735c.createTodoList(<generated>) at com.lds.oneplanning.wps.warning.workbench.handlers.MaterialAtpAbnormalHandler.saveData(MaterialAtpAbnormalHandler.java:180) at com.lds.oneplanning.wps.warning.workbench.handlers.MaterialAtpAbnormalHandler$$FastClassBySpringCGLIB$$1b212fd1.invoke(<generated>) at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771) at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749) at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366) at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118) at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749) at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691) at com.lds.oneplanning.wps.warning.workbench.handlers.MaterialAtpAbnormalHandler$$EnhancerBySpringCGLIB$$7a48fdc1.saveData(<generated>) at com.lds.oneplanning.wps.warning.workbench.handlers.MaterialAtpAbnormalHandler.analyzeAbnormalData(MaterialAtpAbnormalHandler.java:159) at com.lds.oneplanning.wps.warning.workbench.handlers.MaterialAtpAbnormalHandler.doExecute(MaterialAtpAbnormalHandler.java:105) at com.lds.oneplanning.wps.warning.workbench.handlers.MaterialAtpAbnormalHandler.execute(MaterialAtpAbnormalHandler.java:66) at com.lds.oneplanning.wps.warning.workbench.handlers.MaterialAtpAbnormalHandler$$FastClassBySpringCGLIB$$1b212fd1.invoke(<generated>) at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687) at com.lds.oneplanning.wps.warning.workbench.handlers.MaterialAtpAbnormalHandler$$EnhancerBySpringCGLIB$$7a48fdc1.execute(<generated>) at com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningPipeline.processSingleHandler(WpsWorkbenchWarningPipeline.java:120) at com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningPipeline.lambda$execute$0(WpsWorkbenchWarningPipeline.java:90) at java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:948) at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:647) at com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningPipeline.execute(WpsWorkbenchWarningPipeline.java:73) at com.lds.oneplanning.wps.warning.WpsPlanWarningManager.processMessage(WpsPlanWarningManager.java:82) at com.lds.oneplanning.consumer.rocket.WpsPlanWarningNotifyListener.processMessage(WpsPlanWarningNotifyListener.java:32) at com.lds.coral.mq.rocketmq.listener.AbstractPullRocketListener.batchProcessMessage(AbstractPullRocketListener.java:150) at com.lds.coral.mq.rocketmq.listener.AbstractPullRocketListener.process(AbstractPullRocketListener.java:128) at com.lds.coral.mq.rocketmq.listener.AbstractPullRocketListener.lambda$prepareStart$0(AbstractPullRocketListener.java:113) at org.apache.rocketmq.client.impl.consumer.ConsumeMessageConcurrentlyService$ConsumeRequest.run(ConsumeMessageConcurrentlyService.java:411) at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) at java.util.concurrent.FutureTask.run(FutureTask.java:266) at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) at java.lang.Thread.run(Thread.java:750)\n";

        log = log.replaceAll("at ", "\n\tat ");

        System.out.println(log);
    }

    @Test
    @SneakyThrows
    public void enumPrint() {
        String json1 = "{\n" +
                "    \"170\": \"32010014\",\n" +
                "    \"193\": \"31010088\",\n" +
                "    \"194\": \"100014223\",\n" +
                "    \"284\": \"31035901\",\n" +
                "    \"142\": \"100030809\",\n" +
                "    \"296\": \"31036657\",\n" +
                "    \"033\": \"30000095\",\n" +
                "    \"155\": \"30104423\",\n" +
                "    \"650\": \"100032862\",\n" +
                "    \"298\": \"10300406\",\n" +
                "    \"212\": \"100031947\",\n" +
                "    \"058\": \"100033927\",\n" +
                "    \"300\": \"31087419\",\n" +
                "    \"136\": \"11007021\",\n" +
                "    \"203\": \"30100751\",\n" +
                "    \"302\": \"30100267\",\n" +
                "    \"017\": \"30105743\",\n" +
                "    \"215\": \"100020783\",\n" +
                "    \"007\": \"30106151\",\n" +
                "    \"679\": \"30001446\"\n" +
                "  }";

        JSONObject poGroupJobNoMap = JSON.parseObject(json1);

        String json2 = "{\n" +
                "    \"30100751\": \"wangqz\",\n" +
                "    \"30100267\": \"linhy3\",\n" +
                "    \"10300406\": \"shensz\",\n" +
                "    \"31010088\": \"liq\",\n" +
                "    \"100014223\": \"xuqingyan\",\n" +
                "    \"32010014\": \"yangjb\",\n" +
                "    \"100020783\": \"wuyupeng\",\n" +
                "    \"11007021\": \"chenyingting\",\n" +
                "    \"30001446\": \"yangjw\",\n" +
                "    \"31087419\": \"linjiaozhen\",\n" +
                "    \"100030809\": \"jianwenhang\",\n" +
                "    \"30106151\": \"wumingyang\",\n" +
                "    \"100033927\": \"chenxiumei2\",\n" +
                "    \"30000095\": \"laixp\",\n" +
                "    \"100031947\": \"wuruyu\",\n" +
                "    \"31035901\": \"lianghuijuan\",\n" +
                "    \"30104423\": \"zhangnarong\",\n" +
                "    \"30105743\": \"wangzhiyu1\",\n" +
                "    \"31036657\": \"zhengxiaoxian\"\n" +
                "  }";
        JSONObject jobNoLoginNameMap = JSON.parseObject(json2);

        Map<String, Object> collect = poGroupJobNoMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> jobNoLoginNameMap.get(entry.getValue())));

        System.out.println(JSON.toJSONString(collect, true));

    }
}
