package com.lds.oneplanning.esb.datafetch.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.esb.biz.req.GetDpsLineUphRequest;
import com.lds.oneplanning.esb.biz.req.GetMesLineRequest;
import com.lds.oneplanning.esb.biz.resp.GetDpsLineUphResponse;
import com.lds.oneplanning.esb.biz.resp.GetMesLineResponse;
import com.lds.oneplanning.esb.datafetch.model.EsbGetLineUphDetail;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
class EsbMesDataFetchServiceTest extends Junit5BaseTest {

    @Autowired
    private EsbMesDataFetchService esbMesDataFetchService;

    @Test
    void getDpsLineUphList() {
        List<GetDpsLineUphRequest> getDpsLineUphRequests = Lists.newArrayList();
        getDpsLineUphRequests.add(GetDpsLineUphRequest.builder().lineCode("72093656").productCode("6005900051").build());
        List<GetDpsLineUphResponse> dpsLineUphList = esbMesDataFetchService.getDpsLineUphList(getDpsLineUphRequests);
        log.info("dpsLineUphList: {}.", JSON.toJSON(dpsLineUphList));
    }

    @Test
    void getBestDetail(){
        List<EsbGetLineUphDetail> esbGetLineUphDetails = Lists.newArrayList();
        esbGetLineUphDetails.add(EsbGetLineUphDetail.builder()
                .lineCode("50030231").productCode("1021012167")
                .uph(100F)
                .build());
        esbGetLineUphDetails.add(EsbGetLineUphDetail.builder()
                .lineCode("50030231").productCode("1021012167")
                .groupCode("1").groupCounter("2").uph(200F)
                .build());
        Optional<EsbGetLineUphDetail> bestDetail = esbMesDataFetchService.getBestDetail(esbGetLineUphDetails);
        log.info("bestDetail: {}.", JSON.toJSON(bestDetail));
    }

    @Test
    void syncMesLineList() {
        Set<GetMesLineResponse> getMesLineResponses = esbMesDataFetchService.syncMesLineList(GetMesLineRequest.builder().build());
        log.info("getMesLineResponses: {}.", JSON.toJSON(getMesLineResponses));
    }
}