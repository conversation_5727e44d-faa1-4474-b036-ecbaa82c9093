package com.lds.oneplanning.esb.datafetch.service.impl;

import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.esb.datafetch.model.EsbGetHbData;
import com.lds.oneplanning.esb.datafetch.model.EsbIncomeMaterialCheckAbnormalData;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.skd.job.SyncSkdDataHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
class EsbDasDataFetchServiceTest extends Junit5BaseTest {

    @Autowired
    private IEsbDataFetchService esbDataFetchService;

    @Autowired
    private SyncSkdDataHandler syncSkdDataHandler;

    @Test
    void fetchFactoryList() {
        esbDataFetchService.fetchFactoryList();
    }
    @Test
    void getPoGroupData() {
        EsbIncomeMaterialCheckAbnormalData data = new EsbIncomeMaterialCheckAbnormalData();
        data.setEBELN("61738379");
        data.setEBELP("10");
        List<EsbIncomeMaterialCheckAbnormalData> datas = new ArrayList<>();
        datas.add(data);
        List<EsbIncomeMaterialCheckAbnormalData> poGroupData = esbDataFetchService.incomeMaterialCheckAbnormal(datas);
        System.out.println(poGroupData);
    }

    @Test
    void getHbData() {
        List<String> jobNos = new ArrayList<>();
        jobNos.add("100012801");
        jobNos.add("11006650");
        List<EsbGetHbData> hbData = esbDataFetchService.getHbData(jobNos);
        log.info("hbData:{}", hbData.get(0).getLeader());
    }

    @Test
    void getSkdData() throws Exception {
        syncSkdDataHandler.execute("{\"factoryCode\":\"2602\"}");
    }
}
