package com.lds.oneplanning.common.service.impl;

import com.google.common.collect.Lists;
import com.lds.oneplanning.Junit5BaseTest;
import com.lds.oneplanning.common.service.IBasicUserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class BasicUserServiceImplTest extends Junit5BaseTest {

    @Autowired
    private IBasicUserService basicUserService;

    @Test
    void batchGetLoginNamesByJobNos() {
        List<String> jobNos = Lists.newArrayList("11006650", "100012801");
        Map<String, String> loginNamesByJobNos = basicUserService.batchGetLoginNamesByJobNos(jobNos);
        log.info("batchGetLoginNamesByJobNos:{}.", loginNamesByJobNos);
    }

    @Test
    void batchGetLoginNamesByPoGroups() {
        List<String> poGroups = Lists.newArrayList("002", "003");
        Map<String, String> poGroupsLoginNames = basicUserService.batchGetLoginNamesByPoGroups(poGroups);
        log.info("batchGetLoginNamesByPoGroups:{}.", poGroupsLoginNames);
    }

    @Test
    void batchGetLeaderLoginNames(){
        List<String> jobNos = Lists.newArrayList("11006650", "100012801");
        Map<String, String> jobNoLeaderLoginNameMap = basicUserService.batchGetLeaderLoginNames(jobNos);
        log.info("batchGetLeaderLoginNames:{}.",jobNoLeaderLoginNameMap);
    }
}