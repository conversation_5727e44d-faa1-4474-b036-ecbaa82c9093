<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>利润计算器 - 完整原型</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4361ee;
            --secondary: #3f37c9;
            --success: #4cc9f0;
            --danger: #f72585;
            --warning: #f8961e;
            --light: #f8f9fa;
            --dark: #212529;
            --border: #dee2e6;
            --card-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f5f7fb;
            color: #333;
            line-height: 1.6;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: var(--card-shadow);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo i {
            font-size: 2.2rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 12px;
            border-radius: 50%;
        }

        .logo h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #4cc9f0, #4895ef);
            color: white;
            padding: 15px 20px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 18px;
        }

        label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #555;
        }

        input, select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border);
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group > div {
            flex: 1;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary);
        }

        .btn-outline {
            background: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
        }

        .btn-outline:hover {
            background: rgba(67, 97, 238, 0.1);
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .btn-danger:hover {
            background: #e11571;
        }

        .product-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .product-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--border);
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .product-info {
            flex: 1;
        }

        .product-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .product-meta {
            display: flex;
            gap: 15px;
            font-size: 0.85rem;
            color: #666;
        }

        .product-profit {
            min-width: 100px;
            text-align: right;
        }

        .profit-positive {
            color: #2ecc71;
            font-weight: 700;
        }

        .profit-negative {
            color: var(--danger);
            font-weight: 700;
        }

        .summary-card {
            background: linear-gradient(135deg, #7209b7, #560bad);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 25px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .summary-item {
            background: rgba(255, 255, 255, 0.15);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .summary-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 10px 0;
        }

        .history-list {
            list-style: none;
        }

        .history-item {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-date {
            font-size: 0.85rem;
            color: #777;
        }

        .chart-container {
            height: 200px;
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            padding: 20px 10px 0;
            gap: 10px;
        }

        .chart-bar {
            width: 40px;
            background: var(--primary);
            border-radius: 4px 4px 0 0;
            position: relative;
            text-align: center;
            color: white;
            font-weight: 500;
            font-size: 0.85rem;
            padding: 5px 0;
        }

        .chart-bar.negative {
            background: var(--danger);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .action-buttons .btn {
            flex: 1;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #777;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #ccc;
        }

        .profit-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .positive {
            background: #2ecc71;
        }

        .negative {
            background: var(--danger);
        }

        .instructions {
            background: #e3f2fd;
            border-left: 4px solid var(--primary);
            padding: 15px;
            border-radius: 0 8px 8px 0;
            margin: 20px 0;
            font-size: 0.9rem;
        }

        .instructions h3 {
            margin-bottom: 10px;
            color: var(--primary);
        }
    </style>
</head>
<body>
<div class="app-container">
    <header>
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <h1>利润计算器</h1>
            </div>
            <div>
                <button class="btn btn-outline">
                    <i class="fas fa-history"></i> 历史记录
                </button>
            </div>
        </div>
    </header>

    <div class="instructions">
        <h3><i class="fas fa-info-circle"></i> 使用说明</h3>
        <p>添加产品信息，系统将自动计算利润。支持多货币计算，可添加多个产品并查看总利润。</p>
    </div>

    <div class="main-content">
        <!-- 左侧：产品输入区域 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-plus-circle"></i>
                <span>添加新产品</span>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label for="productName">产品名称</label>
                    <input type="text" id="productName" placeholder="例如：白色简装">
                </div>

                <div class="input-group">
                    <div class="form-group">
                        <label for="quantity">数量</label>
                        <input type="number" id="quantity" placeholder="例如：789">
                    </div>

                    <div class="form-group">
                        <label for="currency">采购货币</label>
                        <select id="currency">
                            <option value="USD">USD</option>
                            <option value="HKD" selected>HKD</option>
                            <option value="CNY">CNY</option>
                            <option value="EUR">EUR</option>
                        </select>
                    </div>
                </div>

                <div class="input-group">
                    <div class="form-group">
                        <label for="purchasePrice">采购单价</label>
                        <input type="number" id="purchasePrice" placeholder="例如：106" step="0.01">
                    </div>

                    <div class="form-group">
                        <label for="exchangeRate">汇率 (1 USD = ?)</label>
                        <input type="number" id="exchangeRate" value="7.84" step="0.01">
                    </div>
                </div>

                <div class="form-group">
                    <label for="salePrice">销售单价 (USD)</label>
                    <input type="number" id="salePrice" placeholder="例如：16.88" step="0.01">
                </div>

                <button class="btn btn-primary" style="width: 100%;">
                    <i class="fas fa-plus"></i> 添加产品
                </button>
            </div>
        </div>

        <!-- 右侧：产品列表 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list"></i>
                <span>产品列表</span>
            </div>
            <div class="card-body">
                <div class="product-list">
                    <!-- 产品项 1 -->
                    <div class="product-item">
                        <div class="product-info">
                            <div class="product-name">6400</div>
                            <div class="product-meta">
                                <span>数量: 6400</span>
                                <span>采购: 28.7 HKD</span>
                                <span>销售: 28.0 USD</span>
                            </div>
                        </div>
                        <div class="product-profit profit-negative">
                            -4,478 USD
                        </div>
                    </div>

                    <!-- 产品项 2 -->
                    <div class="product-item">
                        <div class="product-info">
                            <div class="product-name">256简装</div>
                            <div class="product-meta">
                                <span>数量: 256</span>
                                <span>采购: 0.0 HKD</span>
                                <span>销售: 16.88 USD</span>
                            </div>
                        </div>
                        <div class="product-profit profit-positive">
                            +4,321 USD
                        </div>
                    </div>

                    <!-- 产品项 3 -->
                    <div class="product-item">
                        <div class="product-info">
                            <div class="product-name">白色简装</div>
                            <div class="product-meta">
                                <span>数量: 789</span>
                                <span>采购: 106 HKD</span>
                                <span>销售: 16.88 USD</span>
                            </div>
                        </div>
                        <div class="product-profit profit-positive">
                            +2,651 USD
                        </div>
                    </div>

                    <!-- 产品项 4 -->
                    <div class="product-item">
                        <div class="product-info">
                            <div class="product-name">其他三款 1</div>
                            <div class="product-meta">
                                <span>数量: 1000</span>
                                <span>采购: 98 HKD</span>
                                <span>销售: 14.88 USD</span>
                            </div>
                        </div>
                        <div class="product-profit profit-positive">
                            +2,300 USD
                        </div>
                    </div>

                    <!-- 产品项 5 -->
                    <div class="product-item">
                        <div class="product-info">
                            <div class="product-name">其他三款 2</div>
                            <div class="product-meta">
                                <span>数量: 1000</span>
                                <span>采购: 101 HKD</span>
                                <span>销售: 15.88 USD</span>
                            </div>
                        </div>
                        <div class="product-profit profit-positive">
                            +2,920 USD
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-outline">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-danger">
                        <i class="fas fa-trash"></i> 清空
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 利润汇总卡片 -->
    <div class="summary-card">
        <h2><i class="fas fa-chart-line"></i> 利润汇总</h2>
        <div class="summary-grid">
            <div class="summary-item">
                <div>总利润</div>
                <div class="summary-value">9,864 USD</div>
                <div>≈ 71,010 CNY</div>
            </div>
            <div class="summary-item">
                <div>利润率</div>
                <div class="summary-value">4.0%</div>
                <div>整体回报率</div>
            </div>
            <div class="summary-item">
                <div>产品数量</div>
                <div class="summary-value">6</div>
                <div>盈利产品: 5</div>
            </div>
        </div>
    </div>

    <!-- 底部：图表和历史记录 -->
    <div class="main-content" style="margin-top: 25px;">
        <!-- 利润图表 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i>
                <span>利润分布</span>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <div class="chart-bar" style="height: 25%;">-4,478</div>
                    <div class="chart-bar" style="height: 85%;">4,321</div>
                    <div class="chart-bar" style="height: 53%;">2,651</div>
                    <div class="chart-bar" style="height: 46%;">2,300</div>
                    <div class="chart-bar" style="height: 59%;">2,920</div>
                    <div class="chart-bar" style="height: 44%;">2,150</div>
                </div>
            </div>
        </div>

        <!-- 历史记录 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-history"></i>
                <span>最近计算</span>
            </div>
            <div class="card-body">
                <ul class="history-list">
                    <li class="history-item">
                        <div>
                            <div>2023-10-15 计算</div>
                            <div class="history-date">6个产品, 总利润: 9,864 USD</div>
                        </div>
                        <div class="profit-positive">+9,864 USD</div>
                    </li>
                    <li class="history-item">
                        <div>
                            <div>2023-10-10 计算</div>
                            <div class="history-date">4个产品, 总利润: 7,421 USD</div>
                        </div>
                        <div class="profit-positive">+7,421 USD</div>
                    </li>
                    <li class="history-item">
                        <div>
                            <div>2023-10-05 计算</div>
                            <div class="history-date">3个产品, 总利润: -1,258 USD</div>
                        </div>
                        <div class="profit-negative">-1,258 USD</div>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons" style="margin-top: 25px;">
        <button class="btn btn-primary">
            <i class="fas fa-file-export"></i> 导出数据
        </button>
        <button class="btn btn-outline">
            <i class="fas fa-print"></i> 打印报告
        </button>
        <button class="btn btn-outline">
            <i class="fas fa-sync-alt"></i> 重新计算
        </button>
    </div>
</div>

<script>
    // 这里可以添加交互逻辑
    // 实际开发中会包含完整的计算功能、数据存储等
    document.addEventListener('DOMContentLoaded', function() {
        // 示例：添加产品按钮事件
        document.querySelector('.btn-primary').addEventListener('click', function() {
            alert('在实际应用中，这里会添加新产品并重新计算利润');
        });

        // 示例：清空按钮事件
        document.querySelector('.btn-danger').addEventListener('click', function() {
            if(confirm('确定要清空所有产品数据吗？')) {
                alert('在实际应用中，这里会清空产品列表');
            }
        });
    });
</script>
</body>
</html>