<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓禾利润计算APP - 原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }
        .screen {
            display: none; /* Hidden by default */
            animation: fadeIn 0.3s ease-in-out;
        }
        .screen.active {
            display: block; /* Show active screen */
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .bottom-nav {
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        .bottom-nav a.active {
            color: #2563EB; /* blue-600 */
            border-top-width: 2px;
            border-color: #2563EB;
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1; /* coolGray-300 */
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8; /* coolGray-400 */
        }
        /* Helper class for required fields */
        .required-label::after {
            content: '*';
            color: red;
            margin-left: 2px;
        }
    </style>
</head>
<body class="bg-gray-100">

<div id="app-container" class="max-w-md mx-auto bg-white shadow-lg min-h-screen flex flex-col">

    <!-- Main Content Area -->
    <main class="flex-grow pb-20 overflow-y-auto">
        <!-- P1: Dashboard/Home Screen -->
        <section id="screen-dashboard" class="screen active p-4 space-y-6">
            <header class="text-center py-4">
                <h1 class="text-2xl font-bold text-blue-700">利润计算</h1>
            </header>

            <div class="bg-gradient-to-r from-blue-500 to-blue-700 text-white p-6 rounded-xl shadow-lg">
                <p class="text-sm opacity-80">本月总利润 (CNY)</p>
                <p class="text-4xl font-bold mt-1">¥ 71,010.00</p>
                <p class="text-sm opacity-80 mt-2">约合 9,864.00 USD</p>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <button onclick="navigateTo('screen-new-calculation')" class="bg-orange-500 hover:bg-orange-600 text-white p-4 rounded-lg shadow flex flex-col items-center justify-center space-y-2 transition-transform transform hover:scale-105">
                    <i class="fas fa-calculator fa-2x"></i>
                    <span class="font-semibold">新建计算</span>
                </button>
                <button onclick="navigateTo('screen-product-list')" class="bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg shadow flex flex-col items-center justify-center space-y-2 transition-transform transform hover:scale-105">
                    <i class="fas fa-box-open fa-2x"></i>
                    <span class="font-semibold">产品管理</span>
                </button>
                <button onclick="navigateTo('screen-history')" class="bg-indigo-500 hover:bg-indigo-600 text-white p-4 rounded-lg shadow flex flex-col items-center justify-center space-y-2 transition-transform transform hover:scale-105">
                    <i class="fas fa-history fa-2x"></i>
                    <span class="font-semibold">历史记录</span>
                </button>
                <button onclick="navigateTo('screen-settings')" class="bg-gray-500 hover:bg-gray-600 text-white p-4 rounded-lg shadow flex flex-col items-center justify-center space-y-2 transition-transform transform hover:scale-105">
                    <i class="fas fa-cog fa-2x"></i>
                    <span class="font-semibold">设置</span>
                </button>
            </div>

            <!-- Placeholder for a simple chart -->
            <div class="mt-6 bg-white p-4 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-gray-700 mb-2">近期利润趋势 (示意)</h3>
                <div class="h-40 bg-gray-200 rounded flex items-center justify-center">
                    <i class="fas fa-chart-line fa-3x text-gray-400"></i>
                    <p class="ml-2 text-gray-500">图表区域</p>
                </div>
            </div>
        </section>

        <!-- P2: New Calculation Screen -->
        <section id="screen-new-calculation" class="screen p-4 space-y-4">
            <header class="flex items-center justify-between py-3">
                <button onclick="goBack('screen-dashboard')" class="text-blue-600 hover:text-blue-800"><i class="fas fa-arrow-left fa-lg"></i> 返回</button>
                <h1 class="text-xl font-bold text-gray-800">新建计算</h1>
                <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg shadow text-sm font-semibold"><i class="fas fa-save mr-1"></i> 保存</button>
            </header>

            <div>
                <label for="scenario-name" class="block text-sm font-medium text-gray-700">批次名称/备注</label>
                <input type="text" id="scenario-name" value="2024-06-04 混合采购批次" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>

            <div id="calculation-items-container" class="space-y-4">
                <!-- Item 1 (Example from user) -->
                <div class="bg-white p-4 rounded-lg shadow border border-gray-200 calculation-item">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-md font-semibold text-blue-700">产品 1: 6400</h3>
                        <button class="text-red-500 hover:text-red-700 text-sm"><i class="fas fa-trash-alt mr-1"></i>移除</button>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-600">产品名称</label>
                            <input type="text" value="6400" class="mt-1 block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm" placeholder="选择或输入产品">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 required-label">数量</label>
                            <input type="number" value="6400" class="mt-1 block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 required-label">采购单价</label>
                            <div class="flex">
                                <input type="number" step="0.01" value="28.70" class="mt-1 block w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm">
                                <select class="mt-1 block w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-50">
                                    <option>CNY</option><option selected>USD</option><option>HKD</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 required-label">销售单价</label>
                            <div class="flex">
                                <input type="number" step="0.01" value="28.00" class="mt-1 block w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm">
                                <select class="mt-1 block w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-50">
                                    <option>CNY</option><option selected>USD</option><option>HKD</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-200 text-sm space-y-1">
                        <p>采购额 (USD): <span class="font-semibold">183,680.00</span></p>
                        <p>销售额 (USD): <span class="font-semibold">179,200.00</span></p>
                        <p>单品利润 (USD): <span class="font-semibold text-red-600">-4,480.00</span></p>
                    </div>
                </div>

                <!-- Item 2 (Example from user - 0 cost) -->
                <div class="bg-white p-4 rounded-lg shadow border border-gray-200 calculation-item">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-md font-semibold text-blue-700">产品 2: 256简装</h3>
                        <button class="text-red-500 hover:text-red-700 text-sm"><i class="fas fa-trash-alt mr-1"></i>移除</button>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-600">产品名称</label>
                            <input type="text" value="256简装" class="mt-1 block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 required-label">数量</label>
                            <input type="number" value="256" class="mt-1 block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600">采购单价</label>
                            <div class="flex">
                                <input type="number" step="0.01" value="0.00" class="mt-1 block w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm bg-gray-100" readonly>
                                <select class="mt-1 block w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-50">
                                    <option selected>CNY</option><option>USD</option><option>HKD</option>
                                </select>
                            </div>
                            <div class="mt-1"><input type="checkbox" id="zero_cost_1" checked class="mr-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"><label for="zero_cost_1" class="text-xs text-gray-600">0成本</label></div>
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 required-label">销售单价</label>
                            <div class="flex">
                                <input type="number" step="0.01" value="16.88" class="mt-1 block w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm">
                                <select class="mt-1 block w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-50">
                                    <option selected>CNY</option><option>USD</option><option>HKD</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-200 text-sm space-y-1">
                        <p>采购额 (CNY): <span class="font-semibold">0.00</span></p>
                        <p>销售额 (CNY): <span class="font-semibold">4,321.28</span></p>
                        <p>单品利润 (CNY): <span class="font-semibold text-green-600">4,321.28</span></p>
                    </div>
                </div>
                <!-- Item 3 (Example from user - HKD cost) -->
                <div class="bg-white p-4 rounded-lg shadow border border-gray-200 calculation-item">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-md font-semibold text-blue-700">产品 3: 白色简装</h3>
                        <button class="text-red-500 hover:text-red-700 text-sm"><i class="fas fa-trash-alt mr-1"></i>移除</button>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-600">产品名称</label>
                            <input type="text" value="白色简装" class="mt-1 block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 required-label">数量</label>
                            <input type="number" value="789" class="mt-1 block w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 required-label">采购单价</label>
                            <div class="flex">
                                <input type="number" step="0.01" value="106.00" class="mt-1 block w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm">
                                <select class="mt-1 block w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-50">
                                    <option>CNY</option><option>USD</option><option selected>HKD</option>
                                </select>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">约 13.52 CNY/件 (汇率: 1 HKD = 0.92 CNY)</p>
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 required-label">销售单价</label>
                            <div class="flex">
                                <input type="number" step="0.01" value="16.88" class="mt-1 block w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm">
                                <select class="mt-1 block w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-50">
                                    <option selected>CNY</option><option>USD</option><option>HKD</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-200 text-sm space-y-1">
                        <p>采购额 (CNY): <span class="font-semibold">10,667.28</span> (83,634.00 HKD)</p>
                        <p>销售额 (CNY): <span class="font-semibold">13,318.32</span></p>
                        <p>单品利润 (CNY): <span class="font-semibold text-green-600">2,651.04</span></p>
                    </div>
                </div>
            </div>

            <button class="w-full bg-blue-100 hover:bg-blue-200 text-blue-700 font-semibold py-2.5 px-4 rounded-lg shadow-sm border border-blue-300 flex items-center justify-center">
                <i class="fas fa-plus mr-2"></i> 添加产品项
            </button>

            <div class="mt-6 bg-gray-50 p-4 rounded-lg shadow-inner sticky bottom-0 border-t-2 border-blue-500">
                <h3 class="text-lg font-bold text-gray-800 mb-2">总计 (基准货币: CNY)</h3>
                <div class="space-y-1 text-sm">
                    <p class="flex justify-between">总采购成本: <span class="font-semibold">¥ 194,347.28</span></p>
                    <p class="flex justify-between">总销售收入: <span class="font-semibold">¥ 196,839.60</span></p>
                    <hr class="my-1">
                    <p class="flex justify-between text-lg text-green-700">总利润: <span class="font-bold">¥ 71,010.00</span></p>
                    <p class="flex justify-between text-sm text-gray-600">利润率: <span class="font-semibold">4.09%</span></p>
                    <p class="flex justify-between text-xs text-gray-500">折合USD (汇率 1 USD = 7.20 CNY): <span class="font-semibold">$ 9,862.50</span></p>
                </div>
            </div>
        </section>

        <!-- P3: Product List Screen -->
        <section id="screen-product-list" class="screen p-4 space-y-4">
            <header class="flex items-center justify-between py-3">
                <button onclick="goBack('screen-dashboard')" class="text-blue-600 hover:text-blue-800"><i class="fas fa-arrow-left fa-lg"></i> 返回</button>
                <h1 class="text-xl font-bold text-gray-800">产品管理</h1>
                <button onclick="navigateTo('screen-add-edit-product', {isEdit: false})" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-lg shadow text-sm font-semibold"><i class="fas fa-plus mr-1"></i> 添加</button>
            </header>

            <div class="relative">
                <input type="text" placeholder="搜索产品..." class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>

            <div class="space-y-3">
                <!-- Product Item 1 -->
                <div class="bg-white p-3 rounded-lg shadow border border-gray-200 flex items-center justify-between">
                    <div>
                        <h3 class="font-semibold text-gray-800">6400</h3>
                        <p class="text-xs text-gray-500">采购: 28.70 USD | 销售: 28.00 USD</p>
                    </div>
                    <div class="space-x-2">
                        <button onclick="navigateTo('screen-add-edit-product', {isEdit: true, productId: 1})" class="text-blue-500 hover:text-blue-700"><i class="fas fa-edit"></i></button>
                        <button class="text-red-500 hover:text-red-700"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <!-- Product Item 2 -->
                <div class="bg-white p-3 rounded-lg shadow border border-gray-200 flex items-center justify-between">
                    <div>
                        <h3 class="font-semibold text-gray-800">256简装</h3>
                        <p class="text-xs text-gray-500">采购: 0.00 CNY | 销售: 16.88 CNY</p>
                    </div>
                    <div class="space-x-2">
                        <button onclick="navigateTo('screen-add-edit-product', {isEdit: true, productId: 2})" class="text-blue-500 hover:text-blue-700"><i class="fas fa-edit"></i></button>
                        <button class="text-red-500 hover:text-red-700"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <!-- Product Item 3 -->
                <div class="bg-white p-3 rounded-lg shadow border border-gray-200 flex items-center justify-between">
                    <div>
                        <h3 class="font-semibold text-gray-800">白色简装</h3>
                        <p class="text-xs text-gray-500">采购: 106.00 HKD | 销售: 16.88 CNY</p>
                    </div>
                    <div class="space-x-2">
                        <button onclick="navigateTo('screen-add-edit-product', {isEdit: true, productId: 3})" class="text-blue-500 hover:text-blue-700"><i class="fas fa-edit"></i></button>
                        <button class="text-red-500 hover:text-red-700"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <!-- Product Item 4 -->
                <div class="bg-white p-3 rounded-lg shadow border border-gray-200 flex items-center justify-between">
                    <div>
                        <h3 class="font-semibold text-gray-800">其他三款-1</h3>
                        <p class="text-xs text-gray-500">采购: 98.00 HKD | 销售: 14.88 CNY</p>
                    </div>
                    <div class="space-x-2">
                        <button onclick="navigateTo('screen-add-edit-product', {isEdit: true, productId: 4})" class="text-blue-500 hover:text-blue-700"><i class="fas fa-edit"></i></button>
                        <button class="text-red-500 hover:text-red-700"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
            </div>
        </section>

        <!-- P4: Add/Edit Product Screen -->
        <section id="screen-add-edit-product" class="screen p-4 space-y-4">
            <header class="flex items-center justify-between py-3">
                <button onclick="goBack('screen-product-list')" class="text-blue-600 hover:text-blue-800"><i class="fas fa-arrow-left fa-lg"></i> 返回</button>
                <h1 id="product-form-title" class="text-xl font-bold text-gray-800">添加新产品</h1>
                <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg shadow text-sm font-semibold"><i class="fas fa-save mr-1"></i> 保存</button>
            </header>

            <div class="space-y-4 bg-white p-4 rounded-lg shadow">
                <div>
                    <label for="product-name" class="block text-sm font-medium text-gray-700 required-label">产品名称</label>
                    <input type="text" id="product-name" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">默认单位采购成本</label>
                    <div class="mt-1 flex rounded-md shadow-sm">
                        <input type="number" step="0.01" id="product-purchase-cost" class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 border">
                        <select id="product-purchase-currency" class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                            <option>CNY</option><option>USD</option><option>HKD</option><option>EUR</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">默认单位销售价格</label>
                    <div class="mt-1 flex rounded-md shadow-sm">
                        <input type="number" step="0.01" id="product-sales-price" class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 border">
                        <select id="product-sales-currency" class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                            <option>CNY</option><option>USD</option><option>HKD</option><option>EUR</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label for="product-notes" class="block text-sm font-medium text-gray-700">备注</label>
                    <textarea id="product-notes" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                </div>
            </div>
        </section>

        <!-- P5: Calculation History Screen -->
        <section id="screen-history" class="screen p-4 space-y-4">
            <header class="flex items-center justify-between py-3">
                <button onclick="goBack('screen-dashboard')" class="text-blue-600 hover:text-blue-800"><i class="fas fa-arrow-left fa-lg"></i> 返回</button>
                <h1 class="text-xl font-bold text-gray-800">历史记录</h1>
                <div></div> <!-- Placeholder for potential actions -->
            </header>

            <div class="space-y-3">
                <!-- History Item 1 -->
                <div class="bg-white p-3 rounded-lg shadow border border-gray-200 cursor-pointer hover:shadow-md transition-shadow" onclick="navigateTo('screen-calculation-detail', {historyId: 1})">
                    <div class="flex justify-between items-center">
                        <h3 class="font-semibold text-gray-800">2024-06-04 混合采购批次</h3>
                        <span class="text-xs text-gray-500">2024-06-04 10:30</span>
                    </div>
                    <div class="mt-2 text-sm space-y-0.5">
                        <p class="flex justify-between text-green-600">总利润 (CNY): <span class="font-semibold">¥ 71,010.00</span></p>
                        <p class="flex justify-between text-gray-600">利润率: <span class="font-semibold">4.09%</span></p>
                    </div>
                </div>
                <!-- History Item 2 -->
                <div class="bg-white p-3 rounded-lg shadow border border-gray-200 cursor-pointer hover:shadow-md transition-shadow" onclick="navigateTo('screen-calculation-detail', {historyId: 2})">
                    <div class="flex justify-between items-center">
                        <h3 class="font-semibold text-gray-800">五月大促清仓</h3>
                        <span class="text-xs text-gray-500">2024-05-15 14:22</span>
                    </div>
                    <div class="mt-2 text-sm space-y-0.5">
                        <p class="flex justify-between text-red-600">总利润 (CNY): <span class="font-semibold">¥ -1,250.50</span></p>
                        <p class="flex justify-between text-gray-600">利润率: <span class="font-semibold">-0.85%</span></p>
                    </div>
                </div>
            </div>
        </section>

        <!-- P6: Calculation Detail Screen -->
        <section id="screen-calculation-detail" class="screen p-4 space-y-4">
            <header class="flex items-center justify-between py-3">
                <button onclick="goBack('screen-history')" class="text-blue-600 hover:text-blue-800"><i class="fas fa-arrow-left fa-lg"></i> 返回</button>
                <h1 id="calc-detail-title" class="text-xl font-bold text-gray-800">计算详情</h1>
                <div></div> <!-- Placeholder -->
            </header>

            <div class="bg-white p-4 rounded-lg shadow space-y-3">
                <p class="text-sm text-gray-600">批次名称: <span id="calc-detail-name" class="font-semibold">2024-06-04 混合采购批次</span></p>
                <p class="text-sm text-gray-600">计算日期: <span id="calc-detail-date" class="font-semibold">2024-06-04 10:30</span></p>
            </div>

            <div class="space-y-3">
                <!-- Detail Item 1 -->
                <div class="bg-white p-3 rounded-lg shadow border border-gray-200">
                    <h4 class="font-semibold text-blue-600">产品: 6400 (数量: 6400)</h4>
                    <div class="text-xs text-gray-700 mt-1 space-y-0.5">
                        <p>采购: 28.70 USD/件  => 总 183,680.00 USD</p>
                        <p>销售: 28.00 USD/件  => 总 179,200.00 USD</p>
                        <p>利润: <span class="font-semibold text-red-600">-4,480.00 USD</span> (约 -32,256.00 CNY)</p>
                    </div>
                </div>
                <!-- Detail Item 2 -->
                <div class="bg-white p-3 rounded-lg shadow border border-gray-200">
                    <h4 class="font-semibold text-blue-600">产品: 256简装 (数量: 256)</h4>
                    <div class="text-xs text-gray-700 mt-1 space-y-0.5">
                        <p>采购: 0.00 CNY/件  => 总 0.00 CNY</p>
                        <p>销售: 16.88 CNY/件  => 总 4,321.28 CNY</p>
                        <p>利润: <span class="font-semibold text-green-600">4,321.28 CNY</span></p>
                    </div>
                </div>
                <!-- Add more items as needed from the calculation -->
            </div>

            <div class="mt-4 bg-gray-50 p-4 rounded-lg shadow-inner">
                <h3 class="text-md font-bold text-gray-800 mb-2">总计 (基准货币: CNY)</h3>
                <div class="space-y-1 text-sm">
                    <p class="flex justify-between">总采购成本: <span class="font-semibold">¥ 194,347.28</span></p>
                    <p class="flex justify-between">总销售收入: <span class="font-semibold">¥ 196,839.60</span></p>
                    <hr class="my-1">
                    <p class="flex justify-between text-lg text-green-700">总利润: <span class="font-bold">¥ 71,010.00</span></p>
                    <p class="flex justify-between text-sm text-gray-600">利润率: <span class="font-semibold">4.09%</span></p>
                </div>
            </div>
        </section>

        <!-- P7: Settings Screen -->
        <section id="screen-settings" class="screen p-4 space-y-6">
            <header class="flex items-center justify-between py-3">
                <button onclick="goBack('screen-dashboard')" class="text-blue-600 hover:text-blue-800"><i class="fas fa-arrow-left fa-lg"></i> 返回</button>
                <h1 class="text-xl font-bold text-gray-800">设置</h1>
                <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg shadow text-sm font-semibold"><i class="fas fa-save mr-1"></i> 保存设置</button>
            </header>

            <div class="bg-white p-4 rounded-lg shadow space-y-4">
                <div>
                    <label for="base-currency" class="block text-sm font-medium text-gray-700">基准货币</label>
                    <select id="base-currency" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md border">
                        <option selected>CNY (人民币)</option>
                        <option>USD (美元)</option>
                        <option>EUR (欧元)</option>
                    </select>
                    <p class="mt-1 text-xs text-gray-500">所有汇总数据将以此货币为基准显示。</p>
                </div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow space-y-4">
                <h2 class="text-lg font-semibold text-gray-700">汇率管理 (相对于基准货币 CNY)</h2>
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <span class="w-12 text-sm font-medium text-gray-600">1 USD</span>
                        <span class="text-gray-500">=</span>
                        <input type="number" step="0.0001" value="7.2000" class="flex-1 block w-full px-3 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <span class="text-sm text-gray-600">CNY</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-12 text-sm font-medium text-gray-600">1 HKD</span>
                        <span class="text-gray-500">=</span>
                        <input type="number" step="0.0001" value="0.9200" class="flex-1 block w-full px-3 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <span class="text-sm text-gray-600">CNY</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="w-12 text-sm font-medium text-gray-600">1 EUR</span>
                        <span class="text-gray-500">=</span>
                        <input type="number" step="0.0001" value="7.8500" class="flex-1 block w-full px-3 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <span class="text-sm text-gray-600">CNY</span>
                    </div>
                </div>
                <button class="mt-3 w-full bg-blue-100 hover:bg-blue-200 text-blue-700 font-semibold py-2 px-4 rounded-lg shadow-sm border border-blue-300 text-sm">
                    <i class="fas fa-plus mr-1"></i> 添加汇率
                </button>
            </div>

            <div class="bg-white p-4 rounded-lg shadow space-y-2">
                <h2 class="text-lg font-semibold text-gray-700">关于</h2>
                <p class="text-sm text-gray-600">仓禾利润计算APP v1.0.0</p>
                <p class="text-xs text-gray-500">专为高效利润分析设计</p>
            </div>

        </section>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-gray-200 flex justify-around items-center h-16 bottom-nav">
        <a href="#screen-dashboard" class="flex flex-col items-center justify-center text-gray-600 hover:text-blue-600 w-1/4 pt-1 pb-1 nav-link active" data-target="screen-dashboard">
            <i class="fas fa-home fa-lg"></i>
            <span class="text-xs mt-1">首页</span>
        </a>
        <a href="#screen-new-calculation" class="flex flex-col items-center justify-center text-gray-600 hover:text-blue-600 w-1/4 pt-1 pb-1 nav-link" data-target="screen-new-calculation">
            <i class="fas fa-calculator fa-lg"></i>
            <span class="text-xs mt-1">计算</span>
        </a>
        <a href="#screen-product-list" class="flex flex-col items-center justify-center text-gray-600 hover:text-blue-600 w-1/4 pt-1 pb-1 nav-link" data-target="screen-product-list">
            <i class="fas fa-box-open fa-lg"></i>
            <span class="text-xs mt-1">产品</span>
        </a>
        <a href="#screen-history" class="flex flex-col items-center justify-center text-gray-600 hover:text-blue-600 w-1/4 pt-1 pb-1 nav-link" data-target="screen-history">
            <i class="fas fa-history fa-lg"></i>
            <span class="text-xs mt-1">历史</span>
        </a>
        <a href="#screen-settings" class="flex flex-col items-center justify-center text-gray-600 hover:text-blue-600 w-1/4 pt-1 pb-1 nav-link" data-target="screen-settings">
            <i class="fas fa-cog fa-lg"></i>
            <span class="text-xs mt-1">设置</span>
        </a>
    </nav>
</div>

<script>
    const screens = document.querySelectorAll('.screen');
    const navLinks = document.querySelectorAll('.nav-link');
    let historyStack = ['screen-dashboard']; // Keep track of navigation for back button

    function navigateTo(screenId, params = {}) {
        const currentScreen = document.querySelector('.screen.active');
        if (currentScreen && currentScreen.id !== screenId) {
            if (!historyStack.includes(currentScreen.id) || historyStack[historyStack.length -1] !== currentScreen.id ) {
                // Only push if it's a forward navigation, not a back or refresh
            }
            historyStack.push(currentScreen.id);
        }

        // Remove current screenId from stack if it exists, to place it at the end for goBack
        const existingIndex = historyStack.indexOf(screenId);
        if (existingIndex > -1) {
            historyStack.splice(existingIndex, 1);
        }

        screens.forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.dataset.target === screenId) {
                link.classList.add('active');
            }
        });
        window.scrollTo(0,0); // Scroll to top on new screen

        // Handle specific screen setups based on params
        if (screenId === 'screen-add-edit-product') {
            const titleEl = document.getElementById('product-form-title');
            // Clear form fields (basic example)
            document.getElementById('product-name').value = '';
            document.getElementById('product-purchase-cost').value = '';
            document.getElementById('product-sales-price').value = '';
            document.getElementById('product-notes').value = '';

            if (params.isEdit) {
                titleEl.textContent = '编辑产品';
                // In a real app, you'd fetch and populate product data using params.productId
                // Example prefill:
                if (params.productId === 1) {
                    document.getElementById('product-name').value = '6400';
                    document.getElementById('product-purchase-cost').value = '28.70';
                    document.getElementById('product-purchase-currency').value = 'USD';
                    document.getElementById('product-sales-price').value = '28.00';
                    document.getElementById('product-sales-currency').value = 'USD';
                } else if (params.productId === 2) {
                    document.getElementById('product-name').value = '256简装';
                    // ... and so on
                }

            } else {
                titleEl.textContent = '添加新产品';
            }
        } else if (screenId === 'screen-calculation-detail') {
            // In a real app, fetch and display details for params.historyId
            const detailTitle = document.getElementById('calc-detail-title');
            const detailName = document.getElementById('calc-detail-name');
            const detailDate = document.getElementById('calc-detail-date');
            if (params.historyId === 1) {
                detailTitle.textContent = '计算详情: 2024-06-04 混合采购批次';
                detailName.textContent = '2024-06-04 混合采购批次';
                detailDate.textContent = '2024-06-04 10:30';
            } else {
                detailTitle.textContent = '计算详情: 五月大促清仓';
                detailName.textContent = '五月大促清仓';
                detailDate.textContent = '2024-05-15 14:22';
            }
            // ... populate items
        }
    }

    function goBack(defaultScreen = 'screen-dashboard') {
        let previousScreenId = defaultScreen;
        if (historyStack.length > 1) { // Ensure there's a screen to go back to
            previousScreenId = historyStack.pop(); // Get the last screen
        } else if (historyStack.length === 1 && historyStack[0] !== defaultScreen) {
            previousScreenId = historyStack.pop(); // Go to last screen if it's not default
        }

        // If after pop, stack is empty or current screen is still the target, go to default
        const currentActive = document.querySelector('.screen.active');
        if (currentActive && previousScreenId === currentActive.id && historyStack.length > 0) {
            previousScreenId = historyStack.pop();
        } else if (currentActive && previousScreenId === currentActive.id && historyStack.length === 0) {
            previousScreenId = defaultScreen; // Fallback to default if stuck
        }


        if (previousScreenId) {
            navigateTo(previousScreenId);
            // Remove the screen we are navigating to from history stack as navigateTo will handle it
            const indexInStack = historyStack.indexOf(previousScreenId);
            if(indexInStack > -1) historyStack.splice(indexInStack, 1);

        }
    }

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetScreenId = link.dataset.target;
            // When using bottom nav, we treat it as a new "root" navigation for that tab
            // So, clear history related to other tabs, but keep the dashboard as a base
            if (targetScreenId !== 'screen-dashboard') {
                historyStack = ['screen-dashboard'];
            } else {
                historyStack = []; // Reset if going to dashboard via nav
            }
            navigateTo(targetScreenId);
        });
    });

    // Initialize with the dashboard screen
    // navigateTo('screen-dashboard'); // Already active by default in HTML
</script>

</body>
</html>
