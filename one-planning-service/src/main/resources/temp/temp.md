**需求文档**

**项目名称：** 独立备料S0未转正线上管理及预警系统


**1. 项目背景**

略

**2. 项目目标**

略

**3. 用户角色**

* **提交风险备库人员：** 负责提交风险备库申请的相关人员。
* **提交风险备库人员主管：** 提交风险备库人员的直属领导。
* **计划订单责任人：** 计划订单中指定的责任人。
* **MC-物控：** 物料控制部门的相关人员。
* **采购：** 采购部门的相关人员。
* **系统管理员：** 负责系统的维护和管理。

**4. 功能需求**

**4.1 数据来源与筛选**

* **数据来源：**
    * WPS 排产数据（作为主要数据来源）。
    * 计划订单系统 (OM)（提供预计维护版面时间、责任人等信息）。
    * SAP 系统（获取首次创建日期）。
    * LCP 接口（获取整灯风险备料单号、申请人、预计 SAP 转正日期、预计订单交期）。
    * MPS 系统（获取未来 6 个月的物料消耗计划，用于计算呆滞风险）。
* **筛选条件：**
    * 订单类型为“计划订单”。
    * 筛选所有计划订单，并考虑其“当前日期（以上线日期）+30天”的时间点。
    * 进一步筛选出“计划开始日期 - 当前日期 <= 30天”的订单进行监控和预警。
    * 排除工厂编号以 26 或 27 开头的中国工厂（视为泰国工厂）。

**4.2 预警机制 (触发 OM 的待办事项)**

系统应根据计划上线日期与当前日期的差值，自动触发不同颜色的待办事项，并推送给相应的责任人。

* **红色预警：**
    * **触发条件 (中国工厂):** 计划上线日期 - 当前日期 ≤ 14 天。
    * **触发条件 (泰国工厂):** 计划上线日期 - 当前日期 ≤ 42 天（工厂编号以 26 或 27 开头）。
    * **接收人：** “提交风险备库人员主管”。
        * **接收人逻辑：** 根据“整灯风险备料单号”识别，在组织架构中查找该人员的直属领导（从下往上第一层汇报关系）。
* **黄色预警：**
    * **触发条件 (中国工厂):** 计划上线日期 - 当前日期 ≤ 21 天。
    * **触发条件 (泰国工厂):** 计划上线日期 - 当前日期 ≤ 49 天（工厂编号以 26 或 27 开头）。
    * **接收人：** 计划订单中指定的“责任人”。
* **预警次数记录：** 系统需要记录每个独立备料 SO 的预警次数，并能判断是否超过 3 次。需要明确预警次数的计算逻辑。
* **首次和上次预警时间存储：** 系统需要存储每次预警的首次触发时间和最近一次触发时间。

**4.3 触发 MC 的待办事项**

* **触发条件：** 当计划订单中的“计划单是否取消”字段为“是”时。
* **接收人：** “MC-物控”。
* **推送维度：** 以工厂维度推送待办事项。

**4.4 消除预警条件**

* 当相关的计划订单在 WPS 系统中不再存在时，之前触发的所有与该订单相关的预警待办事项应自动消失。

**4.5 管理报表**

系统需要生成一份详细的管理报表，包含以下字段：

* **基础信息：**
    * 计划订单号 (OM)
    * 责任人
    * 商品 ID
    * 上线日期
    * 工厂
* **预警信息：**
    * 预警日期 (触发预警时的日期)
    * 首次创建日期 (SAP 首次创建日期，按计划单号关联)
    * 整灯风险备料单号 (从 LCP 接口获取)
    * 责任人 (从 LCP 接口获取申请人)
    * 初次预计转正日期 (从 LCP 接口获取)
    * 最新预计转正日期 (从 LCP 接口获取预计 SAP 转正日期)
    * 初次预计出货日期 (从 LCP 接口获取)
    * 预计订单交期 (从 LCP 接口获取)
    * 预警次数
    * 距离需求日期天数 (需要明确需求日期是哪个字段)
    * 灯色
* **状态信息：**
    * 计划单是否取消
    * 物料是否可消耗 (同个订单号下只要有一个物料不可消耗，则该订单不可消耗)
    * 已变更次数
* **关联信息：**
    * 销售订单号
    * 销售订单行项目
    * 计划订单创建人
    * 类别
    * 翻单类型
* **时间信息：**
    * 原始完工日期
    * 延迟后转正日期 (有值取值，无值在某个日期基础上 +7 天，需要明确这个基础日期)
    * 调整后上线日期
    * update\_at (更新时间)
    * create\_at (创建时间)
* **特殊信息：**
    * 待办 OM/业务回复进度 (关联待办，根据回复日期调整，未回复自动后延 1 周)
    * 供应商是否同意变更/取消 (按单识别)
    * 物料 ID 库存数量 (需要明确如何获取)
    * 物料呆滞风险等级 (基于 MPS 未来 6 个月的消耗计划计算)
    * 取消数量对冲 (需要明确如何实现)
    * 首次预警时间
    * 上次预警时间

**4.6 待办事项管理**

* 系统能够生成并推送预警相关的待办事项给相应的用户。
* 待办事项中应包含关键的订单信息和预警级别。
* 待办事项应能关联到相应的计划订单详情。
* 针对计划单取消触发的 MC 待办事项，需要按工厂维度进行推送。
* 待办事项的状态应能根据计划订单在 WPS 中的状态自动更新（例如，订单不存在时自动消失）。
* 待办事项应能记录业务回复进度，并根据回复日期调整相关信息。

**5. 数据接口**

* 需要与 WPS 系统进行数据对接，获取排产和订单状态信息。
* 需要与计划订单系统 (OM) 进行数据对接，获取预计维护版面时间、责任人等信息。
* 需要与 SAP 系统进行数据对接，获取首次创建日期等信息。
* 需要与 LCP 接口进行数据对接，获取整灯风险备料单号、申请人、预计 SAP 转正日期、预计订单交期等信息。
* 需要明确如何以及以什么维度从 MPS 系统获取物料消耗计划数据。
* 需要明确如何获取物料 ID 对应的库存数量。

**6. 疑问点 (需要进一步明确)**

* **MPS 数据取值：** 如何以及以什么维度从 MPS 获取数据？
* **物料 ID 库存数量：** 如何获取物料 ID 对应的库存数量？
* **延迟后转正 +7 天：** 这个 +7 天是基于哪个字段的时间进行计算的？是计划上线时间吗？
* **预警次数计算：** “预警日期存储超过 3 次”是如何计算次数的？是根据预警级别还是预警触发的频率？
* **距离需求日期天数：** 报表中的“距离需求日期天数”是基于哪个字段的时间计算的？
* **物料是否可消耗：** 这个字段的数据从哪里获取？判断逻辑是否仅基于同个订单号下的物料？
* **取消数量对冲逻辑：** 如何实现取消数量的对冲？具体涉及哪些数据和业务流程？


