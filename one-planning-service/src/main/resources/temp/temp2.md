通过WPS排产计算出30天的订单（基于SAP查询出来的订单号的计划开始日期在30天内进行查询，按计划开始日期从早到晚排）
过滤订单类型为计划订单+生产订单
订单批量调用《ATP物料齐套多阶》接口获取数据，并过滤使用类型为：采购订单的物料
根据采购组织+供应商+物料id批量查询送货方式（JIT+PO2），保留物料需求日期7天内的JIT物料+物料需求日期14天内的PO2物料
根据采购组织+供应商查询【物流提前期】（运输时间）
重算叫料计划数量
-----总叫料计划 = 新增叫料计划 + 已锁定上一个版本未入库数量
总叫料计划 = 当天最新叫料总需求数量（ATP，包含上一版本已叫料PO订单明细） - 【上一版本已叫未送数量】
-----上一版本已叫未送数量 = 上一版本叫料总需求数量 - 上一版本的采购订单入库数量（前一天）
已锁定上一个版本未入库数量
PO2
N（物流时间） + 1天
JIT(<= 12小时)
固定叫料计划（包含采购订单+物料ID+叫料数量），新版本叫料计划生成时：
扣减已收货的采购订单的数量，如有未交货完成部分，需固定   采购订单+物料ID+叫料数量（剩余未清数量），到当前版本的叫料需求数量。
JIT( >  12小时)
当前版本=上个版本的锁定需求扣减  （包含采购订单+行项目+物料ID）的入库数量。
重算叫料计划数量
生成【初步叫料计划A】
填充锁定PO记录（保证相同生产+供应商+物料id在锁定期内的总数量不变）
对比上版本锁定PO和当前版本ATP的PO记录，存在交集的优先填入当前版本的PO记录，并标记锁定
【锁定叫料计划冲销】计算上一版本叫料记录，按工厂+供应商+物料id进行剩余数量的冲销
A=上一版本锁定记录按工厂+供应商+物料id的数量进行分组统计
B=对比上一版本的交集PO记录按工厂+供应商+物料id的数量进行分组统计
A-B对比出差异的数量，如果不为0，用后面的PO进行抵扣（需相同工厂+供应商+物料id一样才能抵扣）
剩余的PO未被冲销的PO正常加入叫料计划
检查供应商对应【物流提前期】内非锁定的订单，往后移动到当天+物流提前期+1天的日期
加入送货单已开，但是送货已过期PO，填充到今天
重新标记锁定
PO2锁定
【物流提前期】+1天
JIT锁定
【物流提前期】 <= 12小时的不锁定
【物流提前期】 > 12小时的锁定当天