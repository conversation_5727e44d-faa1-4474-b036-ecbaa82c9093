<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>装修工程时间线 - 2025年项目 (Vue 3)</title>
    <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            color: #333;
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            text-align: center;
            padding: 30px 20px; /* 调整内边距 */
            background: linear-gradient(120deg, #1a3a5f, #2c5282);
            color: white;
            border-radius: 12px;
            margin-bottom: 40px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><polygon points="0,0 100,0 100,100" fill="rgba(255,255,255,0.1)"/></svg>');
            background-size: cover;
        }

        h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .current-date {
            background: rgba(255, 255, 255, 0.2);
            display: inline-block;
            padding: 8px 20px;
            border-radius: 30px;
            margin-top: 15px;
            font-weight: 600;
            position: relative;
            z-index: 2;
        }

        .filters {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: white;
            border: none;
            padding: 10px 20px;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .filter-btn.active {
            background: #2c5282;
            color: white;
        }

        .timeline-container {
            position: relative;
            padding: 50px 0;
        }

        .timeline-line {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 6px;
            background: linear-gradient(to bottom, #4a7cbd, #1a3a5f);
            transform: translateX(-50%);
            border-radius: 10px;
            z-index: 1;
        }

        .timeline-items {
            display: flex;
            flex-direction: column;
            gap: 60px;
        }

        .timeline-item {
            width: calc(50% - 60px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
            transition: all 0.4s ease;
            background: white;
        }

        .timeline-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
        }

        .timeline-item:nth-child(odd) {
            align-self: flex-start;
        }

        .timeline-item:nth-child(even) {
            align-self: flex-end;
        }

        .timeline-item::before {
            content: "";
            position: absolute;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            top: 40px;
            background: white;
            border: 4px solid #2c5282;
            z-index: 3;
        }

        .timeline-item:nth-child(odd)::before {
            right: -73px; /* 60px space + 6px line / 2 + 10px half of dot */
        }

        .timeline-item:nth-child(even)::before {
            left: -73px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f4f8;
            flex-wrap: wrap; /* 允许换行 */
            gap: 8px; /* 换行后的间距 */
        }

        .timeline-date {
            font-size: 1.1rem;
            font-weight: 700;
            color: #2c5282;
        }

        .timeline-week {
            background: #e6f0ff;
            color: #2c5282;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .timeline-phase {
            background: #e6f7ff;
            color: #1890ff;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .timeline-status {
            font-weight: 700;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .status-completed {
            background: #e6ffed;
            color: #52c41a;
        }

        .status-inprogress {
            background: #fff7e6;
            color: #fa8c16;
            animation: pulse 2s infinite;
        }

        .status-upcoming {
            background: #f6ffed;
            color: #a0d911;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
            100% {
                opacity: 1;
            }
        }

        .task {
            padding: 12px 0;
            border-bottom: 1px dashed #eee;
        }

        .task:last-child {
            border-bottom: none;
        }

        .task-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #1a3a5f;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .task-details {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 8px;
            font-size: 0.95rem;
        }

        .task-personnel,
        .task-material {
            display: flex;
            align-items: flex-start;
            gap: 6px;
        }

        .task-personnel::before {
            content: "👷";
            font-size: 1.1rem;
        }

        .task-material::before {
            content: "📦";
            font-size: 1.1rem;
        }

        .task-material.highlight {
            background: #fffbe6;
            padding: 5px 10px;
            border-radius: 8px;
            border-left: 3px solid #faad14;
        }

        .progress-bar {
            height: 8px;
            background: #f0f4f8;
            border-radius: 10px;
            margin-top: 20px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4a7cbd, #2c5282);
            border-radius: 10px;
            width: 0;
            transition: width 1.5s ease;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 25px;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        .legend-completed {
            background: #52c41a;
        }

        .legend-inprogress {
            background: #fa8c16;
        }

        .legend-upcoming {
            background: #a0d911;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            color: #666;
            font-size: 0.9rem;
        }

        /* --- 移动端优化 (Mobile Optimizations) --- */
        @media (max-width: 900px) {
            body {
                padding: 10px; /* 减小页面边距 */
            }

            h1 {
                font-size: 2rem; /* 减小标题字号 */
            }

            .subtitle {
                font-size: 1rem; /* 减小副标题字号 */
            }

            .timeline-container {
                /* 调整内边距，为左侧时间线留出空间 */
                padding: 30px 0 30px 40px;
            }

            .timeline-line {
                /* 将时间线移动到左侧 */
                left: 20px;
                transform: translateX(0);
            }

            .timeline-item {
                /* 宽度占满剩余空间，不再计算 */
                width: 100%;
                align-self: flex-start !important; /* 强制所有项靠左 */
            }

            .timeline-item:nth-child(odd),
            .timeline-item:nth-child(even) {
                align-self: flex-start !important;
            }

            .timeline-item::before {
                /* 将圆点定位到左侧时间线上 */
                left: -53px; /* 40px padding - (25px width / 2) + 20px line_left - a bit adjustment */
                right: auto;
            }

            .timeline-item:nth-child(odd)::before,
            .timeline-item:nth-child(even)::before {
                /* 统一圆点位置 */
                right: auto;
                left: -53px;
            }

            .timeline-header {
                /* 头部元素垂直排列，避免拥挤 */
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }

        @media (max-width: 480px) {
            h1 {
                font-size: 1.8rem;
            }

            .current-date {
                font-size: 0.9rem;
                padding: 8px 15px;
            }

            .filter-btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }

            .timeline-item {
                padding: 20px;
            }
        }

        .progress-label {
            transition: all 0.3s ease;
            font-weight: 600;
        }
        .progress-label:hover {
            transform: scale(1.05);
            color: #2c5282;
        }

        .progress-label {
            font-size: 1.1rem;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        @media (max-width: 768px) {
            .progress-label {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
<div id="app" class="container">
    <header>
        <h1><i class="fas fa-hammer"></i> 装修工程进度时间线</h1>
        <p class="subtitle">2025年6月3日 - 2025年9月19日 | 总工期15周</p>
        <div class="current-date">
            <i class="fas fa-calendar-alt"></i> 当前日期: 2025年6月9日
            (第2周进行中)
        </div>
    </header>

    <div class="filters">
        <button
                v-for="filter in filters"
                :key="filter.name"
                :class="['filter-btn', { active: activeFilter === filter.name }]"
                @click="setFilter(filter.name)"
        >
            <i :class="filter.icon"></i> {{ filter.name }}
        </button>
    </div>

    <div class="timeline-container">
        <div class="timeline-line"></div>
        <div class="timeline-items">
            <div
                    class="timeline-item"
                    v-for="item in filteredTimelineItems"
                    :key="item.week"
            >
                <div class="timeline-header">
                    <div class="timeline-date">{{ item.date }}</div>
                    <div class="timeline-week">{{ item.week }}</div>
                    <div class="timeline-phase">{{ item.phase }}</div>
                    <div class="timeline-status" :class="item.status.class">
                        {{ item.status.text }}
                    </div>
                </div>
                <div class="task" v-for="(task, index) in item.tasks" :key="index">
                    <div class="task-title">
                        <i class="fas fa-tasks"></i> {{ task.project }}
                    </div>
                    <div class="task-details">
                        <div v-if="task.personnel" class="task-personnel">
                            {{ task.personnel }}
                        </div>
                        <div
                                v-if="task.material"
                                class="task-material"
                                :class="{ highlight: task.material.includes('确认') || task.material.includes('下单') }"
                        >
                            {{ task.material }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="legend">
        <div class="legend-item">
            <div class="legend-color legend-completed"></div>
            <span>已完成</span>
        </div>
        <div class="legend-item">
            <div class="legend-color legend-inprogress"></div>
            <span>进行中</span>
        </div>
        <div class="legend-item">
            <div class="legend-color legend-upcoming"></div>
            <span>未开始</span>
        </div>
    </div>

    <div>
        <div class="progress-label">
            <i class="fas fa-chart-line"></i>
            整体进度: {{ overallProgress }}%
        </div>

        <div class="progress-bar">
            <div
                    class="progress-fill"
                    :style="{ width: overallProgress + '%' }"
            ></div>
        </div>
    </div>

    <div class="footer">
        <p>© 2025 装修工程管理系统 | 最后更新: 2025年6月9日</p>
    </div>
</div>

<script>
    const { createApp, ref, computed } = Vue;

    createApp({
        setup() {
            // --- STATE ---
            const activeFilter = ref("全部阶段");
            const currentDate = new Date(2025, 5, 9); // 月份从0开始，5代表6月

            const filters = ref([
                { name: "全部阶段", icon: "fas fa-list" },
                { name: "基础工程", icon: "fas fa-hard-hat" },
                { name: "隐蔽工程", icon: "fas fa-toolbox" },
                { name: "饰面工程", icon: "fas fa-paint-roller" },
                { name: "安装工程", icon: "fas fa-wrench" },
            ]);

            const timelineData = ref([
                {
                    date: "6月3日-6月6日",
                    week: "第一周",
                    phase: "基础工程",
                    tasks: [
                        {
                            project: "敲打作业",
                            personnel: "现场管理、搬运班组",
                            material: "入户门确认供应商",
                        },
                        {
                            project: "打拆清场完成",
                            personnel: "现场管理、搬运班组",
                            material: "空调供应商确认，需到场放样",
                        },
                    ],
                },
                {
                    date: "6月9日-6月13日",
                    week: "第二周",
                    phase: "基础工程",
                    tasks: [
                        {
                            project: "泥水砌墙材料进场",
                            personnel: "现场管理、搬运班组",
                            material: "",
                        },
                        {
                            project: "泥水砌墙施工",
                            personnel: "现场管理、泥水班组",
                            material: "",
                        },
                        {
                            project: "泥水砌墙完成",
                            personnel: "现场管理、泥水班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "6月16日-6月20日",
                    week: "第三周",
                    phase: "基础工程",
                    tasks: [
                        {
                            project: "全景放样",
                            personnel: "现场管理，泥水，木作班组，空调供应商",
                            material: "",
                        },
                        {
                            project: "全景放样完成准备施工",
                            personnel: "现场管理",
                            material: "",
                        },
                    ],
                },
                {
                    date: "6月23日-6月27日",
                    week: "第四周",
                    phase: "基础工程",
                    tasks: [
                        {
                            project: "水电隐蔽工程施工",
                            personnel: "现场管理、水电班组",
                            material: "",
                        },
                        {
                            project: "水电隐蔽工程施工完成",
                            personnel: "现场管理、水电班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "6月30日-7月4日",
                    week: "第五周",
                    phase: "隐蔽工程",
                    tasks: [
                        {
                            project: "水电隐蔽预埋工程验收及整改",
                            personnel: "现场管理、水电班组",
                            material: "瓷砖供应商确认，加工砖",
                        },
                        {
                            project: "水电线槽修复",
                            personnel: "现场管理、泥水班组",
                            material: "",
                        },
                        {
                            project: "厨房、阳台、卫生间客厅地面找平",
                            personnel: "现场管理、泥水班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "7月7日-7月11日",
                    week: "第六周",
                    phase: "隐蔽工程",
                    tasks: [
                        {
                            project: "木作材料进场",
                            personnel: "现场管理、木作班组",
                            material: "",
                        },
                        {
                            project: "防水施工",
                            personnel: "现场管理、专业防水人员",
                            material: "",
                        },
                        {
                            project: "防水验收",
                            personnel: "现场管理、业主代表、物业代表",
                            material: "",
                        },
                        {
                            project: "吊顶木作框架完成",
                            personnel: "现场管理、木作班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "7月14日-7月18日",
                    week: "第七周",
                    phase: "隐蔽、饰面工程",
                    tasks: [
                        {
                            project: "吊顶木作框架完成及验收",
                            personnel: "现场管理、木作班组",
                            material: "",
                        },
                        {
                            project: "入户门安装",
                            personnel: "现场管理、专业安装人员",
                            material: "",
                        },
                        {
                            project: "吊顶木作封板",
                            personnel: "现场管理、木作班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "7月21日-7月25日",
                    week: "第八周",
                    phase: "隐蔽、饰面工程",
                    tasks: [
                        {
                            project: "瓷砖进场",
                            personnel: "现场管理、泥水班组",
                            material: "",
                        },
                        {
                            project: "地砖铺贴",
                            personnel: "现场管理、泥水班组",
                            material: "",
                        },
                        {
                            project: "厨房墙砖铺贴",
                            personnel: "现场管理、泥水班组",
                            material: "整木，橱柜下单",
                        },
                    ],
                },
                {
                    date: "7月28日-8月1日",
                    week: "第九周",
                    phase: "隐蔽、饰面工程",
                    tasks: [
                        {
                            project: "地砖铺贴完成",
                            personnel: "现场管理、泥水班组",
                            material: "",
                        },
                        {
                            project: "厨房墙砖铺贴完成",
                            personnel: "现场管理、泥水班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "8月4日-8月8日",
                    week: "第十周",
                    phase: "饰面工程",
                    tasks: [
                        {
                            project: "石材进场施工",
                            personnel: "现场管理、泥水班组",
                            material: "",
                        },
                        { project: "泥水收尾", personnel: "", material: "" },
                        {
                            project: "美缝施工",
                            personnel: "现场管理、美缝班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "8月11日-8月15日",
                    week: "第十一周",
                    phase: "饰面工程",
                    tasks: [
                        {
                            project: "地面保护",
                            personnel: "现场管理、保护班组",
                            material: "",
                        },
                        {
                            project: "油漆材料进场",
                            personnel: "现场管理、油漆班组",
                            material: "",
                        },
                        {
                            project: "油漆进场基层处理",
                            personnel: "现场管理、油漆班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "8月18日-8月22日",
                    week: "第十二周",
                    phase: "饰面工程",
                    tasks: [
                        {
                            project: "油漆第一道腻子施工完成",
                            personnel: "现场管理、油漆班组",
                            material: "",
                        },
                        {
                            project: "油漆第二道腻子施工完成",
                            personnel: "现场管理、油漆班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "8月25日-8月29日",
                    week: "第十三周",
                    phase: "安装工程",
                    tasks: [
                        {
                            project: "油漆第三道腻子施工完成",
                            personnel: "现场管理、油漆班组",
                            material: "",
                        },
                        {
                            project: "油漆打磨",
                            personnel: "现场管理、油漆班组",
                            material: "",
                        },
                        {
                            project: "油漆上底漆完成",
                            personnel: "现场管理、油漆班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "9月1日-9月5日",
                    week: "第十四周",
                    phase: "安装工程",
                    tasks: [
                        {
                            project: "油漆上漆完成",
                            personnel: "现场管理、油漆班组",
                            material: "",
                        },
                        {
                            project: "铝合金推拉门以及房门安装",
                            personnel: "现场管理、业主代表",
                            material: "",
                        },
                        {
                            project: "整木房门安装",
                            personnel: "现场管理、整木班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "9月8日-9月12日",
                    week: "第十五周",
                    phase: "安装工程",
                    tasks: [
                        {
                            project: "集成吊顶安装",
                            personnel: "现场管理、橱柜安装人员",
                            material: "",
                        },
                        {
                            project: "开关面板灯具洁具安装",
                            personnel: "现场管理、水电班组",
                            material: "",
                        },
                        {
                            project: "空调进场安装",
                            personnel: "现场管理、专业安装人员",
                            material: "",
                        },
                        {
                            project: "保洁",
                            personnel: "现场管理、保洁班组",
                            material: "",
                        },
                    ],
                },
                {
                    date: "9月15日-9月19日",
                    week: "第十六周",
                    phase: "安装工程",
                    tasks: [
                        {
                            project: "整体细节收尾",
                            personnel: "业主、现场管理、设计师、各材料供应商",
                            material: "",
                        },
                        {
                            project: "整体验收",
                            personnel: "各施工单位、各材料供应商",
                            material: "",
                        },
                        {
                            project: "整体验收后整改",
                            personnel: "各施工单位、各材料供应商",
                            material: "",
                        },
                        { project: "整体完工", personnel: "", material: "" },
                    ],
                },
            ]);

            // --- METHODS ---
            const setFilter = (filterName) => {
                activeFilter.value = filterName;
            };

            const getDateFromString = (dateStr) => {
                const [month, day] = dateStr.replace("日", "").split("月");
                return new Date(2025, parseInt(month) - 1, parseInt(day));
            };

            // --- COMPUTED PROPERTIES ---
            const timelineItemsWithStatus = computed(() => {
                return timelineData.value.map((item) => {
                    const [startStr, endStr] = item.date.split("-");
                    const startDate = getDateFromString(startStr);
                    const endDate = getDateFromString(endStr);

                    let status = {};
                    if (currentDate > endDate) {
                        status = { text: "已完成", class: "status-completed" };
                    } else if (currentDate >= startDate && currentDate <= endDate) {
                        status = { text: "进行中", class: "status-inprogress" };
                    } else {
                        status = { text: "未开始", class: "status-upcoming" };
                    }
                    return { ...item, status };
                });
            });

            const filteredTimelineItems = computed(() => {
                if (activeFilter.value === "全部阶段") {
                    return timelineItemsWithStatus.value;
                }
                return timelineItemsWithStatus.value.filter((item) =>
                    item.phase.includes(activeFilter.value)
                );
            });

            const overallProgress = computed(() => {
                const completedCount = timelineItemsWithStatus.value.filter(
                    (item) => item.status.text === "已完成"
                ).length;
                const totalCount = timelineData.value.length;
                return totalCount > 0 ? (completedCount / totalCount) * 100 : 0;
            });

            // --- RETURN TO TEMPLATE ---
            return {
                filters,
                activeFilter,
                setFilter,
                filteredTimelineItems,
                overallProgress,
            };
        },
    }).mount("#app");
</script>
</body>
</html>
