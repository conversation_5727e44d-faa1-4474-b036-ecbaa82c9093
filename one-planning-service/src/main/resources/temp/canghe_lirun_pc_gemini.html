<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓禾利润计算 - 桌面版原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: #f3f4f6; /* gray-100 */
        }
        .sidebar {
            width: 260px;
            transition: width 0.3s ease-in-out;
        }
        .sidebar-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.25rem; /* py-3 px-5 */
            color: #d1d5db; /* gray-300 */
            border-radius: 0.5rem; /* rounded-lg */
            transition: background-color 0.2s ease, color 0.2s ease;
        }
        .sidebar-link:hover {
            background-color: #374151; /* gray-700 */
            color: white;
        }
        .sidebar-link.active {
            background-color: #1d4ed8; /* blue-700 */
            color: white;
            font-weight: 600;
        }
        .sidebar-link i {
            margin-right: 0.75rem; /* mr-3 */
            width: 1.25rem; /* w-5 for icon alignment */
            text-align: center;
        }
        .main-content-section {
            display: none;
            animation: fadeInContent 0.5s ease-in-out;
        }
        .main-content-section.active {
            display: block;
        }
        @keyframes fadeInContent {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #e5e7eb; /* gray-200 */
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #9ca3af; /* gray-400 */
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #6b7280; /* gray-500 */
        }
        .table th, .table td {
            padding: 0.75rem 1rem; /* py-3 px-4 */
            border-bottom-width: 1px;
            border-color: #e5e7eb; /* gray-200 */
        }
        .table th {
            background-color: #f9fafb; /* gray-50 */
            font-weight: 600;
            text-align: left;
        }
        .modal-overlay {
            background-color: rgba(0,0,0,0.5);
            z-index: 40;
        }
        .modal-content {
            z-index: 50;
            max-height: 90vh;
        }
    </style>
</head>
<body class="flex h-screen overflow-hidden">

<!-- Sidebar -->
<aside class="sidebar bg-gray-800 text-gray-100 p-4 space-y-2 flex flex-col shadow-lg">
    <div class="text-center py-4 mb-4">
        <h1 class="text-2xl font-bold text-white">仓禾利润计算</h1>
        <span class="text-xs text-gray-400">桌面版</span>
    </div>
    <nav class="flex-grow">
        <a href="#dashboard" class="sidebar-link active" data-target="section-dashboard">
            <i class="fas fa-tachometer-alt"></i><span>仪表盘</span>
        </a>
        <a href="#new-calculation" class="sidebar-link" data-target="section-new-calculation">
            <i class="fas fa-calculator"></i><span>新建计算</span>
        </a>
        <a href="#calculation-history" class="sidebar-link" data-target="section-calculation-history">
            <i class="fas fa-history"></i><span>计算历史</span>
        </a>
        <a href="#products" class="sidebar-link" data-target="section-products">
            <i class="fas fa-box-open"></i><span>产品管理</span>
        </a>
        <a href="#settings" class="sidebar-link" data-target="section-settings">
            <i class="fas fa-cog"></i><span>系统设置</span>
        </a>
    </nav>
    <div class="mt-auto pt-4 border-t border-gray-700">
        <div class="flex items-center p-2">
            <img src="https://placehold.co/40x40/E2E8F0/4A5568?text=User" alt="[用户头像]" class="w-10 h-10 rounded-full mr-3">
            <div>
                <p class="text-sm font-semibold text-white">用户名</p>
                <a href="#" class="text-xs text-blue-400 hover:text-blue-300">退出登录</a>
            </div>
        </div>
    </div>
</aside>

<!-- Main Content Area -->
<main class="flex-1 p-6 overflow-y-auto">
    <!-- Section: Dashboard -->
    <section id="section-dashboard" class="main-content-section active space-y-6">
        <header class="mb-6">
            <h2 class="text-3xl font-semibold text-gray-800">仪表盘</h2>
            <p class="text-gray-600">欢迎回来！这是您当前的利润概览。</p>
        </header>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                <div class="flex items-center text-blue-600">
                    <i class="fas fa-wallet fa-2x mr-3"></i>
                    <h3 class="text-lg font-semibold">本月总利润 (CNY)</h3>
                </div>
                <p class="text-4xl font-bold text-gray-800 mt-2">¥ 71,010.00</p>
                <p class="text-sm text-gray-500 mt-1">约合 9,864.00 USD</p>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                <div class="flex items-center text-green-600">
                    <i class="fas fa-chart-line fa-2x mr-3"></i>
                    <h3 class="text-lg font-semibold">平均利润率</h3>
                </div>
                <p class="text-4xl font-bold text-gray-800 mt-2">4.09%</p>
                <p class="text-sm text-gray-500 mt-1">基于最近30天计算</p>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                <div class="flex items-center text-orange-500">
                    <i class="fas fa-cubes fa-2x mr-3"></i>
                    <h3 class="text-lg font-semibold">活跃产品数</h3>
                </div>
                <p class="text-4xl font-bold text-gray-800 mt-2">15</p>
                <p class="text-sm text-gray-500 mt-1">参与近期计算的产品</p>
            </div>
        </div>

        <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 class="text-xl font-semibold text-gray-700 mb-4">近期利润趋势 (示意图)</h3>
            <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-chart-area fa-4x text-gray-400"></i>
                <p class="ml-3 text-gray-500">利润趋势图表区域</p>
            </div>
        </div>
    </section>

    <!-- Section: New Calculation -->
    <section id="section-new-calculation" class="main-content-section space-y-6">
        <header class="flex justify-between items-center mb-6">
            <h2 class="text-3xl font-semibold text-gray-800">新建利润计算</h2>
            <button class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-6 rounded-lg shadow transition-colors">
                <i class="fas fa-save mr-2"></i>保存计算
            </button>
        </header>

        <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <div class="mb-4">
                <label for="calc-batch-name" class="block text-sm font-medium text-gray-700 mb-1">批次名称/备注</label>
                <input type="text" id="calc-batch-name" value="桌面版 - 2024-06-05 混合采购" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>

            <h3 class="text-xl font-semibold text-gray-700 mb-3 mt-6">产品条目</h3>
            <div id="desktop-calc-items-container" class="space-y-3">
                <!-- Calculation Item Row Template (for JS to clone) -->
                <div class="grid grid-cols-12 gap-3 items-end p-3 bg-gray-50 rounded-md border calculation-item-row">
                    <div class="col-span-3">
                        <label class="block text-xs font-medium text-gray-600">产品名称</label>
                        <input type="text" placeholder="选择或输入产品" class="mt-1 w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm">
                    </div>
                    <div class="col-span-1">
                        <label class="block text-xs font-medium text-gray-600">数量</label>
                        <input type="number" value="1" class="mt-1 w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm">
                    </div>
                    <div class="col-span-2">
                        <label class="block text-xs font-medium text-gray-600">采购单价</label>
                        <div class="flex mt-1">
                            <input type="number" step="0.01" class="w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm">
                            <select class="w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-100">
                                <option>CNY</option><option selected>USD</option><option>HKD</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-span-2">
                        <label class="block text-xs font-medium text-gray-600">销售单价</label>
                        <div class="flex mt-1">
                            <input type="number" step="0.01" class="w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm">
                            <select class="w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-100">
                                <option selected>CNY</option><option>USD</option><option>HKD</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-span-1">
                        <label class="block text-xs font-medium text-gray-600 invisible">0成本</label> <!-- Spacer for alignment -->
                        <div class="mt-1 flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label class="ml-1.5 text-xs text-gray-600">0成本</label>
                        </div>
                    </div>
                    <div class="col-span-2 text-right">
                        <p class="text-xs text-gray-500">单品利润</p>
                        <p class="text-sm font-semibold text-green-600">¥ 0.00</p>
                    </div>
                    <div class="col-span-1 flex items-end justify-end">
                        <button class="text-red-500 hover:text-red-700"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <!-- Example Item 1 (from mobile) -->
                <div class="grid grid-cols-12 gap-3 items-end p-3 bg-gray-50 rounded-md border calculation-item-row">
                    <div class="col-span-3">
                        <label class="block text-xs font-medium text-gray-600">产品名称</label>
                        <input type="text" value="6400" class="mt-1 w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm">
                    </div>
                    <div class="col-span-1">
                        <label class="block text-xs font-medium text-gray-600">数量</label>
                        <input type="number" value="6400" class="mt-1 w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm">
                    </div>
                    <div class="col-span-2">
                        <label class="block text-xs font-medium text-gray-600">采购单价</label>
                        <div class="flex mt-1">
                            <input type="number" step="0.01" value="28.70" class="w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm">
                            <select class="w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-100">
                                <option>CNY</option><option selected>USD</option><option>HKD</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-span-2">
                        <label class="block text-xs font-medium text-gray-600">销售单价</label>
                        <div class="flex mt-1">
                            <input type="number" step="0.01" value="28.00" class="w-2/3 px-2 py-1.5 border border-gray-300 rounded-l-md shadow-sm text-sm">
                            <select class="w-1/3 px-1 py-1.5 border-t border-b border-r border-gray-300 rounded-r-md shadow-sm text-xs bg-gray-100">
                                <option>CNY</option><option selected>USD</option><option>HKD</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-span-1">
                        <label class="block text-xs font-medium text-gray-600 invisible">0成本</label>
                        <div class="mt-1 flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label class="ml-1.5 text-xs text-gray-600">0成本</label>
                        </div>
                    </div>
                    <div class="col-span-2 text-right">
                        <p class="text-xs text-gray-500">单品利润 (USD)</p>
                        <p class="text-sm font-semibold text-red-600">-4,480.00</p>
                    </div>
                    <div class="col-span-1 flex items-end justify-end">
                        <button class="text-red-500 hover:text-red-700"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
            </div>
            <button class="mt-4 w-full bg-blue-100 hover:bg-blue-200 text-blue-700 font-semibold py-2.5 px-4 rounded-lg shadow-sm border border-blue-300 flex items-center justify-center">
                <i class="fas fa-plus mr-2"></i> 添加产品项
            </button>
        </div>

        <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-200 mt-6">
            <h3 class="text-xl font-bold text-gray-800 mb-3">总计 (基准货币: CNY)</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2 text-md">
                <p class="flex justify-between">总采购成本: <span class="font-semibold">¥ 194,347.28</span></p>
                <p class="flex justify-between">总销售收入: <span class="font-semibold">¥ 196,839.60</span></p>
                <p class="flex justify-between text-lg text-green-700 col-span-1 md:col-span-2 border-t pt-2 mt-1">总利润: <span class="font-bold">¥ 71,010.00</span></p>
                <p class="flex justify-between text-gray-600">利润率: <span class="font-semibold">4.09%</span></p>
                <p class="flex justify-between text-sm text-gray-500">折合USD (汇率 1 USD = 7.20 CNY): <span class="font-semibold">$ 9,862.50</span></p>
            </div>
        </div>
    </section>

    <!-- Section: Calculation History -->
    <section id="section-calculation-history" class="main-content-section space-y-6">
        <header class="flex justify-between items-center mb-6">
            <h2 class="text-3xl font-semibold text-gray-800">计算历史</h2>
            <div class="flex items-center space-x-2">
                <input type="text" placeholder="搜索历史记录..." class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow transition-colors">
                    <i class="fas fa-search mr-1"></i>搜索
                </button>
            </div>
        </header>
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-x-auto">
            <table class="w-full table">
                <thead>
                <tr>
                    <th>批次名称</th>
                    <th>计算日期</th>
                    <th>总利润 (CNY)</th>
                    <th>利润率</th>
                    <th>条目数</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>2024-06-04 混合采购批次</td>
                    <td>2024-06-04 10:30</td>
                    <td class="text-green-600 font-semibold">¥ 71,010.00</td>
                    <td>4.09%</td>
                    <td>6</td>
                    <td>
                        <button class="text-blue-600 hover:text-blue-800 mr-2" title="查看详情"><i class="fas fa-eye"></i></button>
                        <button class="text-orange-500 hover:text-orange-700 mr-2" title="复制为新计算"><i class="fas fa-copy"></i></button>
                        <button class="text-red-500 hover:text-red-700" title="删除"><i class="fas fa-trash-alt"></i></button>
                    </td>
                </tr>
                <tr>
                    <td>五月大促清仓</td>
                    <td>2024-05-15 14:22</td>
                    <td class="text-red-600 font-semibold">¥ -1,250.50</td>
                    <td>-0.85%</td>
                    <td>3</td>
                    <td>
                        <button class="text-blue-600 hover:text-blue-800 mr-2"><i class="fas fa-eye"></i></button>
                        <button class="text-orange-500 hover:text-orange-700 mr-2"><i class="fas fa-copy"></i></button>
                        <button class="text-red-500 hover:text-red-700"><i class="fas fa-trash-alt"></i></button>
                    </td>
                </tr>
                <!-- More rows -->
                </tbody>
            </table>
        </div>
    </section>

    <!-- Section: Products -->
    <section id="section-products" class="main-content-section space-y-6">
        <header class="flex justify-between items-center mb-6">
            <h2 class="text-3xl font-semibold text-gray-800">产品管理</h2>
            <button onclick="openProductModal(false)" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg shadow transition-colors">
                <i class="fas fa-plus mr-2"></i>添加新产品
            </button>
        </header>
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-x-auto">
            <table class="w-full table">
                <thead>
                <tr>
                    <th>产品ID</th>
                    <th>产品名称</th>
                    <th>默认采购成本</th>
                    <th>默认销售价格</th>
                    <th>备注</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>PROD001</td>
                    <td>6400</td>
                    <td>28.70 USD</td>
                    <td>28.00 USD</td>
                    <td>常规型号</td>
                    <td>
                        <button onclick="openProductModal(true, {id: 'PROD001', name: '6400', cost: '28.70', costCurrency: 'USD', price: '28.00', priceCurrency: 'USD', notes: '常规型号'})" class="text-blue-600 hover:text-blue-800 mr-2" title="编辑"><i class="fas fa-edit"></i></button>
                        <button class="text-red-500 hover:text-red-700" title="删除"><i class="fas fa-trash-alt"></i></button>
                    </td>
                </tr>
                <tr>
                    <td>PROD002</td>
                    <td>256简装</td>
                    <td>0.00 CNY</td>
                    <td>16.88 CNY</td>
                    <td>0成本特例</td>
                    <td>
                        <button onclick="openProductModal(true, {id: 'PROD002', name: '256简装', cost: '0.00', costCurrency: 'CNY', price: '16.88', priceCurrency: 'CNY', notes: '0成本特例'})" class="text-blue-600 hover:text-blue-800 mr-2"><i class="fas fa-edit"></i></button>
                        <button class="text-red-500 hover:text-red-700"><i class="fas fa-trash-alt"></i></button>
                    </td>
                </tr>
                <!-- More rows -->
                </tbody>
            </table>
        </div>
    </section>

    <!-- Section: Settings -->
    <section id="section-settings" class="main-content-section space-y-6">
        <header class="flex justify-between items-center mb-6">
            <h2 class="text-3xl font-semibold text-gray-800">系统设置</h2>
            <button class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-6 rounded-lg shadow transition-colors">
                <i class="fas fa-save mr-2"></i>保存设置
            </button>
        </header>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-200 space-y-4">
                <h3 class="text-xl font-semibold text-gray-700">通用设置</h3>
                <div>
                    <label for="desktop-base-currency" class="block text-sm font-medium text-gray-700">基准货币</label>
                    <select id="desktop-base-currency" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md border">
                        <option selected>CNY (人民币)</option>
                        <option>USD (美元)</option>
                        <option>EUR (欧元)</option>
                    </select>
                    <p class="mt-1 text-xs text-gray-500">所有汇总数据将以此货币为基准显示。</p>
                </div>
                <div>
                    <label for="desktop-date-format" class="block text-sm font-medium text-gray-700">日期格式</label>
                    <select id="desktop-date-format" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md border">
                        <option selected>YYYY-MM-DD</option>
                        <option>DD/MM/YYYY</option>
                        <option>MM/DD/YYYY</option>
                    </select>
                </div>
            </div>

            <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-200 space-y-4">
                <h3 class="text-xl font-semibold text-gray-700">汇率管理 (相对于基准货币 CNY)</h3>
                <div class="space-y-3 max-h-60 overflow-y-auto pr-2">
                    <div class="flex items-center space-x-3">
                        <span class="w-16 text-sm font-medium text-gray-600">1 USD</span>
                        <span class="text-gray-500">=</span>
                        <input type="number" step="0.0001" value="7.2000" class="flex-1 block w-full px-3 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <span class="text-sm text-gray-600">CNY</span>
                        <button class="text-gray-400 hover:text-red-500"><i class="fas fa-times-circle"></i></button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="w-16 text-sm font-medium text-gray-600">1 HKD</span>
                        <span class="text-gray-500">=</span>
                        <input type="number" step="0.0001" value="0.9200" class="flex-1 block w-full px-3 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <span class="text-sm text-gray-600">CNY</span>
                        <button class="text-gray-400 hover:text-red-500"><i class="fas fa-times-circle"></i></button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="w-16 text-sm font-medium text-gray-600">1 EUR</span>
                        <span class="text-gray-500">=</span>
                        <input type="number" step="0.0001" value="7.8500" class="flex-1 block w-full px-3 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <span class="text-sm text-gray-600">CNY</span>
                        <button class="text-gray-400 hover:text-red-500"><i class="fas fa-times-circle"></i></button>
                    </div>
                </div>
                <button class="mt-3 w-full bg-blue-100 hover:bg-blue-200 text-blue-700 font-semibold py-2 px-4 rounded-lg shadow-sm border border-blue-300 text-sm">
                    <i class="fas fa-plus mr-1"></i> 添加汇率
                </button>
            </div>
        </div>
        <div class="bg-white p-6 rounded-xl shadow-lg border border-gray-200 mt-6">
            <h3 class="text-xl font-semibold text-gray-700">关于</h3>
            <p class="text-sm text-gray-600 mt-2">仓禾利润计算桌面版 v1.0.0</p>
            <p class="text-xs text-gray-500">© 2024 仓禾科技. All rights reserved.</p>
        </div>
    </section>
</main>

<!-- Product Add/Edit Modal -->
<div id="product-modal" class="fixed inset-0 modal-overlay items-center justify-center flex hidden">
    <div class="bg-white p-6 md:p-8 rounded-xl shadow-2xl w-full max-w-lg modal-content transform transition-all scale-95 opacity-0 overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h3 id="product-modal-title" class="text-2xl font-semibold text-gray-800">添加新产品</h3>
            <button onclick="closeProductModal()" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
        </div>
        <form id="product-form" class="space-y-4">
            <div>
                <label for="modal-product-name" class="block text-sm font-medium text-gray-700 mb-1">产品名称 <span class="text-red-500">*</span></label>
                <input type="text" id="modal-product-name" required class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">默认单位采购成本</label>
                <div class="flex">
                    <input type="number" step="0.01" id="modal-product-purchase-cost" class="w-2/3 px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <select id="modal-product-purchase-currency" class="w-1/3 px-2 py-2 border-t border-b border-r border-gray-300 bg-gray-50 rounded-r-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option>CNY</option><option>USD</option><option>HKD</option><option>EUR</option>
                    </select>
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">默认单位销售价格</label>
                <div class="flex">
                    <input type="number" step="0.01" id="modal-product-sales-price" class="w-2/3 px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <select id="modal-product-sales-currency" class="w-1/3 px-2 py-2 border-t border-b border-r border-gray-300 bg-gray-50 rounded-r-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option>CNY</option><option>USD</option><option>HKD</option><option>EUR</option>
                    </select>
                </div>
            </div>
            <div>
                <label for="modal-product-notes" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                <textarea id="modal-product-notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
            </div>
            <input type="hidden" id="modal-product-id">
            <div class="flex justify-end space-x-3 pt-4">
                <button type="button" onclick="closeProductModal()" class="px-6 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg border border-gray-300">取消</button>
                <button type="submit" class="px-6 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow">保存产品</button>
            </div>
        </form>
    </div>
</div>


<script>
    const sidebarLinks = document.querySelectorAll('.sidebar-link');
    const contentSections = document.querySelectorAll('.main-content-section');
    const productModal = document.getElementById('product-modal');
    const productModalContent = productModal.querySelector('.modal-content');
    const productForm = document.getElementById('product-form');

    function navigateToSection(targetId) {
        contentSections.forEach(section => {
            section.classList.remove('active');
        });
        const targetSection = document.getElementById(targetId);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        sidebarLinks.forEach(link => {
            link.classList.remove('active');
            if (link.dataset.target === targetId) {
                link.classList.add('active');
            }
        });
        window.scrollTo(0,0);
    }

    sidebarLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetSectionId = link.dataset.target;
            navigateToSection(targetSectionId);
            // Update URL hash for basic history/bookmarking
            // window.location.hash = link.getAttribute('href');
        });
    });

    // Initial section display based on URL hash or default
    // const currentHash = window.location.hash.substring(1);
    // if (currentHash && document.getElementById(currentHash)) {
    //    navigateToSection(currentHash);
    // } else {
    //    navigateToSection('section-dashboard'); // Default
    // }
    // For prototype, always start with dashboard
    navigateToSection('section-dashboard');


    // Product Modal Logic
    function openProductModal(isEdit = false, productData = null) {
        productForm.reset(); // Clear form
        document.getElementById('modal-product-id').value = '';
        const titleEl = document.getElementById('product-modal-title');

        if (isEdit && productData) {
            titleEl.textContent = '编辑产品';
            document.getElementById('modal-product-id').value = productData.id || '';
            document.getElementById('modal-product-name').value = productData.name || '';
            document.getElementById('modal-product-purchase-cost').value = productData.cost || '';
            document.getElementById('modal-product-purchase-currency').value = productData.costCurrency || 'CNY';
            document.getElementById('modal-product-sales-price').value = productData.price || '';
            document.getElementById('modal-product-sales-currency').value = productData.priceCurrency || 'CNY';
            document.getElementById('modal-product-notes').value = productData.notes || '';
        } else {
            titleEl.textContent = '添加新产品';
        }

        productModal.classList.remove('hidden');
        productModal.classList.add('flex');
        setTimeout(() => { // For transition
            productModalContent.classList.remove('scale-95', 'opacity-0');
            productModalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
    }

    function closeProductModal() {
        productModalContent.classList.add('scale-95', 'opacity-0');
        productModalContent.classList.remove('scale-100', 'opacity-100');
        setTimeout(() => {
            productModal.classList.add('hidden');
            productModal.classList.remove('flex');
        }, 200); // Match transition duration
    }

    productForm.addEventListener('submit', function(e) {
        e.preventDefault();
        // Add actual save logic here
        console.log('Product form submitted');
        // const formData = new FormData(productForm);
        // for (let [key, value] of formData.entries()) {
        //     console.log(key, value);
        // }
        closeProductModal();
        // Potentially refresh product list table
    });

    // Close modal on escape key
    window.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !productModal.classList.contains('hidden')) {
            closeProductModal();
        }
    });
    // Close modal on overlay click
    productModal.addEventListener('click', (e) => {
        if (e.target === productModal) {
            closeProductModal();
        }
    });

</script>
</body>
</html>
