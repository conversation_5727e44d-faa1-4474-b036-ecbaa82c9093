<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profit Calculator App Prototype</title>
    <script src="https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/@babel/standalone@7.25.6/babel.min.js"></script>
    <style>
        body { font-family: 'Roboto', sans-serif; }
        .animate-pulse { animation: pulse 1.5s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        input:focus, button:focus { outline: 2px solid #10b981; outline-offset: 2px; }
        /* Fallback for icons if FontAwesome fails */
        .fa-icon-fallback::before { content: '[Icon]'; font-size: 0.8em; color: #666; }
    </style>
</head>
<body class="bg-gray-100">
<div id="root"></div>
<script type="text/babel">
    const { useState, useEffect } = React;

    const App = () => {
        const [page, setPage] = useState('dashboard');
        const [products, setProducts] = useState([]);
        const [history, setHistory] = useState(JSON.parse(localStorage.getItem('calculations')) || []);
        const [currency, setCurrency] = useState('USD');
        const [isMenuOpen, setIsMenuOpen] = useState(false);

        const HKD_TO_USD = 0.1275;

        const calculateProfit = (product) => {
            const cost = product.cost * product.quantity * (currency === 'USD' ? 1 : 1 / HKD_TO_USD);
            const sales = product.price * product.quantity * (currency === 'USD' ? 1 : 1 / HKD_TO_USD);
            return sales - cost;
        };

        const totalProfit = products.reduce((sum, p) => sum + calculateProfit(p), 0);
        const totalSales = products.reduce((sum, p) => sum + p.price * p.quantity * (currency === 'USD' ? 1 : 1 / HKD_TO_USD), 0);
        const profitMargin = totalSales ? ((totalProfit / totalSales) * 100).toFixed(2) : 0;

        const addProduct = () => {
            setProducts([...products, { id: Date.now(), name: '', quantity: 0, cost: 0, price: 0 }]);
        };

        const updateProduct = (id, field, value) => {
            setProducts(products.map(p => p.id === id ? { ...p, [field]: value } : p));
        };

        const removeProduct = (id) => {
            setProducts(products.filter(p => p.id !== id));
        };

        const saveCalculation = () => {
            const calculation = {
                id: Date.now(),
                date: new Date().toLocaleString(),
                products,
                totalProfit,
                profitMargin
            };
            const newHistory = [...history, calculation];
            setHistory(newHistory);
            localStorage.setItem('calculations', JSON.stringify(newHistory));
            setPage('results');
        };

        const exportResults = () => {
            const text = `Profit Calculation Summary\nDate: ${new Date().toLocaleString()}\n\n` +
                products.map(p => `${p.name || 'Unnamed Product'}:\n  Quantity: ${p.quantity}\n  Cost: ${currency === 'USD' ? '$' : 'HK$'}${p.cost.toFixed(2)}\n  Sales: ${currency === 'USD' ? '$' : 'HK$'}${p.price.toFixed(2)}\n  Profit: ${currency === 'USD' ? '$' : 'HK$'}${calculateProfit(p).toFixed(2)}\n`).join('\n') +
                `\nTotal Profit: ${currency === 'USD' ? '$' : 'HK$'}${totalProfit.toFixed(2)}\nProfit Margin: ${profitMargin}%`;
            const blob = new Blob([text], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'profit_summary.txt';
            a.click();
            URL.revokeObjectURL(url);
        };

        return (
            <div className="min-h-screen bg-gray-100">
                {/* Header */}
                <header className="bg-white shadow sticky top-0 z-10">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
                        <h1 className="text-xl font-bold text-gray-900">Profit Calculator</h1>
                        <button onClick={() => setIsMenuOpen(!isMenuOpen)} className="md:hidden text-gray-600">
                            <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-xl fa-icon-fallback`}></i>
                        </button>
                        <nav className="hidden md:flex space-x-4">
                            {['dashboard', 'calculation', 'results', 'history', 'settings'].map(p => (
                                <button
                                    key={p}
                                    onClick={() => setPage(p)}
                                    className={`px-3 py-2 rounded-md text-sm font-medium ${page === p ? 'bg-green-500 text-white' : 'text-gray-600 hover:bg-gray-200'}`}
                                >
                                    {p.charAt(0).toUpperCase() + p.slice(1)}
                                </button>
                            ))}
                        </nav>
                    </div>
                    {isMenuOpen && (
                        <nav className="md:hidden bg-white shadow px-4 py-2">
                            {['dashboard', 'calculation', 'results', 'history', 'settings'].map(p => (
                                <button
                                    key={p}
                                    onClick={() => { setPage(p); setIsMenuOpen(false); }}
                                    className={`block w-full text-left px-3 py-2 rounded-md text-sm font-medium ${page === p ? 'bg-green-500 text-white' : 'text-gray-600 hover:bg-gray-200'}`}
                                >
                                    {p.charAt(0).toUpperCase() + p.slice(1)}
                                </button>
                            ))}
                        </nav>
                    )}
                </header>

                {/* Main Content */}
                <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    {page === 'dashboard' && (
                        <div className="space-y-6">
                            <div className="bg-white p-6 rounded-lg shadow">
                                <h2 className="text-lg font-semibold mb-4">Overview</h2>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div className="p-4 bg-green-100 rounded-lg">
                                        <p className="text-sm text-gray-600">Total Profit</p>
                                        <p className="text-2xl font-bold text-green-600">{currency === 'USD' ? '$' : 'HK$'}{totalProfit.toFixed(2)}</p>
                                    </div>
                                    <div className="p-4 bg-blue-100 rounded-lg">
                                        <p className="text-sm text-gray-600">Profit Margin</p>
                                        <p className="text-2xl font-bold text-blue-600">{profitMargin}%</p>
                                    </div>
                                </div>
                            </div>
                            <button
                                onClick={() => setPage('calculation')}
                                className="w
                    w-full sm:w-auto px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center justify-center"
                            >
                                <i className="fas fa-plus mr-2 fa-icon-fallback"></i> New Calculation
                            </button>
                        </div>
                    )}

                    {page === 'calculation' && (
                        <div className="space-y-6">
                            <h2 className="text-lg font-semibold">New Calculation</h2>
                            {products.map(product => (
                                <div key={product.id} className="bg-white p-4 rounded-lg shadow flex flex-col space-y-4">
                                    <input
                                        type="text"
                                        placeholder="Product Name"
                                        value={product.name}
                                        onChange={(e) => updateProduct(product.id, 'name', e.target.value)}
                                        className="border border-gray-300 rounded-md p-2 focus:ring-2 focus:ring-green-500"
                                        aria-label="Product Name"
                                    />
                                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        <input
                                            type="number"
                                            placeholder="Quantity"
                                            value={product.quantity}
                                            onChange={(e) => updateProduct(product.id, 'quantity', parseFloat(e.target.value) || 0)}
                                            className="border border-gray-300 rounded-md p-2 focus:ring-2 focus:ring-green-500"
                                            aria-label="Quantity"
                                        />
                                        <input
                                            type="number"
                                            placeholder={`Cost (${currency})`}
                                            value={product.cost}
                                            onChange={(e) => updateProduct(product.id, 'cost', parseFloat(e.target.value) || 0)}
                                            className="border border-gray-300 rounded-md p-2 focus:ring-2 focus:ring-green-500"
                                            aria-label={`Cost in ${currency}`}
                                        />
                                        <input
                                            type="number"
                                            placeholder={`Price (${currency})`}
                                            value={product.price}
                                            onChange={(e) => updateProduct(product.id, 'price', parseFloat(e.target.value) || 0)}
                                            className="border border-gray-300 rounded-md p-2 focus:ring-2 focus:ring-green-500"
                                            aria-label={`Price in ${currency}`}
                                        />
                                    </div>
                                    <p className="text-sm">
                                        Profit: <span className={calculateProfit(product) >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {currency === 'USD' ? '$' : 'HK$'}{calculateProfit(product).toFixed(2)}
                      </span>
                                    </p>
                                    <button
                                        onClick={() => removeProduct(product.id)}
                                        className="text-red-600 hover:text-red-800"
                                        aria-label={`Remove ${product.name || 'product'}`}
                                    >
                                        <i className="fas fa-trash fa-icon-fallback"></i> Remove
                                    </button>
                                </div>
                            ))}
                            <button
                                onClick={addProduct}
                                className="w-full sm:w-auto px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center justify-center"
                            >
                                <i className="fas fa-plus mr-2 fa-icon-fallback"></i> Add Product
                            </button>
                            <button
                                onClick={saveCalculation}
                                disabled={products.length === 0}
                                className="w-full sm:w-auto px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 flex items-center justify-center"
                            >
                                <i className="fas fa-save mr-2 fa-icon-fallback"></i> Save Calculation
                            </button>
                        </div>
                    )}

                    {page === 'results' && (
                        <div className="space-y-6">
                            <h2 className="text-lg font-semibold">Calculation Results</h2>
                            <div className="bg-white p-6 rounded-lg shadow">
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                    <div className="p-4 bg-green-100 rounded-lg">
                                        <p className="text-sm text-gray-600">Total Profit</p>
                                        <p className="text-2xl font-bold text-green-600">{currency === 'USD' ? '$' : 'HK$'}{totalProfit.toFixed(2)}</p>
                                    </div>
                                    <div className="p-4 bg-blue-100 rounded-lg">
                                        <p className="text-sm text-gray-600">Profit Margin</p>
                                        <p className="text-2xl font-bold text-blue-600">{profitMargin}%</p>
                                    </div>
                                </div>
                                {products.map(product => (
                                    <div key={product.id} className="border-b py-2">
                                        <p className="font-semibold">{product.name || 'Unnamed Product'}</p>
                                        <p className="text-sm">Quantity: {product.quantity}</p>
                                        <p className="text-sm">Cost: {currency === 'USD' ? '$' : 'HK$'}{product.cost.toFixed(2)}</p>
                                        <p className="text-sm">Sales: {currency === 'USD' ? '$' : 'HK$'}{product.price.toFixed(2)}</p>
                                        <p className="text-sm">
                                            Profit: <span className={calculateProfit(product) >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {currency === 'USD' ? '$' : 'HK$'}{calculateProfit(product).toFixed(2)}
                        </span>
                                        </p>
                                    </div>
                                ))}
                            </div>
                            <button
                                onClick={exportResults}
                                className="w-full sm:w-auto px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center justify-center"
                            >
                                <i className="fas fa-download mr-2 fa-icon-fallback"></i> Export Results
                            </button>
                        </div>
                    )}

                    {page === 'history' && (
                        <div className="space-y-6">
                            <h2 className="text-lg font-semibold">Calculation History</h2>
                            {history.length === 0 ? (
                                <p className="text-gray-600">No calculations saved yet.</p>
                            ) : (
                                history.map(calc => (
                                    <div key={calc.id} className="bg-white p-4 rounded-lg shadow">
                                        <p className="font-semibold">{calc.date}</p>
                                        <p>Total Profit: {currency === 'USD' ? '$' : 'HK$'}{calc.totalProfit.toFixed(2)}</p>
                                        <p>Profit Margin: {calc.profitMargin}%</p>
                                        <p className="text-sm">Products: {calc.products.map(p => p.name || 'Unnamed').join(', ')}</p>
                                    </div>
                                ))
                            )}
                        </div>
                    )}

                    {page === 'settings' && (
                        <div className="space-y-6">
                            <h2 className="text-lg font-semibold">Settings</h2>
                            <div className="bg-white p-6 rounded-lg shadow">
                                <label className="block text-sm font-medium text-gray-700" htmlFor="currency">Currency</label>
                                <select
                                    id="currency"
                                    value={currency}
                                    onChange={(e) => setCurrency(e.target.value)}
                                    className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-2 focus:ring-green-500"
                                    aria-label="Select currency"
                                >
                                    <option value="USD">USD</option>
                                    <option value="HKD">HKD</option>
                                </select>
                            </div>
                        </div>
                    )}
                </main>
            </div>
        );
    };

    // Fallback for FontAwesome load failure
    const checkFontAwesome = () => {
        if (!window.FontAwesome) {
            console.warn('FontAwesome failed to load. Using fallback icons.');
        }
    };

    window.addEventListener('load', checkFontAwesome);

    ReactDOM.render(<App />, document.getElementById('root'));
</script>
</body>
</html>