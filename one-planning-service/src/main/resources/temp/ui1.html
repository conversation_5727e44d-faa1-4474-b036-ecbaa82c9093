<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Tester</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: #1e1e1e;
            color: #fff;
            display: flex;
            height: 100vh;
            overflow: hidden;
        }
        .sidebar {
            width: 50px;
            background-color: #252526;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 10px;
        }
        .sidebar-icon {
            color: #8e8e8e;
            font-size: 20px;
            margin: 10px 0;
            cursor: pointer;
        }
        .sidebar-icon:hover {
            color: #fff;
        }
        .workspace {
            width: 250px;
            background-color: #2d2d2d;
            border-right: 1px solid #3e3e3e;
            display: flex;
            flex-direction: column;
        }
        .workspace-header {
            padding: 10px;
            background-color: #3e3e3e;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .workspace-header select {
            background-color: #3e3e3e;
            color: #fff;
            border: none;
        }
        .workspace-actions {
            display: flex;
            justify-content: space-between;
            padding: 10px;
        }
        .workspace-actions button {
            background-color: #3e3e3e;
            color: #fff;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
        }
        .workspace-actions button:hover {
            background-color: #4e4e4e;
        }
        .workspace-content {
            padding: 10px;
            flex-grow: 1;
            overflow-y: auto;
        }
        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .toolbar {
            background-color: #3e3e3e;
            padding: 5px 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .toolbar select {
            background-color: #2d2d2d;
            color: #fff;
            border: 1px solid #4e4e4e;
            padding: 5px;
        }
        .toolbar input[type="text"] {
            flex-grow: 1;
            background-color: #2d2d2d;
            color: #fff;
            border: 1px solid #4e4e4e;
            padding: 5px;
        }
        .toolbar button {
            background-color: #007acc;
            color: #fff;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
        }
        .toolbar button:hover {
            background-color: #005ea6;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #4e4e4e;
            padding: 5px 10px;
            background-color: #2d2d2d;
        }
        .tab {
            padding: 5px 15px;
            cursor: pointer;
            color: #8e8e8e;
        }
        .tab.active {
            color: #fff;
            border-bottom: 2px solid #007acc;
        }
        .tab-content {
            padding: 10px;
            background-color: #252526;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .query-params {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .param-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .param-row input[type="checkbox"] {
            width: 20px;
        }
        .param-row input[type="text"] {
            flex-grow: 1;
            background-color: #2d2d2d;
            color: #fff;
            border: 1px solid #4e4e4e;
            padding: 5px;
        }
        .response-area {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #1e1e1e;
        }
        .response-placeholder {
            text-align: center;
            color: #8e8e8e;
        }
        .response-placeholder button {
            background-color: #007acc;
            color: #fff;
            border: none;
            padding: 5px 10px;
            margin: 10px;
            cursor: pointer;
        }
        .response-placeholder button:hover {
            background-color: #005ea6;
        }
        .bottom-bar {
            background-color: #252526;
            padding: 5px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .bottom-bar a {
            color: #007acc;
            text-decoration: none;
        }
    </style>
</head>
<body>
<div class="sidebar">
    <div class="sidebar-icon">🏠</div>
    <div class="sidebar-icon">🔍</div>
    <div class="sidebar-icon">🌐</div>
    <div class="sidebar-icon">📏</div>
    <div class="sidebar-icon">🔗</div>
    <div class="sidebar-icon">⚙️</div>
    <div class="sidebar-icon">📁</div>
    <div class="sidebar-icon">💻</div>
    <div class="sidebar-icon" style="margin-top: auto;">👤</div>
</div>
<div class="workspace">
    <div class="workspace-header">
        <select>
            <option>Private Workspace</option>
        </select>
        <span>Tutorials</span>
        <span>Website</span>
    </div>
    <div class="workspace-actions">
        <button>+ New</button>
        <button>Import</button>
    </div>
    <div class="workspace-content">
        <div>No environment</div>
        <div style="margin-top: 10px; color: #007acc;">LDX-ONE-PLANNING</div>
    </div>
</div>
<div class="main-content">
    <div class="toolbar">
        <select>
            <option>GET</option>
            <option>POST</option>
            <option>PUT</option>
            <option>DELETE</option>
        </select>
        <input type="text" value="https://one-planning.leedarson.com/api/one-planning/wps/warning/atp/test/downloadResultFile">
        <button>Send Ctrl+⏎</button>
        <button>Save Ctrl+S</button>
    </div>
    <div class="tabs">
        <div class="tab active">Query Params</div>
        <div class="tab">Body</div>
        <div class="tab">Headers</div>
        <div class="tab">Authorization</div>
        <div class="tab">Scripts</div>
    </div>
    <div class="tab-content">
        <div class="query-params">
            <div class="param-row">
                <input type="checkbox">
                <input type="text" placeholder="Key">
                <input type="text" placeholder="Value">
            </div>
            <button style="align-self: flex-start; background-color: #3e3e3e; color: #fff; border: none; padding: 5px 10px; cursor: pointer;">+ Add More</button>
        </div>
    </div>
    <div class="response-area">
        <div class="response-placeholder">
            <div>NOTHING TO SEE HERE!</div>
            <div>Please run a request to see the response</div>
            <button>Send request Ctrl+⏎</button>
        </div>
    </div>
</div>
<script>
    document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', () => {
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
        });
    });
</script>
</body>
</html>