<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.skd.mapper.SkdSaleOrderMapper">

    <insert id="insertFromSelect" >
        insert into
            skd_sale_order(
               cover_so_id,
               cover_so_no,
               customer_code,
               top_no,
               order_id,
               qty,
               product_code,
               plan_start_date,
               plan_end_date,
               ship_time,
               plant,
               plan_ready_date,
               skd_ready_date,
               skd_ready_date_ship,
               skd_ready_date_non_ship,
               land_num,
               air_num,
               skd_all_send)
        select
            concat(sop.cover_so_no, '_', sop.cover_so_line),
            sop.cover_so_no,
            sop.customer_code,
            sop.top_no,
            sop.work_no ,
            sop.qty,
            sop.item_no,
            sop.plan_date,
            sop.plant_finish,
            sop.ship_time ,
            sop.plant ,
            date_add(sop.plan_date, interval -1 day),
            plan_all_qitao_date,
            skd_qitao_date_ship,
            all_qitao_date,
            ifnull(tsous.landNum, 0),
            ifnull(tsous.airNum, 0),
            ifnull(tsous.top_no, 1) = 1
        from
            skd_order_product sop
                left join
            (
                select
                    sous.top_no,
                    sum(if(sous.latest_suggest_transport_mode = 'LAND', 1, 0)) as landNum,
                    sum(if(sous.latest_suggest_transport_mode = 'AIR', 1, 0)) as airNum
                from
                    skd_order_use_sub sous
                group by
                    sous.top_no
            ) tsous on tsous.top_no = sop.top_no
                left join
            (
                select
                    sou.top_no,
                    max(spl.thai_arrive_inbound_date) as thai_arrive_inbound_date
                from
                    skd_purchase_logistics spl
                        join skd_order_use sou on spl.purchase_no = sou.use_no
                group by
                    sou.top_no
            ) tspl on
                tspl.top_no = sop.top_no
        where
            sop.status = 0 and sop.cover_so_no is not null and sop.item_no not like '3%';
    </insert>

<!--    <select id="querySaleOrder" resultType="java.lang.Long">-->
<!--        select-->
<!--            sop.top_no,-->
<!--            concat(sop.cover_so_no, '_', sop.cover_so_line) as coverSoId,-->
<!--            sop.cover_so_no as coverSoNo,-->
<!--            sop.work_no as orderId,-->
<!--            sop.item_no as productCode,-->
<!--            sop.plan_date as planStartDate,-->
<!--            sop.plant_finish as planEndDate,-->
<!--            sop.qty as qty,-->
<!--            sop.ship_time as shipTime,-->
<!--            sop.plant as plant,-->
<!--            date_add(sop.plan_date, interval -1 day) as planReadyDate,-->
<!--            t.chinese_send_date as skdReadyDate,-->
<!--            t.arrive_date_by_sea as skdReadyDateShip,-->
<!--            t.thai_arrive_date as skdReadyDateNonShip,-->
<!--            ifnull(t.landNum, 0) as landNum,-->
<!--            ifnull(t.airNum, 0) as airNum-->
<!--        from-->
<!--            skd_order_product sop-->
<!--        left join (-->
<!--            select-->
<!--                sous.top_no,-->
<!--                max(sous.chinese_send_date) as chinese_send_date,-->
<!--                max(sous.arrive_date_by_sea) as arrive_date_by_sea,-->
<!--                max(sous.thai_arrive_date) as thai_arrive_date,-->
<!--                sum( if( sous.latest_suggest_transport_mode = 'LAND',1,0)) as landNum,-->
<!--                sum(if(sous.latest_suggest_transport_mode = 'AIR', 1, 0)) as airNum-->
<!--            from-->
<!--                skd_order_use_sub sous-->
<!--            group by-->
<!--                sous.top_no-->
<!--            ) t on t.top_no = sop.top_no-->
<!--        where-->
<!--            sop.cover_so_no is not null-->
<!--            <if test="topNo != null and topNo!=''">-->
<!--                sop.top_no = #{topNo}-->
<!--            </if>-->
<!--    </select>-->
</mapper>
