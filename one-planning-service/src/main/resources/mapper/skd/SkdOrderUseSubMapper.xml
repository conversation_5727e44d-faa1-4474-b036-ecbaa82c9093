<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.skd.mapper.SkdOrderUseSubMapper">

    <!-- 公共 SELECT 字段 -->
    <sql id="selectSalesOrderFields">
        sop.cover_so_no as salesOrderNo,
        sop.cover_so_line as salesOrderLine,
        sop.customer_code as customerCode,
        sop.top_no as topNo,
        sop.work_type_name as type,
        sop.work_no as planOrderNo,
        sop.plan_all_qitao_date as fullSetReadyDate3317,
        sop.skd_qitao_date_ship as estThailandInboundFullSeaOnly,
        sop.all_qitao_date as estThailandInboundFullWithLandAir,
        sop.plan_qitao_date as estFullTime,
        case when sous.to_thai_way is null and sou.use_type_name = 'SKD在途' then
            'SKD在途'
        else
            sous.to_thai_way
        end  as deliveryToThailandMethod,
        sop.qty as orderQty,
        sop.item_no as productCode,
        sop.plan_date as planStartDate,
        sop.plant_finish as planFinishDate,
        sop.order_delivery as orderDeliveryDate,
        sou.use_type_name as useTypeThailand,
        sou.use_no as useNoThailand,
        sous.cover_so_no as salesOrderNo3317,
        sous.cover_so_line as salesOrderLine3317,
        sous.cover_so_id as coverSoId,
        sous.use_type as useType3317,
        sous.use_no as useNo3317,
        sous.supply as supplierSub3317,
        sous.use_note as supplierChineseDesc,
        sous.use_no as purchaseOrderNo,
        sou.cover_so_line as poItem,
        sous.po_group_r as purchaser,
        som.material_item_no as materialNo,
        som.material_item_name as materialName,
        sous.on_order_qty as onOrderQty,
        sous.latest_due_date as latestDueDate,
        sous.purchase_qty as purchaseOrderQty,
        IFNULL(sous.use_qty, sou.use_qty) as useQty,
        sous.pull_days as pullDate,
        sous.thai_send_due_date as thailandShipDueDateBestMode,
        sous.thai_transport_mode as thailandTransportMode,
        sous.chinese_send_date as chinaShipEstDate,
        IFNULL(sous.thai_arrive_date,spl.thai_arrive_inbound_date) as thailandInboundEstDate,
        sous.latest_suggest_transport_mode as finalSuggestedTransportMode,
        sousm.distribution_qty as manualDistributionQty,
        sousm.send_time as manualSendTime,
        sousm.transport_mode as manualTransportMode,
        sop.ship_time as shipSchedule,
        sop.plant as orderPlant,
        sop.is_can_send_un_ready as isSendUnReady,
        som.material_lead_time_days as materialLeadTimeDays
    </sql>

    <!-- 公共子查询 -->
    <sql id="subMaxInfoSubQuery">
        select
            top_no,
            max(latest_due_date) as thai_send_due_date,
            max(thai_arrive_date) as thai_arrive_date
        from skd_order_use_sub
        group by top_no
    </sql>

    <sql id="subLogisticsInfoQuery">
        select
            purchase_no,
            max(thai_arrive_inbound_date) as thai_arrive_inbound_date
        from skd_purchase_logistics
        group by purchase_no
    </sql>

    <!-- 公共 WHERE 条件 -->
    <sql id="commonWhereCondition">
        sop.status='0'
        <if test="req.topNo != null and req.topNo != ''">
            and sop.top_no = #{req.topNo, jdbcType=VARCHAR}
        </if>
        <if test="req.topNoList != null and req.topNoList.size() > 0">
            and sop.top_no in
            <foreach collection="req.topNoList" item="topNo" open="(" close=")" separator=",">
                #{topNo, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="req.customerCode != null and req.customerCode != ''">
            and sop.customer_code = #{req.customerCode, jdbcType=VARCHAR}
        </if>
        <if test="req.factoryCode != null and req.factoryCode != ''">
            and sop.plant = #{req.factoryCode, jdbcType=VARCHAR}
        </if>
        <if test="req.coverSoNo != null and req.coverSoNo != ''">
            and sop.cover_so_no = #{req.coverSoNo, jdbcType=VARCHAR}
        </if>
        <if test="req.coverSoNoList != null and req.coverSoNoList.size() > 0">
            and sop.cover_so_no in
            <foreach collection="req.coverSoNoList" item="coverSoNo" open="(" close=")" separator=",">
                #{coverSoNo, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="req.thaiSendDueDateStart != null and req.thaiSendDueDateEnd != null">
            and sous.chinese_send_date between #{req.thaiSendDueDateStart, jdbcType=DATE} and #{req.thaiSendDueDateEnd, jdbcType=DATE}
        </if>
    </sql>

    <!-- 聚合查询销售订单信息 -->
    <select id="aggregateQueriesSalesOrderInfo" resultType="com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO">
        select
            sop.top_no as topNo,
            sop.work_no as workNo,
            sop.work_line as workLine,
        max(sous_max_info.thai_send_due_date) as fullSetReadyDate3317,
        max(IFNULL(sous_max_info.thai_arrive_date,spl.thai_arrive_inbound_date)) as estThailandInboundFullWithLandAir
        from skd_order_product sop
            inner join skd_order_material som on sop.id = som.order_product_id
            inner join skd_order_use sou on sou.order_material_id = som.id
            left join skd_order_use_sub sous on sous.order_use_id = sou.order_use_id
            left join skd_purchase_logistics spl on sou.use_no = spl.purchase_no
            left join (
                <include refid="subMaxInfoSubQuery"/>
            ) sous_max_info on sous_max_info.top_no = sop.top_no
        group by sop.top_no, sop.work_no, sop.work_line
    </select>


    <!-- 查询销售订单信息 -->
    <select id="querySalesOrderInfo" resultType="com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO">
        select
        <include refid="selectSalesOrderFields"/>
        from skd_order_product sop
        inner join skd_order_material som on sop.id = som.order_product_id
        inner join skd_order_use sou on sou.order_material_id = som.id
        left join skd_order_use_sub sous on sous.order_use_id = sou.order_use_id
        left join skd_order_use_sub_manual sousm on sousm.use_no = sous.use_no and sousm.top_no = sous.top_no
        left join skd_purchase_logistics spl on sou.use_no = spl.purchase_no
        <where>
            <include refid="commonWhereCondition"/>
        </where>
    </select>

    <!-- 查询 SKD 订单物料 -->
    <select id="getSkdOrderMaterial" resultType="com.lds.oneplanning.skd.domain.SkdOrderMaterial">
        select som.*
        from skd_order_material som
            inner join skd_order_use sou on sou.order_material_id = som.id
            left join skd_order_use_sub sous on sous.cover_so_id = sou.use_no and sous.top_no = sou.top_no
        where som.top_no=#{topNo} and sous.use_no=#{useNo}
    </select>

    <!-- 分页查询销售订单信息 -->
    <select id="querySalesOrderInfoPage" resultType="com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO">
        select
            <include refid="selectSalesOrderFields"/>
        from skd_order_product sop
            inner join skd_order_material som on sop.id = som.order_product_id
            inner join skd_order_use sou on sou.order_material_id = som.id
            left join skd_order_use_sub sous on sous.order_use_id = sou.order_use_id
            left join skd_order_use_sub_manual sousm on sousm.use_no = sous.use_no and sousm.top_no = sous.top_no
            left join skd_purchase_logistics spl on sou.use_no = spl.purchase_no
        <where>
            <include refid="commonWhereCondition"/>
        </where>
    </select>

</mapper>
