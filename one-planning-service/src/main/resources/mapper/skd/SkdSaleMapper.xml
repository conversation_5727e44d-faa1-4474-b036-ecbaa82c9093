<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.skd.mapper.SkdSaleMapper">

    <insert id="insertFromSelect" >
        insert into
            skd_sale(
                cover_so_no,
                customer_code,
                top_no,
                qty,
                plan_start_date,
                plan_end_date,
                ship_time,
                plant,
                plan_ready_date,
                skd_ready_date,
                skd_ready_date_ship,
                skd_ready_date_non_ship,
                land_num,
                air_num,
                skd_all_send
        )
        select
            cover_so_no,
            customer_code,
            top_no,
            sum(qty),
            min(plan_start_date),
            max(plan_end_date),
            max(ship_time),
            plant,
            max(plan_ready_date),
            max(skd_ready_date),
            max(skd_ready_date_ship),
            max(skd_ready_date_non_ship),
            land_num,
            air_num,
            max(skd_all_send)
        from
            skd_sale_order
        group by
            cover_so_no;
    </insert>
</mapper>
