<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.skd.mapper.SkdOrderProductMapper">

    <insert id="batchInsertOrUpdate">
        <foreach item="item" collection="list" separator=";" index="index">
            insert into skd_order_product (
            top_no,
            status,
            create_time)
            values (
            #{item.topNo},
            #{item.status},
            NOW()
            )
            on duplicate key update
            status = #{item.status},
            update_time = NOW()
            )
        </foreach>
    </insert>
</mapper>
