<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.skd.mapper.SkdOrderMaterialMapper">

    <update id="updateFromSelect" >
        update
            skd_order_material som
            left join (
                select
                    sou.order_material_id,
                    SUM(case when sou.use_type_name like '%库存%' then sou.use_qty else 0 end) as inventory_qty,
                    SUM(case when sou.use_type_name = 'SKD在途' then sou.use_qty else 0 end) as transit_qty
                from
                    skd_order_use sou
                group by
                    sou.order_material_id
                ) ssou on
                som.id = ssou.order_material_id
            set
                som.inventory_qty = ssou.inventory_qty,
                som.remaining_qty = ROUND(som.need_qty - ssou.inventory_qty, 3),
                som.transit_qty = ssou.transit_qty,
                som.un_transit_qty = ROUND(som.need_qty - ssou.inventory_qty - ssou.transit_qty, 3)
    </update>
</mapper>
