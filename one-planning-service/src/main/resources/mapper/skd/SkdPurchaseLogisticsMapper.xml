<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.skd.mapper.SkdPurchaseLogisticsMapper">

    <!-- 公共 SELECT 字段 -->
    <sql id="selectFields">
        group_concat(sop.top_no) as topNo,
        group_concat(sop.cover_so_no) as coverSoNo,
        group_concat(sop.work_no) as workNo,
        som.material_item_no as materialItemNo,
        som.material_item_name as materialItemName,
        som.def_place as defPlace,
        som.def_place_name as defPlaceName,
        som.place as place,
        som.place_name as placeName,
        som.item_pack_place as itemPackPlace,
        som.po_type as poType,
        spl.purchase_no as purchaseNo,
        spl.outbound_delivery_no as outboundDeliveryNo,
        spl.outbound_delivery_line as outboundDeliveryLine,
        spl.transit_qty as transitQty,
        spl.logistics_no as logisticsNo,
        spl.transport_mode as transportMode,
        spl.send_date as sendDate,
        spl.status as status,
        spl.eta as eta,
        spl.thai_arrive_date as thaiArriveDate,
        spl.is_abnormal as isAbnormal,
        spl.gap_days as gapDays,
        spl.is_need_urgent as isNeedUrgent,
        spl.plan_arrive_factory_date as planArriveFactoryDate,
        spl.arrive_factory_date as arriveFactoryDate
    </sql>

    <!-- 公共 FROM + JOIN 子句 -->
    <sql id="fromJoins">
        from skd_order_product sop
        inner join skd_order_material som on sop.id = som.order_product_id
        inner join skd_order_use sou on sou.order_material_id = som.id
        join skd_purchase_logistics spl on sou.use_no = spl.purchase_no
    </sql>

    <!-- 公共 WHERE 条件 -->
    <sql id="whereConditions">
        <where>
            <if test="bo.topNo != null and bo.topNo != ''">
                and sop.top_no = #{bo.topNo, jdbcType=VARCHAR}
            </if>
            <if test="bo.topNoList != null and bo.topNoList.size() > 0">
                and sop.top_no in
                <foreach collection="bo.topNoList" item="topNo" open="(" close=")" separator=",">
                    #{topNo, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="bo.coverSoNo != null and bo.coverSoNo != ''">
                and concat(sop.cover_so_no,'_',sop.cover_so_line) like concat(#{bo.coverSoNo, jdbcType=VARCHAR} ,'%')
            </if>
            <if test="bo.workNo != null and bo.workNo != ''">
                and sop.work_no = #{bo.workNo, jdbcType=VARCHAR}
            </if>
            <if test="bo.purchaseNo != null and bo.purchaseNo != ''">
                and spl.purchase_no = #{bo.purchaseNo, jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <select id="queryPage" resultType="com.lds.oneplanning.skd.domain.vo.SkdPurchaseLogisticsVO">
        select
            <include refid="selectFields"/>
        <include refid="fromJoins"/>
        <include refid="whereConditions"/>
        group by spl.purchase_no
    </select>

    <select id="queryList" resultType="com.lds.oneplanning.skd.domain.vo.SkdPurchaseLogisticsVO">
        select
            <include refid="selectFields"/>
        <include refid="fromJoins"/>
        <include refid="whereConditions"/>
        group by spl.purchase_no
    </select>

</mapper>
