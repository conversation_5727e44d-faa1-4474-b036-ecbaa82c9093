<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningQualityHistoryRiskMapper">
    <select id="findList" resultType="com.lds.oneplanning.wps.vo.QualityHistoryRiskVO">
        select
        w.id,
        w.plan_time,
        w.production_workshop,
        w.factory_code,
        w.order_no,
        w.product_id,
        w.product_category,
        w.plan_qty,
        w.system_push ,
        group_concat(pqh.cbyyfx) as exceptionReason ,
        group_concat(pqh.sqrq) as exceptionDate ,
        group_concat(pqh.csdc) as measure ,
        group_concat(concat(pqh.creatorcode,'-',pqh.sqr) ) as exception<PERSON>erson ,
        group_concat(concat(pqh.zrrcode ,'-',pqh.zrr) ) as qualityPerson ,
        group_concat(pqh. xgyz) as effectVerification
        from
        warning_quality_history_risk w
        inner join product_quality_history pqh on pqh.cpid = w.product_id
        <where>
            <if test="orderNo != null and orderNo != ''">
                and w.order_no = #{orderNo}
            </if>
            <if test="factoryCodes != null and factoryCodes != ''">
                and w.factory_code in
                <foreach item="item" index="index" collection="factoryCodes.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productId != null and productId != ''">
                and w.product_id = #{productId}
            </if>
            <if test="planStartTime != null and planStartTime != ''">
                and w.plan_time >= #{planStartTime}
            </if>
            <if test="planEndTime != null and planEndTime != ''">
                and w.plan_time &lt;= #{planEndTime}
            </if>
            <if test="qualityPerson != null and qualityPerson != ''">
                and pqh.zrr like concat('%',#{qualityPerson},'%')
            </if>
        </where>
        group by w.id
        order by w.plan_time,w.id
    </select>
</mapper>