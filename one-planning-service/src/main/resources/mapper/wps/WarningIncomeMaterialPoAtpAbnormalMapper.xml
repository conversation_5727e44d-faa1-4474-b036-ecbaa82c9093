<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningIncomeMaterialPoAtpAbnormalMapper">

    <select id="queryUnHandleData" resultType="com.lds.oneplanning.wps.entity.WarningIncomeMaterialPoAtpAbnormal">
        select a.shortage_material_id, a.order_no, a.id
        from warning_income_material_po_atp_abnormal a
                 inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'MATERIAL_INSPECTION_ABNORMAL'
        where t.process_status &lt;&gt; 'CLOSED'
    </select>
</mapper>
