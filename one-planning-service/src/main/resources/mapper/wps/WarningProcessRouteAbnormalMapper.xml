<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningProcessRouteAbnormalMapper">

    <select id="queryUnHandleData" resultType="com.lds.oneplanning.wps.entity.WarningProcessRouteAbnormal">
        select a.material_id, a.planned_order, a.id
        from warning_process_route_abnormal a
                 inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'PROCESS_ROUTE_ABNORMAL'
        where t.process_status &lt;&gt; 'CLOSED'
    </select>

    <select id="selectPage" resultType="com.lds.oneplanning.wps.vo.WarningProcessRouteAbnormalVO">
        select
        wpra.*,
        t.process_status,
        t.push_status
        from warning_process_route_abnormal wpra
        inner join warning_todo_list t on wpra.id = t.biz_id and t.warning_type = 'PROCESS_ROUTE_ABNORMAL'
        <where>
            wpra.planned_order is not null
            <if test="params.plannedOrder != null and params.plannedOrder != ''">
                AND wpra.planned_order LIKE CONCAT('%', #{params.plannedOrder}, '%')
            </if>
            <if test="params.factory != null and params.factory != ''">
                AND wpra.factory IN
                <foreach item="item" collection="params.factory.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.customer != null and params.customer != ''">
                AND wpra.customer  LIKE CONCAT('%', #{params.customer}, '%')
            </if>
            <if test="params.materialId != null and params.materialId != ''">
                AND wpra.material_id LIKE CONCAT('%', #{params.materialId}, '%')
            </if>
            <if test="params.npiUserid != null and params.npiUserid != ''">
                AND wpra.npi_person_userid LIKE CONCAT('%', #{params.npiUserid}, '%')
            </if>
            <if test="params.processStatus != null and params.processStatus != ''">
                AND t.process_status = #{params.processStatus}
            </if>
        </where>
        order by wpra.planned_online_time
    </select>
</mapper>
