<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningIndependentPrepareMaterialAbnormalMapper">

  <select id="queryUnHandleData"
          resultType="com.lds.oneplanning.wps.entity.WarningIndependentPrepareMaterialAbnormal">
    select a.id,a.order_number
    from warning_independent_prepare_material_abnormal a
           inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'INDEPENDENT_PREPARE_MATERIAL_ABNORMAL'
    where t.process_status &lt;&gt; 'CLOSED'
  </select>
</mapper>
