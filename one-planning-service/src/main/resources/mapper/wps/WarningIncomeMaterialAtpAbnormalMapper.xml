<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningIncomeMaterialAtpAbnormalMapper">

    <select id="selectPage" resultType="com.lds.oneplanning.wps.vo.WarningIncomeMaterialAtpAbnormalVO2">
        select distinct wimaa.id as id,
            wimaa.customer,
    wimaa.product_id,
    wimaa.order_no,
    wimaa.material_desc,
    wimaa.plan_date,
    wimaa.plan_quantity,
        wimaa.sales_order_number,
        wimaa.line_number,
        wimaa.Latest_demand_time,
        wimaa.factory,
        CONCAT(wimaa.sales_order_number, '-', wimaa.line_number) as xsddhxm,
        wimaa.online_time,
        t.process_status

        from warning_income_material_atp_abnormal wimaa
        inner join warning_income_material_po_atp_abnormal wimpaa on wimaa.order_no = wimpaa.order_no
        inner join warning_todo_list t on wimpaa.id = t.biz_id and t.warning_type = 'MATERIAL_INSPECTION_ABNORMAL'
        <where>
            wimpaa.order_no is not null
            <if test="params.orderNo != null and params.orderNo != ''">
                AND wimpaa.order_no LIKE CONCAT('%', #{params.orderNo}, '%')
            </if>
            <if test="params.factory != null and params.factory != ''">
                AND wimaa.factory IN
                <foreach item="item" collection="params.factory.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.customer != null and params.customer != ''">
                AND wimaa.customer  LIKE CONCAT('%', #{params.customer}, '%')
            </if>
            <if test="params.productId != null and params.productId != ''">
                AND wimaa.product_id LIKE CONCAT('%', #{params.productId}, '%')
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                AND wimpaa.supply_name LIKE CONCAT('%', #{params.supplierName}, '%')
            </if>
            <if test="params.purchaseContract != null and params.purchaseContract != ''">
                AND wimpaa.purchase_po LIKE CONCAT('%', #{params.purchaseContract}, '%')
            </if>
            <if test="params.materialId != null and params.materialId != ''">
                AND wimpaa.shortage_material_id LIKE CONCAT('%', #{params.materialId}, '%')
            </if>
            <if test="params.startPlanDate != null and params.startPlanDate != ''">
                AND wimaa.plan_date &gt;= #{params.startPlanDate}
            </if>
            <if test="params.endPlanDate != null and params.endPlanDate != ''">
                AND wimaa.plan_date &lt;= #{params.endPlanDate}
            </if>
            <if test="params.processStatus != null and params.processStatus != ''">
                AND t.process_status = #{params.processStatus}
            </if>
        </where>
        order by wimaa.plan_date
    </select>
    <select id="selectPageIqc" resultType="com.lds.oneplanning.wps.vo.WarningIncomeMaterialAtpAbnormalVO">
        select wimaa.id as id,
            wimaa.customer,
    wimaa.product_id,
    wimaa.order_no,
    wimaa.material_desc,
    wimaa.plan_date,
    wimaa.plan_quantity,
        wimaa.sales_order_number,
        wimaa.line_number,
        wimaa.Latest_demand_time,
        wimaa.factory,
        CONCAT(wimaa.sales_order_number, '-', wimaa.line_number) as xsddhxm,
        wimaa.online_time,
        wimpaa.Impact_quantity,
        wimpaa.id as poid,wimpaa.shortage_material_id,wimpaa.purchase_po,wimpaa.shortage_material_desc,wimpaa.check_quantity,
        wimpaa.deal_result,
        wimpaa.quality_inspectors,wimpaa.quality_inspectors_gh,wimpaa.purchaser,wimpaa.purchaser_gh,
        wimpaa.next_arrival_date,wimpaa.earliest_plan_date,wimpaa.sure_replan_date,wimpaa.sfyxsxcjh,wimpaa.impact_type,wimpaa.supply_name,t.process_status

        from warning_income_material_atp_abnormal wimaa
        inner join warning_income_material_po_atp_abnormal wimpaa on wimaa.order_no = wimpaa.order_no
        inner join warning_todo_list t on wimpaa.id = t.biz_id and t.warning_type = 'MATERIAL_INSPECTION_ABNORMAL'
        <where>
            wimpaa.order_no is not null
            <if test="dealResult != null and dealResult != ''">
                AND wimpaa.deal_result IN
                <foreach item="item" collection="dealResult.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                AND wimpaa.supply_name LIKE CONCAT('%', #{params.supplierName}, '%')
            </if>
            <if test="params.dealResult != null and params.dealResult != ''">
                AND wimpaa.deal_result LIKE CONCAT('%', #{params.dealResult}, '%')
            </if>
            <if test="params.materialId != null and params.materialId != ''">
                AND wimpaa.shortage_material_id LIKE CONCAT('%', #{params.materialId}, '%')
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                AND wimaa.order_no LIKE CONCAT('%', #{params.orderNo}, '%')
            </if>
            <if test="params.gh != null and params.gh != ''">
                AND wimpaa.quality_inspectors_gh LIKE CONCAT('%', #{params.gh}, '%')
            </if>
            <if test="params.processStatus != null and params.processStatus != ''">
                AND t.process_status = #{params.processStatus}
            </if>
        </where>
        group by t.biz_id
        order by wimaa.plan_date
    </select>
    <select id="selectPageMc" resultType="com.lds.oneplanning.wps.vo.WarningIncomeMaterialAtpAbnormalVO">
        select wimaa.id as id,
        wimaa.customer,
        wimaa.product_id,
        wimaa.order_no,
        wimaa.material_desc,
        wimaa.plan_date,
        wimaa.plan_quantity,
        wimaa.sales_order_number,
        wimaa.line_number,
        wimaa.Latest_demand_time,
        wimaa.factory,
        CONCAT(wimaa.sales_order_number, '-', wimaa.line_number) as xsddhxm,
        wimaa.online_time,
        wimpaa.Impact_quantity,
        wimpaa.id as poid,wimpaa.shortage_material_id,wimpaa.purchase_po,wimpaa.shortage_material_desc,wimpaa.check_quantity,
        wimpaa.deal_result,
        wimpaa.quality_inspectors,wimpaa.quality_inspectors_gh,wimpaa.purchaser,wimpaa.purchaser_gh,
        wimpaa.next_arrival_date,wimpaa.earliest_plan_date,wimpaa.sure_replan_date,wimpaa.sfyxsxcjh,wimpaa.impact_type,wimpaa.supply_name,t.process_status

        from warning_income_material_atp_abnormal wimaa
        inner join warning_income_material_po_atp_abnormal wimpaa on wimaa.order_no = wimpaa.order_no
        inner join warning_todo_list t on wimpaa.id = t.biz_id and t.warning_type = 'MATERIAL_INSPECTION_ABNORMAL'
        <where>
            wimpaa.order_no is not null
            <if test="dealResult != null and dealResult != ''">
                AND wimpaa.deal_result IN
                <foreach item="item" collection="dealResult.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                AND wimpaa.supply_name LIKE CONCAT('%', #{params.supplierName}, '%')
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                AND wimaa.order_no LIKE CONCAT('%', #{params.orderNo}, '%')
            </if>
            <if test="params.materialId != null and params.materialId != ''">
                AND wimpaa.shortage_material_id LIKE CONCAT('%', #{params.materialId}, '%')
            </if>
            <if test="params.purchaseContract != null and params.purchaseContract != ''">
                AND wimpaa.purchase_po LIKE CONCAT('%', #{params.purchaseContract}, '%')
            </if>
            <if test="params.gh != null and params.gh != ''">
                AND wimpaa.purchaser_gh = #{params.gh}
            </if>
            <if test="params.processStatus != null and params.processStatus != ''">
                AND t.process_status = #{params.processStatus}
            </if>
        </where>
        group by t.biz_id
        order by wimaa.plan_date
    </select>
</mapper>
