<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningInProductionAbnormalMapper">
    <select id="findList" resultType="com.lds.oneplanning.wps.model.InProductionAbnormalDTO">
        select
        wipa.id,
        m.scheduling_date,
        m.material_off_shelf_date,
        m.online_date,
        m.production_workshop,
        m.production_foreman,
        m.production_line,
        m.work_order_no,
        m.order_no,
        m.material_id,
        m.associated_order_no,
        m.planned_quantity,
        m.production_days,
        m.actual_input_quantity,
        m.actual_reporting_quantity,
        m.plan_type,
        m.line_uuid,
        m.line_code,
        m.material_name,
        m.factory_code,
        m.foreman_gh,
        m.line_leader_gh,
        m.line_leader_name,
        m.planner,
        m.planner_emp_no,
        m.sap_status,
        m.in_production_exception,
        m.is_completed,
        m.inbound_quantity,
        t.process_status,
        wipa.affects_upper_level_plan ,
        wipa.impact_type ,
        wipa.estimated_completion_date ,
        wipa.reason_category ,
        wipa.insufficient_order_reason ,
        wipa.light_color ,
        wipa.pc_estimated_completion_date ,
        wipa.adjust_plan_date
        from
        mes_process_work_order m inner join
        warning_in_production_abnormal wipa on wipa.order_no = m.order_no
        left join warning_todo_list t
        on wipa.id = t.biz_id and t.warning_type = 'IN_PRODUCTION_EXCEPTION'
        <where>
            <if test="assignee != null">
                and t.assignee = #{assignee}
            </if>
            <if test="processStatus != null">
                and t.process_status = #{processStatus}
            </if>
            <if test="plannerEmpNo != null">
                and m.planner_emp_no = #{plannerEmpNo}
            </if>
            <if test="planType != null">
                and m.plan_type = #{planType}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and m.order_no like concat('%',#{orderNo},'%')
            </if>
            <if test="workOrderNo != null and workOrderNo != ''">
                and m.work_order_no like concat('%',#{workOrderNo},'%')
            </if>
            <if test="materialId != null and materialId != ''">
                and m.material_id like concat('%',#{materialId},'%')
            </if>
            <if test="factoryCodes != null and factoryCodes != ''">
                and m.factory_code in
                <foreach item="item" index="index" collection="factoryCodes.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="factoryCodeList != null and factoryCodeList.size() > 0">
                and m.factory_code in
                <foreach item="item" index="index" collection="factoryCodeList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="schedulingStartDate != null and schedulingStartDate != ''">
                and m.scheduling_date >= #{schedulingStartDate}
            </if>
            <if test="schedulingEndDate != null and schedulingEndDate != ''">
                and m.scheduling_date &lt;= #{schedulingEndDate}
            </if>
            <if test="inProductionException != null">
                and m.in_production_exception = #{inProductionException}
            </if>
            <if test="isCompleted != null">
                and m.is_completed = #{isCompleted}
            </if>
            <if test="minProductionDays != null">
                and DATEDIFF(CURDATE(), m.material_off_shelf_date) >= #{minProductionDays}
            </if>
            <if test="maxProductionDays != null">
                and DATEDIFF(CURDATE(), m.material_off_shelf_date) &lt;= #{maxProductionDays}
            </if>
            <if test="lightColor != null">
                and wipa.light_color = #{lightColor}
            </if>
        </where>
        order by case
        when wipa.light_color = 'RED' then 1
        when wipa.light_color = 'YELLOW' then 2
        else 3
        end, m.material_off_shelf_date desc,m.production_line ASC,m.id desc
    </select>

</mapper>