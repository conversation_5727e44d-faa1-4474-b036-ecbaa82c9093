<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WpsOrderPlanWarningMapper">

  <!--
  &lt;&gt;
  -->

  <!-- 物料不齐套异常消警 -->
  <update id="eliminateAtpAlarms">
UPDATE wps_order_plan_warning w
SET handle_status = 3
WHERE w.warning_type = 'ATP_EXCEPTION'
  AND NOT EXISTS (
    SELECT 1
    FROM warning_material_atp_abnormal_shortage a
     inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'ATP_EXCEPTION'
    WHERE w.order_no = a.order_number
      AND t.process_status &lt;&gt; 'CLOSED'
)
  </update>
<!--  来料异常-->
  <update id="eliminateAtpAlarmsLlyc">
      UPDATE wps_order_plan_warning w
      SET handle_status = 3
      WHERE w.warning_type = 'MATERIAL_INSPECTION_ABNORMAL'
        AND NOT EXISTS (
          SELECT 1
          FROM warning_income_material_po_atp_abnormal a
           inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'MATERIAL_INSPECTION_ABNORMAL'
          WHERE w.order_no = a.order_no
            AND t.process_status &lt;&gt; 'CLOSED'
      )
  </update>
<!--  冻结解冻异常-->
  <update id="eliminateAtpAlarmsDjjd">
      UPDATE wps_order_plan_warning w
      SET handle_status = 3
      WHERE w.warning_type = 'FROZEN_UNFROZEN_WARNING'
        AND NOT EXISTS (
          SELECT 1
          FROM warning_frozen_unfrozen_abnormal a
           inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'FROZEN_UNFROZEN_WARNING'
          WHERE w.order_no = a.order_no
            AND t.process_status &lt;&gt; 'CLOSED'
      )
  </update>
    <!--  工艺路线异常-->
    <update id="eliminateAtpAlarmsGylx">
        UPDATE wps_order_plan_warning w
        SET handle_status = 3
        WHERE w.warning_type = 'PROCESS_ROUTE_ABNORMAL'
          AND NOT EXISTS (
            SELECT 1
            FROM warning_process_route_abnormal a
                     inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'PROCESS_ROUTE_ABNORMAL'
            WHERE w.order_no = a.order_no
              AND t.process_status &lt;&gt; 'CLOSED'
        )
    </update>
  <!-- 交期异常警告消除 -->
  <update id="eliminateDeliveryDateAbnormalAlarms">
      UPDATE wps_order_plan_warning w
      SET handle_status = 3
      WHERE w.warning_type = 'DELIVERY_DATE_ABNORMAL'
        AND NOT EXISTS (
          SELECT 1
          FROM warning_delivery_date_abnormal a
           inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'DELIVERY_DATE_ABNORMAL'
          WHERE w.order_no = a.order_number
            AND t.process_status &lt;&gt; 'CLOSED'
      )
  </update>
    <!-- 船期临近未订舱异常警告消除 -->
    <update id="eliminateShipBookingUrgentAlarms">
        UPDATE wps_order_plan_warning w
        SET handle_status = 3
        WHERE w.warning_type = 'SHIP_BOOKING_URGENT'
          AND NOT EXISTS (
            SELECT 1
            FROM warning_ship_booking_urgent_abnormal a
            INNER JOIN warning_todo_list t ON a.id = t.biz_id AND t.warning_type = 'SHIP_BOOKING_URGENT'
            WHERE w.order_no = a.order_no
              AND t.process_status &lt;&gt; 'CLOSED'
        )
    </update>
  <select id="countByOrderNos" resultType="com.lds.oneplanning.wps.vo.WpsOrderPlanWarningCountVO">
    select order_no, warning_type,handle_status,warning_level, count(*) as num
    from wps_order_plan_warning
    where handle_status in (1,2) and order_no in
    <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
      #{orderNo}
    </foreach>
    group by order_no, warning_type,warning_level
  </select>

</mapper>
