<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningShipBookingUrgentAbnormalMapper">
    <select id="queryUnHandleData" resultType="com.lds.oneplanning.wps.entity.WarningShipBookingUrgentAbnormal">
        select a.id, a.order_no
        from warning_ship_booking_urgent_abnormal a
                 inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'SHIP_BOOKING_URGENT'
        where t.process_status &lt;&gt; 'CLOSED'
    </select>
    <select id="queryPage" resultType="com.lds.oneplanning.wps.vo.ShipBookingUrgentVO">
        select a.*,t.process_status,t.assignee,t.process_id
        from warning_ship_booking_urgent_abnormal a
        inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'SHIP_BOOKING_URGENT'
        <where>
            <if test="userId != null">
                and t.assignee = #{userId}
            </if>
            <if test="req.factoryCodeList != null and req.factoryCodeList.size() > 0">
                and a.factory_code in
                <foreach item="item" collection="req.factoryCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.customer != null and req.customer != ''">
                and a.customer = #{req.customer}
            </if>
            <if test="req.sellOrderNo != null and req.sellOrderNo != ''">
                and a.sell_order_no = #{req.sellOrderNo}
            </if>
            <if test="req.orderNo != null and req.orderNo != ''">
                and a.order_no = #{req.orderNo}
            </if>
            <if test="req.bookingStatus != null and req.bookingStatus != ''">
                and a.booking_status = #{req.bookingStatus}
            </if>
            <if test="req.shipScheduleStartDate != null and req.shipScheduleStartDate != ''">
                and a.ship_schedule_date >= #{req.shipScheduleStartDate}
            </if>
            <if test="req.shipScheduleEndDate != null and req.shipScheduleEndDate != ''">
                and a.ship_schedule_date &lt;= #{req.shipScheduleEndDate}
            </if>
            <if test="req.commodityId != null">
                and a.commodity_id = #{req.commodityId}
            </if>
            <if test="req.lightColor != null">
                and a.light_color = #{req.lightColor}
            </if>
            <if test="req.processStatus != null">
                and t.process_status = #{req.processStatus}
            </if>
        </where>
        order by case
        when a.light_color = 'RED' then 1
        when a.light_color = 'YELLOW' then 2
        else 3
        end, a.ship_schedule_date asc,a.id desc
    </select>
</mapper>