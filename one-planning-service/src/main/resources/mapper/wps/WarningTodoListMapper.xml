<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningTodoListMapper">

  <select id="queryUnHandleData" resultType="com.lds.oneplanning.wps.po.WarningTodoPO">
    select assignee, warning_type, factory_code, count(*) as num
    from warning_todo_list
    where assignee &lt;&gt; ''
      and push_status = #{pushStatus}
      and process_status = 'UN_HANDLE'
    <if test="types != null and types.size() > 0">
      and warning_type in
      <foreach item="item" collection="types" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    group by assignee, warning_type, factory_code
  </select>
  <select id="queryUnClosedData" resultType="com.lds.oneplanning.wps.po.WarningTodoPO">
    select assignee, warning_type, factory_code, count(*) as num
    from warning_todo_list
    where assignee &lt;&gt; ''
    and process_status &lt;&gt; 'CLOSED'
    and warning_type = #{type}
    <if test="bizIds != null and bizIds.size() > 0">
      and biz_id in
      <foreach item="item" collection="bizIds" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    group by assignee, warning_type, factory_code
  </select>

  <update id="updatePushStatusByAssigneeWarningTypeFactory">
    UPDATE warning_todo_list
    SET push_status = push_status + 1
    WHERE assignee = #{assignee}
      AND warning_type = #{warningType}
      AND factory_code = #{factoryCode}
  </update>
</mapper>
