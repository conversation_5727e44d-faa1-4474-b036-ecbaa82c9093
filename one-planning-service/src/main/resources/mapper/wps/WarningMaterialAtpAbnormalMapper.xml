<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningMaterialAtpAbnormalMapper">

  <select id="queryUnHandleData" resultType="com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal">
    select a.shortage_id, a.order_number, a.id
    from warning_material_atp_abnormal a
           inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'ATP_EXCEPTION'
    where t.process_status &lt;&gt; 'CLOSED'
  </select>


  <select id="queryPage" resultType="com.lds.oneplanning.wps.vo.MaterialAtpAbnormalVO">
    select distinct a.*,t.process_status
    from warning_material_atp_abnormal a
    inner join warning_material_atp_abnormal_shortage s on a.id = s.abnormal_id
    inner join warning_todo_list t on s.id = t.biz_id and t.warning_type = 'ATP_EXCEPTION'
    <where>
      <if test="userId != null">
        and t.assignee = #{userId}
      </if>
      <if test="vo.factoryCode != null and vo.factoryCode != ''">
        and a.factory_code = #{vo.factoryCode}
      </if>
      <if test="vo.factoryCodeList != null and vo.factoryCodeList.size() > 0">
        and a.factory_code in
        <foreach item="item" collection="vo.factoryCodeList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="vo.id != null">
        and a.id = #{vo.id}
      </if>
      <if test="vo.plannedOnlineTime != null">
        and a.planned_online_time = #{vo.plannedOnlineTime}
      </if>
      <if test="vo.startDate != null">
        and a.planned_online_time &gt;= #{vo.startDate}
      </if>
      <if test="vo.endDate != null">
        and a.planned_online_time &lt;= #{vo.endDate}
      </if>
      <if test="vo.onlineQuantity != null">
        and a.online_quantity = #{vo.onlineQuantity}
      </if>
      <if test="vo.customer != null and vo.customer != ''">
        and a.customer = #{vo.customer}
      </if>
      <if test="vo.salesOrderNumber != null and vo.salesOrderNumber != ''">
        and a.sales_order_number = #{vo.salesOrderNumber}
      </if>
      <if test="vo.lineNumber != null and vo.lineNumber != ''">
        and a.line_number = #{vo.lineNumber}
      </if>
      <if test="vo.orderNumber != null and vo.orderNumber != ''">
        and a.order_number = #{vo.orderNumber}
      </if>
      <if test="vo.materialId != null and vo.materialId != ''">
        and a.material_id = #{vo.materialId}
      </if>
      <if test="vo.materialDescription != null and vo.materialDescription != ''">
        and a.material_description = #{vo.materialDescription}
      </if>
      <if test="vo.lightColor != null">
        and a.light_color = #{vo.lightColor}
      </if>
      <if test="vo.estimatedPlanDate != null">
        and a.estimated_plan_date = #{vo.estimatedPlanDate}
      </if>
      <if test="vo.adjustedOnlineTime != null">
        and a.adjusted_online_time = #{vo.adjustedOnlineTime}
      </if>
      <if test="vo.adjustedGapDays != null">
        and a.adjusted_gap_days = #{vo.adjustedGapDays}
      </if>
      <if test="vo.affectsUpperLevelPlan != null">
        and a.affects_upper_level_plan = #{vo.affectsUpperLevelPlan}
      </if>
      <if test="vo.impactType != null and vo.impactType != ''">
        and a.impact_type = #{vo.impactType}
      </if>
      <if test="vo.processStatus != null">
        and t.process_status = #{vo.processStatus}
      </if>
    </where>
    order by a.planned_online_time
  </select>


  <select id="queryMcPage" resultType="com.lds.oneplanning.wps.vo.MaterialAtpAbnormalMcVO">
    select a.id,
          a.light_color,
          a.abnormal_type,
          a.sales_order_number,
          line_number,
          customer,
          a.order_number,
          material_id,
          planned_online_time,
          online_quantity,
          s.shortage_id,
          s.shortage_description,
          s.required_time,
          s.shortage_quantity,
          gap_days,
          supplier,
          purchase_delivery_time,
          purchase_in_charge,
          responsible_person_reply,
          group_in_charge,
          a.impact_type,
          t.process_status
    from warning_material_atp_abnormal a
    inner join warning_material_atp_abnormal_shortage s on a.id = s.abnormal_id
    inner join warning_todo_list t on s.id = t.biz_id and t.warning_type = 'ATP_EXCEPTION'
    <where>
      <if test="userId != null">
        and t.assignee = #{userId}
      </if>
      <if test="vo.factoryCode != null and vo.factoryCode != ''">
        and a.factory_code = #{vo.factoryCode}
      </if>
      <if test="vo.factoryCodeList != null and vo.factoryCodeList.size() > 0">
        and a.factory_code in
        <foreach item="item" collection="vo.factoryCodeList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="vo.id != null">
        and a.id = #{vo.id}
      </if>
      <if test="vo.plannedOnlineTime != null">
        and a.planned_online_time = #{vo.plannedOnlineTime}
      </if>
      <if test="vo.startDate != null">
      and a.planned_online_time &gt;= #{vo.startDate}
      </if>
      <if test="vo.endDate != null">
      and a.planned_online_time &lt;= #{vo.endDate}
      </if>
      <if test="vo.onlineQuantity != null">
        and a.online_quantity = #{vo.onlineQuantity}
      </if>
      <if test="vo.customer != null and vo.customer != ''">
        and a.customer = #{vo.customer}
      </if>
      <if test="vo.salesOrderNumber != null and vo.salesOrderNumber != ''">
        and a.sales_order_number = #{vo.salesOrderNumber}
      </if>
      <if test="vo.lineNumber != null and vo.lineNumber != ''">
        and a.line_number = #{vo.lineNumber}
      </if>
      <if test="vo.orderNumber != null and vo.orderNumber != ''">
        and a.order_number like concat('%',#{vo.orderNumber},'%')
      </if>
      <if test="vo.materialId != null and vo.materialId != ''">
        and a.material_id = #{vo.materialId}
      </if>
      <if test="vo.materialDescription != null and vo.materialDescription != ''">
        and a.material_description = #{vo.materialDescription}
      </if>
      <if test="vo.lightColor != null">
        and a.light_color = #{vo.lightColor}
      </if>
      <if test="vo.estimatedPlanDate != null">
        and a.estimated_plan_date = #{vo.estimatedPlanDate}
      </if>
      <if test="vo.adjustedOnlineTime != null">
        and a.adjusted_online_time = #{vo.adjustedOnlineTime}
      </if>
      <if test="vo.adjustedGapDays != null">
        and a.adjusted_gap_days = #{vo.adjustedGapDays}
      </if>
      <if test="vo.affectsUpperLevelPlan != null">
        and a.affects_upper_level_plan = #{vo.affectsUpperLevelPlan}
      </if>
      <if test="vo.impactType != null and vo.impactType != ''">
        and a.impact_type = #{vo.impactType}
      </if>
    <if test="vo.processStatus != null">
      and t.process_status = #{vo.processStatus}
    </if>
  </where>
    order by a.planned_online_time
  </select>
</mapper>
