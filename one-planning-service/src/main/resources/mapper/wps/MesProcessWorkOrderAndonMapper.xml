<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.MesProcessWorkOrderAndonMapper">

    <select id="findNewestList" resultType="com.lds.oneplanning.wps.entity.MesProcessWorkOrderAndon">
        select *
        from (select mpwoa.*,
        (row_number() over(partition by mpwoa.work_order_no
        order by
        mpwoa.sort_no desc)) rowNumber
        from mes_process_work_order_andon mpwoa
        <where>
            <if test="workOrderNumberList != null and workOrderNumberList.size() > 0">
                <foreach collection="workOrderNumberList" item="workOrderNumber" separator=" or " open="(" close=")">
                    mpwoa.work_order_no = #{workOrderNumber}
                </foreach>
            </if>
        </where>
        ) a
        where rowNumber = 1
    </select>
</mapper>
