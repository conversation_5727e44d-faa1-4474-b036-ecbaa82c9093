<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningFrozenUnfrozenAbnormalMapper">

    <select id="queryUnHandleData" resultType="com.lds.oneplanning.wps.entity.WarningFrozenUnfrozenAbnormal">
        select a.commodity_id, a.order_no, a.id
        from warning_frozen_unfrozen_abnormal a
                 inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'FROZEN_UNFROZEN_WARNING'
        where t.process_status &lt;&gt; 'CLOSED'
    </select>
    <select id="selectPage" resultType="com.lds.oneplanning.wps.vo.FreezeUnFreezeAbnormalVO">
        select distinct
        fu.*,
        t.process_status, CONCAT(fu.sales_order_number, '-', fu.line_number) as xsddhxm

        from warning_frozen_unfrozen_abnormal fu
        inner join warning_todo_list t on fu.id = t.biz_id and t.warning_type = 'FROZEN_UNFROZEN_WARNING'

        <if test="params.processStatus != null and params.processStatus != ''">
            AND t.process_status = #{params.processStatus}
        </if>

        <if test="params.customer != null and params.customer != ''">
            AND fu.customer = #{params.customer}
        </if>
        <if test="params.factory != null and params.factory != ''">
            AND fu.factory IN
            <foreach item="item" collection="params.factory.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.orderNo != null and params.orderNo != ''">
            AND fu.order_no = #{params.orderNo}
        </if>
        <if test="params.npiUserid != null and params.npiUserid != ''">
            AND fu.npi_person_userid = #{params.npiUserid}
        </if>
        <if test="params.qplUserid != null and params.qplUserid != ''">
            AND fu.qpl_person_userid = #{params.qplUserid}
        </if>
        <if test="params.commodityId != null and params.commodityId != ''">
            AND fu.commodity_id = #{params.commodityId}
        </if>
        <if test="params.startDate != null and params.startDate != ''">
            AND fu.planned_online_time &gt;= #{params.startDate}
        </if>
        <if test="params.endDate != null and params.endDate != ''">
            AND fu.planned_online_time &lt;= #{params.endDate}
        </if>
    </select>
</mapper>
