<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.wps.mapper.WarningDeliveryDateAbnormalMapper">
  <select id="queryUnHandleData" resultType="com.lds.oneplanning.wps.entity.WarningDeliveryDateAbnormal">
    select a.id,a.order_number
    from warning_delivery_date_abnormal a
           inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'DELIVERY_DATE_ABNORMAL'
    where t.process_status &lt;&gt; 'CLOSED'
  </select>
  <select id="queryPage" resultType="com.lds.oneplanning.wps.vo.DeliveryDateAbnormalVO">
    select a.*,t.process_status,t.assignee,t.process_id
    from warning_delivery_date_abnormal a
    inner join warning_todo_list t on a.id = t.biz_id and t.warning_type = 'DELIVERY_DATE_ABNORMAL'
    <where>
      <if test="userId != null">
        and t.assignee = #{userId}
      </if>
      <if test="vo.factoryCode != null and vo.factoryCode != ''">
        and a.factory_code = #{vo.factoryCode}
      </if>
      <if test="vo.factoryCodeList != null and vo.factoryCodeList.size() > 0">
        and a.factory_code in
        <foreach item="item" collection="vo.factoryCodeList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="vo.id != null">
        and a.id = #{vo.id}
      </if>
      <!-- 自动生成功能开始 -->
      <if test="vo.plannedOnlineTime != null">
        and a.planned_online_time = #{vo.plannedOnlineTime}
      </if>
    <if test="vo.startDate != null">
      and a.planned_online_time &gt;= #{vo.startDate}
    </if>
    <if test="vo.endDate != null">
      and a.planned_online_time &lt;= #{vo.endDate}
    </if>
      <if test="vo.customer != null and vo.customer != ''">
        and a.customer = #{vo.customer}
      </if>
      <if test="vo.salesOrderNumber != null and vo.salesOrderNumber != ''">
        and a.sales_order_number = #{vo.salesOrderNumber}
      </if>
      <if test="vo.lineNumber != null">
        and a.line_number = #{vo.lineNumber}
      </if>
      <if test="vo.orderNumber != null and vo.orderNumber != ''">
        and a.order_number like concat('%',#{vo.orderNumber},'%')
      </if>
      <if test="vo.materialId != null and vo.materialId != ''">
        and a.material_id = #{vo.materialId}
      </if>
      <if test="vo.materialDescription != null and vo.materialDescription != ''">
        and a.material_description = #{vo.materialDescription}
      </if>
      <if test="vo.orderUnitQty != null">
        and a.order_unit_qty = #{vo.orderUnitQty}
      </if>
      <if test="vo.originalFinishTime != null">
        and a.original_finish_time = #{vo.originalFinishTime}
      </if>
      <if test="vo.estFinishTime != null">
        and a.est_finish_time = #{vo.estFinishTime}
      </if>
      <if test="vo.deliveryDateAbnormalImpactType != null and vo.deliveryDateAbnormalImpactType != ''">
        and a.delivery_date_abnormal_impact_type = #{vo.deliveryDateAbnormalImpactType}
      </if>
      <if test="vo.lightColor != null">
        and a.light_color = #{vo.lightColor}
      </if>
      <if test="vo.adjustedOnlineTime != null">
        and a.adjusted_online_time = #{vo.adjustedOnlineTime}
      </if>
      <if test="vo.processStatus != null">
        and t.process_status = #{vo.processStatus}
      </if>
      <!-- 自动生成功能结束 -->
    </where>
    order by a.planned_online_time
  </select>
</mapper>
