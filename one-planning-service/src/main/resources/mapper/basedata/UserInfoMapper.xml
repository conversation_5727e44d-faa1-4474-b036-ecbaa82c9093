<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lds.oneplanning.basedata.mapper.UserInfoMapper">
    <select id="list" resultType="com.lds.oneplanning.basedata.model.UserInfoDTO">
        select
        u.id,
        u.user_id,
        u.user_name,
        u.user_type,
        u.emp_no,
        u.create_by,
        u.create_time,
        u.update_by,
        u.update_time,
        Group_concat(r.factory_code) as factoryCodes
        from user_info u
        left join user_factory_rel r on r.user_id = u.user_id
        <where>
            <if test="keyword != null and keyword != ''">
                and (u.user_name like concat('%',#{keyword},'%') or u.emp_no like concat('%',#{keyword},'%'))
            </if>
            <if test="factoryCode != null and factoryCode != ''">
                and r.factory_code = #{factoryCode}
            </if>
            <if test="factoryCodeList != null and factoryCodeList.size() > 0">
                and r.factory_code in
                <foreach item="item" index="index" collection="factoryCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userType != null and userType != ''">
                and u.user_type = #{userType}
            </if>
            <if test="empNo != null and empNo != ''">
                and u.emp_no = #{empNo}
            </if>
            <if test="userId != null and userId != ''">
                and u.user_id = #{userId}
            </if>
        </where>
        group by u.user_id
        order by u.id desc
    </select>
    
    <select id="getOneByUserId" resultType="com.lds.oneplanning.basedata.model.UserInfoDTO">
        select *
        from user_info
        where user_id = #{userId} limit 1
    </select>
</mapper>
