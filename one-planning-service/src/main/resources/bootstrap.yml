server:
  port: 8092
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  messages:
    encoding: UTF-8
  tomcat:
    basedir: /data/apps/temp
    uri-encoding: UTF-8
    max-threads: 500
    accesslog:
      buffered: true # Buffer output such that it is only flushed periodically.
      directory: /data/logs/${spring.application.name}/access_logs #
      enabled: false #enable access log
      file-date-format: .yyyy-MM-dd # Date format to place in log file name.
      pattern: '%h %l %u %t "%r" %s %{iot_log_id}o %b %D' # Format pattern for access logs.
      prefix: ${spring.application.name}_access_log # Log file name prefix.
      rename-on-rotate: false # Defer inclusion of the date stamp in the file name until rotate time.
      request-attributes-enabled: false # Set request attributes for IP address, Hostname, protocol and port used for the request.
      rotate: true # Enable access log rotation.
      suffix: .log # Log file name suffix.
      max-days: 7

app:
  version: @project.version@
  build:
    time: @timestamp@

spring:
  banner:
    charset: UTF-8
  messages:
    basename: i18n/messages,i18n/ValidationMessages
    encoding: UTF-8
  main:
    allow-bean-definition-overriding: true
  application:
    name: one-planning-service
  profiles:
    active: dev
  cloud:
    consul:
      host: localhost
      port: 8500
      enabled: false                              #修改点
      failFast: true
      discovery:
        prefer-ip-address: true
        instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}
        health-check-interval: 15s
        enabled: ${spring.cloud.consul.enabled}  #修改点
        healthCheckUrl: http://${spring.cloud.client.ip-address}:${server.port}/actuator/health
    config:
      #uri: http://*************:${config.port:8888}
      fail-fast: true
      discovery:
        enabled: ${spring.cloud.consul.enabled}   #修改点 是否通过注册中心发现config-service
        service-id: iot-cloud-config
      name: ${spring.application.name}
      profile: ${spring.profiles.active}
      username: ${spring.application.name}
      password: 1@0A5M@@Kh
      label: ${spring.profiles.active}
      enabled: ${spring.cloud.consul.enabled}     #修改点
      allow-override: true
    kubernetes: #添加kubernetes 配置 以下全部为新增
      client:
        namespace: env-dev   #命名空间
      enabled: false
      discovery:
        enabled: ${spring.cloud.kubernetes.enabled}
        service-name: ${spring.application.name}
      config:
        enabled: ${spring.cloud.kubernetes.enabled}
        namespace: ${spring.cloud.kubernetes.client.namespace}
        sources:
          - name: application-config
          - name: ${spring.application.name}-${spring.cloud.config.profile}-config
        enable-api: true
      ribbon:
        mode: SERVICE
        enabled: ${spring.cloud.kubernetes.enabled}
      secrets:
        enabled: ${spring.cloud.kubernetes.config.enabled}
        namespace: ${spring.cloud.kubernetes.client.namespace}
        enable-api: true
        name: ${spring.application.name}-secret
      reload:
        enabled: ${spring.cloud.kubernetes.config.enabled}
        mode: polling
        period: 5000
        strategy: refresh
        monitoring-secrets: true
        monitoring-config-maps: true
  liquibase:
    change-log: classpath:liquibase/master.xml
    url: ${spring.datasource.dynamic.datasource.master.url}
    user: ${spring.datasource.dynamic.datasource.master.username}
    password: ${spring.datasource.dynamic.datasource.master.password}
    enabled: true

mybatis-plus:
  global-config:
    db-config:
      id-type: auto
      field-strategy: not_empty
      column-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0
      db-type: mysql
    refresh: false
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  mapper-locations: classpath*:/mapper/**/*.xml

#consul配置
lds:
  consul:
    remotehost: consul-server.consul.svc.cluster.local
    remoteport: 80
    services:
  security:
    auth:
      token-ttl: "2h"
      principal-ttl: "never"
    context:
      enabled: true
#      silent-intercept: false
      fetch-mode: "redis"
      path-patterns:
        - "/v1/departments/tree"
management:
  security:
    enabled: false
  health:
    consul:
      enabled: false


core:
  security:
    baseKey: 0fe3027dbe354abee58b37350d41ce4b
  aes:
    key: CloudIsANiceFramePresentedByLucky

logging:
  config: classpath:logback-spring.xml


lito:
  web:
    jackson-config:
      enable: false

init:
  record: ${spring.application.name}-record


