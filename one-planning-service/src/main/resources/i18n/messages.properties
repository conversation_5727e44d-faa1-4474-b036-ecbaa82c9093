068003=The current serial number exceeds the maximum value
068004= illegal user name
068005= role in used
100006=system param is null
021003=userName is null
021020=pwd is null
021021=param is error
021049=verifyCode is null
021045=verifyCode expired
021034=old password wrong please try again
021029=mail too frequent
021010=register verifyCode error
021048=user name or password is incorrect
021051=user name or password form is incorrect
021031=lock ten minutes
021032=lock one hour
021033=lock Twenty-four hours
021052=account has not been activated
021050=account has been frozen
210480=user name or password is incorrect-
021901=user is not active
600813=user name or password is incorrect

670006=department code is repeat
670007=department name is repeat
670008=department name code is repeat
670010=department is not exit
600057=no login %s
600058=staff limit
100000=role is null
021009=reset pwd verify code error
021046=send email retry
021019=user not register
068006=location unauthorized
600400=user name is exist

700043=please choose first
700044=device threshold illegal
700045=telPhone is exit

650017=param is error
650040=Duplicate name
650067=Data does not exist
650068=Parameter error
610502=param is null
610507=repeat version

670011=locaiton is not exit
670012=edge nodes are inconsistent and cannot be attached
710514=admin user is null
710515=old password is error

690000=The classification code is not repeatable
690001=The application code is not repeatable
690002=The category cannot be deleted because it has been associated with applications
690003=The application cannot be deleted because it has been associated with an organization
690004=The application data does not exist
670009=department has children

800026=department filter code error
801001=code is exist
802001=double check fail
802002=Configure organization level application classification organization ID cannot be empty
802003=Prohibit modifying or deleting base application categories at the organizational level
802004=Prohibit modifying or deleting base application at the organizational level
802005=Prohibit modifying the classification of base applications at the organizational level
802006=The platform level configuration of this application has no address, so there is no need to configure the current application address separately
900036=Both ID number and student registration number are empty
900037=Account personnel mapping relationship is generated
900038=There are multiple mapping relationships between account personnel