068003=The current serial number exceeds the maximum value
068004= illegal user name
068005= role in used
100006=system param is null
210480=user name or password is incorrect-
021901=user is not active
021034=old password wrong please try again
021029=mail too frequent
600813=user name or password is incorrect
670006=department code is repeat
670007=department name is repeat
670008=department name code is repeat
670010=department is not exit
600057=no login %s
600058=staff limit
100000=role is null
21045=verify code expired
21009=reset pwd verify code error
21046=send email retry
21019=user not register
021003=userName is null
021020=pwd is null
021021=param is error
021049=verifyCode is null
021045=verifyCode expired
021010=register verifyCode error
021048=user name or password is incorrect
021051=user name or password form is incorrect
021031=lock ten minutes
021032=lock one hour
021033=lock Twenty-four hours
021052=account has not been activated
021050=account has been frozen
021009=reset pwd verify code error
021046=send email retry
021019=user not register
068006=location unauthorized
600400=user name is exist

660002=tenant.code.exist
660008=tenantId.is.null
660025=param.error
660026=tenant.name.exist
660032=tenant.info.is.not.complete
660033=Account locked
660034=param.error

700043=please choose first
700044=device threshold illegal
700045=telPhone is exit

650017=param is error
650040=Duplicate name
650067=Data does not exist
650068=Parameter error
610502=param is null
610507=repeat version
610508=The role is not created by the current user and cannot be edited
610509=The role is not created by the current user and cannot be deleted
610510=The clockin template name is exit
660001=tenant.not.exist
660003=user.org.exist
660004=user.org.not.exist
660005=user.org.default.used.not.delete
660006=user.org.delete.error
660007=addresId.is.null
660009=country.is.null
670011=locaiton is not exit
670012=edge nodes are inconsistent and cannot be attached
810001=organization name cannot be duplicate
810002=mobile phone number has been occupied by other personnel
710514=admin user is null
710515=old password is error
410001=The flow role already exists and cannot be created
410002=The flow role not found
410003=This flow role is associated with a person and cannot be deleted


900000=The mobile phone number already exists in the organization
900001=The mobile phone number cannot be changed in the editing state
900002=Please enter 11 digits
900003=Superior application or menu is not empty
900004=PermissionCode Unique under application
900005=In edit mode, it cannot be modified
900006=The third level menu cannot be selected
900007=Publish app is null
900008=permissionType can not be sys
900009=Please delete the associated function before adding a submenu
900010=Please delete the submenu before adding associated function
900011=Please do not add new functions under the application
900012=There is a function menu under the menu to be moved, which cannot be moved temporarily. To move, please remove the function menu first
900013=After moving to the target menu, it will exceed the third level menu and cannot be moved temporarily
900014=ButtonCode Unique under application
900015=account exits
900016=location permissions is empty
900017=no permission for phoneNumber
900018=account not exits
900019=account and tel all not config
900020=Non compliant with rules
900021=The new account has been occupied by other users on the platform
900022=PermissionName Unique under application
900036=Both ID number and student registration number are empty
900037=Account personnel mapping relationship is generated
900038=There are multiple mapping relationships between account personnel

690000=The classification code is not repeatable
690001=The application code is not repeatable
690002=The category cannot be deleted because it has been associated with applications
690003=The application cannot be deleted because it has been associated with an organization
690004=The application data does not exist
670009=department has children

800026=department filter code error
801001=code is exist
802001=double check fail
802002=Configure organization level application classification organization ID cannot be empty
802003=Prohibit modifying or deleting base application categories at the organizational level