<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <!--指定输出的文件名称-->
    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>

    <!--指定生成文件路径-->
    <property name="PATH" value="/data/logs/${APP_NAME}"/>

    <contextName>${APP_NAME}</contextName>

    <!-- 控制台彩色打印 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- 控制台输出格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <!-- 写入文件格式 -->
    <property name="FILE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <!--console  控制台输出-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!--默认 日志文件生成-->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PATH}/info.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天一归档 -->
            <fileNamePattern>${PATH}/info/info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!--表示只保留最近30天的日志，以防止日志填满整个磁盘空间-->
            <maxHistory>30</maxHistory>
            <!--用来指定日志文件的上限大小，例如设置为1GB的话，那么到了这个值，就会删除旧的日志。-->
            <!--<totalSizeCap>1GB</totalSizeCap>-->
        </rollingPolicy>
    </appender>

    <!--error 日志级别生成-->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PATH}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- <FileNamePattern>${PATH}/info/${YEAR}/${MONTH}/error.%d{yyyy-MM-dd}.log</FileNamePattern>-->
            <FileNamePattern>${PATH}/error/error.%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--表示只保留最近30天的日志，以防止日志填满整个磁盘空间-->
            <maxHistory>30</maxHistory>
            <!--用来指定日志文件的上限大小，例如设置为1GB的话，那么到了这个值，就会删除旧的日志。-->
            <!--<totalSizeCap>1GB</totalSizeCap>-->
        </rollingPolicy>
        <layout>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </layout>
    </appender>

    <!-- DEBUG级别日志 appender -->
    <appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PATH}/debug.log</file>
        <!-- 过滤器，只记录DEBUG级别的日志 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${PATH}/debug/debug.%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger - %msg%n</pattern>
        </encoder>
    </appender>

    <springProfile name="dev">
        <logger name="com.iot" level="DEBUG">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </logger>
    </springProfile>

    <springProfile name="test">
        <logger name="com.iot" level="DEBUG">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="DEBUG_FILE"/>
        </logger>
    </springProfile>

    <springProfile name="prod">
        <logger name="com.iot" level="ERROR">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="DEBUG_FILE"/>
        </logger>
    </springProfile>

    <springProfile name="debug">
        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>

</configuration>