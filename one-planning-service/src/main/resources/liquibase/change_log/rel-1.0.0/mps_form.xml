<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.mps_form_info.20250207" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mps_form_info"/>
            </not>
        </preConditions>
        <createTable tableName="mps_form_info">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="biz_id" type="varchar(128)"  remarks="业务id">
                <constraints nullable="false"/>
            </column>
            <column name="old_reply_completion_date" type="datetime(0)"  remarks="原回复完工日期" />
            <column name="new_reply_completion_date" type="datetime(0)"  remarks="新回复完工日期" />
            <column name="remark_one" type="varchar(256)"  remarks="备注1"/>
            <column name="remark_two" type="varchar(256)"  remarks="备注2"/>
            <column name="packaging_or_final_assembly" type="varchar(50)"  remarks="包装/总装"/>
            <column name="remark_hazardous_info" type="varchar(256)"  remarks="风险物料信息备注"/>
            <column name="guest_seq" type="varchar(50)"  remarks="客人顺序"/>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="mps_form_info" unique="false" indexName="idx_mps_form_info_biz_id">
            <column name="biz_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="createTable.mps_week_plan.20250207" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mps_week_plan"/>
            </not>
        </preConditions>
        <createTable tableName="mps_week_plan">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="biz_id" type="varchar(128)"  remarks="业务id">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="datetime(0)"  remarks="开始日期" />
            <column name="end_date" type="datetime(0)"  remarks="截止日期" />
            <column name="day_num" type="int(2)"  remarks="天数"/>
            <column name="nature_week" type="int(2)"  remarks="自然周"/>
            <column name="week_type" type="int(1)"  remarks="上/下半周"/>
            <column name="pre_plan_quantity" type="int(11)"  remarks="预产数量"/>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="mps_week_plan" unique="false" indexName="idx_mps_week_plan_biz_id">
            <column name="biz_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="alterTable.mps_week_plan.20250208" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <columnExists tableName="mps_week_plan" columnName="current_year"/>
            </not>
        </preConditions>
        <addColumn tableName="mps_week_plan">
            <column name="current_year" type="int(4)" remarks="当前年份"/>
        </addColumn>
    </changeSet>


    <changeSet id="alterTable.mps_form_info.20250213" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="mps_form_info"/>
        </preConditions>
        <addColumn tableName="mps_form_info">
            <column name="line_category_code" type="varchar(32)" remarks="产线列表编码"/>
            <column name="report_qty" type="int" remarks="报工数量"/>
        </addColumn>
    </changeSet>

    <changeSet id="alterTable.mps_form_info.20250219" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="mps_form_info"/>
        </preConditions>
        <addColumn tableName="mps_form_info">
            <column name="order_type" type="varchar(64)" remarks="订单类型"/>
            <column name="order_pcs_qty" type="int" remarks="订单只数"/>
        </addColumn>
    </changeSet>


    <changeSet id="alterTable.mps_week_plan.20250213" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="mps_week_plan"/>
        </preConditions>
        <addColumn tableName="mps_week_plan">
            <column name="line_category_code" type="varchar(32)" remarks="产线列表编码"/>
            <column name="frozen_qty" type="int" remarks="冻结数"/>
        </addColumn>
    </changeSet>



</databaseChangeLog>