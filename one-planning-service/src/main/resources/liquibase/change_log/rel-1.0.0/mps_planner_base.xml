<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.mps_planner_base.20250211" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mps_planner_base"/>
            </not>
        </preConditions>
        <createTable tableName="mps_planner_base">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="bigint(22)"  remarks="用户id">
                <constraints nullable="false"/>
            </column>
            <column name="user_name" type="varchar(32)"  remarks="姓名"/>
            <column name="emp_no" type="varchar(32)"  remarks="工号"/>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码"/>
            <column name="sbu" type="varchar(128)"  remarks="sbu" />
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="mps_planner_base" unique="false" indexName="idx_user_name">
            <column name="user_name"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>