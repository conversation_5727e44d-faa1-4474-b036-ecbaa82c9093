<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.mps_line_category_capacity.20250213" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mps_line_category_capacity"/>
            </not>
        </preConditions>
        <createTable tableName="mps_line_category_capacity">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="line_category_code" type="varchar(32)"  remarks="产线类别编码">
                <constraints nullable="false"/>
            </column>
            <column name="schedule_date" type="date(0)"  remarks="日期"/>
            <column name="product_hours" type="tinyint(3)"  remarks="生产小时数"/>
            <column name="load_percent" type="float"  remarks="负载百分比"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="mps_line_category_capacity" unique="false" indexName="idx_category_code">
            <column name="line_category_code"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>