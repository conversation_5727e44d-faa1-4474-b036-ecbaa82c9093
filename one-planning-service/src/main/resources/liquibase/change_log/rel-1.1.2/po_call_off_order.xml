<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create.po_demand_order" author="daishaokun1">
        <sql>
          CREATE TABLE if not exists po_demand_order
          (
            id              BIGINT PRIMARY KEY COMMENT '主键',
            version         INT COMMENT '版本号',
            latest          BOOLEAN default true COMMENT '是否最新版本',
            org             VARCHAR(16) COMMENT '采购组织',
            transport_type  VARCHAR(12) COMMENT '运输方式（TransportType枚举）',
            factory_code    VARCHAR(16) COMMENT '工厂编号',
            order_no        VARCHAR(32) COMMENT '订单号',
            demand_date     DATE COMMENT '需求日期',
            material_group  VARCHAR(32) COMMENT '物料组',
            material_id     VARCHAR(32) COMMENT '物料ID',
            material_name   VARCHAR(255) COMMENT '物料名称',
            supplier        VARCHAR(32) COMMENT '供应商编码',
            supplier_name   VARCHAR(255) COMMENT '供应商名称',
            po_no           VARCHAR(32) COMMENT '采购订单号',
            po_line         VARCHAR(32) COMMENT '采购订单行项目',
            sales_no        VARCHAR(32) COMMENT '销售订单号',
            sales_line      VARCHAR(32) COMMENT '销售订单行项目',
            po_group        VARCHAR(32) COMMENT '采购组',
            demand_quantity INT COMMENT '需求数量',
            transport_time  INT COMMENT '运输时间（PO2-天，JIT-小时）',
            lock_date       DATE COMMENT '锁定日期',
            created_at                         TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at                         TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
          ) ENGINE = InnoDB
            DEFAULT CHARSET = utf8mb4 COMMENT ='需求单表';

          CREATE TABLE if not exists po_call_off_order
          (
            id                BIGINT PRIMARY KEY COMMENT '主键',
            version           INT COMMENT '版本号',
            latest            BOOLEAN default true COMMENT '是否最新版本',
            org               VARCHAR(16) COMMENT '采购组织',
            transport_type    VARCHAR(12) COMMENT '运输方式（TransportType枚举）',
            factory_code      VARCHAR(16) COMMENT '工厂编号',
            order_no          VARCHAR(32) COMMENT '订单号',
            demand_date       DATE COMMENT '需求日期',
            material_group    VARCHAR(32) COMMENT '物料组',
            material_id       VARCHAR(32) COMMENT '物料ID',
            material_name     VARCHAR(255) COMMENT '物料名称',
            supplier          VARCHAR(32) COMMENT '供应商编码',
            supplier_name     VARCHAR(255) COMMENT '供应商名称',
            po_no             VARCHAR(32) COMMENT '采购订单号',
            po_line           VARCHAR(32) COMMENT '采购订单行项目',
            sales_no          VARCHAR(32) COMMENT '销售订单号',
            sales_line        VARCHAR(32) COMMENT '销售订单行项目',
            po_group          VARCHAR(32) COMMENT '采购组',
            demand_quantity   INT COMMENT '需求数量',
            transport_time    INT COMMENT '运输时间（PO2-天，JIT-小时）',
            lock_date         DATE COMMENT '锁定日期',
            call_off_date     DATE COMMENT '叫料日期',
            call_off_quantity INT COMMENT '叫料数量',
            received_quantity INT COMMENT '入库数量 - 实时根据接口查询',
            created_at                         TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at                         TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
          ) ENGINE = InnoDB
            DEFAULT CHARSET = utf8mb4 COMMENT ='叫料订单表';
        </sql>
    </changeSet>

    <changeSet id="create.po_demand_order_index" author="daishaokun1">
      <sql>
        alter table po_call_off_order
        modify id bigint auto_increment comment '主键';

        alter table po_call_off_order
        auto_increment = 1;

        alter table po_call_off_order
        modify id bigint auto_increment comment '主键';

        alter table po_call_off_order
        auto_increment = 1;

      </sql>

    </changeSet>
  <changeSet id="alter.po_demand_order_index.auto_increment" author="daishaokun1">
    <sql>
      alter table po_demand_order
        modify id bigint auto_increment comment '主键';

      alter table po_demand_order
        auto_increment = 1;
    </sql>
  </changeSet>

  <changeSet id="add.po_demand_order.use_type" author="daishaokun1">
    <sql>
      alter table po_demand_order
        add use_type int null comment 'atp USE_TYPE_NAME 使用类型';
      alter table po_call_off_order
        add use_type int null comment 'atp USE_TYPE_NAME 使用类型';
    </sql>
  </changeSet>

  <changeSet id="change.po_demand_order.use_type" author="daishaokun1">
    <sql>
      alter table po_demand_order
        modify use_type varchar(32) null comment 'atp USE_TYPE_NAME 使用类型';

      alter table po_call_off_order
        modify use_type varchar(32) null comment 'atp USE_TYPE_NAME 使用类型';
    </sql>
  </changeSet>

  <changeSet id="add.po_demand_order.eta" author="daishaokun1">
    <sql>
      alter table po_call_off_order
        add eta date null comment '预计到货日期';
    </sql>
  </changeSet>

  <changeSet id="index.po_demand_order.20250611" author="daishaokun1">
    <sql>
      CREATE INDEX idx_factory_created_version ON po_call_off_order (factory_code, created_at DESC, version DESC);
    </sql>
  </changeSet>

</databaseChangeLog>
