<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create_skd_order_material_table" author="gl">
        <sql>
            create table if not exists `skd_order_material` (
              `id` bigint NOT NULL AUTO_INCREMENT,
              `top_no` varchar(32)  NOT NULL COMMENT '顶层单号',
              `cover_so_no` varchar(32)  DEFAULT NULL COMMENT '销售单号',
              `cover_so_line` varchar(32)  DEFAULT NULL COMMENT '销售单项次',
              `work_no` varchar(32)  DEFAULT NULL COMMENT '计划单号',
              `work_line` varchar(32)  DEFAULT NULL COMMENT '计划单项次',
              `material_item_no` varchar(32)  DEFAULT NULL COMMENT '子件物料编码',
              `material_item_name` varchar(32)  DEFAULT NULL COMMENT '子件物料名称',
              `delivery_plant` varchar(32)  DEFAULT NULL COMMENT '子件工厂',
              `mstae` varchar(10)  DEFAULT NULL COMMENT '跨工厂状态',
              `item_group` varchar(32)  DEFAULT NULL COMMENT '物料组',
              `def_place` varchar(32)  DEFAULT NULL COMMENT '库位',
              `def_place_name` varchar(32)  DEFAULT NULL COMMENT '库位描述',
              `place` varchar(32)  DEFAULT NULL COMMENT 'po库位',
              `place_name` varchar(32)  DEFAULT NULL COMMENT 'po库位描述',
              `item_pack_place` varchar(32)  DEFAULT NULL COMMENT '包材物料库位',
              `po_type` varchar(10)  DEFAULT NULL COMMENT '物料采购类型',
              `material_need_date` datetime DEFAULT NULL COMMENT '子件需求日期',
              `need_qty` double DEFAULT NULL COMMENT '子件物料需求数量',
              `inventory_qty` double DEFAULT NULL COMMENT '泰国在库分配满足数量',
              `remaining_qty` double DEFAULT NULL COMMENT '剩余需求量',
              `transit_qty` double DEFAULT NULL COMMENT '在途数量',
              `un_transit_qty` double DEFAULT NULL COMMENT 'PO剩余待中国出货',
              `status` char(2)  DEFAULT '0' COMMENT '状态',
              `create_by` bigint DEFAULT NULL COMMENT '创建者id',
              `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `update_by` bigint DEFAULT NULL COMMENT '更新人id',
              `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`id`),
              KEY `skd_order_material_cover_so_no_IDX` (`cover_so_no`,`cover_so_line`) USING BTREE,
              KEY `skd_order_material_work_no_IDX` (`work_no`,`work_line`) USING BTREE,
              KEY `skd_order_material_material_item_no_IDX` (`material_item_no`) USING BTREE,
              KEY `skd_order_material_def_place_IDX` (`def_place`) USING BTREE
            ) COMMENT '订单物料表';
        </sql>
    </changeSet>

    <changeSet id="modifyColumn.material_item_name.skd_order_material.1" author="gl" failOnError="false">
      <sql>
        ALTER TABLE skd_order_material MODIFY material_item_name VARCHAR(255) NULL;
      </sql>
      <createIndex tableName="skd_order_material" unique="false" indexName="skd_order_material_top_no_IDX">
        <column name="top_no"/>
      </createIndex>
    </changeSet>

    <changeSet id="modify.skd_order_material.2" author="gl" failOnError="false">
      <sql>
        ALTER TABLE skd_order_material ADD order_product_id bigint NULL;
      </sql>
      <createIndex tableName="skd_order_material" unique="false" indexName="skd_order_material_order_product_id_IDX">
        <column name="order_product_id"/>
      </createIndex>
    </changeSet>

  <changeSet id="del_skd_order_material_index.2025.05.28" author="gl">
    <sql>
      DROP INDEX skd_order_material_def_place_IDX ON skd_order_material;
    </sql>
  </changeSet>

  <changeSet id="modify.skd_order_material.2025.06.04" author="gl" failOnError="false">
    <sql>
      ALTER TABLE skd_order_material ADD cover_so_id VARCHAR(64) NULL;
    </sql>
    <createIndex tableName="skd_order_material" unique="false" indexName="skd_order_material_cover_so_id_IDX">
      <column name="cover_so_id"/>
    </createIndex>
  </changeSet>

  <changeSet id="modify.skd_order_material.2025.06.05" author="gl" failOnError="false">
    <sql>
      ALTER TABLE skd_order_material ADD material_lead_time_days int COMMENT '子件前置期';
    </sql>
  </changeSet>

</databaseChangeLog>
