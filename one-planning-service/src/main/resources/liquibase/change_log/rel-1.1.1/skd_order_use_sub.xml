<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.skd_order_use_sub" author="yecz" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="skd_order_use_sub"/>
            </not>
        </preConditions>
        <sql>
            create table ldx_one_planning.skd_order_use_sub
            (
                id                bigint auto_increment
                    primary key,
                top_no            varchar(32)                        not null comment '顶层单号',
                use_no            varchar(32)                        null comment '使用单号(销售单号）',
                to_thai_way        varchar(32)                        null comment '交泰方式',
                thai_send_due_date  datetime                            null comment '泰国需求发货日期（基于最佳运输方式）',
                pull_days         int                                null comment '需要提拉天数',
                thai_transport_mode varchar(32)                        null comment '泰国需求发运方式',
                chinese_send_date datetime                            null comment '预计中国发货时间',
                thai_arrive_date  datetime                            null comment'预计到泰入库日期',
                is_ready           tinyint(1) default 0                null comment'是否能满足泰国上线需求（连空运也满足不了，要调整计划）',
                arrive_date_by_sea       datetime                            null comment'预计海运最快泰国入库日期',
                thai_plan_due_date datetime                            null comment '泰国需求入库日期',
                gap               int                                null comment '距离需求入库时间',
                latest_suggest_transport_mode varchar(32)                        null comment'最新建议运输方式',
                is_can_send        varchar(32)                null comment'是否可发状态',
                create_by         bigint                             null comment '创建者id',
                create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
                update_by         bigint                             null comment '更新人id',
                update_time       datetime default CURRENT_TIMESTAMP null comment '更新时间'
            )
            comment '使用订单子表' charset = utf8mb4;
        </sql>

    </changeSet>

    <changeSet id="add.column.outbound_delivery_no.and.outbound_delivery_no" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub
                add `outbound_delivery_no` varchar(32) null comment '外向单号' after use_no;

            alter table skd_order_use_sub
                add outbound_delivery_line varchar(32) null comment '外向单行项目' after `outbound_delivery_no`;
        </sql>
    </changeSet>
    <changeSet id="add.column.supply.and.po_group_r" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub
                add `supply` varchar(32) null comment '供应商' ;

            alter table skd_order_use_sub
                add po_group_r varchar(32) null comment '使用采购组描述' ;
        </sql>
    </changeSet>

    <changeSet id="add.column.supply.and.cover_so" author="gl" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `cover_so_id` varchar(32) null comment '销售单Id' ;

            alter table skd_order_use_sub add `cover_so_no` varchar(32) null comment '销售单号' ;

            alter table skd_order_use_sub add cover_so_line varchar(32) null comment '销售单项次' ;

            alter table skd_order_use_sub add po_group varchar(32) null comment '使用采购组' ;

            alter table skd_order_use_sub modify po_group_r varchar(255) null comment '使用采购组描述' ;
        </sql>

        <createIndex tableName="skd_order_use_sub" unique="false" indexName="skd_order_use_sub_cover_so_id_IDX">
            <column name="cover_so_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="add.column.supply.and.use_type" author="gl" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `use_type` varchar(32) null comment '使用类型' ;
        </sql>
    </changeSet>

    <changeSet id="add.column.supply.and.use_note" author="gl" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `use_note` varchar(255) null comment '使用说明' ;
        </sql>
    </changeSet>
    <changeSet id="add.column.latest_due_date" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `latest_due_date` datetime null comment '最新交期' ;
        </sql>
    </changeSet>

    <changeSet id="add.column.supply.and.un_finished_num" author="gl" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `purchase_qty` double null comment '采购订单数量' ;
            alter table skd_order_use_sub add `on_order_qty` double null comment '采购未完数量' ;
            alter table skd_order_use_sub add `use_qty` double null comment '分配数量' ;
        </sql>
    </changeSet>

    <changeSet id="modify.skd_order_use_sub.20250526.1" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub MODIFY `supply` varchar(255) null comment '供应商' ;
        </sql>
    </changeSet>

    <changeSet id="modify.skd_order_use_sub.20250526.2" author="gl">
        <sql>
            create  index skd_order_use_sub_top_no_index on skd_order_use_sub (top_no);
        </sql>
    </changeSet>

    <changeSet id="add.column.material_need_date" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `material_need_date` datetime DEFAULT NULL COMMENT '子件需求日期' ;
        </sql>
    </changeSet>
    <changeSet id="add.column.orig_transport_mode" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `orig_transport_mode` varchar(32) DEFAULT NULL COMMENT '原运输方式' ;
        </sql>
    </changeSet>
    <changeSet id="add.column.is_expedited_shipping" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `is_expedited_shipping` tinyint(1) DEFAULT 0 COMMENT '是否需要加急发货' ;
        </sql>
    </changeSet>
    <changeSet id="add.column.expedited_shipping_reason" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `expedited_shipping_reason` varchar(255) DEFAULT NULL COMMENT '加急发货原因' ;
        </sql>
    </changeSet>

    <changeSet id="add.column.original_latest_due_date" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub add `original_latest_due_date` datetime null comment '原始最新交期' ;
        </sql>
    </changeSet>

    <changeSet id="modify.skd_order_use_sub.2025.06.05" author="gl" failOnError="false">
        <sql>
            ALTER TABLE skd_order_use_sub ADD order_use_id varchar(255) NULL COMMENT '使用单Id';
        </sql>
    </changeSet>

</databaseChangeLog>
