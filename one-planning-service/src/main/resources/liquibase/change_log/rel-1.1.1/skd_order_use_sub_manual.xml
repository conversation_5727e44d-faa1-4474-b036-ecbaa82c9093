<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.skd_order_use_sub_manual" author="yecz" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="skd_order_use_sub_manual"/>
            </not>
        </preConditions>
        <sql>
            create table ldx_one_planning.skd_order_use_sub_manual
            (
                id                bigint auto_increment
                    primary key,
                top_no            varchar(32)                        not null comment '顶层单号',
                use_no            varchar(32)                        null comment '使用单号(销售单号）',
                distribution_qty  double                                null comment '分配数量',
                send_time         datetime                            null comment'发货时间',
                transport_mode    varchar(32)                        null comment'运输方式'
            )
                comment '使用订单子表人工填写数据' charset = utf8mb4;
        </sql>

    </changeSet>

    <changeSet id="modify.skd_order_use_sub_manual.20250526.1" author="gl">
        <sql>
            create index skd_order_use_sub_manual_use_no_index on skd_order_use_sub_manual (use_no);
        </sql>
    </changeSet>

    <changeSet id="add.column.use_type" author="yecz" failOnError="false">
        <sql>
            alter table skd_order_use_sub_manual add `use_type` varchar(32) null comment '使用类型' ;
        </sql>
    </changeSet>

</databaseChangeLog>
