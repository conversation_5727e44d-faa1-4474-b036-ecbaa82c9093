<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_schedule_plan_log.20250604" author="chenyangbin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_schedule_plan_log"/>
            </not>
        </preConditions>
        <createTable tableName="wps_schedule_plan_log">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码" />
            <column name="order_type" type="varchar(64)" remarks="订单类型"/>
            <column name="sell_order_no" type="varchar(64)"  remarks="销售订单号"/>
            <column name="order_no" type="varchar(64)"  remarks="订单号"/>
            <column name="row_item" type="varchar(64)"  remarks="行项目" />
            <column name="product_id" type="varchar(128)"  remarks="产品id"/>
            <column name="commodity_id" type="varchar(128)"  remarks="商品id"/>
            <column name="error_code" type="varchar(30)"  remarks="错误码"/>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
            <column name="remark" type="varchar(150)"  remarks="备注"/>
        </createTable>
        <createIndex tableName="wps_schedule_plan_log"  indexName="idx_order_no">
            <column name="order_no"/>
        </createIndex>
        <createIndex tableName="wps_schedule_plan_log"  indexName="idx_factory_code">
            <column name="factory_code"/>
        </createIndex>
        <createIndex tableName="wps_schedule_plan_log" indexName="idx_create_time">
            <column name="create_time"/>
        </createIndex>
    </changeSet>
    <changeSet id="update_column_remark" author="chenyangbin">
        <modifyDataType tableName="wps_schedule_plan_log" columnName="remark" newDataType="varchar(250)"></modifyDataType>
        <setTableRemarks tableName="wps_schedule_plan_log" remarks="自动排产异常日志"></setTableRemarks>
    </changeSet>

</databaseChangeLog>