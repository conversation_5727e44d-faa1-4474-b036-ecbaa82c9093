<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.skd_purchase_logistics" author="yecz" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="skd_purchase_logistics"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE `skd_purchase_logistics` (
                                                      `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY,
                                                      `top_no` varchar(32)  DEFAULT NULL COMMENT '顶层单号',
                                                      `purchase_no` varchar(32)  DEFAULT NULL COMMENT '采购单号',
                                                      `outbound_delivery_no` varchar(32)  DEFAULT NULL COMMENT '外向单',
                                                      `outbound_delivery_line` varchar(32)  DEFAULT NULL COMMENT '外向单项次',
                                                      `transit_qty` double DEFAULT NULL COMMENT '数量',
                                                      `logistics_no` varchar(32)  DEFAULT NULL COMMENT '关联物流单号',
                                                      `transport_mode` varchar(32)  DEFAULT NULL COMMENT'运输方式',
                                                      `send_date` datetime default null COMMENT '发出日期',
                                                      `status` varchar(32)  DEFAULT '0' COMMENT '目前状态',
                                                      `eta` datetime default null COMMENT'系统ETA',
                                                      `thai_arrive_date` datetime default null COMMENT '到泰日期',
                                                      `is_abnormal` tinyint(1) DEFAULT 0 comment '是否异常',
                                                      `gap_days` int DEFAULT NULL COMMENT '距离需求入库时间',
                                                      `is_need_urgent` tinyint(1) DEFAULT 0 COMMENT '是否需要加急清关'
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '采购物流表';
        create index outbound_delivery_no_outbound_delivery_line_index
            on skd_purchase_logistics (outbound_delivery_no, outbound_delivery_line);
        </sql>

    </changeSet>

    <changeSet id="alter.cover_so_no.20250529.1" author="gl">
        <sql>
            alter table skd_purchase_logistics
                modify logistics_no varchar(256) null comment '关联物流单号';
        </sql>
    </changeSet>

    <changeSet id="add.column.cover_so_no.and.cover_so_line" author="yecz">
        <sql>
            alter table skd_purchase_logistics
                add column  `cover_so_no` varchar(32) DEFAULT NULL COMMENT '销售单号',
                add column `cover_so_line` varchar(32) DEFAULT NULL COMMENT '销售单项次';
        </sql>
    </changeSet>

    <changeSet id="add.column.material_need_date" author="yecz">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="skd_purchase_logistics" columnName="material_need_date"/>
            </not>
        </preConditions>
        <sql>
            alter table skd_purchase_logistics
                add column  `material_need_date` datetime DEFAULT NULL COMMENT '子件物料需求时间';
        </sql>
    </changeSet>
    <changeSet id="add.column.thai_arrive_inbound_date" author="yecz">
        <preConditions onFail="MARK_RAN">
             <not>
                <columnExists tableName="skd_purchase_logistics" columnName="thai_arrive_inbound_date"/>
            </not>
        </preConditions>
        <sql>
            alter table skd_purchase_logistics
                add column  `thai_arrive_inbound_date` datetime DEFAULT NULL COMMENT '到泰入库日期';
        </sql>
    </changeSet>
    <changeSet id="add.column.plan_arrive_factory_date" author="yecz">
        <preConditions onFail="MARK_RAN">
             <not>
                <columnExists tableName="skd_purchase_logistics" columnName="plan_arrive_factory_date"/>
            </not>
        </preConditions>
        <sql>
            alter table skd_purchase_logistics
                add column  `plan_arrive_factory_date` datetime DEFAULT NULL COMMENT '预计到门';
        </sql>
    </changeSet>
    <changeSet id="add.column.arrive_factory_date" author="yecz">
        <preConditions onFail="MARK_RAN">
             <not>
                <columnExists tableName="skd_purchase_logistics" columnName="arrive_factory_date"/>
            </not>
        </preConditions>
        <sql>
            alter table skd_purchase_logistics
                add column  `arrive_factory_date` datetime DEFAULT NULL COMMENT '实际到门时间';
        </sql>
    </changeSet>
    <changeSet id="modify.column.status" author="yecz">
        <sql>
            alter table skd_purchase_logistics
                modify status varchar(32)  COMMENT '目前状态';
        </sql>
    </changeSet>

</databaseChangeLog>
