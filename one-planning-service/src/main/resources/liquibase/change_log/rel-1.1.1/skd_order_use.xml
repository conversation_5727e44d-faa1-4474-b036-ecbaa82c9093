<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create_skd_order_use_table" author="gl">
        <sql>
            create table if not exists `skd_order_use` (
             `id` bigint NOT NULL AUTO_INCREMENT,
             `top_no` varchar(32) NOT NULL COMMENT '顶层单号',
             `cover_so_no` varchar(32) DEFAULT NULL COMMENT '销售单号',
             `cover_so_line` varchar(32) DEFAULT NULL COMMENT '销售单项次',
             `work_no` varchar(32) DEFAULT NULL COMMENT '计划单号',
             `work_line` varchar(32) DEFAULT NULL COMMENT '计划单项次',
             `material_item_no` varchar(32) DEFAULT NULL COMMENT '子件物料编码',
             `use_type_name` varchar(32) DEFAULT NULL COMMENT '使用类型',
             `use_no` varchar(32) DEFAULT NULL COMMENT '使用单号',
             `use_note` varchar(32) DEFAULT NULL COMMENT '使用说明',
             `supply` varchar(32) DEFAULT NULL COMMENT '供应商',
             `use_qty` double DEFAULT NULL COMMENT '使用数量',
             `unit` varchar(10) DEFAULT NULL COMMENT '单位',
             `po_group` varchar(32) DEFAULT NULL COMMENT '使用采购组',
             `po_group_r` varchar(64) DEFAULT NULL COMMENT '使用采购组描述',
             `remark` varchar(255) DEFAULT NULL COMMENT '备注',
             `status` char(2) DEFAULT '0' COMMENT '状态',
             `create_by` bigint DEFAULT NULL COMMENT '创建者id',
             `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             `update_by` bigint DEFAULT NULL COMMENT '更新人id',
             `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
             PRIMARY KEY (`id`),
             KEY `skd_order_use_top_no_IDX` (`top_no`) USING BTREE,
             KEY `skd_order_use_cover_so_no_IDX` (`cover_so_no`,`cover_so_line`) USING BTREE,
             KEY `skd_order_use_work_no_IDX` (`work_no`,`work_line`) USING BTREE,
             KEY `skd_order_use_material_item_no_IDX` (`material_item_no`) USING BTREE,
             KEY `skd_order_use_use_type_name_IDX` (`use_type_name`) USING BTREE,
             KEY `skd_order_use_use_no_IDX` (`use_no`) USING BTREE,
             KEY `skd_order_use_supply_IDX` (`supply`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单使用表';
        </sql>
    </changeSet>

    <changeSet id="modify.skd_order_use.1" author="gl" failOnError="false">
     <sql>
       ALTER TABLE skd_order_use ADD order_material_id bigint NULL;
     </sql>
     <createIndex tableName="skd_order_use" unique="false" indexName="skd_order_use_order_material_id_IDX">
       <column name="order_material_id"/>
     </createIndex>
    </changeSet>

    <changeSet id="modify.skd_order_use.2" author="gl" failOnError="false">
        <sql>
            ALTER TABLE skd_order_use MODIFY supply varchar(255) NULL;
        </sql>
    </changeSet>

    <changeSet id="modify.skd_order_use.3" author="gl" failOnError="false">
        <sql>
            ALTER TABLE skd_order_use MODIFY use_note varchar(255) NULL;
        </sql>
    </changeSet>

    <changeSet id="del_skd_order_use_index.2025.05.28" author="gl">
        <sql>
            DROP INDEX skd_order_use_use_type_name_IDX ON skd_order_use;
            DROP INDEX skd_order_use_supply_IDX ON skd_order_use;
        </sql>
    </changeSet>

    <changeSet id="modify.skd_order_use.2025.06.05" author="gl" failOnError="false">
        <sql>
            ALTER TABLE skd_order_use ADD order_use_id varchar(255) NULL COMMENT '使用单Id';
        </sql>
    </changeSet>

<!--    <changeSet id="modify.skd_order_use.4" author="gl" failOnError="false">-->
<!--        <sql>-->
<!--            ALTER TABLE skd_order_use ADD direct_qty double NULL COMMENT '本直采数量';-->
<!--            ALTER TABLE skd_order_use ADD skd_qty double NULL COMMENT 'SKD数量';-->
<!--            ALTER TABLE skd_order_use ADD transit_qty double NULL COMMENT '在途数量';-->
<!--            ALTER TABLE skd_order_use ADD un_transit_qty double NULL COMMENT 'PO剩余待中国出货';-->
<!--        </sql>-->
<!--    </changeSet>-->

</databaseChangeLog>
