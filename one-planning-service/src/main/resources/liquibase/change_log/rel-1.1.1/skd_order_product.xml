<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create_skd_order_product_table" author="gl">
        <sql>
            create table if not exists `skd_order_product` (
                `id` bigint not null auto_increment,
                `top_no` varchar(32) default null COMMENT '顶层单号',
                `cover_so_no` varchar(32) default null COMMENT '销售单号',
                `cover_so_line` varchar(32) default null COMMENT '销售单项次',
                `work_no` varchar(32) default null COMMENT '计划单号',
                `work_line` varchar(32) default null COMMENT '计划单项次',
                `plan_date` datetime default null COMMENT '计划开始日期',
                `qty` double default null COMMENT '订单数量',
                `item_no` varchar(32) default null COMMENT '产品编码',
                `plant` varchar(32) not null COMMENT '订单工厂',
                `work_type_name` varchar(32) default null COMMENT '类型',
                `plan_qitao_date` datetime default null COMMENT '需求最晚齐套时间',
                `plan_all_qitao_date` datetime default null COMMENT '计划总齐套日期',
                `all_qitao_date` datetime default null COMMENT '总齐套日期',
                `not_skd_qitao_date` datetime default null COMMENT '本直齐套日期',
                `skd_qitao_date` datetime default null COMMENT 'skd物料齐套日期',
                `has_not_ship_transport` char(1) default '0' COMMENT '是否有非海运',
                `skd_qitao_date_ship` datetime default CURRENT_TIMESTAMP COMMENT 'skd海运物料齐套日期',
                `status` char(2) default '0' COMMENT '状态',
                `create_by` bigint default null COMMENT '创建者id',
                `create_time` datetime default CURRENT_TIMESTAMP COMMENT '创建时间',
                `update_by` bigint default null COMMENT '更新人id',
                `update_time` datetime default CURRENT_TIMESTAMP COMMENT '更新时间',
                primary key (`id`),
                key `skd_order_cover_so_no_IDX` (`cover_so_no`, `cover_so_line`) using BTREE,
                key `skd_order_work_no_IDX` (`work_no`, `work_line`) using BTREE,
                key `skd_order_status_IDX` (`status`) using BTREE,
                key `skd_order_has_not_ship_transport_IDX` (`has_not_ship_transport`) using BTREE
                ) engine = InnoDB default CHARSET = utf8mb4 COMMENT '订单产品表';
        </sql>
    </changeSet>
    <changeSet id="add_skd_order_product_top_no_index" author="yecz">
        <sql>
            create  index skd_order_product_top_no_index
                on skd_order_product (top_no);
        </sql>
    </changeSet>
    <changeSet id="modify.skd_order_product.1" author="gl" failOnError="false">
        <sql>
            ALTER TABLE skd_order_product ADD plant_finish datetime NULL COMMENT '计划完工日期';
            ALTER TABLE skd_order_product ADD order_delivery datetime NULL COMMENT '订单交期';
            ALTER TABLE skd_order_product ADD ship_time datetime NULL COMMENT '船期';
        </sql>
    </changeSet>
    <changeSet id="modify.skd_order_product.20250527.1" author="gl" failOnError="false">
        <sql>
             ALTER TABLE skd_order_product ADD customer_code varchar(64) NULL COMMENT '客户代码';
        </sql>
    </changeSet>

    <changeSet id="add.column.isCanSendUnReady" author="yecz" failOnError="false">
        <sql>
            ALTER TABLE skd_order_product ADD isCanSendUnReady tinyint(1) NULL COMMENT '是否不齐套发货';
        </sql>
    </changeSet>
    <changeSet id="add.column.is_can_send_un_ready" author="yecz" failOnError="false">
        <sql>
            ALTER TABLE skd_order_product ADD is_can_send_un_ready tinyint(1) NULL COMMENT '是否不齐套发货';
            alter table skd_order_product drop column isCanSendUnReady;

        </sql>
    </changeSet>
    <changeSet id="del_skd_order_product_index.2025.05.28" author="gl">
        <sql>
            DROP INDEX skd_order_work_no_IDX ON skd_order_product;
            DROP INDEX skd_order_status_IDX ON skd_order_product;
            DROP INDEX skd_order_has_not_ship_transport_IDX ON skd_order_product;
        </sql>
    </changeSet>

</databaseChangeLog>
