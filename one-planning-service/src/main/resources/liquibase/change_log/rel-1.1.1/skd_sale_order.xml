<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create_skd_sale_order_table" author="gl">
        <sql>
            create table if not exists `skd_sale_order` (
                `id` bigint not null auto_increment,
                `top_no` varchar(32) default null COMMENT '顶层单号',
                `cover_so_id` varchar(64) default null COMMENT '销售单Id',
                `cover_so_no` varchar(32) default null COMMENT '销售单号',
                `order_id` varchar(64) default null COMMENT '订单Id',
                `plan_start_date` datetime default null COMMENT '计划开始日期',
                `plan_end_date` datetime default null COMMENT '计划完工日期',
                `qty` double default null COMMENT '订单数量',
                `ship_time` datetime NULL COMMENT '船期',
                `plant` varchar(32) not null COMMENT '订单工厂',
                `plan_ready_date` datetime default null COMMENT '需求最晚齐套时间',
                `skd_ready_date` datetime default null COMMENT 'skd可发货日期',
                `skd_ready_date_ship` datetime default CURRENT_TIMESTAMP COMMENT '海运入库时间',
                `skd_ready_date_non_ship` datetime default CURRENT_TIMESTAMP COMMENT '非海运入库时间',
                `land_num` int default 0 COMMENT '陆运笔数',
                `air_num` int default 0 COMMENT '空运笔数',
                `status` char(2) default '0' COMMENT '状态',
                `create_by` bigint default null COMMENT '创建者id',
                `create_time` datetime default CURRENT_TIMESTAMP COMMENT '创建时间',
                `update_by` bigint default null COMMENT '更新人id',
                `update_time` datetime default CURRENT_TIMESTAMP COMMENT '更新时间',
                primary key (`id`),
                key `skd_sale_order_top_no_IDX` (`top_no`) using BTREE,
                key `skd_sale_order_cover_so_id_IDX` (`cover_so_id`) using BTREE,
                key `skd_sale_order_order_id_IDX` (`order_id`) using BTREE
                ) engine = InnoDB default CHARSET = utf8mb4 COMMENT '销售订单表';
        </sql>
    </changeSet>

    <changeSet id="add.column.skd_sale_order.customer_code" author="gl" failOnError="false">
        <sql>
            ALTER TABLE skd_sale_order ADD customer_code varchar(64) NULL COMMENT '客户代码';
        </sql>
    </changeSet>

    <changeSet id="add.column.skd_sale_order.skd_all_send" author="gl" failOnError="false">
        <sql>
            ALTER TABLE skd_sale_order ADD skd_all_send tinyint(1) default 0 COMMENT '已全发货';
        </sql>
    </changeSet>
</databaseChangeLog>
