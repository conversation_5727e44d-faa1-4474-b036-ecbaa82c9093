<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
   <!--冻结解冻表-->
    <changeSet id="addTable.warning_process_route_abnormal.***********" author="hongzhenping">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="warning_process_route_abnormal"/>
            </not>
        </preConditions>
        <createTable tableName="warning_process_route_abnormal">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="customer" type="varchar(32)" remarks="客户" />
            <column name="production_line" type="varchar(100)" remarks="排产线体" />
            <column name="planned_order" type="varchar(100)" remarks="计划订单"/>
            <column name="material_id" type="varchar(32)"  remarks="物料ID" />
            <column name="material_desc" type="varchar(255)"  remarks="物料描述"/>
            <column name="planned_online_time" type="date(0)"  remarks="计划上线时间"/>
            <column name="days" type="int"  remarks="距离计划上线时间剩余天数"/>
            <column name="factory" type="varchar(32)"  remarks="生产工厂"/>
            <column name="light_color" type="varchar(32)"  remarks="灯色"/>
            <column name="sfwhgylx" type="varchar(10)"  remarks="是否维护工艺路线"/>
            <column name="sfcfdbdzrr" type="varchar(10)"  remarks="是否触发待办到责任人"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
    </changeSet>
    <changeSet id="addIndex.warning_process_route_abnormal.***********" author="hongzhenping">
        <createIndex tableName="warning_process_route_abnormal" unique="false" indexName="idx_planned_order">
            <column name="planned_order"/>
        </createIndex>
    </changeSet>
    <changeSet id="addColumn.warning_process_route_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_process_route_abnormal" columnName="line_name"/>
                    <columnExists tableName="warning_process_route_abnormal" columnName="npi_person"/>
                    <columnExists tableName="warning_process_route_abnormal" columnName="npi_person_gh"/>
                    <columnExists tableName="warning_process_route_abnormal" columnName="npi_person_userid"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_process_route_abnormal">
            <column name="line_name" type="VARCHAR(32)" remarks="线体名称" />
            <column name="npi_person" type="VARCHAR(255)" remarks="NPI人员" />
            <column name="npi_person_gh" type="VARCHAR(255)" remarks="NPI人员工号" />
            <column name="npi_person_userid" type="VARCHAR(255)" remarks="NPI人员USERID" />
        </addColumn>
    </changeSet>
</databaseChangeLog>
