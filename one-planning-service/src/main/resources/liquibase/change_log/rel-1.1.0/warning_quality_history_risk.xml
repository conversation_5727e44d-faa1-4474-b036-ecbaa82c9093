<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create.table.warning_quality_history_risk" author="zhang<PERSON><PERSON>an">
        <sql>
            CREATE TABLE if not exists `warning_quality_history_risk` (
                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `plan_time` date DEFAULT NULL COMMENT '计划时间',
                `production_workshop` varchar(255) DEFAULT NULL COMMENT '生产车间',
                `line_leader_no` varchar(50)  DEFAULT NULL COMMENT '生产线长(工号)',
                `line_leader_name` varchar(100) DEFAULT NULL COMMENT '生产线长(名字)',
                `factory_code` varchar(50)  DEFAULT NULL COMMENT '工厂编号',
                `order_no` varchar(100)  DEFAULT NULL COMMENT '生产订单',
                `product_id` varchar(100) DEFAULT NULL COMMENT '产品ID',
                `product_category` varchar(100) DEFAULT NULL COMMENT '产品品类',
                `plan_qty` int DEFAULT NULL COMMENT '计划数量',
                `system_push` tinyint(1) DEFAULT NULL COMMENT '系统推送 (0:否, 1:是)',
                `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `update_by` bigint DEFAULT NULL COMMENT '更新人id',
                `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                `create_by` bigint DEFAULT NULL COMMENT '创建者id',
                PRIMARY KEY (`id`)
                ) COMMENT='品质履历记录表'
        </sql>
    </changeSet>
</databaseChangeLog>
