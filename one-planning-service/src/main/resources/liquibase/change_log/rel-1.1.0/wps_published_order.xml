<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_published_order.20250303.1" author="liurongfu" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_published_order"/>
            </not>
        </preConditions>
        <createTable tableName="wps_published_order">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="biz_id" type="varchar(32)" remarks="业务编码">
                <constraints nullable="false"/>
            </column>
            <column name="line_code" type="varchar(32)" remarks="线体编码">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="date(0)" remarks="开始日期"/>
            <column name="end_date" type="date(0)" remarks="截止日期"/>
            <column name="publish_target" type="varchar(32)" defaultValue="dps" remarks="发布目的类型: dps">
                <constraints nullable="false"/>
            </column>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_published_order" unique="false" indexName="idx_biz_id">
            <column name="biz_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>