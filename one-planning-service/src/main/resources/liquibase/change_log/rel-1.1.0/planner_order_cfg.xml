<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.planner_order_cfg.20250327" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="planner_order_cfg"/>
            </not>
        </preConditions>
        <createTable tableName="planner_order_cfg">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="emp_no" type="varchar(32)"  remarks="计划员工号">
                <constraints nullable="false"/>
            </column>
            <column name="business_type" type="tinyint(3)" defaultValue="2" remarks="业务类型：1订单大类 2生产订单小类">
                <constraints nullable="false"/>
            </column>
            <column name="order_type_value" type="varchar(32)"  remarks="生产订单子类型:ZP02-07 ZP12-13">
                <constraints nullable="false"/>
            </column>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="planner_order_cfg" unique="true" indexName="idx_emp_no_order_type_value">
            <column name="emp_no"/>
            <column name="order_type_value"/>
        </createIndex>
        <createIndex tableName="planner_order_cfg" unique="false" indexName="idx_order_sub_type">
            <column name="order_type_value"/>
        </createIndex>
    </changeSet>




</databaseChangeLog>