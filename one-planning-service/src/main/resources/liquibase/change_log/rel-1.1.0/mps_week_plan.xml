<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.mps_week_plan.20250219" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mps_week_plan"/>
            </not>
        </preConditions>
        <createTable tableName="mps_week_plan">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="biz_id" type="varchar(128)"  remarks="业务id">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="datetime(0)"  remarks="开始日期" />
            <column name="end_date" type="datetime(0)"  remarks="截止日期" />
            <column name="current_year" type="int(4)" remarks="当前年份"/>
            <column name="day_num" type="int(2)"  remarks="天数"/>
            <column name="nature_week" type="int(2)"  remarks="自然周"/>
            <column name="week_type" type="int(1)"  remarks="上/下半周"/>
            <column name="pre_plan_quantity" type="int(11)"  remarks="预产数量"/>
            <column name="line_category_code" type="varchar(32)" remarks="产线列表编码"/>
            <column name="frozen_qty" type="int" remarks="冻结数"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="mps_week_plan" unique="false" indexName="idx_mps_week_plan_biz_id">
            <column name="biz_id"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>