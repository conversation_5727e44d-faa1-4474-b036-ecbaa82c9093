<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create.table.warning_in_production_abnormal" author="zhang<PERSON><PERSON>an">
        <sql>
            CREATE TABLE if not exists `warning_in_production_abnormal`
            (
                `id`                        bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `create_time`               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `update_by`                 bigint                DEFAULT NULL COMMENT '更新人id',
                `update_time`               datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                `create_by`                 bigint                DEFAULT NULL COMMENT '创建者id',
                `work_order_no`             varchar(100) NOT NULL COMMENT '工单号',
                `order_no`                  varchar(100) NOT NULL COMMENT '订单号',
                `factory_code`              varchar(32)  NOT NULL DEFAULT '' COMMENT '工厂编码',
                `affects_upper_level_plan`  tinyint               DEFAULT '0' COMMENT '是否影响上层计划：0=否，1=是',
                `impact_type`               varchar(100)          DEFAULT NULL COMMENT '影响类型',
                `estimated_completion_date` varchar(16)           DEFAULT NULL COMMENT '预计可完工日期',
                `reason_category`           varchar(100)          DEFAULT NULL COMMENT '原因分类',
                `insufficient_order_reason` varchar(100)          DEFAULT NULL COMMENT '未满单原因',
                `light_color`               varchar(32)  NOT NULL COMMENT '预警灯色',
                `scheduling_date`           date                  DEFAULT NULL COMMENT '排产日期',
                `material_off_shelf_date`   date                  DEFAULT NULL COMMENT '物料下架日期',
                `online_date`               date                  DEFAULT NULL COMMENT '上线日期',
                `production_workshop`       varchar(100)          DEFAULT NULL COMMENT '生产车间',
                `production_foreman`        varchar(100)          DEFAULT NULL COMMENT '生产课长',
                `production_line`           varchar(100)          DEFAULT NULL COMMENT '生产线体',
                `material_id`               varchar(100)          DEFAULT NULL COMMENT '物料ID',
                `associated_order_no`       varchar(100)          DEFAULT NULL COMMENT '关联订单号',
                `planned_quantity`          int                   DEFAULT NULL COMMENT '计划数量',
                `production_days`           int                   DEFAULT NULL COMMENT '在制天数',
                `actual_input_quantity`     int                   DEFAULT NULL COMMENT '实际投入数量',
                `actual_reporting_quantity` int                   DEFAULT NULL COMMENT '实际报工数量',
                `plan_type`                 int          NOT NULL COMMENT '排产场景（0:整机，1:组件，2：部件）',
                `line_uuid`                 varchar(100)          DEFAULT NULL COMMENT '线体uuid',
                `line_code`                 varchar(100)          DEFAULT NULL COMMENT '线体编号',
                `inbound_quantity`          int                   DEFAULT NULL COMMENT '已入库数量',
                PRIMARY KEY (`id`),
                KEY                         `warning_in_production_abnormal_order_no_IDX` (`order_no`) USING BTREE
            ) COMMENT='在制工单异常';
        </sql>
    </changeSet>

    <changeSet id="1747831492186-1" author="zhangzijian">
        <sql>
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal ADD material_name varchar(100) NULL COMMENT '物料名称';
        </sql>
    </changeSet>

    <changeSet id="1747898896224-1" author="zhangzijian">
        <sql>
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal DROP COLUMN production_days;
        </sql>
    </changeSet>

    <changeSet id="1747983173582-1" author="zhangzijian">
        <sql>
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal ADD planner_emp_no varchar(100)  NULL COMMENT '计划员工号';
        </sql>
    </changeSet>

    <changeSet id="1747993394897-1" author="zhangzijian">
        <sql>
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal MODIFY COLUMN estimated_completion_date date NULL COMMENT '预计可完工日期';
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal ADD pc_estimated_completion_date date NULL COMMENT 'pc预计可完工日期';
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal ADD adjust_plan_date date NULL COMMENT '调整后计划日期';
        </sql>
    </changeSet>

    <changeSet id="1747997376395-1" author="zhangzijian">
        <sql>
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal ADD foreman_gh varchar(100) NULL COMMENT '课长工号';
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal ADD line_leader_gh varchar(100) NULL COMMENT '线长工号';
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal ADD line_leader_name varchar(100) NULL COMMENT '线长名称';
        </sql>
    </changeSet>

    <changeSet id="1747997881341-1" author="zhangzijian">
        <sql>
            ALTER TABLE ldx_one_planning.warning_in_production_abnormal MODIFY COLUMN affects_upper_level_plan tinyint NULL COMMENT '是否影响上层计划：0=否，1=是';
        </sql>
    </changeSet>
</databaseChangeLog>
