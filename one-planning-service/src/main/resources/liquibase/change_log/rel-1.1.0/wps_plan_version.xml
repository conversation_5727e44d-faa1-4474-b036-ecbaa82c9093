<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_plan_version.20250508" author="chenyangbin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_plan_version"/>
            </not>
        </preConditions>
        <createTable tableName="wps_plan_version">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="planner_emp_no" type="varchar(32)"  remarks="计划员工号"/>
            <column name="version" type="varchar(32)"  remarks="版本" />
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_plan_version" indexName="idx_planner_emp_no">
            <column name="planner_emp_no"/>
        </createIndex>
    </changeSet>
    <changeSet id="add_column_wps_plan_version_source" author="chenyangbin" failOnError="false">
        <addColumn tableName="wps_plan_version">
            <column name="source" type="int" remarks="版本来源1：保存  2：定时发布"></column>
        </addColumn>
        <addColumn tableName="wps_plan_version">
            <column name="factory_code" type="varchar(32)" remarks="工厂编码"></column>
        </addColumn>
        <setTableRemarks tableName="wps_plan_version" remarks="排产版本管理"></setTableRemarks>
    </changeSet>

</databaseChangeLog>