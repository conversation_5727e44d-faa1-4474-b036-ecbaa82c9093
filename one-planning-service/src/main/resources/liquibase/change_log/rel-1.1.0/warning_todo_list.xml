<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">



    <changeSet id="create.table.warning_todo_list" author="daishaokun1">
        <sql>
            drop table if exists warning_todo_list;
            CREATE TABLE if not exists warning_todo_list
            (
                id             bigint AUTO_INCREMENT PRIMARY KEY,
                biz_id         bigint      NOT NULL comment '业务编号',
                warning_type   VARCHAR(50) NOT NULL comment '告警类型枚举',
                process_status VARCHAR(50) NOT NULL comment '流程状态',
                assignee       VARCHAR(255) comment '代办人',
                process_id     VARCHAR(255) comment '流程编号',
                created_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
            # biz_id需要有索引
            create index idx_biz_id on warning_todo_list (biz_id);
        </sql>
    </changeSet>

  <changeSet id="addColumn.factory_code.warning_todo_list.20250516" author="daishaokun1" failOnError="false">
    <sql>
      alter table warning_todo_list
        add factory_code varchar(32) default '' not null comment '工厂编码';
    </sql>
  </changeSet>

  <changeSet id="addColumn.biz_type.warning_todo_list.20250516" author="daishaokun1" failOnError="false">
    <sql>
      update warning_todo_list set assignee = '' where assignee is null;
      alter table warning_todo_list
        modify assignee varchar(255) default '' not null comment '代办人';
    </sql>
  </changeSet>

  <changeSet id="addColumn.push_status.warning_todo_list.20250516" author="daishaokun1" failOnError="false">
    <sql>
      alter table warning_todo_list
        add push_status tinyint(3) default 0 not null comment '推送状态:0未推送1已推送';
    </sql>
  </changeSet>

  <changeSet id="index.warning_todo_list.20250528" author="daishaokun1" failOnError="false">
    <sql>
      CREATE INDEX idx_biz_id_warning_type ON warning_todo_list(biz_id, warning_type);
    </sql>
  </changeSet>

</databaseChangeLog>
