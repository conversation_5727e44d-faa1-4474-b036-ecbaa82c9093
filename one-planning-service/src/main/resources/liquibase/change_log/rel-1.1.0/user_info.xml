<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create.table.user_info" author="zhang<PERSON>jian">
        <sql>
            CREATE TABLE if not exists `user_info`
            (
                `id`          bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `user_id`     bigint NOT NULL COMMENT '用户id',
                `user_name`   varchar(32)  DEFAULT NULL COMMENT '姓名',
                `emp_no`      varchar(32)  DEFAULT NULL COMMENT '工号',
                `create_by`   bigint    DEFAULT NULL COMMENT '创建者id',
                `create_time` datetime   DEFAULT (now()) COMMENT '创建时间',
                `update_by`   bigint      DEFAULT NULL COMMENT '更新人id',
                `update_time` datetime    DEFAULT (now()) COMMENT '更新时间',
                `user_type`   varchar(64)             DEFAULT NULL COMMENT '用户类型',
                PRIMARY KEY (`id`),
                KEY           `user_info_user_id_IDX` (`user_id`) USING BTREE
            ) COMMENT='用户管理表'
        </sql>
    </changeSet>


</databaseChangeLog>
