<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.factory_order_cfg.20250313" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="factory_order_cfg"/>
            </not>
        </preConditions>
        <createTable tableName="factory_order_cfg">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码">
                <constraints nullable="false"/>
            </column>
            <column name="order_sub_type" type="varchar(32)"  remarks="生产订单子类型:ZP02-07 ZP12-13">
                <constraints nullable="false"/>
            </column>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="factory_order_cfg" unique="true" indexName="idx_factory_order">
            <column name="factory_code"/>
            <column name="order_sub_type"/>
        </createIndex>
        <createIndex tableName="factory_order_cfg" unique="false" indexName="idx_order_sub_type">
            <column name="order_sub_type"/>
        </createIndex>
    </changeSet>


    <changeSet id="addColumn.businessType.factory_order_cfg.20250312" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="factory_order_cfg"/>
        </preConditions>
        <addColumn tableName="factory_order_cfg">
            <column name="business_type" afterColumn="factory_code" type="tinyint(3)" defaultValue="2" remarks="业务类型：1订单大类 2生产订单小类">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="renameColumn.businessType.factory_order_cfg.20250312" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <columnExists columnName="order_sub_type" tableName="factory_order_cfg"/>
        </preConditions>
        <renameColumn tableName="factory_order_cfg" oldColumnName="order_sub_type" columnDataType="varchar(32)" newColumnName="order_type_value"/>
    </changeSet>


</databaseChangeLog>