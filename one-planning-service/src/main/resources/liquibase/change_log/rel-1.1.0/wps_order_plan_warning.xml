<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_order_plan_warning.20250328.1" author="liurongfu" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_order_plan_warning"/>
            </not>
        </preConditions>
        <createTable tableName="wps_order_plan_warning">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="order_no" type="varchar(64)"  remarks="订单编号">
                <constraints nullable="false"/>
            </column>
            <column name="warning_category" type="varchar(64)"  remarks="预警大类" />
            <column name="warning_type" type="varchar(64)"  remarks="预警小类" />
            <column name="warning_level" type="int(1)"  remarks="预警级别:1-低，2-中，3-高" />
            <column name="warning_content" type="varchar(2048)"  remarks="预警内容" />
            <column name="schedule_date" type="datetime(0)" remarks="排产日期"/>
            <column name="trigger_time" type="datetime(0)" remarks="触发时间"/>
            <column name="handle_user" type="bigint(20)" remarks="处理人id"/>
            <column name="handle_status" type="int(1)" remarks="处理状态:1-未处理，2-已处理，3-已关闭"/>
            <column name="handle_time" type="datetime(0)" remarks="处理时间"/>
            <column name="handle_content" type="varchar(2048)"  remarks="处理备注" />
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_order_plan_warning" unique="true" indexName="idx_wopw_order_no_wct">
            <column name="order_no"/>
            <column name="warning_category"/>
            <column name="warning_type"/>
        </createIndex>
    </changeSet>

    <changeSet id="alterTable.wps_order_plan_warning.20250416.1" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <columnExists tableName="wps_order_plan_warning" columnName="product_type"/>
            </not>
        </preConditions>
        <addColumn tableName="wps_order_plan_warning">
            <column name="product_type" type="varchar(64)" defaultValue="WHOLE_MACHINE" remarks="产品类型">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="dropIndex.wps_order_plan_warning.20250416.2" author="liurongfu" failOnError="false">
        <preConditions>
            <indexExists indexName="idx_wopw_order_no_wct"/>
        </preConditions>
        <dropIndex indexName="idx_wopw_order_no_wct" tableName="wps_order_plan_warning"/>
    </changeSet>

    <changeSet id="createIndex.wps_order_plan_warning.20250416.3" author="liurongfu" failOnError="false">
        <preConditions>
            <not>
                <indexExists indexName="idx_opw_order_type"/>
            </not>
        </preConditions>
        <createIndex tableName="wps_order_plan_warning" unique="true" indexName="idx_opw_order_type">
            <column name="order_no"/>
            <column name="warning_category"/>
            <column name="warning_type"/>
            <column name="product_type"/>
        </createIndex>
    </changeSet>

    <changeSet id="alterTable.wps_order_plan_warning.20250418.1" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <columnExists tableName="wps_order_plan_warning" columnName="line_code"/>
            </not>
        </preConditions>
        <addColumn tableName="wps_order_plan_warning">
            <column name="line_code" type="varchar(32)" remarks="产线编码">
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="alterTable.wps_order_plan_warning.20250421.1" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <columnExists tableName="wps_order_plan_warning" columnName="line_uuid"/>
            </not>
        </preConditions>
        <addColumn tableName="wps_order_plan_warning">
            <column name="line_uuid" type="varchar(128)" remarks="产线UUID">
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="dropIndex.wps_order_plan_warning.20250424.1" author="liurongfu" failOnError="false">
        <preConditions>
            <indexExists indexName="idx_opw_order_type"/>
        </preConditions>
        <dropIndex indexName="idx_opw_order_type" tableName="wps_order_plan_warning"/>
    </changeSet>

    <changeSet id="createIndex.wps_order_plan_warning.20250424.2" author="liurongfu" failOnError="false">
        <preConditions>
            <not>
                <indexExists indexName="idx_opw_order_type_level"/>
            </not>
        </preConditions>
        <createIndex tableName="wps_order_plan_warning" unique="true" indexName="idx_opw_order_type_level">
            <column name="order_no"/>
            <column name="warning_category"/>
            <column name="warning_type"/>
            <column name="warning_level"/>
            <column name="product_type"/>
        </createIndex>
    </changeSet>

    <changeSet id="dropIndex.wps_order_plan_warning.20250424.3" author="liurongfu" failOnError="false">
        <preConditions>
            <indexExists indexName="idx_opw_order_type_level"/>
        </preConditions>
        <dropIndex indexName="idx_opw_order_type_level" tableName="wps_order_plan_warning"/>
    </changeSet>

    <changeSet id="createIndex.wps_order_plan_warning.20250424.4" author="liurongfu" failOnError="false">
        <preConditions>
            <not>
                <indexExists indexName="idx_opw_order_line_type_level"/>
            </not>
        </preConditions>
        <createIndex tableName="wps_order_plan_warning" unique="true" indexName="idx_opw_order_line_type_level">
            <column name="order_no"/>
            <column name="warning_category"/>
            <column name="warning_type"/>
            <column name="warning_level"/>
            <column name="line_code"/>
            <column name="line_uuid"/>
            <column name="product_type"/>
        </createIndex>
    </changeSet>

    <changeSet id="dropIndex.wps_order_plan_warning.20250424.5" author="liurongfu" failOnError="false">
        <preConditions>
            <indexExists indexName="idx_opw_order_line_type_level"/>
        </preConditions>
        <dropIndex indexName="idx_opw_order_line_type_level" tableName="wps_order_plan_warning"/>
    </changeSet>

    <changeSet id="createIndex.wps_order_plan_warning.20250424.6" author="liurongfu" failOnError="false">
        <preConditions>
            <not>
                <indexExists indexName="idx_opw_order_type_level"/>
            </not>
        </preConditions>
        <createIndex tableName="wps_order_plan_warning" unique="false" indexName="idx_opw_order_type_level">
            <column name="order_no"/>
            <column name="warning_category"/>
            <column name="warning_type"/>
            <column name="warning_level"/>
            <column name="product_type"/>
        </createIndex>
    </changeSet>

    <changeSet id="alert.wps_order_plan_warning.factory_code" author="daishaokun1" failOnError="false">
        <sql>
            alter table wps_order_plan_warning
            add factory_code varchar(32) default '' not null comment '工厂编码';
        </sql>
    </changeSet>
</databaseChangeLog>