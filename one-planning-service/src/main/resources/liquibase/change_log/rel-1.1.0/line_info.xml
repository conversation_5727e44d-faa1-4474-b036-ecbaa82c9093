<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.line_info.20250220" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="line_info"/>
            </not>
        </preConditions>
        <createTable tableName="line_info">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="code" type="varchar(32)"  remarks="编码">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(128)"  remarks="名称">
                <constraints nullable="false"/>
            </column>
            <column name="line_category_code" type="varchar(32)"  remarks="产线类别编码"/>
            <column name="workshop_code" type="varchar(128)"  remarks="车间编码"/>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码"/>
            <column name="sbu" type="varchar(128)"  remarks="sbu"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="line_info" unique="false" indexName="idx_name">
            <column name="name"/>
        </createIndex>
        <createIndex tableName="line_info" unique="true" indexName="idx_code">
            <column name="code"/>
        </createIndex>
    </changeSet>


    <changeSet id="createIndex.workshop_code.line_info.20250311" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <indexExists indexName="idx_workshop_code" tableName="line_info"/>
            </not>
        </preConditions>
        <createIndex tableName="line_info" unique="false" indexName="idx_workshop_code">
            <column name="workshop_code"/>
        </createIndex>
    </changeSet>

    <changeSet id="createIndex.factory_code.line_info.20250311" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <indexExists indexName="idx_factory_code" tableName="line_info"/>
            </not>
        </preConditions>
        <createIndex tableName="line_info" unique="false" indexName="idx_factory_code">
            <column name="factory_code"/>
        </createIndex>
    </changeSet>



    <changeSet id="addColumn.virtual.line_info.20250313.1" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
             <tableExists tableName="line_info"/>
        </preConditions>
        <addColumn tableName="line_info">
            <column name="virtual_status" type="tinyint(3)" defaultValue="0" remarks="虚拟线体1是0否" afterColumn="name">
                <constraints nullable="false"/>
            </column>
        </addColumn>

    </changeSet>

    <changeSet id="addColumn.enable_status.line_info.20250314" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="line_info"/>
        </preConditions>
        <addColumn tableName="line_info">
            <column name="enable_status" type="tinyint(3)" defaultValue="1" remarks="是否启用" afterColumn="name">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>


    <changeSet id="addColumn.uuid.line_info.20250418" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="line_info"/>
        </preConditions>
        <addColumn tableName="line_info">
            <column name="line_uuid" type="varchar(128)"  remarks="线体uuid" afterColumn="id"/>
        </addColumn>
        <createIndex tableName="line_info" unique="true" indexName="idx_uuid">
            <column name="line_uuid"/>
        </createIndex>
    </changeSet>


    <changeSet id="modifyColumn.uuid.line_info.20250528" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
          <columnExists tableName="line_info" columnName="line_uuid"/>
        </preConditions>
        <sql>
            update  line_info  set line_uuid   = code  where line_uuid is null;
            ALTER TABLE line_info MODIFY line_uuid VARCHAR(128) NOT NULL;
        </sql>
    </changeSet>
    
    <changeSet id="dropIndex.idx_code.20250517" author="zhuangjiayin" failOnError="false">
        <preConditions>
            <indexExists indexName="idx_code" tableName="line_info"/>
        </preConditions>
        <dropIndex tableName="line_info" indexName="idx_code"/>
    </changeSet>


</databaseChangeLog>