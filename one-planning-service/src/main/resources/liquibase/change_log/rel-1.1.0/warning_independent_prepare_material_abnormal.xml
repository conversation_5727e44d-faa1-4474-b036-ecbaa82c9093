<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create.table.warning_independent_prepare_material_abnormal.0528-1" author="daishaokun1">
        <sql>
          drop table if exists warning_independent_prepare_material_abnormal;
          CREATE TABLE `warning_independent_prepare_material_abnormal`
          (
            `id`                                        BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `order_number`                              VARCHAR(255) DEFAULT NULL COMMENT '计划单号',
            `country`                                   VARCHAR(255) DEFAULT NULL COMMENT '国家',
            `responsible_person`                        VARCHAR(255) DEFAULT NULL COMMENT '责任人',
            `responsible_person_id`                     VARCHAR(255) DEFAULT NULL COMMENT '责任人ID',
            `initial_estimated_normalization_date`      DATE         DEFAULT NULL COMMENT '初次预计转正日期',
            `initial_estimated_shipment_date`           DATE         DEFAULT NULL COMMENT '初次预计出货日期',
            `planned_order_number`                      VARCHAR(255) DEFAULT NULL COMMENT '整灯风险备料单号',
            `factory_code`                              VARCHAR(255) DEFAULT NULL COMMENT '工厂',
            `product_id`                                VARCHAR(255) DEFAULT NULL COMMENT '商品ID',
            `material_description`                      VARCHAR(255) DEFAULT NULL COMMENT '物料描述',
            `order_quantity`                            INT          DEFAULT NULL COMMENT '订单数量',
            `planned_online_time`                       DATE         DEFAULT NULL COMMENT '上线日期',
            `original_completion_date`                  DATE         DEFAULT NULL COMMENT '原始完工',
            `delayed_normalization_date`                DATE         DEFAULT NULL COMMENT '延迟后转正',
            `material_change_or_cancellation_completed` INT          DEFAULT NULL COMMENT '物料是否可消耗',
            `latest_estimated_normalization_date`       DATE         DEFAULT NULL COMMENT '最新预计转正日期',
            `latest_estimated_shipment_date`            DATE         DEFAULT NULL COMMENT '最新预计出货日期',
            `change_count`                              INT          DEFAULT NULL COMMENT '已变更次数',
            `light_color`                               VARCHAR(255) DEFAULT NULL COMMENT '灯色',
            `category`                                  VARCHAR(255) DEFAULT NULL COMMENT '类别',
            `sales_order_number`                        VARCHAR(255) DEFAULT NULL COMMENT '销售订单-行项目',
            `purchase_order_number`                     VARCHAR(255) DEFAULT NULL COMMENT '采购订单-行项目',
            `line_number`                               VARCHAR(255) DEFAULT NULL COMMENT '行号',
            `created_by`                                BIGINT       DEFAULT NULL COMMENT '创建人',
            `updated_by`                                BIGINT       DEFAULT NULL COMMENT '更新人',
            created_at                                  TIMESTAMP    DEFAULT CURRENT_TIMESTAMP,
            updated_at                                  TIMESTAMP    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='独立备料SO未转正';
        </sql>
    </changeSet>

  <changeSet id="create.table.warning_independent_prepare_material_abnormal_detail.0528-2" author="daishaokun1">
    <sql>
      drop table if exists warning_independent_prepare_material_abnormal_detail;
      CREATE TABLE warning_independent_prepare_material_abnormal_detail
      (
        id                                        BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        order_number                              VARCHAR(255) COMMENT '计划单号',
        material_id                               VARCHAR(255) COMMENT 'BOM物料ID',
        material_description                      VARCHAR(255) COMMENT '物料描述',
        order_quantity                            INT COMMENT '数量',
        material_change_or_cancellation_completed TINYINT(1) COMMENT '物料是否可消耗',
        factory_code                              VARCHAR(255) COMMENT '工厂',
        estimated_consumption_months              VARCHAR(50) COMMENT '预计消耗月份（取值：1-6或者>6）',
        stagnant_risk_level                       VARCHAR(50) COMMENT '呆滞风险等级',
        consumption_n1                            INT COMMENT 'N-1',
        consumption_n2                            INT COMMENT 'N-2',
        consumption_n3                            INT COMMENT 'N-3',
        consumption_n4                            INT COMMENT 'N-4',
        consumption_n5                            INT COMMENT 'N-5',
        consumption_n6                            INT COMMENT 'N-6',
        created_by                                VARCHAR(255) COMMENT '创建人',
        updated_by                                VARCHAR(255) COMMENT '更新人',
        created_at                                TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at                                TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='独立备料SO未转正（MC）';
    </sql>
  </changeSet>

  <changeSet id="modify.table.warning_independent_prepare_material_abnormal_detail.0605-1" author="liurongfu">
    <modifyDataType tableName="warning_independent_prepare_material_abnormal_detail" columnName="created_by"
                    newDataType="bigint(20)"/>
    <modifyDataType tableName="warning_independent_prepare_material_abnormal_detail" columnName="updated_by"
                    newDataType="bigint(20)"/>
  </changeSet>
  <changeSet id="add.column.warning_independent_prepare_material_abnormal.0605-2" author="liurongfu">
    <preConditions onError="MARK_RAN">
      <not>
        <columnExists tableName="warning_independent_prepare_material_abnormal" columnName="latest_warning_date"/>
      </not>
    </preConditions>
    <addColumn tableName="warning_independent_prepare_material_abnormal">
      <column name="latest_warning_date" type="date"/>
    </addColumn>
  </changeSet>
  <changeSet id="modify.column.warning_independent_prepare_material_abnormal_detail.0605-3" author="liurongfu">
    <preConditions onError="MARK_RAN">
      <columnExists tableName="warning_independent_prepare_material_abnormal_detail"
                    columnName="order_quantity"/>
    </preConditions>
    <renameColumn tableName="warning_independent_prepare_material_abnormal_detail"
                  oldColumnName="order_quantity"
                  newColumnName="material_demand_quantity"
                  columnDataType="int"
    />
  </changeSet>
  <changeSet id="modify.column.warning_independent_prepare_material_abnormal_detail.0605-4" author="liurongfu">
    <preConditions onError="MARK_RAN">
      <columnExists tableName="warning_independent_prepare_material_abnormal_detail"
                    columnName="material_change_or_cancellation_completed"/>
    </preConditions>
    <renameColumn tableName="warning_independent_prepare_material_abnormal_detail"
                  oldColumnName="material_change_or_cancellation_completed"
                  newColumnName="material_is_consumable"
                  columnDataType="tinyint(1)"/>
  </changeSet>
</databaseChangeLog>
