<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.mes_daily_plan_detail.20250524.1" author="liurongfu" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mes_daily_plan_detail"/>
            </not>
        </preConditions>
        <createTable tableName="mes_daily_plan_detail">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="factory_code" type="VARCHAR(32)" remarks="工厂编码">
                <constraints nullable="false"/>
            </column>
            <column name="factory_name" type="VARCHAR(100)" remarks="工厂名称">
                <constraints nullable="false"/>
            </column>
            <column name="planner_emp_no" type="VARCHAR(50)" remarks="计划人员-工号"/>
            <column name="planner" type="VARCHAR(50)" remarks="计划人员"/>
            <column name="plan_date" type="DATE" remarks="计划日期">
                <constraints nullable="false"/>
            </column>
            <column name="production_workshop" type="VARCHAR(100)" remarks="生产车间"/>
            <column name="production_line" type="VARCHAR(100)" remarks="生产线体"/>
            <column name="line_leader_no" type="VARCHAR(100)" remarks="生产线长(工号)"/>
            <column name="line_leader_name" type="VARCHAR(100)" remarks="生产线长(名字)"/>
            <column name="work_order_no" type="VARCHAR(100)" remarks="工单号">
                <constraints nullable="false"/>
            </column>
            <column name="material_id" type="VARCHAR(100)" remarks="物料ID"/>
            <column name="planned_quantity" type="INTEGER" remarks="计划数量"/>
            <column name="actual_input_quantity" type="INTEGER" remarks="实际投入数量"/>
            <column name="first_process_gap" type="INTEGER" remarks="第一道工序gap"/>
            <column name="actual_reported_quantity" type="INTEGER" remarks="实际报工数量"/>
            <column name="last_process_gap" type="INTEGER" remarks="最后一道工序gap"/>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间" />
        </createTable>
        <createIndex tableName="mes_daily_plan_detail" indexName="idx_factory_code">
            <column name="factory_code"/>
        </createIndex>
        <createIndex tableName="mes_daily_plan_detail" indexName="idx_work_order_no">
            <column name="work_order_no"/>
        </createIndex>
        <createIndex tableName="mes_daily_plan_detail" indexName="idx_plan_date">
            <column name="plan_date"/>
        </createIndex>
    </changeSet>

    <changeSet id="addIndex.mes_daily_plan_detail.20250524.2" author="liurongfu">
        <createIndex tableName="mes_daily_plan_detail"  indexName="idx_production_line">
            <column name="production_line"/>
        </createIndex>
    </changeSet>

    <changeSet id="dropColumn.mes_daily_plan_detail.20250527.1" author="liurongfu" failOnError="false">
        <preConditions onFail="MARK_RAN">
            <and>
                <columnExists tableName="mes_daily_plan_detail" columnName="first_process_gap"/>
            </and>
        </preConditions>
        <dropColumn tableName="mes_daily_plan_detail" columnName="first_process_gap"/>
    </changeSet>

    <changeSet id="dropColumn.mes_daily_plan_detail.20250527.2" author="liurongfu" failOnError="false">
        <preConditions onFail="MARK_RAN">
            <and>
                <columnExists tableName="mes_daily_plan_detail" columnName="last_process_gap"/>
            </and>
        </preConditions>
        <dropColumn tableName="mes_daily_plan_detail" columnName="last_process_gap"/>
    </changeSet>

    <changeSet id="addColumn.mes_daily_plan_detail.20250527.3" author="liurongfu">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_daily_plan_detail" columnName="foreman_no"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_daily_plan_detail">
            <column name="foreman_no" type="varchar(100)" remarks="生产课长(工号)">
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="addColumn.mes_daily_plan_detail.20250527.4" author="liurongfu">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_daily_plan_detail" columnName="production_foreman"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_daily_plan_detail">
            <column name="production_foreman" type="varchar(100)" remarks="生产课长">
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="addColumn.mes_daily_plan_detail.20250527.5" author="liurongfu">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_daily_plan_detail" columnName="type"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_daily_plan_detail">
            <column name="type" type="varchar(50)" remarks="类型">
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="addColumn.mes_daily_plan_detail.20250527.6" author="liurongfu">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_daily_plan_detail" columnName="shift"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_daily_plan_detail">
            <column name="shift" type="varchar(50)" remarks="班别">
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="addColumn.mes_daily_plan_detail.20250527.7" author="liurongfu">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_daily_plan_detail" columnName="plan_type"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_daily_plan_detail">
            <column name="plan_type" type="int" defaultValue="0" remarks="排产场景（0:整机，1:组件，2：部件）">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="dropColumn.mes_daily_plan_detail.20250527.8" author="liurongfu" failOnError="false">
        <preConditions>
            <columnExists tableName="mes_daily_plan_detail" columnName="factory_name"/>
        </preConditions>
        <dropColumn tableName="mes_daily_plan_detail" columnName="factory_name"/>
    </changeSet>
</databaseChangeLog>