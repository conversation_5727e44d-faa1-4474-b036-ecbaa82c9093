<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.planner_line_cfg.202502120" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="planner_line_cfg"/>
            </not>
        </preConditions>
        <createTable tableName="planner_line_cfg">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="bigint(22)"  remarks="用户id">
                <constraints nullable="false"/>
            </column>
            <column name="config_type" type="tinyint(3)"  remarks="配置类型:1产线类型 2产线">
                <constraints nullable="false"/>
            </column>
            <column name="config_code" type="varchar(64)"  remarks="配置编码">
                <constraints nullable="false"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码">
                <constraints nullable="false"/>
             </column>
            <column name="sbu" type="varchar(128)"  remarks="sbu" />
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
    </changeSet>

    <changeSet id="createIndex.user_id.planner_line_cfg.20250311" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <indexExists indexName="idx_user_id" tableName="planner_line_cfg"/>
            </not>
        </preConditions>
        <createIndex tableName="planner_line_cfg" unique="false" indexName="idx_user_id">
            <column name="user_id"/>
        </createIndex>
    </changeSet>



    <changeSet id="alterColumn.factory_code.planner_line_cfg.20250312" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="planner_line_cfg"/>
        </preConditions>
       <modifyDataType tableName="planner_line_cfg" columnName="factory_code" newDataType="varchar(32) null" />
    </changeSet>


    <changeSet id="addColumn.workshop_code.planner_line_cfg.20250312" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="planner_line_cfg"/>
        </preConditions>
       <addColumn tableName="planner_line_cfg">
           <column name="workshop_code" afterColumn="config_code" type="varchar(32)" remarks="车间编码"/>
       </addColumn>
    </changeSet>


    <changeSet id="addColumn.lineUuid.planner_line_cfg.20250418" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="planner_line_cfg"/>
        </preConditions>
        <addColumn tableName="planner_line_cfg">
            <column name="line_uuid" type="varchar(128)"  remarks="线体uuid" afterColumn="id"/>
        </addColumn>
    </changeSet>


</databaseChangeLog>