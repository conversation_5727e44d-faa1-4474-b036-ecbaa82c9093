<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
   <!--冻结解冻表-->
    <changeSet id="addTable.warning_frozen_unfrozen_abnormal.***********" author="hongzhenping">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="warning_frozen_unfrozen_abnormal"/>
            </not>
        </preConditions>
        <createTable tableName="warning_frozen_unfrozen_abnormal">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="sales_order_number" type="varchar(100)" remarks="销售订单号" />
            <column name="line_number" type="varchar(100)" remarks="销售订单行项目"/>
            <column name="customer" type="varchar(32)" remarks="客户" />
            <column name="order_no" type="varchar(32)"  remarks="生产订单">
                <constraints nullable="true" />
            </column>
            <column name="product_id" type="varchar(100)" remarks="订单id（商品id）">
                <constraints nullable="true" />
            </column>
            <column name="material_desc" type="varchar(255)"  remarks="物料描述"/>
            <column name="planned_online_time" type="varchar(32)"  remarks="计划上线时间"/>
            <column name="remaining_days_to_plan" type="int"  remarks="距离计划剩余天数"/>
            <column name="light_color" type="varchar(32)"  remarks="灯色"/>
            <column name="sfcfdbdzrr" type="varchar(32)"  remarks="是否触发待办到责任人"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
    </changeSet>
    <changeSet id="addIndex.warning_frozen_unfrozen_abnormal.***********" author="hongzhenping">
        <createIndex tableName="warning_frozen_unfrozen_abnormal" unique="false" indexName="idx_order_no">
            <column name="order_no"/>
        </createIndex>
    </changeSet>
    <changeSet id="addColumn.warning_frozen_unfrozen_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="factory"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_frozen_unfrozen_abnormal">
            <column name="factory" type="varchar(32)" remarks="工厂" />
        </addColumn>
    </changeSet>
    <changeSet id="modify.warning_frozen_unfrozen_abnormal.type.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="planned_online_time"/>
        </preConditions>
        <sql dbms="mysql">
            ALTER TABLE warning_frozen_unfrozen_abnormal
                MODIFY COLUMN planned_online_time date COMMENT '计划上线日期';
        </sql>
    </changeSet>
    <changeSet id="addColumn.warning_frozen_unfrozen_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="commodity_id"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_frozen_unfrozen_abnormal">
            <column name="commodity_id" type="varchar(32)" remarks="商品id" />
        </addColumn>
    </changeSet>
    <changeSet id="dropMultipleColumns.warning_frozen_unfrozen_abnormal.***********" author="hongzhenping" failOnError="false">
        <preConditions onFail="MARK_RAN">
            <and>
                <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="product_id"/>
            </and>
        </preConditions>
        <dropColumn tableName="warning_frozen_unfrozen_abnormal" columnName="product_id"/>
    </changeSet>
    <changeSet id="addColumn.warning_frozen_unfrozen_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="product_group_code"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_frozen_unfrozen_abnormal">
            <column name="product_group_code" type="varchar(100)" remarks="产品组编码" />
        </addColumn>
    </changeSet>
    <changeSet id="addColumn.warning_frozen_unfrozen_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="npi_person"/>
                    <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="npi_person_gh"/>
                    <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="npi_person_userid"/>
                    <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="qpl_person"/>
                    <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="qpl_person_gh"/>
                    <columnExists tableName="warning_frozen_unfrozen_abnormal" columnName="qpl_person_userid"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_frozen_unfrozen_abnormal">
            <column name="npi_person" type="VARCHAR(255)" remarks="NPI人员" />
            <column name="npi_person_gh" type="VARCHAR(255)" remarks="NPI人员工号" />
            <column name="npi_person_userid" type="VARCHAR(255)" remarks="NPI人员USERID" />
            <column name="qpl_person" type="VARCHAR(255)" remarks="QPL人员" />
            <column name="qpl_person_gh" type="VARCHAR(255)" remarks="QPL人员工号" />
            <column name="qpl_person_userid" type="VARCHAR(255)" remarks="QPL人员USERID" />
        </addColumn>
    </changeSet>
</databaseChangeLog>
