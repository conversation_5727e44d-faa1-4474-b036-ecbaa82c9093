<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_form_info.20250228" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_form_info"/>
            </not>
        </preConditions>
        <createTable tableName="wps_form_info">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="biz_id" type="varchar(128)"  remarks="业务id">
                <constraints nullable="false"/>
            </column>
            <column name="planner_emp_no" type="varchar(32)"  remarks="计划员工号"/>
            <column name="old_reply_completion_date" type="datetime(0)"  remarks="原回复完工日期" />
            <column name="new_reply_completion_date" type="datetime(0)"  remarks="新回复完工日期" />
            <column name="remark_one" type="varchar(256)"  remarks="备注1"/>
            <column name="remark_two" type="varchar(256)"  remarks="备注2"/>
            <column name="packaging_or_final_assembly" type="varchar(50)"  remarks="包装/总装"/>
            <column name="remark_hazardous_info" type="varchar(256)"  remarks="风险物料信息备注"/>
            <column name="guest_seq" type="varchar(50)"  remarks="客人顺序"/>
            <column name="line_category_code" type="varchar(32)" remarks="产线列表编码"/>
            <column name="line_code" type="varchar(32)" remarks="线体编码"/>
            <column name="report_qty" type="int" defaultValue="0" remarks="报工数量"/>
            <column name="order_type" type="varchar(64)" remarks="订单类型"/>
            <column name="order_pcs_qty" type="int" defaultValue="0" remarks="订单只数"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_form_info" unique="false" indexName="idx_wps_form_info_biz_id">
            <column name="biz_id"/>
        </createIndex>
    </changeSet>



    <changeSet id="addColumn.lineUuid.wps_form_info.20250418" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="wps_form_info"/>
        </preConditions>
        <addColumn tableName="wps_form_info">
            <column name="line_uuid" type="varchar(128)"  remarks="线体uuid" afterColumn="line_code"/>
        </addColumn>
    </changeSet>


</databaseChangeLog>