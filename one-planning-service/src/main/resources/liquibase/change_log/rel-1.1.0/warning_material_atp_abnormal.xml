<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create_warning_material_atp_abnormal_table" author="zulu">
        <createTable tableName="warning_material_atp_abnormal">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="planned_online_time" type="date" remarks="计划上线时间">
                <constraints nullable="true"/>
            </column>
            <column name="online_quantity" type="int" defaultValue="0" remarks="上线数量">
                <constraints nullable="true" />
            </column>
            <column name="customer" type="varchar(255)" remarks="客户">
                <constraints nullable="true"/>
            </column>
            <column name="order_number" type="varchar(255)" remarks="销售订单/采购单号">
                <constraints nullable="true"/>
            </column>
            <column name="line_number" type="varchar(255)" remarks="行项目">
                <constraints nullable="true"/>
            </column>
            <column name="sales_order_number" type="varchar(255)" remarks="订单号">
                <constraints nullable="false"/>
            </column>
            <column name="material_id" type="varchar(255)" remarks="物料ID">
                <constraints nullable="true"/>
            </column>
            <column name="material_description" type="varchar(255)" remarks="物料描述">
                <constraints nullable="true"/>
            </column>
            <column name="shortage_id" type="varchar(255)" remarks="欠料ID">
                <constraints nullable="false"/>
            </column>
            <column name="shortage_description" type="varchar(255)" remarks="欠料描述">
                <constraints nullable="true"/>
            </column>
            <column name="light_color" type="tinyint" remarks="预警灯色">
                <constraints nullable="false"/>
            </column>
            <column name="responsible_person_reply" type="varchar(255)" remarks="相关责任人回复">
                <constraints nullable="true"/>
            </column>
            <column name="required_quantity" type="int" remarks="需求数量">
                <constraints nullable="true"/>
            </column>
            <column name="required_time" type="date" remarks="需求时间">
                <constraints nullable="true"/>
            </column>
            <column name="shortage_quantity" type="int" remarks="欠料数量">
                <constraints nullable="true"/>
            </column>
            <column name="gap_days" type="int" remarks="GAP天数">
                <constraints nullable="true"/>
            </column>
            <column name="supplier" type="varchar(255)" remarks="供应商">
                <constraints nullable="true"/>
            </column>
            <column name="purchase_delivery_date" type="date" remarks="采购交期">
                <constraints nullable="true"/>
            </column>
            <column name="purchase_in_charge" type="varchar(255)" remarks="采购负责人">
                <constraints nullable="true"/>
            </column>
            <column name="estimated_plan_date" type="date" remarks="预计可计划日期">
                <constraints nullable="true"/>
            </column>
            <column name="adjusted_online_time" type="date" remarks="调整后上线时间">
                <constraints nullable="true"/>
            </column>
            <column name="adjusted_gap_days" type="int" remarks="GAP天数">
                <constraints nullable="true"/>
            </column>
            <column name="affects_upper_level_plan" type="boolean" remarks="是否影响上下层计划">
                <constraints nullable="true"/>
            </column>
            <column name="impact_type" type="varchar(255)" remarks="影响类型">
                <constraints nullable="true"/>
            </column>
            <column name="lcp_no" type="varchar(255)" remarks="LCP流程编号">
                <constraints nullable="true"/>
            </column>
            <column name="flow_status" type="tinyint" remarks="流程状态">
                <constraints nullable="true"/>
            </column>
            <column name="material_atp_abnormal_type" type="tinyint" remarks="物料异常类型">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="alter.warning_material_atp_abnormal.enum" author="daishaokun1">
        <sql>
            alter table warning_material_atp_abnormal
                modify light_color varchar(32) not null comment '预警灯色';

            alter table warning_material_atp_abnormal
                modify flow_status varchar(32) null comment '流程状态';

            alter table warning_material_atp_abnormal
                modify material_atp_abnormal_type varchar(32) not null comment '物料异常类型';
        </sql>
    </changeSet>

    <changeSet id="recreate.table.warning_material_atp_abnormal" author="daishaokun1">
        <sql>
            DROP TABLE IF EXISTS `warning_material_atp_abnormal`;
            CREATE TABLE `warning_material_atp_abnormal`
            (
                `id`                         BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
                `planned_online_time`        DATE COMMENT '计划上线时间',
                `online_quantity`            INT COMMENT '上线数量',
                `customer`                   VARCHAR(255) COMMENT '客户',
                `order_number`               VARCHAR(255) COMMENT '销售订单/采购单号',
                `line_number`                VARCHAR(255) COMMENT '行项目',
                `sales_order_number`         VARCHAR(255) not null COMMENT '订单号',
                `material_id`                VARCHAR(255) COMMENT '物料ID',
                `material_description`       VARCHAR(255) COMMENT '物料描述',
                `shortage_id`                VARCHAR(255) not null COMMENT '欠料ID',
                `shortage_description`       VARCHAR(255) COMMENT '物料描述',
                `required_time`              DATE COMMENT '需求时间',
                `shortage_quantity`          INT COMMENT '欠料数量',
                `gap_days`                   INT COMMENT 'GAP天数',
                `supplier`                   VARCHAR(255) COMMENT '供应商 - 采购交期',
                `purchase_in_charge`         VARCHAR(255) COMMENT '采购人员',
                `group_in_charge`            VARCHAR(255) COMMENT '采购组',
                `light_color`                VARCHAR(32) not null COMMENT '预警灯色',
                `estimated_plan_date`        DATE COMMENT '预计可计划日期',
                `adjusted_online_time`       DATE COMMENT '调整后上线时间',
                `adjusted_gap_days`          INT COMMENT 'GAP天数',
                `responsible_person_reply`   VARCHAR(1024) COMMENT '相关责任人回复',
                `affects_upper_level_plan`   bit COMMENT '是否影响上下层计划',
                `impact_type`                VARCHAR(255) COMMENT '影响类型',
                `lcp_no`                     VARCHAR(255) COMMENT 'LCP流程编号',
                `flow_status`                VARCHAR(32) COMMENT '流程状态',
                `material_atp_abnormal_type` VARCHAR(32) not null COMMENT '物料异常类型'
            ) ENGINE = InnoDB
              DEFAULT CHARSET = utf8mb4 COMMENT ='物料ATP异常预警';
        </sql>
    </changeSet>

    <changeSet id="alter.warning_material_atp_abnormal.shortageGroup" author="daishaokun1">
        <sql>
            alter table warning_material_atp_abnormal
                modify order_number varchar (255) not null comment '生产订单号';

            alter table warning_material_atp_abnormal
                modify sales_order_number varchar (255) null comment '销售订单/采购单号';

            alter table warning_material_atp_abnormal
                add shortage_group varchar(32) null comment '物料组' after material_description;
        </sql>
    </changeSet>

    <changeSet id="alter.warning_material_atp_abnormal.type" author="daishaokun1">
        <sql>
            alter table warning_material_atp_abnormal
                modify required_time varchar(32) null comment '需求时间';

            alter table warning_material_atp_abnormal
                add available_date varchar(32) null comment '可得日期' after required_time;


        </sql>
    </changeSet>

    <changeSet id="alter.warning_material_atp_abnormal.character" author="daishaokun1">
        <sql>
            ALTER TABLE warning_material_atp_abnormal CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        </sql>
    </changeSet>

    <changeSet id="alter.warning_material_atp_abnormal.20250516" author="daishaokun1">
        <sql>
            CREATE INDEX idx_warning_type_status ON wps_order_plan_warning(warning_type, handle_status);
            CREATE INDEX idx_order_number_flow_status ON warning_material_atp_abnormal(order_number, flow_status);
            alter table warning_material_atp_abnormal
                change material_atp_abnormal_type abnormal_type varchar(32) not null comment '物料异常类型';
            alter table warning_material_atp_abnormal drop column lcp_no;
            alter table warning_material_atp_abnormal drop column flow_status;

        </sql>
    </changeSet>


    <changeSet id="alert.warning_material_atp_abnormal.factory_code" author="daishaokun1" failOnError="false">
        <sql>
            alter table warning_material_atp_abnormal
                add factory_code varchar(32) default '' not null comment '工厂编码',
                add created_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                add updated_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                add created_by     bigint(20) default 0 not null comment '创建者id',
                add updated_by     bigint(20) default 0 not null comment '更新人id';

        </sql>
    </changeSet>

    <changeSet id="dropColumn.warning_material_atp_abnormal.20250521" author="daishaokun1">
        <sql>
            alter table warning_material_atp_abnormal
            drop column shortage_group;

            alter table warning_material_atp_abnormal
            drop column shortage_id;

            alter table warning_material_atp_abnormal
            drop column shortage_description;

            alter table warning_material_atp_abnormal
            drop column required_time;

            alter table warning_material_atp_abnormal
            drop column available_date;

            alter table warning_material_atp_abnormal
            drop column shortage_quantity;

            alter table warning_material_atp_abnormal
            drop column gap_days;

            alter table warning_material_atp_abnormal
            drop column supplier;

            alter table warning_material_atp_abnormal
            drop column purchase_in_charge;

            alter table warning_material_atp_abnormal
            drop column group_in_charge;
        </sql>
    </changeSet>

    <changeSet id="addColumn.warning_material_atp_abnormal.available_date" author="daishaokun1">
    <sql>
        alter table warning_material_atp_abnormal
            add available_date date null comment '可得日期' after estimated_plan_date;
        alter table warning_material_atp_abnormal
            add adjusted_online_time_edited date null comment '调整后上线时间-前端编辑' after adjusted_online_time;
    </sql>
    </changeSet>

    <changeSet id="addColumn.warning_material_atp_abnormal.20250521" author="daishaokun1">
        <sql>
            alter table warning_material_atp_abnormal
                add order_data MEDIUMTEXT null comment '订单数据' after adjusted_online_time_edited;
            alter table warning_material_atp_abnormal
                add apt_data MEDIUMTEXT null comment 'Apt数据' after order_data;
        </sql>
    </changeSet>

    <changeSet id="addColumn.warning_material_atp_abnormal.20250522" author="daishaokun1">
        <sql>
            alter table warning_material_atp_abnormal_shortage
                add qitao_date date null comment '齐套日期' after available_date;

        </sql>
    </changeSet>

    <changeSet id="addColumn.warning_material_atp_abnormal.20250523" author="daishaokun1">
        <sql>
            alter table warning_material_atp_abnormal_shortage
                add latest_purchase_delivery_time date null comment '最新采购交期' after purchase_delivery_time;

            alter table warning_material_atp_abnormal_shortage
                add po_no varchar(128) null comment '采购单号' after latest_purchase_delivery_time;

            alter table warning_material_atp_abnormal_shortage
                add po_item_no varchar(128) null comment '采购行项目' after po_no;

            alter table warning_material_atp_abnormal_shortage
                add un_received_qty_num INT null comment '采购未收数量' after po_no;

            alter table warning_material_atp_abnormal_shortage
                add factory_code varchar(128) null comment '工厂编码' after po_no;


        </sql>
    </changeSet>

</databaseChangeLog>
