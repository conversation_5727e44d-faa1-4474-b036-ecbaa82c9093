<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create.table.user_factory_rel" author="zhang<PERSON>jian">
        <sql>
            CREATE TABLE if not exists `user_factory_rel`
            (
                `id`                   bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `user_id`              bigint      NOT NULL COMMENT '用户id',
                `factory_code`         varchar(32) NOT NULL COMMENT '工厂编码',
                `create_by`            bigint        DEFAULT NULL COMMENT '创建者id',
                `create_time`          datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `update_by`            bigint        DEFAULT NULL COMMENT '更新人id',
                `update_time`          datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                `store_location_codes` varchar(2048) DEFAULT NULL COMMENT '库位编号，多个用逗号分隔',
                PRIMARY KEY (`id`),
                KEY                    `user_factory_rel_user_id_IDX` (`user_id`) USING BTREE
            ) COMMENT='用户工厂关联表';
        </sql>
    </changeSet>


</databaseChangeLog>
