<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_order_warning_cfg.20250331" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_order_warning_cfg"/>
            </not>
        </preConditions>
        <createTable tableName="wps_order_warning_cfg">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码"/>
            <column name="warning_level" type="tinyint(3)" defaultValue="1"  remarks="告警等级：1预警 2告警">
                <constraints nullable="false"></constraints>
            </column>
            <column name="warning_type" type="varchar(64)"  remarks="预警类型">
                <constraints nullable="false"></constraints>
            </column>
            <column name="status" type="tinyint(3)" defaultValue="0" remarks="启用状态：0否 1是">
                <constraints nullable="false"></constraints>
            </column>
            <column name="param_json" type="varchar(256)"  remarks="参数字段"/>
            <column name="remark" type="varchar(1024)"  remarks="备注"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_order_warning_cfg" unique="false" indexName="idx_factory_code">
            <column name="factory_code"/>
            <column name="warning_level"/>
            <column name="status"/>
        </createIndex>
        <createIndex tableName="wps_order_warning_cfg" unique="false" indexName="idx_warning_type">
            <column name="warning_type"/>
        </createIndex>
    </changeSet>

    <changeSet id="alterTable.wps_order_warning_cfg.20250520.1" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <columnExists tableName="wps_order_warning_cfg" columnName="plan_type"/>
            </not>
        </preConditions>
        <addColumn tableName="wps_order_warning_cfg">
            <column name="plan_type" type="int" defaultValue="0" remarks="排产场景（0:整机，1:组件，2：部件）">
                <constraints nullable="false"></constraints>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>