<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.schedule_direction_cfg.20250514" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="schedule_direction_cfg"/>
            </not>
        </preConditions>
        <createTable tableName="schedule_direction_cfg">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码"/>
            <column name="config_type" type="tinyint(3)"  remarks="1产品编码2产品id商品id">
                <constraints nullable="false"/>
            </column>
            <column name="config_value" type="varchar(64)"  remarks="对应具体的值">
                <constraints nullable="false"/>
            </column>
            <column name="direction" type="tinyint(3)" defaultValue="-1"  remarks="排序方向 1 -1 -2"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="schedule_direction_cfg" unique="false" indexName="idx_config_type_value">
            <column name="config_type"/>
            <column name="config_value"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>