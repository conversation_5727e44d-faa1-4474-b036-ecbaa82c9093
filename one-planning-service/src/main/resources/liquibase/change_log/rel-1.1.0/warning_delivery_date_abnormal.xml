<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create_warning_material_atp_abnormal_table" author="zulu">
        <sql>
            drop table if exists warning_delivery_date_abnormal;
            create table if not exists warning_delivery_date_abnormal
            (
                id                                 bigint auto_increment comment '主键' primary key,
                planned_online_time                date         null comment '计划上线时间',
                customer                           varchar(255) null comment '客户',
                order_number                       varchar(255) not null comment '生产订单号',
                line_number                        varchar(255) null comment '行项目',
                sales_order_number                 varchar(255) null comment '销售订单/采购单号',
                material_id                        varchar(255) null comment '物料ID',
                material_description               varchar(255) null comment '物料描述',
                shortage_group                     varchar(32)  null comment '物料组',
                order_unit_qty                     int          null comment '订单数量',
                original_finish_time               date         null comment '原完工日期',
                calculate_finish_time              date         null comment '计算的允许最后完工日期 - SAP完工日期',
                est_finish_time                    date         null comment '预计最新完工时间-WPS排产的最后一天，可编辑',
                est_finish_time_edited             date         null comment '预计最新完工时间-WPS排产的最后一天，编辑后',
                gap_days                           int          null comment 'GAP天数',
                delivery_date_abnormal_impact_type varchar(255) null comment '交期异常影响类型',
                light_color                        varchar(32)  not null comment '预警灯色',
                adjusted_online_time               date         null comment '调整后上线时间，可编辑',
                adjusted_online_time_edited        date         null comment '调整后上线时间，编辑后',
                affects_upper_level_plan           bit          null comment '是否影响上下层计划，可编辑',
                impact_type                        varchar(255) null comment '影响类型，可编辑',
                product_type                       varchar(255) null comment '产品类型 (整机/组件/部件)',
                factory_code                       varchar(32)  null comment '工厂编码',
                created_by                         bigint(20) default 0 not null comment '创建者id',
                updated_by                         bigint(20) default 0 not null comment '更新人id',
                created_at                         TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at                         TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) comment '交期异常';
        </sql>
    </changeSet>


</databaseChangeLog>
