<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.line_change_cfg.20250519" author="chenyangbin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="line_change_cfg"/>
            </not>
        </preConditions>
        <createTable tableName="line_change_cfg">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编号">
                <constraints nullable="false"/>
            </column>
            <column name="hour" type="int"  remarks="小时">
                <constraints nullable="false"/>
            </column>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="line_change_cfg" unique="false" indexName="idx_factory_code">
            <column name="factory_code"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>