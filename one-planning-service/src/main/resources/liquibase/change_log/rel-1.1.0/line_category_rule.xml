<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.line_category_rule.20250219" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="line_category_rule"/>
            </not>
        </preConditions>
        <createTable tableName="line_category_rule">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="line_category_code" type="varchar(32)"  remarks="产线类别编码">
                <constraints nullable="false"/>
            </column>
            <column name="customer_code" type="varchar(64)"  remarks="客户编码"/>
            <column name="product_id" type="varchar(128)"  remarks="产品id"/>
            <column name="remark" type="varchar(128)"  remarks="备注"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="line_category_rule" unique="false" indexName="idx_category_code">
            <column name="line_category_code"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>