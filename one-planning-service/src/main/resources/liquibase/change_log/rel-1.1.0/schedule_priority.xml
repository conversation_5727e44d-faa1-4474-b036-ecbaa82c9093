<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.schedule_priority.20250221" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="schedule_priority"/>
            </not>
        </preConditions>
        <createTable tableName="schedule_priority">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="line_code" type="varchar(32)"  remarks="线体编码"/>
            <column name="priority_seq" type="tinyint(3)"  remarks="优先级：1、2/3"/>
            <column name="product_group_code" type="varchar(32)"  remarks="产品组"/>
            <column name="customer_code" type="varchar(128)"  remarks="客户编号"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="schedule_priority" unique="false" indexName="idx_line_code">
            <column name="line_code"/>
        </createIndex>
    </changeSet>

    <changeSet id="createIndex.product_group_code.schedule_priority.20250311" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <indexExists indexName="idx_product_group_code" tableName="schedule_priority"/>
            </not>
        </preConditions>
        <createIndex tableName="schedule_priority" unique="false" indexName="idx_product_group_code">
            <column name="product_group_code"/>
        </createIndex>
    </changeSet>


    <changeSet id="addColumn.lineUuid.schedule_priority.20250418" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="schedule_priority"/>
        </preConditions>
        <addColumn tableName="schedule_priority">
            <column name="line_uuid" type="varchar(128)"  remarks="线体uuid" afterColumn="line_code"/>
        </addColumn>
    </changeSet>


</databaseChangeLog>