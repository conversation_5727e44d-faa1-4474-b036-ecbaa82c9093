<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.workshop.20250220" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="workshop"/>
            </not>
        </preConditions>
        <createTable tableName="workshop">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="code" type="varchar(32)"  remarks="编码">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(128)"  remarks="名称">
                <constraints nullable="false"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码"/>
            <column name="sbu" type="varchar(128)"  remarks="sbu"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="workshop" unique="false" indexName="idx_name">
            <column name="name"/>
        </createIndex>
        <createIndex tableName="workshop" unique="true" indexName="idx_code">
            <column name="code"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>