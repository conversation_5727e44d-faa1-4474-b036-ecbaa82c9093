<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.factory_schedule_buffer.20250313" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="factory_schedule_buffer"/>
            </not>
        </preConditions>
        <createTable tableName="factory_schedule_buffer">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码">
                <constraints nullable="false"/>
            </column>
            <column name="business_type" type="tinyint(3)"  remarks="业务类型：1包材无版面2风险备库">
                <constraints nullable="false"/>
            </column>
            <column name="buffer_days" type="int" defaultValue="0" remarks="缓冲时间，要提前的时间天数">
                <constraints nullable="false"/>
            </column>
            <column name="remark" type="varchar(500)" remarks="备注"/>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="factory_schedule_buffer" unique="true" indexName="idx_factory_business">
            <column name="factory_code"/>
            <column name="business_type"/>
        </createIndex>
        <createIndex tableName="factory_schedule_buffer" unique="false" indexName="idx_business_type">
            <column name="business_type"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>