<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.planner_data_permission.20250221" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="planner_data_permission"/>
            </not>
        </preConditions>
        <createTable tableName="planner_data_permission">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="bigint(22)"  remarks="用户id">
                <constraints nullable="false"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码"/>
            <column name="workshop_code" type="varchar(32)"  remarks="车间编码"/>
            <column name="product_group_code" type="varchar(32)"  remarks="产品组编码"/>
            <column name="commodity_id" type="varchar(128)"  remarks="商品id"/>
            <column name="order_no" type="varchar(128)"  remarks="订单号"/>
            <column name="remark" type="varchar(128)"  remarks="备注"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
    </changeSet>


    <changeSet id="createIndex.user_id.planner_data_permission.20250311" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <indexExists indexName="idx_user_id" tableName="planner_data_permission"/>
            </not>
        </preConditions>
        <createIndex tableName="planner_data_permission" unique="false" indexName="idx_user_id">
            <column name="user_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>