<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_row_ext.20250303.1" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_row_ext"/>
            </not>
        </preConditions>
        <createTable tableName="wps_row_ext">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="biz_id" type="varchar(32)"  remarks="业务编码">
                <constraints nullable="false"/>
            </column>
            <column name="planner_emp_no" type="varchar(32)"  remarks="计划员工号"/>
            <column name="line_code" type="varchar(32)" remarks="线体编码"/>
            <column name="schedule_date" type="date(0)"  remarks="排产日期"/>
            <column name="schedule_year" type="smallint"  remarks="排产年份如2025"/>
            <column name="schedule_week" type="smallint"  remarks="开始排产自然周：如6,7"/>
            <column name="schedule_seq" type="bigint"  remarks="2025021200001 年月日+5位数"/>
            <column name="frozen_status" type="tinyint(3)" defaultValue="0" remarks="是否冻结产能：0否1是，默认是0">
                <constraints nullable="false"/>
            </column>
            <column name="est_production_finish_date" type="datetime(0)" remarks="预计生产完成时间"/>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_row_ext" unique="false" indexName="idx_biz_id_line_code">
            <column name="biz_id"/>
            <column name="line_code"/>
        </createIndex>
    </changeSet>


    <changeSet id="addColumn.lineUuid.wps_row_ext.20250418" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="wps_row_ext"/>
        </preConditions>
        <addColumn tableName="wps_row_ext">
            <column name="line_uuid" type="varchar(128)"  remarks="线体uuid" afterColumn="line_code"/>
        </addColumn>
    </changeSet>


    <changeSet id="addColumn.factory_code.wps_row_ext.20250418" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="wps_row_ext"/>
        </preConditions>
        <addColumn tableName="wps_row_ext">
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码" afterColumn="biz_id"/>
        </addColumn>
    </changeSet>

    <changeSet id="dropIndex.wps_row_ext.20250529.1" author="liurongfu" >
        <preConditions>
            <indexExists indexName="idx_biz_id_line_code" tableName="wps_row_ext"/>
        </preConditions>
        <dropIndex tableName="wps_row_ext" indexName="idx_biz_id_line_code"/>
    </changeSet>

    <changeSet id="addIndex.wps_row_ext.20250529.2" author="liurongfu">
        <createIndex tableName="wps_row_ext"  indexName="idx_wre_biz_id">
            <column name="biz_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>