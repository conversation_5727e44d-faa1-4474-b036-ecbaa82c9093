<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
   <!--来料检验异常主表-->
    <changeSet id="warning_income_material_atp_abnormal.202505171" author="hzp">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="warning_income_material_atp_abnormal"/>
            </not>
        </preConditions>
        <createTable tableName="warning_income_material_atp_abnormal">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="customer" type="varchar(255)" remarks="客户">
                <constraints nullable="true"/>
            </column>
            <column name="order_no" type="varchar(255)"  remarks="生产订单">
                <constraints nullable="true" />
            </column>
            <column name="product_id" type="varchar(255)" remarks="订单id（商品id）">
                <constraints nullable="true" />
            </column>
            <column name="material_desc" type="varchar(255)"  remarks="物料描述"/>
            <column name="plan_date" type="date(0)"  remarks="计划日期"/>
            <column name="plan_quantity" type="int"  remarks="计划数量"/>
            <column name="Impact_quantity" type="int"  remarks="影响数量"/>
        </createTable>
    </changeSet>
    <changeSet id="warning_income_material_po_atp_abnormal.202505171" author="hzp">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="warning_income_material_po_atp_abnormal"/>
            </not>
        </preConditions>
        <createTable tableName="warning_income_material_po_atp_abnormal">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="order_no" type="varchar(255)"  remarks="生产订单">
                <constraints nullable="true" />
            </column>
            <column name="shortage_material_id" type="varchar(255)" remarks="具体欠料ID">
                <constraints nullable="true"/>
            </column>
            <column name="purchase_po" type="varchar(255)"  remarks="采购PO">
                <constraints nullable="true" />
            </column>
            <column name="shortage_material_desc" type="varchar(255)"  remarks="具体欠料描述"/>
            <column name="check_quantity" type="int"  remarks="来料检验数量"/>
            <column name="Latest_demand_time" type="varchar(32)"  remarks="最晚需求时间"/>
            <column name="deal_result" type="varchar(255)"  remarks="处理结果"/>
            <column name="quality_inspectors" type="varchar(32)"  remarks="质检人员"/>
            <column name="quality_inspectors_gh" type="varchar(32)"  remarks="质检人员工号"/>
            <column name="purchaser" type="varchar(32)"  remarks="采购人员"/>
            <column name="purchaser_gh" type="varchar(32)"  remarks="采购人员工号"/>
            <column name="next_arrival_date" type="varchar(32)"  remarks="下一批到料日期"/>
            <column name="earliest_plan_date" type="varchar(32)"  remarks="最早可再计划日期"/>
            <column name="sure_replan_date" type="varchar(32)"  remarks="确定再计划日期"/>
            <column name="sfyxsxcjh" type="int"  remarks="是否影响上下层计划"/>
            <column name="impact_type" type="varchar(100)"  remarks="影响类型"/>
        </createTable>
    </changeSet>
    <changeSet id="addColumn.warning_income_material_po_atp_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_income_material_po_atp_abnormal" columnName="supply_name"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_income_material_po_atp_abnormal">
            <column name="supply_name" type="varchar(100)" remarks="供应商名称" />
        </addColumn>
    </changeSet>
    <changeSet id="addColumn.warning_income_material_atp_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_income_material_atp_abnormal" columnName="online_time"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_income_material_atp_abnormal">
            <column name="online_time" type="date(0)" remarks="上线时间" />
        </addColumn>
    </changeSet>
    <changeSet id="addColumn.warning_income_material_atp_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_income_material_atp_abnormal" columnName="factory"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_income_material_atp_abnormal">
            <column name="factory" type="varchar(100)" remarks="工厂" />
        </addColumn>
    </changeSet>
    <changeSet id="addColumn.warning_income_material_atp_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_income_material_atp_abnormal" columnName="Latest_demand_time"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_income_material_atp_abnormal">
            <column name="Latest_demand_time" type="date(0)" remarks="最晚需求时间" />
        </addColumn>
    </changeSet>
    <changeSet id="dropMultipleColumns.warning_income_material_po_atp_abnormal.***********" author="hongzhenping" failOnError="false">
        <preConditions onFail="MARK_RAN">
            <and>
                <columnExists tableName="warning_income_material_po_atp_abnormal" columnName="Latest_demand_time"/>
            </and>
        </preConditions>
        <dropColumn tableName="warning_income_material_po_atp_abnormal" columnName="Latest_demand_time"/>
    </changeSet>

    <changeSet id="modify.warning_income_material_po_atp_abnormal.length.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="warning_income_material_po_atp_abnormal" columnName="quality_inspectors"/>
            <columnExists tableName="warning_income_material_po_atp_abnormal" columnName="quality_inspectors_gh"/>
        </preConditions>
        <sql dbms="mysql">
            ALTER TABLE warning_income_material_po_atp_abnormal
                MODIFY COLUMN quality_inspectors VARCHAR(500) COMMENT '质检人员';
            ALTER TABLE warning_income_material_po_atp_abnormal
                MODIFY COLUMN quality_inspectors_gh VARCHAR(500) COMMENT '质检人员工号';
        </sql>
    </changeSet>
    <changeSet id="dropMultipleColumns.warning_income_material_atp_abnormal.***********" author="hongzhenping" failOnError="false">
        <preConditions onFail="MARK_RAN">
            <and>
                <columnExists tableName="warning_income_material_atp_abnormal" columnName="Impact_quantity"/>
            </and>
        </preConditions>
        <dropColumn tableName="warning_income_material_atp_abnormal" columnName="Impact_quantity"/>
    </changeSet>
    <changeSet id="addColumn.warning_income_material_po_atp_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_income_material_po_atp_abnormal" columnName="Impact_quantity"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_income_material_po_atp_abnormal">
            <column name="Impact_quantity" type="int" remarks="影响数量" />
        </addColumn>
    </changeSet>
    <changeSet id="modify.warning_income_material_po_atp_abnormal.type.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="warning_income_material_po_atp_abnormal" columnName="Impact_quantity"/>
        </preConditions>
        <sql dbms="mysql">
            ALTER TABLE warning_income_material_po_atp_abnormal
                MODIFY COLUMN Impact_quantity VARCHAR(100) COMMENT '影响数量';
        </sql>
    </changeSet>
    <changeSet id="addColumn.warning_income_material_atp_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_income_material_atp_abnormal" columnName="line_number"/>
                    <columnExists tableName="warning_income_material_atp_abnormal" columnName="sales_order_number"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_income_material_atp_abnormal">
            <column name="line_number" type="VARCHAR(100)" remarks="销售订单行项目" />
            <column name="sales_order_number" type="VARCHAR(100)" remarks="销售订单号" />
        </addColumn>
    </changeSet>
    <changeSet id="addIndex.warning_income_material_atp_abnormal.***********" author="hongzhenping">
        <createIndex tableName="warning_income_material_atp_abnormal" unique="false" indexName="idx_order_no">
            <column name="order_no"/>
        </createIndex>
    </changeSet>
    <changeSet id="addIndex.warning_income_material_po_atp_abnormal.***********" author="hongzhenping">
        <createIndex tableName="warning_income_material_po_atp_abnormal" unique="false" indexName="idx_order_no">
            <column name="order_no"/>
        </createIndex>
    </changeSet>
    <changeSet id="addColumn.warning_income_material_po_atp_abnormal.20250603" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_income_material_po_atp_abnormal" columnName="factory"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_income_material_po_atp_abnormal">
            <column name="factory" type="VARCHAR(32)" remarks="工厂" />
        </addColumn>
    </changeSet>
    <changeSet id="addColumn.warning_income_material_po_atp_abnormal.***********" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="warning_income_material_po_atp_abnormal" columnName="light_color"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="warning_income_material_po_atp_abnormal">
            <column name="light_color" type="VARCHAR(10)" remarks="灯色" />
        </addColumn>
    </changeSet>
</databaseChangeLog>
