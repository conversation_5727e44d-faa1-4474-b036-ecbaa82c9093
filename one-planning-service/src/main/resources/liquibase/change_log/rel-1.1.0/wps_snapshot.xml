<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_snapshot.20250508" author="chenyangbin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_snapshot"/>
            </not>
        </preConditions>
        <createTable tableName="wps_snapshot">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="biz_id" type="varchar(128)"  remarks="业务id">
                <constraints nullable="false"/>
            </column>
            <column name="planner_emp_no" type="varchar(32)"  remarks="计划员工号"/>
            <column name="plan_version_id" type="bigint(20)"  remarks="版本号ID"/>
            <column name="line_code" type="varchar(32)" remarks="线体编码"/>
            <column name="line_uuid" type="varchar(64)" remarks="线体ID"/>
            <column name="wps_data" type="text"  remarks="计划数据" />
            <column name="version" type="varchar(32)"  remarks="版本" />
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码"/>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_snapshot"  indexName="idx_biz_id">
            <column name="biz_id"/>
        </createIndex>
        <createIndex tableName="wps_snapshot"  indexName="idx_planner_emp_no_version">
            <column name="planner_emp_no"/>
            <column name="version"/>
        </createIndex>
        <createIndex tableName="wps_snapshot"  indexName="idx_factory_code">
            <column name="factory_code"/>
        </createIndex>
    </changeSet>
    <changeSet id="wps_snapshot_add_source_column" author="chenyangbin" failOnError="false">
        <addColumn tableName="wps_snapshot">
            <column name="source" type="int" remarks="版本来源1：保存  2：定时发布"></column>
        </addColumn>
        <setTableRemarks tableName="wps_snapshot" remarks="排产快照"></setTableRemarks>
        <dropIndex tableName="wps_snapshot" indexName="idx_planner_emp_no_version"></dropIndex>
        <createIndex tableName="wps_snapshot" indexName="ldx_planner_emp_no">
            <column name="version"></column>
        </createIndex>
        <createIndex tableName="wps_snapshot" indexName="ldx_factory_code">
            <column name="factory_code"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>