<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <!--船期临近未订舱异常主表-->
    <changeSet id="createTable.warning_ship_booking_urgent_abnormal.20250527.1" author="liurongfu">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="warning_ship_booking_urgent_abnormal"/>
            </not>
        </preConditions>
        <createTable tableName="warning_ship_booking_urgent_abnormal">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="factory_code" type="varchar(32)" remarks="工厂编号">
                <constraints nullable="false"/>
            </column>
            <column name="order_no" type="varchar(100)" remarks="生产订单号">
                <constraints nullable="false"/>
            </column>
            <column name="sell_order_no" type="varchar(100)" remarks="销售订单号">
                <constraints nullable="false"/>
            </column>
            <column name="row_item" type="varchar(100)" remarks="行项目"/>
            <column name="customer" type="varchar(200)" remarks="客户">
                <constraints nullable="false"/>
            </column>
            <column name="commodity_id" type="varchar(100)" remarks="商品ID"/>
            <column name="material_description" type="varchar(255)" remarks="物料描述"/>
            <column name="ship_schedule_date" type="date" remarks="船期日期"/>
            <column name="booking_status" type="varchar(50)" remarks="订舱状态"/>
            <column name="light_color" type="varchar(20)" remarks="预警灯色"/>
            <column name="order_quantity" type="int" remarks="订单数量"/>
            <column name="last_ship_schedule_date" type="date" remarks="最新船期日期"/>
            <column name="om_job_no" type="varchar(50)" remarks="OM工号"/>
            <column name="om_name" type="varchar(100)" remarks="OM姓名"/>
            <column name="outbound_delivery_order" type="varchar(100)" remarks="外向交货单"/>
            <column name="outbound_delivery_order_item" type="varchar(50)" remarks="外向交货单行号"/>
            <column name="create_time" type="datetime" remarks="创建时间"/>
            <column name="update_time" type="datetime" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="warning_ship_booking_urgent_abnormal" unique="false" indexName="idx_wsbua_order">
            <column name="factory_code"/>
            <column name="order_no"/>
            <column name="sell_order_no"/>
            <column name="row_item"/>
        </createIndex>
    </changeSet>

    <changeSet id="alterTable.warning_ship_booking_urgent_abnormal.20250527.2" author="liurongfu" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <columnExists tableName="warning_ship_booking_urgent_abnormal" columnName="create_by"/>
            </not>
        </preConditions>
        <addColumn tableName="warning_ship_booking_urgent_abnormal">
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
        </addColumn>
    </changeSet>

    <changeSet id="alterTable.warning_ship_booking_urgent_abnormal.20250527.3" author="liurongfu" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <columnExists tableName="warning_ship_booking_urgent_abnormal" columnName="update_by"/>
            </not>
        </preConditions>
        <addColumn tableName="warning_ship_booking_urgent_abnormal">
            <column name="update_by" type="bigint(20)" remarks="创建者id"/>
        </addColumn>
    </changeSet>
    <changeSet id="modifyColumn.warning_ship_booking_urgent_abnormal.20250603.1" author="liurongfu">
        <modifyDataType tableName="warning_ship_booking_urgent_abnormal" columnName="om_job_no" newDataType="varchar(100)"/>
    </changeSet>
    <changeSet id="dropColumn.warning_ship_booking_urgent_abnormal.20250603.2" author="liurongfu">
        <dropColumn tableName="warning_ship_booking_urgent_abnormal" columnName="om_job_no"/>
        <dropColumn tableName="warning_ship_booking_urgent_abnormal" columnName="om_name"/>
    </changeSet>
    <changeSet id="addColumn.warning_ship_booking_urgent_abnormal.20250603.3" author="liurongfu">
        <addColumn tableName="warning_ship_booking_urgent_abnormal">
            <column name="om_job_nos" type="text" remarks="OM工号"/>
            <column name="om_names" type="text" remarks="OM姓名"/>
        </addColumn>
    </changeSet>
    <changeSet id="dropColumn.warning_ship_booking_urgent_abnormal.20250603.4" author="liurongfu">
        <dropColumn tableName="warning_ship_booking_urgent_abnormal" columnName="last_ship_schedule_date"/>
    </changeSet>
</databaseChangeLog>
