<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.mps_week_plan_ext.20250220" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mps_week_plan_ext"/>
            </not>
        </preConditions>
        <createTable tableName="mps_week_plan_ext">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="biz_id" type="varchar(32)"  remarks="业务编码">
                <constraints nullable="false"/>
            </column>
            <column name="schedule_date" type="date(0)"  remarks="排产日期第一天"/>
            <column name="schedule_year" type="smallint"  remarks="排产年份如2025"/>
            <column name="schedule_week" type="smallint"  remarks="开始排产自然周：如6,7"/>
            <column name="week_type" type="tinyint(3)"  remarks="0上半周 1下班周"/>
            <column name="schedule_seq" type="bigint"  remarks="2025021200001 年月日+5位数"/>
            <column name="line_category_code" type="varchar(32)" remarks="产线列表编码"/>
            <column name="line_code" type="varchar(32)" remarks="线体编码"/>
            <column name="frozen_status" type="tinyint(3)" defaultValue="0" remarks="是否冻结产能：0否1是，默认是0">
                <constraints nullable="false"/>
            </column>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="mps_week_plan_ext" unique="false" indexName="idx_biz_id">
            <column name="biz_id"/>
        </createIndex>
    </changeSet>



</databaseChangeLog>