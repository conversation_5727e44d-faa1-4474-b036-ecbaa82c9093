<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.system_log.20250410" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="system_log"/>
            </not>
        </preConditions>
        <createTable tableName="system_log">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="log_type" type="varchar(32)"  remarks="日志类型:接口调用api 用户行为user">
                <constraints nullable="false"/>
            </column>
            <column name="business_name" type="varchar(128)"  remarks="业务名称"/>
            <column name="operation" type="varchar(256)"  remarks="操作内容"/>
            <column name="operator_id" type="bigint(20)"  remarks="操作人id"/>
            <column name="operator_name" type="varchar(128)"  remarks="操作人名称"/>
            <column name="request_data" type="text"  remarks="请求数据"/>
            <column name="response_data" type="longtext"  remarks="响应数据"/>
            <column name="status" type="tinyint(3)" defaultValue="1" remarks="0失败 1成功"/>
            <column name="error_message" type="text" remarks="错误信息"/>
            <column name="duration" type="int" defaultValue="0" remarks="耗时：毫秒"/>
            <column name="ip_address" type="varchar(50)" remarks="操作来源ip"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="system_log" unique="false" indexName="idx_business_name">
            <column name="business_name"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>