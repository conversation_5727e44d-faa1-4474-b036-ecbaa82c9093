<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.schedule_date_cfg.20250226.2" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="schedule_date_cfg"/>
            </not>
        </preConditions>
        <createTable tableName="schedule_date_cfg">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="factory_code" type="varchar(32)"  remarks="工厂编码"/>
            <column name="product_group_code" type="varchar(32)"  remarks="产品组"/>
            <column name="sell_order_no" type="varchar(64)"  remarks="销售订单号"/>
            <column name="before_start_days" type="smallint"  remarks="上线开始日期提前天数"/>
            <column name="before_end_days" type="smallint"  remarks="上线截止日期提前天数"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="schedule_date_cfg" unique="false" indexName="idx_factory_code">
            <column name="factory_code"/>
        </createIndex>
        <createIndex tableName="schedule_date_cfg" unique="false" indexName="idx_product_group_code">
            <column name="product_group_code"/>
        </createIndex>
        <createIndex tableName="schedule_date_cfg" unique="false" indexName="idx_sell_order_no">
            <column name="sell_order_no"/>
        </createIndex>
    </changeSet>

    <changeSet id="createIndex.sell_order_no.schedule_date_cfg.20250311" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <indexExists indexName="idx_sell_order_no" tableName="schedule_date_cfg"/>
            </not>
        </preConditions>
        <createIndex tableName="schedule_date_cfg" unique="false" indexName="idx_sell_order_no">
            <column name="sell_order_no"/>
        </createIndex>
    </changeSet>


    <changeSet id="addColumn.order_no.schedule_date_cfg.20250326" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
              <columnExists tableName="schedule_date_cfg" columnName="order_no"/>
            </not>
        </preConditions>
        <addColumn tableName="schedule_date_cfg">
            <column name="order_no" type="varchar(64)" remarks="订单号" afterColumn="sell_order_no"/>
        </addColumn>
        <createIndex tableName="schedule_date_cfg" unique="false" indexName="idx_order_no">
            <column name="order_no"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>