<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.line_uph.20250219" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="line_uph"/>
            </not>
        </preConditions>
        <createTable tableName="line_uph">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="config_type" type="tinyint(3)"  remarks="配置类型:1产线类型 2产线">
                <constraints nullable="false"/>
            </column>
            <column name="config_code" type="varchar(64)"  remarks="配置编码">
                <constraints nullable="false"/>
            </column>
            <column name="product_id" type="varchar(128)"  remarks="产品id"/>
            <column name="uph" type="int"  remarks="每小时产能"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="line_uph" unique="false" indexName="idx_config_code">
            <column name="config_code"/>
        </createIndex>
    </changeSet>


    <changeSet id="addColumn.lineUuid.line_uph.20250418" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="line_uph"/>
        </preConditions>
        <addColumn tableName="line_uph">
            <column name="line_uuid" type="varchar(128)"  remarks="线体uuid" afterColumn="id"/>
        </addColumn>
    </changeSet>

    <changeSet id="dropIndex.line_uph.20250529.1" author="liurongfu" >
        <preConditions>
            <indexExists indexName="idx_config_code" tableName="line_uph"/>
        </preConditions>
        <dropIndex tableName="line_uph" indexName="idx_config_code"/>
    </changeSet>

    <changeSet id="addIndex.line_uph.20250529.2" author="liurongfu">
        <createIndex tableName="line_uph"  indexName="idx_c_type_l_uuid_pid">
            <column name="config_type"/>
            <column name="line_uuid"/>
            <column name="product_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>