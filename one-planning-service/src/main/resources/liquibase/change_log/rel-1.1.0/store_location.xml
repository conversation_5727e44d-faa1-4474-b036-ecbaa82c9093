<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create.table.store_location" author="zhang<PERSON>jian">
        <sql>
            CREATE TABLE if not exists `store_location` (
              `id` bigint NOT NULL AUTO_INCREMENT,
              `code` varchar(32)  NOT NULL COMMENT '仓位编码',
              `name` varchar(64)  NOT NULL COMMENT '仓位名称',
              `factory_unit_code` varchar(32)  NOT NULL COMMENT '工厂编码',
              `create_by` bigint DEFAULT NULL COMMENT '创建者id',
              `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `update_by` bigint DEFAULT NULL COMMENT '更新者id',
              `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`id`),
              KEY `store_location_factory_unit_code_IDX` (`factory_unit_code`) USING BTREE
            ) COMMENT='库位管理表'
        </sql>
    </changeSet>


</databaseChangeLog>
