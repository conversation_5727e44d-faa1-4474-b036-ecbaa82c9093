<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet id="create.table.product_quality_history" author="zhang<PERSON><PERSON>an">
        <sql>
            CREATE TABLE if not exists `product_quality_history` (
             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             `update_by` bigint DEFAULT NULL COMMENT '更新人id',
             `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
             `create_by` bigint DEFAULT NULL COMMENT '创建者id',
             `wjcpx` varchar(100) DEFAULT NULL COMMENT '五级产品线',
            `cbyyfx` varchar(100) DEFAULT NULL COMMENT '初步原因分析',
            `sqrq` varchar(100) DEFAULT NULL COMMENT '申请日期',
            `csdc` varchar(100) DEFAULT NULL COMMENT '产生对策',
            `creatorcode` varchar(100) DEFAULT NULL COMMENT '申请人工号',
            `sqr` varchar(100) DEFAULT NULL COMMENT '申请人姓名',
            `zrrcode` varchar(100) DEFAULT NULL COMMENT '负责人工号',
            `zrr` varchar(100) DEFAULT NULL COMMENT '品质负责人',
            `xgyz` varchar(100) DEFAULT NULL COMMENT '效果验证',
            `lastcode` varchar(100) DEFAULT NULL COMMENT 'T812环节最后提交人员工号',
            `cpid` varchar(100) DEFAULT NULL COMMENT '产品id',
            `bh` varchar(100) DEFAULT NULL COMMENT '流程编号',
                PRIMARY KEY (`id`)
                ) COMMENT='产品异常历史'
        </sql>
    </changeSet>
</databaseChangeLog>
