<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.sys_param_cfg.20250521" author="chenyangbin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="sys_param_cfg"/>
            </not>
        </preConditions>
        <createTable tableName="sys_param_cfg">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="code" type="varchar(32)"  remarks="编码">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(128)"  remarks="名称">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="varchar(32)"  remarks="编码">
                <constraints nullable="false"/>
            </column>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="sys_param_cfg" unique="false" indexName="idx_code">
            <column name="code"/>
        </createIndex>
        <sql>
            INSERT INTO sys_param_cfg
                (code, name, value, create_by, create_time, update_by, update_time)
            VALUES('ORDER_EXPIRE_DAYS', '订单过期天数', '0', NULL, '2025-05-21 14:54:51', NULL, '2025-05-21 14:54:51');
        </sql>
    </changeSet>
</databaseChangeLog>