<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.product_group_rel.20250221" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="product_group_rel"/>
            </not>
        </preConditions>
        <createTable tableName="product_group_rel">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="product_group_code" type="varchar(32)"  remarks="产品组编码">
                <constraints nullable="false"/>
            </column>
            <column name="product_id" type="varchar(128)"  remarks="产品id">
                <constraints nullable="false"/>
            </column>
            <column name="product_name" type="varchar(128)"  remarks="产品名称"/>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="product_group_rel" unique="false" indexName="idx_group_code">
            <column name="product_group_code"/>
        </createIndex>
    </changeSet>
    <changeSet id="createIndexProductId.product_group_rel.20250311.1" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <indexExists indexName="idx_product_id" tableName="product_group_rel"/>
            </not>
        </preConditions>
        <createIndex tableName="product_group_rel" unique="false" indexName="idx_product_id">
            <column name="product_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>