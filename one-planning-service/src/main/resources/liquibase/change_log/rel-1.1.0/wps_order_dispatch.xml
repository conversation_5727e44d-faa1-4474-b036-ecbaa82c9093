<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_order_dispatch.20250303" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_order_dispatch"/>
            </not>
        </preConditions>
        <createTable tableName="wps_order_dispatch">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="order_no" type="varchar(64)"  remarks="订单编号">
                <constraints nullable="false"/>
            </column>
            <column name="planner_emp_no" type="varchar(32)"  remarks="计划员工号">
                <constraints nullable="false"/>
            </column>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_order_dispatch" unique="false" indexName="idx_order_no">
            <column name="order_no"/>
        </createIndex>
        <createIndex tableName="wps_order_dispatch" unique="false" indexName="idx_planner_emp_no">
            <column name="planner_emp_no"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>