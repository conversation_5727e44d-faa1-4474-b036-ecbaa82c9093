<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_custom_headers.20250327.1" author="liurongfu" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_custom_headers"/>
            </not>
        </preConditions>
        <createTable tableName="wps_custom_headers">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="code" type="varchar(100)"  remarks="excel自定义头编码">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="bigint(20)"  remarks="用户ID"/>
            <column name="factory_code" type="varchar(32)" remarks="工厂编码"/>
            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_custom_headers" unique="false" indexName="idx_wch_uid_fc">
            <column name="user_id"/>
            <column name="factory_code"/>
        </createIndex>
    </changeSet>

    <changeSet id="dropColumn.wps_custom_headers.20250327.2" author="liurongfu" failOnError="false">
        <preConditions>
            <columnExists tableName="wps_custom_headers" columnName="factory_code"/>
        </preConditions>
        <dropColumn tableName="wps_custom_headers" columnName="factory_code"/>
    </changeSet>

    <changeSet id="addColumn.wps_custom_headers.20250327.3" author="liurongfu" failOnError="false">
        <preConditions>
            <not>
                <columnExists tableName="wps_custom_headers" columnName="sort"/>
            </not>
        </preConditions>
        <addColumn tableName="wps_custom_headers">
            <column name="sort" type="int(11)" defaultValue="0" remarks="排序字段">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="dropIndex.wps_custom_headers.20250327.4" author="liurongfu" failOnError="false">
        <preConditions>
            <indexExists indexName="idx_wch_uid_fc"/>
        </preConditions>
        <dropIndex indexName="idx_wch_uid_fc" tableName="wps_custom_headers"/>
    </changeSet>

    <changeSet id="createIndex.wps_custom_headers.20250327.5" author="liurongfu" failOnError="false">
        <preConditions>
            <not>
                <indexExists indexName="idx_wch_uid"/>
            </not>
        </preConditions>
        <createIndex tableName="wps_custom_headers" unique="false" indexName="idx_wch_uid">
            <column name="user_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>