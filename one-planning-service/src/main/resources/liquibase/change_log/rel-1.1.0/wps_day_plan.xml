<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.wps_day_plan.20250228" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="wps_day_plan"/>
            </not>
        </preConditions>
        <createTable tableName="wps_day_plan">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="biz_id" type="varchar(128)"  remarks="业务id">
                <constraints nullable="false"/>
            </column>
            <column name="line_code" type="varchar(32)" remarks="产线列表编码"/>
            <column name="schedule_date" type="date(0)"  remarks="排产日期" />
            <column name="pre_plan_quantity" type="int"  remarks="预产数量"/>
            <column name="frozen_qty" type="int" remarks="冻结数"/>

            <column name="create_by" type="bigint(20)" remarks="创建者id"/>
            <column name="create_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="bigint(20)" remarks="更新人id"/>
            <column name="update_time" type="datetime(0)" defaultValueComputed="CURRENT_TIMESTAMP" remarks="更新时间"/>
        </createTable>
        <createIndex tableName="wps_day_plan" unique="false" indexName="idx_biz_id_line_code">
            <column name="biz_id"/>
            <column name="line_code"/>
        </createIndex>
    </changeSet>

    <changeSet id="alterTable.wps_day_plan.20250324" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <columnExists tableName="wps_day_plan" columnName="pre_plan_duration"/>
            </not>
        </preConditions>
        <addColumn tableName="wps_day_plan">
            <column name="pre_plan_duration" type="float" remarks="预排产时长"/>
        </addColumn>
    </changeSet>

    <changeSet id="alterTable.wps_day_plan.20250417" author="Daniel" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <columnExists tableName="wps_day_plan" columnName="full_biz_id"/>
            </not>
        </preConditions>
        <addColumn tableName="wps_day_plan">
            <column name="full_biz_id" type="varchar(128)" remarks="完整业务id"/>
        </addColumn>
    </changeSet>


    <changeSet id="addColumn.lineUuid.wps_day_plan.20250418" author="zhuangjiayin" failOnError="false">
        <preConditions onError="MARK_RAN">
            <tableExists tableName="wps_day_plan"/>
        </preConditions>
        <addColumn tableName="wps_day_plan">
            <column name="line_uuid" type="varchar(128)"  remarks="线体uuid" afterColumn="line_code"/>
        </addColumn>
    </changeSet>

    <changeSet id="addIndex.wps_day_plan.20250529.1" author="liurongfu">
        <createIndex tableName="wps_day_plan"  indexName="idx_wdp_schedule_date">
            <column name="schedule_date"/>
        </createIndex>
    </changeSet>

    <changeSet id="dropIndex.wps_day_plan.20250529.2" author="liurongfu" >
        <preConditions>
            <indexExists indexName="idx_biz_id_line_code" tableName="wps_day_plan"/>
        </preConditions>
        <dropIndex tableName="wps_day_plan" indexName="idx_biz_id_line_code"/>
    </changeSet>

    <changeSet id="addIndex.wps_day_plan.20250529.3" author="liurongfu">
        <createIndex tableName="wps_day_plan"  indexName="idx_wdp_biz_id_line_uuid">
            <column name="biz_id"/>
            <column name="line_uuid"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>