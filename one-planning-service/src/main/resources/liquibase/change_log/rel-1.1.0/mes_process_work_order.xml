<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <preConditions>
        <dbms type="mysql"/>
    </preConditions>

    <changeSet id="createTable.mes_process_work_order.**********" author="hongzhenping" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mes_process_work_order"/>
            </not>
        </preConditions>
        <createTable tableName="mes_process_work_order">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="scheduling_date" type="date(0)"  remarks="排产日期" >
                <constraints nullable="false"/>
            </column>
            <column name="material_off_shelf_date" type="date(0)"  remarks="物料下架日期" />
            <column name="online_date" type="date(0)"  remarks="上线日期" />
            <column name="scheduling_equipment" type="varchar(100)" remarks="排产设备"/>
            <column name="scheduling_mold" type="varchar(100)" remarks="排产模具"/>
            <column name="production_workshop" type="varchar(100)" remarks="生产车间"/>
            <column name="production_foreman" type="varchar(100)" remarks="生产课长"/>
            <column name="production_line" type="varchar(100)" remarks="生产线体"/>
            <column name="work_order_no" type="varchar(100)" remarks="工单号">
                 <constraints nullable="false"/>
            </column>
            <column name="order_no" type="varchar(100)" remarks="订单号">
                <constraints nullable="false"/>
            </column>
            <column name="material_id" type="varchar(100)" remarks="物料ID"/>
            <column name="associated_order_no" type="varchar(100)" remarks="关联订单号"/>
            <column name="planned_quantity" type="int"  remarks="计划数量"/>
            <column name="production_days" type="int"  remarks="在制天数"/>
            <column name="actual_input_quantity" type="int"  remarks="实际投入数量"/>
            <column name="actual_reporting_quantity" type="int"  remarks="实际报工数量"/>
            <column name="plan_type" type="int"  remarks="排产场景（0:整机，1:组件，2：部件）">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="createTable.mes_process_work_order_procedure.**********" author="hongzhenping" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mes_process_work_order_procedure"/>
            </not>
        </preConditions>
        <createTable tableName="mes_process_work_order_procedure">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="work_order_no" type="varchar(100)" remarks="工单号">
                <constraints nullable="false"/>
            </column>
            <column name="procedure_code" type="varchar(100)" remarks="工序编码">
                <constraints nullable="false"/>
            </column>
            <column name="procedure_name" type="varchar(100)" remarks="工序名称">
                <constraints nullable="false"/>
            </column>
            <column name="qty" type="int"  remarks="报工数量">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="createTable.mes_process_work_order_andon.**********" author="hongzhenping" failOnError="false">
        <preConditions onError="MARK_RAN">
            <not>
                <tableExists tableName="mes_process_work_order_andon"/>
            </not>
        </preConditions>
        <createTable tableName="mes_process_work_order_andon">
            <column name="id" type="bigint(20)" remarks="主键" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="work_order_no" type="varchar(100)" remarks="工单号">
                <constraints nullable="false"/>
            </column>
            <column name="exception_details" type="varchar(100)" remarks="异常事项">
                <constraints nullable="false"/>
            </column>
            <column name="is_production_stopped" type="int"  remarks="是否停线">
                <constraints nullable="false"/>
            </column>
            <column name="estimated_resolve_time" type="date(0)"  remarks="预计关闭时间" >
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="dropMultipleColumns.mes_process_work_order.20250519001" author="hongzhenping" failOnError="false">
        <preConditions onFail="MARK_RAN">
            <and>
                <columnExists tableName="mes_process_work_order" columnName="scheduling_equipment"/>
                <columnExists tableName="mes_process_work_order" columnName="scheduling_mold"/>
            </and>
        </preConditions>
        <dropColumn tableName="mes_process_work_order" columnName="scheduling_equipment"/>
        <dropColumn tableName="mes_process_work_order" columnName="scheduling_mold"/>
    </changeSet>

    <changeSet id="addMultipleColumns.mes_process_work_order_procedure.20250519002" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_process_work_order_procedure" columnName="scheduling_equipment"/>
                </not>
                <not>
                    <columnExists tableName="mes_process_work_order_procedure" columnName="scheduling_mold"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_process_work_order_procedure">
            <column name="scheduling_equipment" type="varchar(100)" remarks="排产设备">
            </column>
        </addColumn>
        <addColumn tableName="mes_process_work_order_procedure">
            <column name="scheduling_mold" type="varchar(100)" remarks="排产模具">
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="addColumn.mes_process_work_order_procedure.20250519001" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_process_work_order_procedure" columnName="sort_no"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_process_work_order_procedure">
            <column name="sort_no" type="int" remarks="排序" />
        </addColumn>
        <createIndex tableName="mes_process_work_order_procedure" indexName="idx_work_order_no">
            <column name="work_order_no"/>
        </createIndex>
    </changeSet>
    <changeSet id="addColumn.mes_process_work_order_andon.20250519001" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_process_work_order_andon" columnName="sort_no"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_process_work_order_andon">
            <column name="sort_no" type="int" remarks="排序" />
        </addColumn>
        <createIndex tableName="mes_process_work_order_andon" indexName="idx_work_order_no">
            <column name="work_order_no"/>
        </createIndex>
    </changeSet>
    <changeSet id="addIndex.mes_process_work_order.20250519001" author="hongzhenping">
        <createIndex tableName="mes_process_work_order"  indexName="idx_order_no">
            <column name="order_no"/>
        </createIndex>
    </changeSet>

    <changeSet id="addColumn.mes_process_work_order.20250519001" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_process_work_order" columnName="line_uuid"/>
                    <columnExists tableName="mes_process_work_order" columnName="line_code"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_process_work_order">
            <column name="line_uuid" type="varchar(100)" remarks="线体uuid" />
            <column name="line_code" type="varchar(100)" remarks="线体编号" />
        </addColumn>
    </changeSet>
    <changeSet id="addColumn.mes_process_work_order.20250521001" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_process_work_order" columnName="material_name"/>
                    <columnExists tableName="mes_process_work_order" columnName="factory_code"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_process_work_order">
            <column name="material_name" type="varchar(100)" remarks="物料名称" />
            <column name="factory_code" type="varchar(100)" remarks="工厂编号" />
        </addColumn>
    </changeSet>
    <changeSet id="addColumn.mes_process_work_order.20250523001" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_process_work_order" columnName="foreman_gh"/>
                    <columnExists tableName="mes_process_work_order" columnName="line_leader_gh"/>
                    <columnExists tableName="mes_process_work_order" columnName="line_leader_name"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_process_work_order">
            <column name="foreman_gh" type="varchar(100)" remarks="课长工号" />
            <column name="line_leader_gh" type="varchar(100)" remarks="线长工号" />
            <column name="line_leader_name" type="varchar(100)" remarks="线长名称" />
        </addColumn>
    </changeSet>
    <changeSet id="modify.mes_process_work_order.type.20250523001" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="mes_process_work_order" columnName="production_foreman"/>
        </preConditions>
        <sql dbms="mysql">
            ALTER TABLE mes_process_work_order
                MODIFY COLUMN production_foreman VARCHAR(100) COMMENT '生产课长名称';
        </sql>
    </changeSet>
    <changeSet id="addColumn.mes_process_work_order.20250528001" author="hongzhenping">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_process_work_order" columnName="planner"/>
                    <columnExists tableName="mes_process_work_order" columnName="planner_emp_no"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_process_work_order">
            <column name="planner" type="varchar(32)" remarks="计划人员-名称" />
            <column name="planner_emp_no" type="varchar(32)" remarks="计划人员-工号" />
        </addColumn>
    </changeSet>
    <changeSet id="addColumn.mes_process_work_order.202505281433" author="zhangzijian">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="mes_process_work_order" columnName="sap_status"/>
                    <columnExists tableName="mes_process_work_order" columnName="in_production_exception"/>
                    <columnExists tableName="mes_process_work_order" columnName="is_completed"/>
                    <columnExists tableName="mes_process_work_order" columnName="inbound_quantity"/>
                </not>
            </and>
        </preConditions>
        <addColumn tableName="mes_process_work_order">
            <column name="sap_status" type="varchar(255)" remarks="sap状态" />
            <column name="in_production_exception" type="int" defaultValue="0" remarks="是否产生在制异常"/>
            <column name="is_completed" type="int" defaultValue="0" remarks="是否完结" />
            <column name="inbound_quantity" type="int" remarks="已入库数量" />
        </addColumn>
    </changeSet>
</databaseChangeLog>