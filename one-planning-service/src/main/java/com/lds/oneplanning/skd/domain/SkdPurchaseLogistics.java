package com.lds.oneplanning.skd.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;


/**
 * 采购物流表(SkdPurchaseLogistics)实体类
 *
 * <AUTHOR>
 * @since 2025-05-23 11:49:44
 */

@Data
@TableName(value = "skd_purchase_logistics")
@ApiModel(value = "SkdPurchaseLogistics对象", description = "采购物流表")
public class SkdPurchaseLogistics implements Serializable {

    private static final long serialVersionUID = -53402927087859314L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "顶层单号")
    private String topNo;

    @ApiModelProperty(value = "采购单号")
    private String purchaseNo;

    /**
     * 销售单号
     */
    @ApiModelProperty(value = "销售单号")
    private String coverSoNo;

    /**
     * 销售单项次
     */
    @ApiModelProperty(value = "销售单项次")
    private String coverSoLine;

    @ApiModelProperty(value = "外向单")
    private String outboundDeliveryNo;

    @ApiModelProperty(value = "外向单项次")
    private String outboundDeliveryLine;

    @ApiModelProperty(value = "数量")
    private Double transitQty;

    @ApiModelProperty(value = "关联物流单号")
    private String logisticsNo;

    @ApiModelProperty(value = "运输方式")
    private String transportMode;

    @ApiModelProperty(value = "发出日期")
    private Date sendDate;

    @ApiModelProperty(value = "目前状态")
    private String status;

    @ApiModelProperty(value = "系统ETA")
    private Date eta;

    @ApiModelProperty(value = "到泰日期")
    private Date thaiArriveDate;

    @ApiModelProperty(value = "是否异常")
    private Integer isAbnormal;

    @ApiModelProperty(value = "距离需求入库时间")
    private Integer gapDays;

    @ApiModelProperty(value = "是否需要加急清关")
    private Integer isNeedUrgent;


    @ApiModelProperty(value = "子件物料需求时间")
    private Date materialNeedDate;

    @ApiModelProperty(value = "到泰入库日期")
    private Date thaiArriveInboundDate;

    @ApiModelProperty("预计到门")
    private Date planArriveFactoryDate;

    @ApiModelProperty("实际到门时间")
    private Date arriveFactoryDate;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SkdPurchaseLogistics)) return false;
        SkdPurchaseLogistics that = (SkdPurchaseLogistics) o;
        return Objects.equals(outboundDeliveryNo, that.outboundDeliveryNo) &&
               Objects.equals(outboundDeliveryLine, that.outboundDeliveryLine);
    }

    @Override
    public int hashCode() {
        return Objects.hash(outboundDeliveryNo, outboundDeliveryLine);
    }
}

