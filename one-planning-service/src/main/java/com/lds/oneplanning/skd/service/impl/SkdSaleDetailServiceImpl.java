package com.lds.oneplanning.skd.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.skd.domain.SkdSale;
import com.lds.oneplanning.skd.domain.SkdSaleDetail;
import com.lds.oneplanning.skd.domain.SkdSaleOrder;
import com.lds.oneplanning.skd.domain.bo.SkdSaleDetailBo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleDetailVo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleVo;
import com.lds.oneplanning.skd.mapper.SkdSaleDetailMapper;
import com.lds.oneplanning.skd.service.ISkdSaleDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * SKD销售单明细(SkdSaleDetailServiceImpl)服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
@AllArgsConstructor
public class SkdSaleDetailServiceImpl extends ServiceImpl<SkdSaleDetailMapper, SkdSaleDetail> implements ISkdSaleDetailService {
    @Override
    public List<SkdSaleDetailVo> queryList(SkdSaleDetailBo bo) {
        List<SkdSaleDetail> res = baseMapper.selectList(buildQueryWrapper(bo));
        if (CollUtil.isEmpty(res)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(res, SkdSaleDetailVo.class);
    }

    @Override
    public int deleteAll() {
        return baseMapper.delete(Wrappers.lambdaQuery());
    }

    @Override
    public int insertFromSelect() {
        return baseMapper.insertFromSelect();
    }

    public void saveBatch(List<SkdSaleDetail> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Set<String> coverSoIds = list.stream().map(SkdSaleDetail::getCoverSoId).collect(Collectors.toSet());
        Map<String, SkdSaleDetail> detailMap = queryByCoverSoIds(coverSoIds);
        list.forEach(item -> {
            if (detailMap.containsKey(item.getCoverSoId())) {
                item.setId(detailMap.get(item.getCoverSoId()).getId());
            }
        });
        super.saveOrUpdateBatch(list);
    }

    public Map<String, SkdSaleDetail> queryByCoverSoIds(Collection coverSoIds) {
        if (CollUtil.isEmpty(coverSoIds)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<SkdSaleDetail> lqw = Wrappers.lambdaQuery();
        lqw.in(SkdSaleDetail::getCoverSoId, coverSoIds);
        List<SkdSaleDetail> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(SkdSaleDetail::getCoverSoId, e -> e, (e1, e2) -> e2));
    }

    private LambdaQueryWrapper<SkdSaleDetail> buildQueryWrapper(SkdSaleDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SkdSaleDetail> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTopNo()), SkdSaleDetail::getTopNo, bo.getTopNo());
        lqw.like(StringUtils.isNotBlank(bo.getCoverSoId()), SkdSaleDetail::getCoverSoId, bo.getCoverSoId());
        lqw.like(StringUtils.isNotBlank(bo.getCoverSoNo()), SkdSaleDetail::getCoverSoNo, bo.getCoverSoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverSoLine()), SkdSaleDetail::getCoverSoLine, bo.getCoverSoLine());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerCode()), SkdSaleDetail::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductCode()), SkdSaleDetail::getProductCode, bo.getProductCode());
        lqw.between(params.get("beginPlanStartDate") != null && params.get("endPlanStartDate") != null,
                SkdSaleDetail::getPlanStartDate ,params.get("beginPlanStartDate"), params.get("endPlanStartDate"));
        lqw.between(params.get("beginPlanEndDate") != null && params.get("endPlanEndDate") != null,
                SkdSaleDetail::getPlanEndDate ,params.get("beginPlanEndDate"), params.get("endPlanEndDate"));
        lqw.between(params.get("beginShipTime") != null && params.get("endShipTime") != null,
                SkdSaleDetail::getShipTime ,params.get("beginShipTime"), params.get("endShipTime"));
        lqw.eq(StringUtils.isNotBlank(bo.getPlant()), SkdSaleDetail::getPlant, bo.getPlant());
        lqw.between(params.get("beginPlanReadyDate") != null && params.get("endPlanReadyDate") != null,
                SkdSaleDetail::getPlanReadyDate ,params.get("beginPlanReadyDate"), params.get("endPlanReadyDate"));
        lqw.between(params.get("beginSkdReadyDate") != null && params.get("endSkdReadyDate") != null,
                SkdSaleDetail::getSkdReadyDate ,params.get("beginSkdReadyDate"), params.get("endSkdReadyDate"));
        lqw.between(params.get("beginSkdReadyDateShip") != null && params.get("endSkdReadyDateShip") != null,
                SkdSaleDetail::getSkdReadyDateShip ,params.get("beginSkdReadyDateShip"), params.get("endSkdReadyDateShip"));
        lqw.between(params.get("beginSkdReadyDateNonShip") != null && params.get("endSkdReadyDateNonShip") != null,
                SkdSaleDetail::getSkdReadyDateNonShip ,params.get("beginSkdReadyDateNonShip"), params.get("endSkdReadyDateNonShip"));
        return lqw;
    }
}
