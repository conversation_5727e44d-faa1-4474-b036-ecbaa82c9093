package com.lds.oneplanning.skd.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 订单使用对象 skd_order_use
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("skd_order_use")
public class SkdOrderUse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单物料Id
     */
    private Long orderMaterialId;

    /**
     * 使用单Id
     */
    private String orderUseId;

    /**
     * 顶层单号
     */
    private String topNo;

    /**
     * 销售单号
     */
    private String coverSoNo;

    /**
     * 销售单项次
     */
    private String coverSoLine;

    /**
     * 计划单号
     */
    private String workNo;

    /**
     * 计划单项次
     */
    private String workLine;

    /**
     * 子件物料编码
     */
    private String materialItemNo;

    /**
     * 使用类型
     */
    private String useTypeName;

    /**
     * 使用单号
     */
    private String useNo;

    /**
     * 使用说明
     */
    private String useNote;

    /**
     * 供应商
     */
    private String supply;

    /**
     * 使用数量
     */
    private Double useQty;

    /**
     * 单位
     */
    private String unit;

    /**
     * 使用采购组
     */
    private String poGroup;

    /**
     * 使用采购组描述
     */
    private String poGroupR;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status;

    public String getKey() {
        return coverSoNo + coverSoLine + materialItemNo + useNo;
    }

}
