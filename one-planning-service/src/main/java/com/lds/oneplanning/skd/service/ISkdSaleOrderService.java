package com.lds.oneplanning.skd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.skd.domain.SkdSaleOrder;
import com.lds.oneplanning.skd.domain.bo.SkdSaleOrderBo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleOrderVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 销售订单Service接口
 *
 * <AUTHOR>
 * @since 2025-05-27
 */

public interface ISkdSaleOrderService extends IService<SkdSaleOrder> {
    List<SkdSaleOrderVo> queryList(SkdSaleOrderBo bo);

    void export(SkdSaleOrderBo bo, HttpServletResponse response);

    int deleteAll();

    int insertFromSelect();
}
