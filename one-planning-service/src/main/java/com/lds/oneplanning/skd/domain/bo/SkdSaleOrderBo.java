package com.lds.oneplanning.skd.domain.bo;

import com.lds.oneplanning.basedata.model.base.BasePageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 销售订单业务对象 skd_sale_order
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SkdSaleOrderBo extends BasePageEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 顶层单号
     */
    private String topNo;

    /**
     * 销售单Id
     */
    private String coverSoId;

    /**
     * 销售单号
     */
    private String coverSoNo;

    /**
     * 销售单号列表
     */
    private List<String> coverSoNoList;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 订单Id
     */
    private String orderId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 计划开始日期
     */
    private Date planStartDate;

    /**
     * 计划完工日期
     */
    private Date planEndDate;

    /**
     * 订单数量
     */
    private Double qty;

    /**
     * 船期
     */
    private Date shipTime;

    /**
     * 订单工厂
     */
    private String plant;

    /**
     * 需求最晚齐套时间
     */
    private Date planReadyDate;

    /**
     * skd可发货日期
     */
    private Date skdReadyDate;

    /**
     * 海运入库时间
     */
    private Date skdReadyDateShip;

    /**
     * 非海运入库时间
     */
    private Date skdReadyDateNonShip;

    /**
     * 陆运笔数
     */
    private int landNum;

    /**
     * 空运笔数
     */
    private int airNum;

    /**
     * 状态
     */
    private String status;

    /**
     * skd是否全发货
     */
    private int skdAllSend;

}
