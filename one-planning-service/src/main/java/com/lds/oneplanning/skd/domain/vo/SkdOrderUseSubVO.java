package com.lds.oneplanning.skd.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 使用订单子表(SkdOrderUseSub)视图实体类
 *
 * <AUTHOR>
 * @since 2025-05-24 17:24:15
 */
@Data
@ApiModel(value = "SkdOrderUseSub对象", description = "使用订单子表")
public class SkdOrderUseSubVO {

    private Long id;

    /**
     * 顶层单号
     */
    @ApiModelProperty(value = "顶层单号")
    private String topNo;

    /**
     * 使用单号(销售单号）
     */
    @ApiModelProperty(value = "使用单号(销售单号）")
    private String useNo;

    @ApiModelProperty(value = "交泰方式")
    private String toThaiWay;

    /**
     * 泰国需求发货日期（基于最佳运输方式）
     */
    @ApiModelProperty(value = "泰国需求发货日期（基于最佳运输方式）")
    private Date thaiSendDueDate;

    /**
     * 需要提拉天数
     */
    @ApiModelProperty(value = "需要提拉天数")
    private Integer pullDays;

    /**
     * 泰国需求发运方式
     */
    @ApiModelProperty(value = "泰国需求发运方式")
    private String thaiTransportMode;

    /**
     * 预计中国发货时间
     */
    @ApiModelProperty(value = "预计中国发货时间")
    private Date chineseSendDate;

    /**
     * 预计到泰入库日期
     */
    @ApiModelProperty(value = "预计到泰入库日期")
    private Date thaiArriveDate;

    /**
     * 是否能满足泰国上线需求（连空运也满足不了，要调整计划）
     */
    @ApiModelProperty(value = "是否能满足泰国上线需求（连空运也满足不了，要调整计划）")
    private Integer isReady;

    /**
     * 预计海运最快泰国入库日期
     */
    @ApiModelProperty(value = "预计海运最快泰国入库日期")
    private Date arriveDateBySea;

    /**
     * 泰国需求入库日期
     */
    @ApiModelProperty(value = "泰国需求入库日期")
    private Date thaiPlanDueDate;

    /**
     * 距离需求入库时间
     */
    @ApiModelProperty(value = "距离需求入库时间")
    private Integer gap;

    /**
     * 最新建议运输方式
     */
    @ApiModelProperty(value = "最新建议运输方式")
    private String latestSuggestTransportMode;

    /**
     * 是否可发状态
     */
    @ApiModelProperty(value = "是否可发状态")
    private Integer isCanSend;

    /**
     * 创建者id
     */
    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
