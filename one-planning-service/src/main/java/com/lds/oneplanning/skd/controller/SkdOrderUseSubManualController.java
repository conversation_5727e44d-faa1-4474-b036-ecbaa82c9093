package com.lds.oneplanning.skd.controller;



import com.lds.oneplanning.skd.domain.SkdOrderUseSubManual;
import com.lds.oneplanning.skd.domain.bo.SkdOrderUseSubManualBo;
import com.lds.oneplanning.skd.domain.bo.SkdOrderUseSubManualReq;
import com.lds.oneplanning.skd.service.SkdOrderUseSubManualService;
import com.lds.oneplanning.skd.utils.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 使用订单子表人工填写数据(SkdOrderUseSubManual)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-24 17:25:07
 */
@RestController
@RequestMapping("/skdOrderUseSubManuals")
@AllArgsConstructor
@Api(value = "使用订单子表人工填写数据", tags = "使用订单子表人工填写数据 相关接口（前端）")
public class SkdOrderUseSubManualController {
    private final SkdOrderUseSubManualService skdOrderUseSubManualService;
    @ApiOperation(value = "保存或更新使用订单子表人工填写数据")
    @PostMapping("/saveOrUpdateManual")
    public Boolean saveOrUpdateManual(@Valid @RequestBody SkdOrderUseSubManualReq req) {
        List<SkdOrderUseSubManual> skdOrderUseSubManuals= req.getTopNoAndUseNoList().stream().map(skdOrderUseSubManualBo -> {
            SkdOrderUseSubManual skdOrderUseSubManual = new SkdOrderUseSubManual();
            BeanUtils.copyProperties(skdOrderUseSubManualBo,skdOrderUseSubManual);
            skdOrderUseSubManual.setSendTime(req.getManualSendTime());
            skdOrderUseSubManual.setDistributionQty(req.getManualDistributionQty());
            skdOrderUseSubManual.setTransportMode(req.getManualTransportMode());
            skdOrderUseSubManual.setUseType(req.getUseType());
            return skdOrderUseSubManual;
        }).collect(Collectors.toList());

        return skdOrderUseSubManualService.batchSaveOrUpdateManual(skdOrderUseSubManuals);
    }

}
