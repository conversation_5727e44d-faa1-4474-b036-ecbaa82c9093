package com.lds.oneplanning.skd.constants;

import java.util.Arrays;
import java.util.stream.Collectors;

public enum AtpCompletionStatusEnum {

    INVENTORY_COMPLETE("1", "库存齐套"),
    INFORMATION_COMPLETE("2", "信息齐套"),
    FULLY_COMPLETE("3", "全部齐套"),
    INCOMPLETE("4", "不齐套"),
    UNKNOWN("99", "不齐套");

    private final String code;
    private final String description;

    AtpCompletionStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取对应的枚举值
     */
    public static AtpCompletionStatusEnum fromCode(String code) {
        for (AtpCompletionStatusEnum status : AtpCompletionStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
