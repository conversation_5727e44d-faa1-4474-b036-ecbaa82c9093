package com.lds.oneplanning.skd.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * VO类用于封装查询结果数据
 */
@ApiModel(description = "销售订单相关信息")
@Data
public class SalesOrderInfoVO {

    @ApiModelProperty("销售单号")
    @ExcelProperty("销售单号")
    private String salesOrderNo;

    @ApiModelProperty("销售单项次")
    @ExcelProperty("销售单项次")
    private String salesOrderLine;

    @ApiModelProperty("客户代码")
    @ExcelProperty("客户代码")
    private String customerCode;

    @ApiModelProperty("顶层单号")
    @ExcelProperty("顶层单号")
    private String topNo;

    @ApiModelProperty("类型")
    @ExcelProperty("类型")
    private String type;

    @ApiModelProperty("计划订单/生产订单")
    @ExcelProperty("计划订单/生产订单")
    private String planOrderNo;

    @ApiModelProperty("3317整单齐套可发货时间")
    @ExcelProperty("3317整单齐套可发货时间")
    private String fullSetReadyDate3317;

    @ApiModelProperty("整单预计到泰入库配套时间（仅海运）")
    @ExcelProperty("整单预计到泰入库配套时间（仅海运）")
    private String estThailandInboundFullSeaOnly;

    @ApiModelProperty("整单预计到泰入库配套时间（含陆运空运）")
    @ExcelProperty("整单预计到泰入库配套时间（含陆运空运）")
    private String estThailandInboundFullWithLandAir;

    @ApiModelProperty("整单预计配套时间（需求最晚齐套时间）")
    @ExcelProperty("整单预计配套时间（需求最晚齐套时间）")
    private String estFullTime;

    @ApiModelProperty("交泰方式")
    @ExcelProperty("交泰方式")
    private String deliveryToThailandMethod;

    @ApiModelProperty("订单数量")
    @ExcelProperty("订单数量")
    private Double orderQty;

    @ApiModelProperty("产品编码")
    @ExcelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("计划开始日期")
    @ExcelProperty("计划开始日期")
    private String planStartDate;

    @ApiModelProperty("计划完工日期")
    @ExcelProperty("计划完工日期")
    private String planFinishDate;

    @ApiModelProperty("订单交期")
    @ExcelProperty("订单交期")
    private String orderDeliveryDate;

    @ApiModelProperty("使用类型（泰国）")
    @ExcelProperty("使用类型（泰国）")
    private String useTypeThailand;

    @ApiModelProperty("使用单号（泰国）")
    @ExcelProperty("使用单号（泰国）")
    private String useNoThailand;

    @ApiModelProperty("销售单号（3317）")
    @ExcelProperty("销售单号（3317）")
    private String salesOrderNo3317;

    @ApiModelProperty("销售订单行项目(3317)")
    @ExcelProperty("销售订单行项目(3317)")
    private String salesOrderLine3317;

    @ApiModelProperty("cover_so_id")
    @ExcelProperty("cover_so_id")
    private String coverSoId;

    @ApiModelProperty("使用类型(3317)")
    @ExcelProperty("使用类型(3317)")
    private String useType3317;

    @ApiModelProperty("使用单号(3317)")
    @ExcelProperty("使用单号(3317)")
    private String useNo3317;

    @ApiModelProperty("供应商（3317下阶）")
    @ExcelProperty("供应商（3317下阶）")
    private String supplierSub3317;

    @ApiModelProperty("供应商中文描述")
    @ExcelProperty("供应商中文描述")
    private String supplierChineseDesc;

    @ApiModelProperty("采购订单编号")
    @ExcelProperty("采购订单编号")
    private String purchaseOrderNo;

    @ApiModelProperty("行项目")
    @ExcelProperty("行项目")
    private String poItem;

    @ApiModelProperty("采购员")
    @ExcelProperty("采购员")
    private String purchaser;

    @ApiModelProperty("物料编号")
    @ExcelProperty("物料编号")
    private String materialNo;

    @ApiModelProperty("物料名称")
    @ExcelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("采购订单数量")
    @ExcelProperty("采购订单数量")
    private Double purchaseOrderQty;

    @ApiModelProperty("采购未完")
    @ExcelProperty("采购未完")
    private Double onOrderQty;

    @ApiModelProperty("最新交期")
    @ExcelProperty("最新交期")
    private String latestDueDate;

    @ApiModelProperty("异常提示")
    @ExcelProperty("异常提示")
    private String errorMsg;

    @ApiModelProperty("使用数量")
    @ExcelProperty("使用数量")
    private Double useQty;

    @ApiModelProperty("需要提拉日期")
    @ExcelProperty("需要提拉日期")
    private String pullDate;

    @ApiModelProperty("泰国需求发货日期（基于最佳运输方式）")
    @ExcelProperty("泰国需求发货日期（基于最佳运输方式）")
    private String thailandShipDueDateBestMode;

    @ApiModelProperty("泰国需求发运方式")
    @ExcelProperty("泰国需求发运方式")
    private String thailandTransportMode;

    @ApiModelProperty("预计中国发货时间")
    @ExcelProperty("预计中国发货时间")
    private String chinaShipEstDate;

    @ApiModelProperty("预计到泰国入库日期")
    @ExcelProperty("预计到泰国入库日期")
    private String thailandInboundEstDate;

    @ApiModelProperty("最终建议运输方式")
    @ExcelProperty("最终建议运输方式")
    private String finalSuggestedTransportMode;

    @ApiModelProperty("分配数量（人工）")
    @ExcelProperty("分配数量（人工）")
    private Double manualDistributionQty;

    @ApiModelProperty("发货时间（人工）")
    @ExcelProperty("发货时间（人工）")
    private String manualSendTime;

    @ApiModelProperty("运输方式（人工）")
    @ExcelProperty("运输方式（人工）")
    private String manualTransportMode;

    @ApiModelProperty("船期")
    @ExcelProperty("船期")
    private String shipSchedule;

    @ApiModelProperty("订单工厂")
    @ExcelProperty("订单工厂")
    private String orderPlant;

    @ApiModelProperty("是否不齐套发货")
    @ExcelProperty("是否不齐套发货")
    private Integer isSendUnReady;

    @ApiModelProperty("物料提前期天数")
    @ExcelIgnore
    private Integer materialLeadTimeDays;

    /**
     * 计划单号
     */
    @ExcelIgnore
    private String workNo;

    /**
     * 计划单项次
     */
    @ExcelIgnore
    private String workLine;

    @ExcelIgnore
    private String itemNo;

}
