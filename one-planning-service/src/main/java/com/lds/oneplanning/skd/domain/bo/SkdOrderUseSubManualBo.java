package com.lds.oneplanning.skd.domain.bo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 使用订单子表人工填写数据(SkdOrderUseSubManual)数据传输对象
 *
 * <AUTHOR>
 * @since 2025-05-24 17:25:07
 */
@Data
@ApiModel(value = "SkdOrderUseSubManual对象", description = "使用订单子表人工填写数据")
public class SkdOrderUseSubManualBo {

    private Long id;

    /**
     * 顶层单号
     */
    @ApiModelProperty(value = "顶层单号")
    private String topNo;

    /**
     * 使用单号(销售单号）
     */
    @ApiModelProperty(value = "使用单号(销售单号）")
    private String useNo;

    /**
     * 分配数量
     */
    @ApiModelProperty(value = "分配数量")
    @JSONField(name = "manualDistributionQty")
    private Double manualDistributionQty;

    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    @JSONField(name = "manualSendTime")
    private Date manualSendTime;

    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    private String manualTransportMode;

    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    private Date dueDate;
}
