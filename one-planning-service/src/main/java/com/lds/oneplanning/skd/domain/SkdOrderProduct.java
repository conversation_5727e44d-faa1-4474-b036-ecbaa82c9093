package com.lds.oneplanning.skd.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 订单产品对象 skd_order_product
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("skd_order_product")
public class SkdOrderProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    /**
     * 顶层单号
     */
    private String topNo;

    /**
     * 销售单号
     */
    private String coverSoNo;

    /**
     * 销售单项次
     */
    private String coverSoLine;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 计划单号
     */
    private String workNo;

    /**
     * 计划单项次
     */
    private String workLine;

    /**
     * 计划开始日期
     */
    private Date planDate;

    /**
     * 计划完工日期
     */
    private Date plantFinish;

    /**
     * 订单交期
     */
    private Date orderDelivery;

    /**
     * 船期
     */
    private Date shipTime;

    /**
     * 订单数量
     */
    private Double qty;

    /**
     * 产品编码
     */
    private String itemNo;

    /**
     * 订单工厂
     */
    private String plant;

    /**
     * 类型
     */
    private String workTypeName;

    /**
     * 需求最晚齐套时间
     */
    private Date planQitaoDate;

    /**
     * 3317整单齐套可发货时间
     */
    private Date planAllQitaoDate;

    /**
     * 整单预计到泰入库配套时间（含陆运空运）
     */
    private Date allQitaoDate;

    /**
     * 本直齐套日期
     */
    private Date notSkdQitaoDate;

    /**
     * skd物料齐套日期
     */
    private Date skdQitaoDate;

    /**
     * 是否有非海运
     */
    private String hasNotShipTransport;

    /**
     * 整单预计到泰入库配套时间（仅海运）
     */
    private Date skdQitaoDateShip;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否不齐套发货
     */
    private Integer isCanSendUnReady;

    private Date updateTime;

    public String getKey() {
        return topNo + workNo + workLine;
    }

    // 同步时有些字段已经剔除，如果需要更新请记得增加进来！！！
    public int getHashCode() {
        return Objects.hash(topNo, coverSoNo, coverSoLine, customerCode, workNo, workLine, planDate, plantFinish, orderDelivery, shipTime, qty, itemNo, plant, workTypeName, planQitaoDate, status);
    }
}
