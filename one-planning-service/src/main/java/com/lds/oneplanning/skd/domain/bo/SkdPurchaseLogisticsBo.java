package com.lds.oneplanning.skd.domain.bo;

import com.lds.oneplanning.basedata.model.base.BasePageEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 采购物流表(SkdPurchaseLogistics)数据传输对象
 *
 * <AUTHOR>
 * @since 2025-05-23 11:49:47
 */
@Data
@ApiModel(value = "SkdPurchaseLogistics对象", description = "采购物流表")
public class SkdPurchaseLogisticsBo extends BasePageEntity {

    private Long id;

    @ApiModelProperty(value = "顶层单号")
    private String topNo;
    @ApiModelProperty(value = "顶层单号")
    private List<String> topNoList;

    /**
     * 销售单号
     */
    @ApiModelProperty(value = "销售单号")
    private String coverSoNo;

    /**
     * 计划单号
     */
    @ApiModelProperty(value = "计划单号")
    private String workNo;

    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    private String purchaseNo;

    /**
     * 外向单
     */
    @ApiModelProperty(value = "外向单")
    private String outboundDeliveryNo;

    /**
     * 外向单项次
     */
    @ApiModelProperty(value = "外向单项次")
    private String outboundDeliveryLine;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Double transitQty;

    /**
     * 关联物流单号
     */
    @ApiModelProperty(value = "关联物流单号")
    private String logisticsNo;

    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    private String transportMode;

    /**
     * 发出日期
     */
    @ApiModelProperty(value = "发出日期")
    private Date sendDate;

    /**
     * 目前状态
     */
    @ApiModelProperty(value = "目前状态")
    private String status;

    /**
     * 系统ETA
     */
    @ApiModelProperty(value = "系统ETA")
    private Date eta;

    /**
     * 到泰日期
     */
    @ApiModelProperty(value = "到泰日期")
    private Date thaiArriveDate;

    /**
     * 是否异常
     */
    @ApiModelProperty(value = "是否异常")
    private Integer isAbnormal;

    /**
     * 距离需求入库时间
     */
    @ApiModelProperty(value = "距离需求入库时间")
    private Integer gapDays;

    /**
     * 是否需要加急清关
     */
    @ApiModelProperty(value = "是否需要加急清关")
    private Integer isNeedUrgent;

}
