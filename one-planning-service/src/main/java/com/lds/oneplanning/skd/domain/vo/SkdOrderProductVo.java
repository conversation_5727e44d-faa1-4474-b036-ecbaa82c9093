package com.lds.oneplanning.skd.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.Date;

/**
 * 订单产品视图对象 skd_order_product
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SkdOrderProductVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 顶层单号
     */
    private String topNo;

    /**
     * 销售单号
     */
    private String coverSoNo;

    /**
     * 销售单项次
     */
    private String coverSoLine;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 计划单号
     */
    private String workNo;

    /**
     * 计划单项次
     */
    private String workLine;

    /**
     * 计划开始日期
     */
    private LocalDate planDate;

    /**
     * 计划完工日期
     */
    private LocalDate plantFinish;

    /**
     * 订单交期
     */
    private LocalDate orderDelivery;

    /**
     * 船期
     */
    private LocalDate shipTime;

    /**
     * 订单数量
     */
    private Double qty;

    /**
     * 产品编码
     */
    private String itemNo;

    /**
     * 订单工厂
     */
    private String plant;

    /**
     * 类型
     */
    private String workTypeName;

    /**
     * 需求最晚齐套时间
     */
    private LocalDate planQitaoDate;

    /**
     * 计划总齐套日期
     */
    private LocalDate planAllQitaoDate;

    /**
     * 总齐套日期
     */
    private LocalDate allQitaoDate;

    /**
     * 本直齐套日期
     */
    private LocalDate notSkdQitaoDate;

    /**
     * skd物料齐套日期
     */
    private LocalDate skdQitaoDate;

    /**
     * 是否有非海运
     */
    private String hasNotShipTransport;

    /**
     * skd海运物料齐套日期
     */
    private LocalDate skdQitaoDateShip;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否不齐套发货
     */
    private Integer isCanSendUnReady;

}
