package com.lds.oneplanning.skd.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.esb.datafetch.model.EsbShipmentData;
import com.lds.oneplanning.skd.domain.SkdOrderMaterial;
import com.lds.oneplanning.skd.domain.SkdOrderUseSub;
import com.lds.oneplanning.skd.domain.SkdOrderUseSubManual;
import com.lds.oneplanning.skd.domain.bo.SalesOrderInfoReq;
import com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 使用订单子表(SkdOrderUseSub)服务接口
 *
 * <AUTHOR>
 * @since 2025-05-24 17:24:34
 */
public interface SkdOrderUseSubService extends IService<SkdOrderUseSub> {
    /**
     * 计算并填充数据
     *
     * @param topNo
     * @param orderUseSubList
     * @param isAllIn 是否全部入3317库
     * @param isSendUnReady
     * @return
     */
    List<SkdOrderUseSub> calculateAndFillData(String topNo, boolean isSendUnReady,boolean isAllIn , List<SkdOrderUseSub> orderUseSubList);

    /**
     * 删除
     * @param topNo
     * @return
     */

    boolean deleteByTopNo(String topNo);

    /**
     * 同步数据
     */
    void syncData(String topNo, List<SkdOrderUseSub> orderUseSubList);

    /**
     * 填充数据
     * @param dueDate  需求日期
     * @param orderUseSub 使用订单子表数据
     * @param oldOrderUseSub 旧数据
     * @param manual 人工数据
     * @param shipmentData 船期数据
     * @return
     */
    SkdOrderUseSub fillData(LocalDate dueDate, SkdOrderUseSub orderUseSub, SkdOrderUseSub oldOrderUseSub, SkdOrderUseSubManual manual, EsbShipmentData shipmentData) ;

    SkdOrderUseSub getSubOrderUseSub(String topNo, String useNo,String useType);


    List<SalesOrderInfoVO> querySalesOrderInfo(SalesOrderInfoReq req);

    void exportSalesOrderInfo(SalesOrderInfoReq req, HttpServletResponse response);

    SkdOrderMaterial getSkdOrderMaterial(String topNo,String useNo);

    Page<SalesOrderInfoVO> querySalesOrderInfoPage(SalesOrderInfoReq req);

    List<SalesOrderInfoVO> aggregateQueriesSalesOrderInfo();
}
