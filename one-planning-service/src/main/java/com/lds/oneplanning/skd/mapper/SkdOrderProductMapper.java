package com.lds.oneplanning.skd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lds.oneplanning.skd.domain.SkdOrderProduct;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface SkdOrderProductMapper extends BaseMapper<SkdOrderProduct> {

    void batchInsertOrUpdate(@Param("list") List<SkdOrderProduct> skdOrderProduct);
}
