package com.lds.oneplanning.skd.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lds.oneplanning.skd.domain.SkdPurchaseLogistics;
import com.lds.oneplanning.skd.domain.bo.SkdPurchaseLogisticsBo;
import com.lds.oneplanning.skd.domain.vo.SkdPurchaseLogisticsVO;
import com.lds.oneplanning.skd.service.SkdPurchaseLogisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;

import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * 采购物流表(SkdPurchaseLogistics)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-23 11:49:51
 */
@RestController
@AllArgsConstructor
@RequestMapping("/skdPurchaseLogisticss")
@Api(value = "采购物流表", tags = "采购物流表 相关接口（前端）")
public class SkdPurchaseLogisticsController {
   private final SkdPurchaseLogisticsService skdPurchaseLogisticsService;

    @ApiOperation(value = "同步物流信息", notes = "同步物流信息")
    @GetMapping("/syncLogisticss")
    public boolean syncLogisticss(@ApiParam(value = "计划单号") @RequestParam("TOP_NO") String topNo,
                                  @ApiParam(value = "计划时间") @RequestParam("PLAN_DATE") String planDate,
                                  @ApiParam(value = "采购单号") @RequestParam("PO_NO") String poNo,
                                  @ApiParam(value = "外向单号") @RequestParam("OUTBOUND_DELIVERY_NO") String outboundDeliveryNo,
                                  @ApiParam(value = "外向单行号") @RequestParam("OUTBOUND_DELIVERY_LINE") String outboundDeliveryLine){
        SkdPurchaseLogistics skdPurchaseLogistics = new SkdPurchaseLogistics();
        skdPurchaseLogistics.setTopNo(topNo);
        skdPurchaseLogistics.setPurchaseNo(poNo);
        skdPurchaseLogistics.setOutboundDeliveryNo(outboundDeliveryNo);
        skdPurchaseLogistics.setOutboundDeliveryLine(outboundDeliveryLine);
        try {
            skdPurchaseLogistics.setMaterialNeedDate(new SimpleDateFormat("yyyy-MM-dd").parse(planDate));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        try {
            skdPurchaseLogisticsService.syncLogisticsInfo(topNo, Lists.newArrayList(skdPurchaseLogistics));
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "物流信息查询（分页）", notes = "物流信息查询（分页）")
    @PostMapping("/page")
    public Page<SkdPurchaseLogisticsVO> queryPage(@RequestBody SkdPurchaseLogisticsBo bo){
        return skdPurchaseLogisticsService.queryPage(bo);
    }
    @ApiOperation(value = "物流信息导出", notes = "物流信息导出")
    @PostMapping("/export")
    public void export(@RequestBody SkdPurchaseLogisticsBo bo, HttpServletResponse response){
        skdPurchaseLogisticsService.export(bo, response);
    }
}
