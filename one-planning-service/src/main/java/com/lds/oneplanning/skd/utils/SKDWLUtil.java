package com.lds.oneplanning.skd.utils;

import com.lds.oneplanning.skd.constants.Constant;
import com.lds.oneplanning.skd.constants.TransportMode;

import java.time.LocalDate;

/**
 * 在途物流
 *  1、到泰日期（最终取值）
 * 	2、距离需求入库时间
 * 	3、是否需要加急清关
 */
public class SKDWLUtil {

    /**
     * 需求日期（泰国需求日期、到泰需求日期、泰国需求入库日期、到泰需求入库日期、需求日期、物料需求日期、子件物料需求时间）
     */
    private LocalDate dueDate;
    /**
     * 系统ETA
     */
    private LocalDate eta;

    /**
     * 发货日期
     */
    private LocalDate shippingDate;

    /**
     *  运输方式
     */

    private TransportMode transportMode;

    private LocalDate planArriveFactoryDate;

    private LocalDate arriveFactoryDate;

    /**
     * 构造器
     * @param dueDate 需求日期
     * @param eta 系统ETA
     * @param planArriveFactoryDate 预计到门日期
     * @param arriveFactoryDate 实际到门时间
     * @param shippingDate 发货日期
     * @param transportMode 运输方式
     */
    public SKDWLUtil(LocalDate dueDate,LocalDate eta,LocalDate planArriveFactoryDate, LocalDate arriveFactoryDate, LocalDate shippingDate, TransportMode transportMode) {
        this.dueDate = dueDate;
        this.eta = eta;
        this.shippingDate = shippingDate;
        this.transportMode = transportMode;
        this.planArriveFactoryDate = planArriveFactoryDate;
        this.arriveFactoryDate = arriveFactoryDate;
    }

    /**
     * 泰国放假天数
     * @return
     */
    public int  getN(LocalDate date) {
        //TODO 需要获取泰国放假日期，并计算date到放假结束的天数
        return 0;
    }

    /**
     * 到泰日期（最终取值）
     * @Description 优先取逻辑1，逻辑1没有值，取逻辑2；
     * 1.取物流系统ETA维护值；
     * 2.物流系统若无值，直接按发货日期+固定物流周期+N（海运21天、陆运9天、空运5天）；N为中间涉及的假日"
     */
    public LocalDate getThaiArrivalDate(){
        if(eta != null){
            return eta;
        }
        if(shippingDate == null){
            return null;
        }
        if(transportMode == null){
            shippingDate = shippingDate.plusDays(21);
        }else{

            switch (transportMode){
                case SEA:
                    shippingDate = shippingDate.plusDays(21);
                case LAND:
                    shippingDate = shippingDate.plusDays(9);
                default:
                    shippingDate = shippingDate.plusDays(5);
            }
        }
        return shippingDate.plusDays(getN(shippingDate));
    }

    /**
     * 距离需求入库时间（天）
     * @Description 等于today()-子件物料需求时间
     */
    public int getPullDays(){
        return dueDate.getDayOfYear() - LocalDate.now().getDayOfYear() ;
    }

    /**
     * 是否需要加急清关
     * @Description 需求入库时间小于8天，则需要加急清关
     */
    public boolean isNeedUrgentClearance(){
        return Math.abs(getPullDays()) < 8;
    }
    /**
     * 到泰入库日期
     * @Description 物流到门日期不为空，取物流到门日期处理时间天；否则取物流到泰日期处理时间
     */
    public LocalDate getThaiArrivalInboundDate(){
        if(arriveFactoryDate != null){
            return arriveFactoryDate.plusDays(Constant.RECEIVE_PROCESS_TIME);
        }
        if(planArriveFactoryDate != null){
            return planArriveFactoryDate.plusDays(Constant.RECEIVE_PROCESS_TIME);
        }
        if(getThaiArrivalDate()== null){
            return null;
        }
        return getThaiArrivalDate().plusDays(Constant.RECEIVE_PROCESS_TIME);
    }
}
