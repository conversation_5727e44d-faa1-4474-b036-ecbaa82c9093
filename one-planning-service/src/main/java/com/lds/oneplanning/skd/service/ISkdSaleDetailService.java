package com.lds.oneplanning.skd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.skd.domain.SkdSaleDetail;
import com.lds.oneplanning.skd.domain.bo.SkdSaleDetailBo;
import com.lds.oneplanning.skd.domain.bo.SkdSaleOrderBo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleDetailVo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleOrderVo;

import java.util.List;

public interface ISkdSaleDetailService extends IService<SkdSaleDetail> {
    List<SkdSaleDetailVo> queryList(SkdSaleDetailBo bo);

    int deleteAll();

    int insertFromSelect();
}
