package com.lds.oneplanning.skd.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.skd.domain.SkdOrderMaterial;
import com.lds.oneplanning.skd.domain.bo.SkdOrderMaterialBo;
import com.lds.oneplanning.skd.domain.vo.SkdOrderMaterialVo;
import com.lds.oneplanning.skd.mapper.SkdOrderMaterialMapper;
import com.lds.oneplanning.skd.service.ISkdOrderMaterialService;
import com.lds.oneplanning.skd.service.ISkdOrderProductService;
import com.lds.wts.thtask.api.IThTaskLogisticsInfoApi;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class SkdOrderMaterialServiceImpl extends ServiceImpl<SkdOrderMaterialMapper, SkdOrderMaterial> implements ISkdOrderMaterialService {

    private final IThTaskLogisticsInfoApi thTaskLogisticsInfoApi;
    private final EsbDataFetchService esbDataFetchService;

    @Resource
    private ISkdOrderProductService orderProductService;
    @Override
    public Page<SkdOrderMaterialVo> queryPage(SkdOrderMaterialBo bo) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SkdOrderMaterial> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(bo.getPage(), bo.getPageSize());
        LambdaQueryWrapper<SkdOrderMaterial> lqw = buildQueryWrapper(bo);
        entityPage = baseMapper.selectPage(entityPage, lqw);

        Page<SkdOrderMaterialVo> resultPage = new Page<>(bo.getPage(), bo.getPageSize());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.copyProperties(entityPage, Page.class);
            List<SkdOrderMaterialVo> results = BeanUtil.copyToList(entityPage.getRecords(), SkdOrderMaterialVo.class);

            resultPage.setPageNum(bo.getPage());
            resultPage.setPageSize(bo.getPageSize());

            resultPage.setResult(results);
        }

        return resultPage;
    }

    private LambdaQueryWrapper<SkdOrderMaterial> buildQueryWrapper(SkdOrderMaterialBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SkdOrderMaterial> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTopNo()), SkdOrderMaterial::getTopNo, bo.getTopNo());
        lqw.like(StringUtils.isNotBlank(bo.getCoverSoId()), SkdOrderMaterial::getCoverSoId, bo.getCoverSoId());
        lqw.like(StringUtils.isNotBlank(bo.getCoverSoNo()), SkdOrderMaterial::getCoverSoId, bo.getCoverSoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverSoLine()), SkdOrderMaterial::getCoverSoLine, bo.getCoverSoLine());
        lqw.like(StringUtils.isNotBlank(bo.getWorkNo()), SkdOrderMaterial::getWorkNo, bo.getWorkNo());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkLine()), SkdOrderMaterial::getWorkLine, bo.getWorkLine());
        lqw.like(StringUtils.isNotBlank(bo.getMaterialItemNo()), SkdOrderMaterial::getMaterialItemNo, bo.getMaterialItemNo());
        lqw.like(StringUtils.isNotBlank(bo.getMaterialItemName()), SkdOrderMaterial::getMaterialItemName, bo.getMaterialItemName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeliveryPlant()), SkdOrderMaterial::getDeliveryPlant, bo.getDeliveryPlant());
        lqw.eq(StringUtils.isNotBlank(bo.getMstae()), SkdOrderMaterial::getMstae, bo.getMstae());
        lqw.eq(StringUtils.isNotBlank(bo.getItemGroup()), SkdOrderMaterial::getItemGroup, bo.getItemGroup());
        lqw.eq(StringUtils.isNotBlank(bo.getDefPlace()), SkdOrderMaterial::getDefPlace, bo.getDefPlace());
        lqw.like(StringUtils.isNotBlank(bo.getDefPlaceName()), SkdOrderMaterial::getDefPlaceName, bo.getDefPlaceName());
        lqw.eq(StringUtils.isNotBlank(bo.getPlace()), SkdOrderMaterial::getPlace, bo.getPlace());
        lqw.like(StringUtils.isNotBlank(bo.getPlaceName()), SkdOrderMaterial::getPlaceName, bo.getPlaceName());
        lqw.eq(StringUtils.isNotBlank(bo.getItemPackPlace()), SkdOrderMaterial::getItemPackPlace, bo.getItemPackPlace());
        lqw.eq(StringUtils.isNotBlank(bo.getPoType()), SkdOrderMaterial::getPoType, bo.getPoType());
        lqw.eq(bo.getMaterialNeedDate() != null, SkdOrderMaterial::getMaterialNeedDate, bo.getMaterialNeedDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SkdOrderMaterial::getStatus, bo.getStatus());
        lqw.gt(Objects.nonNull(bo.getRemainingQty()), SkdOrderMaterial::getRemainingQty, bo.getRemainingQty());

        return lqw;
    }

    @Override
    public List<SkdOrderMaterial> queryList(SkdOrderMaterialBo bo) {
        LambdaQueryWrapper<SkdOrderMaterial> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    @Override
    public void export(SkdOrderMaterialBo bo, HttpServletResponse response) {
        List<SkdOrderMaterial> list = this.queryList(bo);
        List<SkdOrderMaterialVo> results = BeanUtil.copyToList(list, SkdOrderMaterialVo.class);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=SKDOrderMaterial.xlsx");
        try {
            EasyExcel.write(response.getOutputStream(), SkdOrderMaterialVo.class)
                    .sheet("泰国物料需求表")
                    .doWrite(results);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public Map<String, SkdOrderMaterial> queryMapByTopNo(String topNo) {
        List<SkdOrderMaterial> list = baseMapper.selectList(Wrappers.<SkdOrderMaterial>lambdaQuery().eq(SkdOrderMaterial::getTopNo, topNo));
        Map<String, SkdOrderMaterial> map = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            map = list.stream().collect(Collectors.toMap(item -> item.getKey(), e -> e, (e1, e2) -> e2));
        }

        return map;
    }

    @Override
    public void saveBatchByTopNo(String topNo, Collection<SkdOrderMaterial> skdOrderMaterialData) {
        if (CollUtil.isEmpty(skdOrderMaterialData)) {
            return;
        }

        Map<String, SkdOrderMaterial> dbMap = this.queryMapByTopNo(topNo);

        Set<SkdOrderMaterial> addSet = new HashSet<>();
        Set<SkdOrderMaterial> updateSet = new HashSet<>();
        for (SkdOrderMaterial skdOrderMaterial : skdOrderMaterialData) {
            String key = skdOrderMaterial.getKey();
            if (dbMap.containsKey(key)) {
                if (skdOrderMaterial.hashCode() == dbMap.get(key).hashCode()) {
                    continue;
                }
                skdOrderMaterial.setId(dbMap.get(key).getId());
                updateSet.add(skdOrderMaterial);
            } else {
                addSet.add(skdOrderMaterial);
            }
        }

        if (CollUtil.isNotEmpty(updateSet)) {
            super.updateBatchById(updateSet);
        }

        if (CollUtil.isNotEmpty(addSet)) {
            super.saveBatch(addSet);
        }
    }

    @Override
    public int updateFromSelect() {
        return baseMapper.updateFromSelect();
    }

}
