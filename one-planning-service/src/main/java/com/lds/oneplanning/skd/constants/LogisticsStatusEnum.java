package com.lds.oneplanning.skd.constants;

public enum LogisticsStatusEnum {

    BOOKING(1, "订舱"),
    DEPARTURE(2, "出舱"),
    DEPARTED(3, "离港"),
    ARRIVED(4, "到港"),
    CUSTOMS_CLEARANCE(5, "清关"),
    DELIVERED(6, "到门");

    private final int code;
    private final String description;

    LogisticsStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取对应的枚举值
     */
    public static LogisticsStatusEnum fromCode(int code) {
        for (LogisticsStatusEnum status : LogisticsStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid logistics status code: " + code);
    }
}
