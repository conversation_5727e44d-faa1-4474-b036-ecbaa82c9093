package com.lds.oneplanning.skd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.skd.domain.SkdOrderMaterial;
import com.lds.oneplanning.skd.domain.bo.SkdOrderMaterialBo;
import com.lds.oneplanning.skd.domain.vo.SkdOrderMaterialVo;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 订单物料Service接口
 *
 * <AUTHOR>
 * @since 2025-05-22
 */

public interface ISkdOrderMaterialService extends IService<SkdOrderMaterial> {

    Page<SkdOrderMaterialVo> queryPage(SkdOrderMaterialBo bo);

    List<SkdOrderMaterial> queryList(SkdOrderMaterialBo bo);

    void export(SkdOrderMaterialBo bo, HttpServletResponse response);

    Map<String, SkdOrderMaterial> queryMapByTopNo(String topNo);

    void saveBatchByTopNo(String topNo, Collection<SkdOrderMaterial> collection);

    /**
     * 汇总更新使用数量等
     * @return
     */
    int updateFromSelect();

}
