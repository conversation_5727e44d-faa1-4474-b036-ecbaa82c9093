package com.lds.oneplanning.skd.constants;

import com.google.common.collect.Sets;

import java.util.Set;

public class Constant {
    /**
     * SAP已完成订单状态集合
     */
    public static final Set<String>  COMPLETE_ORDER_STATUS = Sets.newHashSet("I0045", "I0046", "IFO07", "I0076", "I0012");

    /**
     * SAP删除订单状态集合
     */
    public static final Set<String>  DELETE_ORDER_STATUS = Sets.newHashSet("I0013");

    /**
     * 收货处理时间(天
     */
    public  static final int RECEIVE_PROCESS_TIME = 2;
    /**
     * 不接收不齐套发货
     */
    public static final int NO_SEND_UNREADY = 1;
    /*
     * 接收不齐套发货
     */
    public static final int CAN_SEND_UNREADY = 1;
}