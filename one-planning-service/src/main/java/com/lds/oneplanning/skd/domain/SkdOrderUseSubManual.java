package com.lds.oneplanning.skd.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 使用订单子表人工填写数据(SkdOrderUseSubManual)实体类
 *
 * <AUTHOR>
 * @since 2025-05-24 17:25:07
 */
@Data
@TableName(value = "skd_order_use_sub_manual")
@ApiModel(value = "SkdOrderUseSubManual对象", description = "使用订单子表人工填写数据")
public class SkdOrderUseSubManual implements Serializable {

    private static final long serialVersionUID = -98408076865200626L;


    private Long id;

    @ApiModelProperty(value = "顶层单号")
    private String topNo;

    @ApiModelProperty(value = "使用单号(销售单号）")
    private String useNo;

    @ApiModelProperty(value = "分配数量")
    private Double distributionQty;

    @ApiModelProperty(value = "发货时间")
    private Date sendTime;

    @ApiModelProperty(value = "运输方式")
    private String transportMode;

    @ApiModelProperty(value = "使用类型")
    private String useType;

    public String getUniqueKey() {
        return new  StringBuilder().append(topNo).append(useNo).append(useType).toString();
    }
}
