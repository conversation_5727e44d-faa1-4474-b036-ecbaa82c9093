package com.lds.oneplanning.skd.controller;



import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.skd.domain.bo.SalesOrderInfoReq;
import com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO;
import com.lds.oneplanning.skd.service.SkdOrderUseSubService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 使用订单子表(SkdOrderUseSub)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-24 17:24:35
 */
@RestController
@RequestMapping("/skdOrderUseSubs")
@Api(value = "使用订单子表", tags = "使用订单子表 相关接口（前端）")
@AllArgsConstructor
public class SkdOrderUseSubController {
    private final SkdOrderUseSubService skdOrderUseSubService;

    @ApiOperation(value = "查询销售订单相关信息", notes = "查询销售订单相关信息")
    @PostMapping("/querySalesOrderInfo")
    public List<SalesOrderInfoVO> querySalesOrderInfo(@RequestBody SalesOrderInfoReq req){

        return skdOrderUseSubService.querySalesOrderInfo(req);
    }
    @ApiOperation(value = "查询销售订单相关信息（分页）", notes = "查询销售订单相关信息")
    @PostMapping("/querySalesOrderInfoPage")
    public Page<SalesOrderInfoVO> querySalesOrderInfoPage(@RequestBody SalesOrderInfoReq req){
        return skdOrderUseSubService.querySalesOrderInfoPage(req);
    }

    @ApiOperation(value = "导出SKD订单相关信息并生成excel", notes = "SKD订单相关信息并生成excel")
    @PostMapping("/exportSalesOrderInfo")
    public void exportSalesOrderInfo(@RequestBody SalesOrderInfoReq req, HttpServletResponse response){

        skdOrderUseSubService.exportSalesOrderInfo(req,response);
    }
}
