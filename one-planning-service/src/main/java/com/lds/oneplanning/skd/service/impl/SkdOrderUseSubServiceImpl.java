package com.lds.oneplanning.skd.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.iot.common.util.SpringUtil;
import com.lds.oneplanning.esb.datafetch.model.EsbShipmentData;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.skd.constants.Constant;
import com.lds.oneplanning.skd.constants.TransportMode;
import com.lds.oneplanning.skd.domain.SkdOrderMaterial;
import com.lds.oneplanning.skd.domain.SkdOrderUseSub;
import com.lds.oneplanning.skd.domain.SkdOrderUseSubManual;
import com.lds.oneplanning.skd.domain.bo.SalesOrderInfoReq;
import com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO;
import com.lds.oneplanning.skd.mapper.SkdOrderUseSubMapper;
import com.lds.oneplanning.skd.service.SkdOrderUseSubManualService;
import com.lds.oneplanning.skd.service.SkdOrderUseSubService;
import com.lds.oneplanning.skd.utils.DateUtil;
import com.lds.oneplanning.skd.utils.SKDFHUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 使用订单子表(SkdOrderUseSub)服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-24 17:24:34
 */
@Service
@Slf4j
@AllArgsConstructor
public class SkdOrderUseSubServiceImpl extends ServiceImpl<SkdOrderUseSubMapper,SkdOrderUseSub> implements SkdOrderUseSubService {

    private final IEsbDataFetchService esbService;
    @Override
    public List<SkdOrderUseSub> calculateAndFillData(String topNo, boolean isSendUnReady,boolean isAllIn,  List<SkdOrderUseSub> orderUseSubList) {
        if(CollectionUtils.isEmpty(orderUseSubList)){
            return orderUseSubList;
        }
        //数据库获取旧数据
        List<SkdOrderUseSub> oldList = queryListByTopNo(topNo);
        Map<String, SkdOrderUseSub> oldMap = oldList.stream().filter(orderUseSub -> StringUtils.hasText(orderUseSub.getUseType())).collect(Collectors.toMap(SkdOrderUseSub::getUniqueKey, orderUseSub -> orderUseSub, (s, s2) -> s2));
       //获取人工填写数据
        List<SkdOrderUseSubManual> manualList =SpringUtil.getBean(SkdOrderUseSubManualService.class).queryList(orderUseSubList.stream().filter(orderUseSub -> StringUtils.hasText(orderUseSub.getUseNo())).map(SkdOrderUseSub::getUseNo).collect(Collectors.toList()));
        Map<String, SkdOrderUseSubManual> manualMap = manualList.stream().filter(manual -> StringUtils.hasText(manual.getUseType())).collect(Collectors.toMap(SkdOrderUseSubManual::getUniqueKey, manual -> manual, (s, s2) -> s2));
        //查询船期信息
        List<EsbShipmentData> data = orderUseSubList.stream().filter(orderUseSub -> StringUtils.hasText(orderUseSub.getOutboundDeliveryNo())
                && StringUtils.hasText(orderUseSub.getOutboundDeliveryLine())).map(orderUseSub -> {
            EsbShipmentData esbShipmentData = new EsbShipmentData();
            esbShipmentData.setVbeln(orderUseSub.getOutboundDeliveryNo());
            esbShipmentData.setPosnr(orderUseSub.getOutboundDeliveryLine());
            return esbShipmentData;
        }).collect(Collectors.toList());
        List<EsbShipmentData> esbShipmentData = esbService.getShipmentDateData(data);
        Map<String, EsbShipmentData> esbShipmentDataMap = esbShipmentData.stream().collect(Collectors.toMap(item->item.getVbeln()+item.getPosnr(), item -> item,  (s, s2) -> s2));
        return orderUseSubList.stream().peek(orderUseSub -> {
                SkdOrderUseSub oldOrderUseSub = oldMap.get(orderUseSub.getUniqueKey());
                SkdOrderUseSubManual manual = manualMap.get(orderUseSub.getUniqueKey());
                EsbShipmentData shipmentData = esbShipmentDataMap.get(orderUseSub.getOutboundDeliveryNo()+orderUseSub.getOutboundDeliveryLine());
                fillData(DateUtil.getLocalDate(orderUseSub.getMaterialNeedDate()), orderUseSub, oldOrderUseSub, manual, shipmentData);
        }).collect(Collectors.toList());
    }

    public SkdOrderUseSub fillData(LocalDate dueDate, SkdOrderUseSub orderUseSub, SkdOrderUseSub oldOrderUseSub, SkdOrderUseSubManual manual, EsbShipmentData shipmentData) {
        TransportMode transportMode = TransportMode.SEA;
        if(oldOrderUseSub != null){
            transportMode = TransportMode.fromCode(oldOrderUseSub.getLatestSuggestTransportMode());
        }
        LocalDate shippingTimeManual = null;
        TransportMode transportModeManual = null;
        if(manual != null){
            shippingTimeManual = DateUtil.getLocalDate(manual.getSendTime());
            transportModeManual = TransportMode.fromCode(manual.getTransportMode());
        }
        boolean isLoaded = false;
        if(shipmentData != null){
            isLoaded = "已放舱".equals(shipmentData.getZState());
        }
        SKDFHUtil skdFHUtil = new SKDFHUtil(dueDate, DateUtil.getLocalDate(orderUseSub.getOriginalLatestDueDate()),
                Constant.RECEIVE_PROCESS_TIME,transportMode,shippingTimeManual,transportModeManual,isLoaded,
                orderUseSub.getUseType(),orderUseSub.getFactoryCode(),orderUseSub.getMaterialLeadTimeDays());
        orderUseSub.setThaiSendDueDate(DateUtil.getDate(skdFHUtil.getThaiSendDueDate()));
        orderUseSub.setPullDays(skdFHUtil.getPullDays());
        orderUseSub.setThaiArriveDate(DateUtil.getDate(skdFHUtil.getThaiArrivalDate()));
        orderUseSub.setArriveDateBySea(DateUtil.getDate(skdFHUtil.getThaiArrivalDateBySEA()));
        orderUseSub.setThaiTransportMode(skdFHUtil.getTransportMode().getCode());
        orderUseSub.setThaiPlanDueDate(DateUtil.getDate(dueDate));
        orderUseSub.setChineseSendDate(DateUtil.getDate(skdFHUtil.getChinaShippingTime()));
        orderUseSub.setGap(skdFHUtil.getGap());
        orderUseSub.setLatestSuggestTransportMode(skdFHUtil.getLatestTransportMode().getCode());
        orderUseSub.setToThaiWay(skdFHUtil.getToThaiWay());
        orderUseSub.setOrigTransportMode(skdFHUtil.getOriginalTransportMode().getCode());
        orderUseSub.setIsExpeditedShipping(skdFHUtil.isNewSpecialTransportMode()?1:0);
        orderUseSub.setOriginalLatestDueDate(orderUseSub.getOriginalLatestDueDate());
        orderUseSub.setLatestDueDate(DateUtil.getDate(skdFHUtil.getCalculateLatestDueDate()));
        //     orderUseSub.setIsCanSend(skdFHUtil.getStatus(skdFHUtil.getToThaiWay(),dueDate,isAllIn,isSendUnReady));
        return orderUseSub;
    }

    @Override
    public SkdOrderUseSub getSubOrderUseSub(String topNo, String useNo,String useType) {
        LambdaQueryWrapper<SkdOrderUseSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SkdOrderUseSub::getUseNo,useNo);
        queryWrapper.eq(SkdOrderUseSub::getTopNo,topNo);
        queryWrapper.eq(SkdOrderUseSub::getUseType,useType);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<SalesOrderInfoVO> querySalesOrderInfo(SalesOrderInfoReq req) {
        List<SalesOrderInfoVO> resList = baseMapper.querySalesOrderInfo(req);
        return dataConversion(resList,false);
    }

    @NotNull
    private static List<SalesOrderInfoVO> dataConversion(List<SalesOrderInfoVO> resList,boolean isCalculateDate) {
        return resList.stream().peek(item -> {
            if (StringUtils.hasLength(item.getUseNo3317())) {
                String[] arr = item.getUseNo3317().split("\\_", 2);
                item.setPurchaseOrderNo(arr[0]);
                if (arr.length > 1) {
                    item.setPoItem(arr[1]);
                }
            }
            for (java.lang.reflect.Field field : item.getClass().getDeclaredFields()) {
                if (field.getType() == String.class) {
                    field.setAccessible(true);
                    try {
                        String value = (String) field.get(item);
                        if (StringUtils.hasLength(value)) {
                            field.set(item, value.replace(" 00:00:00", ""));
                        }
                    } catch (IllegalAccessException e) {
                        log.error("反射获取属性值失败：{}", field.getName(), e);
                    }
                }
            }
            //计算最终发货方式
//            若【运输方式（人工）】有值，取【运输方式（人工）】的值；
//            若【运输方式（人工）】为空，若泰国接受不齐套发货，则取原逻辑；
//            若【运输方式（人工）】为空，若泰国不接受不齐套发货，则取海运；

            if (StringUtils.hasLength(item.getManualTransportMode())) {
                item.setFinalSuggestedTransportMode(item.getManualTransportMode());
            }else{
                if (!Objects.isNull(item.getIsSendUnReady()) && item.getIsSendUnReady() == Constant.NO_SEND_UNREADY ) {
                    item.setFinalSuggestedTransportMode(TransportMode.SEA.getCode());
                }
            }
            // 同时满足下面2个条件，显示：无PO，需业务确认
            //
            //【使用类型（泰国）】=SKD销售订单
            // 3317使用类型 = 需要采购
            //
            //
            //同时满足下面2个条件，显示：PO逾期，需业务确认
            //
            //【使用类型（泰国）】=SKD销售订单
            //【最新交期】小于今天
            if(isCalculateDate){
                if("SKD销售订单".equals(item.getUseTypeThailand())){
                    if(Sets.newHashSet("需要采购").contains(item.getUseType3317())){
                        item.setErrorMsg("无PO，需业务确认");
                    }else if(item.getLatestDueDate()!=null && LocalDate.parse(item.getLatestDueDate(),DateTimeFormatter.ofPattern("yyyy-MM-dd")).isBefore(LocalDate.now())){
                        item.setErrorMsg("PO逾期，需业务确认");
                    }
                }
                //3317整单齐套可发货时间	max（【最新交期】）+1，遇到周末顺延
                if(StringUtils.hasLength(item.getFullSetReadyDate3317()) ){
                    LocalDate fullSetReadyDate3317 = LocalDate.parse(item.getFullSetReadyDate3317(),DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    fullSetReadyDate3317 = DateUtil.postponedOnWeekends(fullSetReadyDate3317.plusDays(1));
                    fullSetReadyDate3317 =  fullSetReadyDate3317.isAfter(LocalDate.now())?fullSetReadyDate3317:LocalDate.now();
                    item.setFullSetReadyDate3317(fullSetReadyDate3317.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    //整单预计到泰入库配套时间（仅海运）	max(【3317整单齐套可发货时间】 + 21 + 泰国放假天数 + 收货处理时间，【整单预计到泰入库配套时间（含陆运空运）】)
                    if( StringUtils.hasLength(item.getEstThailandInboundFullWithLandAir())){
                        fullSetReadyDate3317 = fullSetReadyDate3317.plusDays(21);
                        fullSetReadyDate3317 = fullSetReadyDate3317.plusDays(SKDFHUtil.getN(fullSetReadyDate3317)).plusDays(Constant.RECEIVE_PROCESS_TIME);
                        LocalDate estThailandInboundFullWithLandAir = LocalDate.parse(item.getEstThailandInboundFullWithLandAir(),DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        fullSetReadyDate3317 = fullSetReadyDate3317.isAfter(estThailandInboundFullWithLandAir)?fullSetReadyDate3317:estThailandInboundFullWithLandAir;
                        item.setEstThailandInboundFullSeaOnly(fullSetReadyDate3317.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    }
                }
            }
        }).collect(Collectors.toList());
    }

    @Override
    public void exportSalesOrderInfo(SalesOrderInfoReq req, HttpServletResponse response) {
        List<SalesOrderInfoVO> salesOrderInfoList = querySalesOrderInfo(req);
        //运输方式转换为中文
        salesOrderInfoList = salesOrderInfoList.stream().peek(item -> {
            if(StringUtils.hasLength(item.getFinalSuggestedTransportMode())){
                item.setFinalSuggestedTransportMode(TransportMode.fromCode(item.getFinalSuggestedTransportMode()).getDescription());
            }
            if(StringUtils.hasLength(item.getManualTransportMode())){
                item.setManualTransportMode(TransportMode.fromCode(item.getManualTransportMode()).getDescription());
            }
            if(StringUtils.hasLength(item.getThailandTransportMode())){
                item.setThailandTransportMode(TransportMode.fromCode(item.getThailandTransportMode()).getDescription());
            }
        }).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=SKDOrderDetail.xlsx");
        try {
            EasyExcel.write(response.getOutputStream(), SalesOrderInfoVO.class)
                    .excludeColumnFieldNames(Collections.singletonList("isSendUnReady")) // 忽略 isSendUnReady 属性
                    .sheet("SKD订单明细")
                    .doWrite(salesOrderInfoList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public SkdOrderMaterial getSkdOrderMaterial(String topNo, String useNo) {
        return baseMapper.getSkdOrderMaterial(topNo, useNo);
    }

    @Override
    public Page<SalesOrderInfoVO> querySalesOrderInfoPage(SalesOrderInfoReq req) {
        Page<SalesOrderInfoVO> result = new Page<>(req.getPageNum(), req.getPageSize());
        baseMapper.querySalesOrderInfoPage(result, req);
        result.setRecords(dataConversion(result.getRecords(),false));
        return result;
    }

    @Override
    public List<SalesOrderInfoVO> aggregateQueriesSalesOrderInfo() {
        List<SalesOrderInfoVO> salesOrderInfoList = baseMapper.aggregateQueriesSalesOrderInfo();
        return  dataConversion(salesOrderInfoList,true);
    }

    @Override
    public boolean deleteByTopNo(String topNo) {
        if(StringUtils.isEmpty(topNo)){
            return false;
        }
        LambdaQueryWrapper<SkdOrderUseSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SkdOrderUseSub::getTopNo,topNo);
        int delNum =  baseMapper.delete(queryWrapper);
        return delNum > 0;
    }

    @Override
    public void syncData(String topNo, List<SkdOrderUseSub> orderUseSubList) {
        //TODO 获取订单 不齐套发货设置
        boolean isSendUnReady = false;
        //TODO 判断是否全部入3317库
        boolean isAllIn = false;
        orderUseSubList = calculateAndFillData(topNo,isSendUnReady,isAllIn,orderUseSubList);
        deleteByTopNo(topNo);
        if(!orderUseSubList.isEmpty()){
            saveBatch(orderUseSubList);
        }
    }

    private List<SkdOrderUseSub> queryListByTopNo(String topNo) {
        if(StringUtils.isEmpty(topNo)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<SkdOrderUseSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SkdOrderUseSub::getTopNo,topNo);
        return baseMapper.selectList(queryWrapper);
    }
}
