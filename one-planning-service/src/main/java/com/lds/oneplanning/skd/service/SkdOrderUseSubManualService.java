package com.lds.oneplanning.skd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.skd.domain.SkdOrderUseSubManual;

import java.time.LocalDate;
import java.util.List;

/**
 * 使用订单子表人工填写数据(SkdOrderUseSubManual)服务接口
 *
 * <AUTHOR>
 * @since 2025-05-24 17:25:07
 */
public interface SkdOrderUseSubManualService extends IService<SkdOrderUseSubManual> {

    List<SkdOrderUseSubManual> queryList(List<String> useNoList);

    /**
     * 保存或更新 人工填写数据
     * @param skdOrderUseSubManual
     * @return
     */
    boolean saveOrUpdateManual(SkdOrderUseSubManual skdOrderUseSubManual);

    Boolean batchSaveOrUpdateManual(List<SkdOrderUseSubManual> skdOrderUseSubManuals);
}
