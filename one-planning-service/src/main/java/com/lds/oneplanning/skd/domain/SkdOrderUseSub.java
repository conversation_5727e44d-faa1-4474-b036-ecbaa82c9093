package com.lds.oneplanning.skd.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 使用订单子表(SkdOrderUseSub)实体类
 *
 * <AUTHOR>
 * @since 2025-05-24 17:24:12
 */
@Data
@TableName(value = "skd_order_use_sub")
@ApiModel(value = "SkdOrderUseSub对象", description = "使用订单子表")
public class SkdOrderUseSub implements Serializable {

    private static final long serialVersionUID = -22193390258419940L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 使用单Id
     */
    private String orderUseId;

    @ApiModelProperty(value = "顶层单号")
    private String topNo;

    @ApiModelProperty(value = "使用单号(销售单号）")
    private String useNo;

    @ApiModelProperty(value = "使用说明")
    private String useNote;

    @ApiModelProperty(value = "销售单Id")
    private String coverSoId;

    @ApiModelProperty(value = "销售单号")
    private String coverSoNo;

    @ApiModelProperty(value = "销售单项次")
    private String coverSoLine;

    @ApiModelProperty(value = "外向单")
    private String outboundDeliveryNo;

    @ApiModelProperty(value = "外向单项次")
    private String outboundDeliveryLine;

    @ApiModelProperty(value = "交泰方式")
    private String toThaiWay;

    @ApiModelProperty(value = "泰国需求发货日期（基于最佳运输方式）")
    private Date thaiSendDueDate;

    @ApiModelProperty(value = "需要提拉天数")
    private Integer pullDays;

    @ApiModelProperty(value = "泰国需求发运方式")
    private String thaiTransportMode;

    @ApiModelProperty(value = "预计中国发货时间")
    private Date chineseSendDate;

    @ApiModelProperty(value = "预计到泰入库日期")
    private Date thaiArriveDate;

    @ApiModelProperty(value = "是否能满足泰国上线需求（连空运也满足不了，要调整计划）")
    private Integer isReady;

    @ApiModelProperty(value = "预计海运最快泰国入库日期")
    private Date arriveDateBySea;

    @ApiModelProperty(value = "泰国需求入库日期")
    private Date thaiPlanDueDate;

    @ApiModelProperty(value = "距离需求入库时间")
    private Integer gap;

    @ApiModelProperty(value = "最新建议运输方式")
    private String latestSuggestTransportMode;

    @ApiModelProperty(value = "是否可发状态")
    private String isCanSend;

    @ApiModelProperty(value = "供应商")
    private String supply;

    @ApiModelProperty(value = "使用采购组")
    private String poGroup;

    @ApiModelProperty(value = "使用采购组描述")
    private String poGroupR;

    @ApiModelProperty(value = "采购订单数量")
    private Double purchaseQty;

    @ApiModelProperty(value = "采购未完数量")
    private Double onOrderQty;

    @ApiModelProperty(value = "分配数量")
    private String useQty;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "最新交期")
    private Date latestDueDate;

    @ApiModelProperty(value = "原始最新交期")
    private Date originalLatestDueDate;

    @ApiModelProperty(value = "工厂代码")
    @TableField(exist = false)
    private String factoryCode;

    @ApiModelProperty(value = "使用类型")
    private String useType;

    @ApiModelProperty(value = "子件物料需求时间")
    private Date materialNeedDate;

    @ApiModelProperty(value = "原运输方式")
    private String origTransportMode;

    @ApiModelProperty(value = "是否需要加急发货")
    private Integer isExpeditedShipping;

    @ApiModelProperty(value = "加急发货原因")
    private String expeditedShippingReason;

    @ApiModelProperty(value = "物料lt")
    @TableField(exist = false)
    private Integer materialLeadTimeDays;


    public String getUniqueKey() {
        return new StringBuilder()
                .append(topNo)
                .append(useNo)
                .append(useType)
                .toString();
    }
}
