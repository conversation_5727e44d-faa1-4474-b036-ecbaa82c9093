package com.lds.oneplanning.skd.domain.bo;

import com.lds.oneplanning.basedata.model.base.BasePageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 订单物料业务对象 skd_order_material
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SkdOrderMaterialBo extends BasePageEntity {

    private Long id;

    /**
     * 订单产品Id
     */
    private String orderProductId;

    /**
     * 顶层单号
     */
    private String topNo;

    /**
     * 销售单号Id
     */
    private String coverSoId;

    /**
     * 销售单号
     */
    private String coverSoNo;

    /**
     * 销售单项次
     */
    private String coverSoLine;

    /**
     * 计划单号
     */
    private String workNo;

    /**
     * 计划单项次
     */
    private String workLine;

    /**
     * 子件物料编码
     */
    private String materialItemNo;

    /**
     * 子件物料名称
     */
    private String materialItemName;

    /**
     * 子件工厂
     */
    private String deliveryPlant;

    /**
     * 跨工厂状态
     */
    private String mstae;

    /**
     * 物料组
     */
    private String itemGroup;

    /**
     * 库位
     */
    private String defPlace;

    /**
     * 库位描述
     */
    private String defPlaceName;

    /**
     * po库位
     */
    private String place;

    /**
     * po库位描述
     */
    private String placeName;

    /**
     * 包材物料库位
     */
    private String itemPackPlace;

    /**
     * 物料采购类型
     */
    private String poType;

    /**
     * 子件需求日期
     */
    private Date materialNeedDate;

    /**
     * 子件物料需求数量
     */
    private Long needQty;

    /**
     * 泰国在库分配满足数量
     */
    private Long inventoryQty;

    /**
     * 剩余需求量
     */
    private Long remainingQty;

    /**
     * 在途数量
     */
    private Long transitQty;

    /**
     * PO剩余待中国出货
     */
    private Long unTransitQty;

    /**
     * 状态
     */
    private String status;
}
