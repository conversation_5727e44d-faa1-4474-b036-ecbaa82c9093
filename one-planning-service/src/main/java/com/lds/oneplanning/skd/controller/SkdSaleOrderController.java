package com.lds.oneplanning.skd.controller;

import com.lds.oneplanning.skd.domain.bo.SkdSaleOrderBo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleOrderVo;
import com.lds.oneplanning.skd.service.ISkdSaleOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@Api(value = "SkdSaleOrderController", tags = "SKD销售订单（主计划）")
@RestController
@AllArgsConstructor
@RequestMapping("/skd/sale/order")
public class SkdSaleOrderController {

    @Resource
    ISkdSaleOrderService saleOrderService;
    @ApiOperation(value = "查询销售订单", notes = "查询销售订单")
    @PostMapping("/list")
    public List<SkdSaleOrderVo> querySaleList(@RequestBody SkdSaleOrderBo bo){
        return saleOrderService.queryList(bo);
    }

    @ApiOperation(value = "导出excel 销售订单", notes = "导出excel 销售订单")
    @PostMapping("/export")
    public void export(@RequestBody SkdSaleOrderBo bo, HttpServletResponse response){
        saleOrderService.export(bo, response);
    }

}
