package com.lds.oneplanning.skd.controller;

import com.lds.oneplanning.skd.domain.bo.SkdSaleDetailBo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleDetailVo;
import com.lds.oneplanning.skd.service.ISkdSaleDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(value = "SkdSaleDetailController", tags = "SKD销售明细（主计划）")
@RestController
@AllArgsConstructor
@RequestMapping("/skd/sale/detail")
public class SkdSaleDetailController {

    @Resource
    ISkdSaleDetailService saleDetailService;
    @ApiOperation(value = "查询销售明细", notes = "查询销售明细")
    @PostMapping("/list")
    public List<SkdSaleDetailVo> querySaleList(@RequestBody SkdSaleDetailBo bo){
        return saleDetailService.queryList(bo);
    }
}
