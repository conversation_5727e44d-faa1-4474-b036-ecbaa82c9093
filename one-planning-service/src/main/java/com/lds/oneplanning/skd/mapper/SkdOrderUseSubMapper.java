package com.lds.oneplanning.skd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.skd.domain.SkdOrderMaterial;
import com.lds.oneplanning.skd.domain.SkdOrderUseSub;
import com.lds.oneplanning.skd.domain.bo.SalesOrderInfoReq;
import com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 使用订单子表(SkdOrderUseSub)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-24 17:24:32
 */
@Mapper
public interface SkdOrderUseSubMapper extends BaseMapper<SkdOrderUseSub> {


    List<SalesOrderInfoVO> querySalesOrderInfo(@Param("req")SalesOrderInfoReq req);
    List<SalesOrderInfoVO> aggregateQueriesSalesOrderInfo();

   SkdOrderMaterial getSkdOrderMaterial(@Param("topNo")String topNo,@Param("useNo")String useNo);

    Page<SalesOrderInfoVO> querySalesOrderInfoPage(Page<SalesOrderInfoVO> result, @Param("req")SalesOrderInfoReq req);
}
