package com.lds.oneplanning.skd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.skd.domain.SkdOrderUse;
import com.lds.oneplanning.skd.domain.bo.SkdOrderUseBo;
import com.lds.oneplanning.skd.domain.vo.SkdOrderUseVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单物料Service接口
 *
 * <AUTHOR>
 * @since 2025-05-22
 */

public interface ISkdOrderUseService extends IService<SkdOrderUse> {

    List<SkdOrderUseVo> queryList(SkdOrderUseBo bo);

    Page<SkdOrderUseVo> queryPage(SkdOrderUseBo bo);

    void saveBatchByTopNo(String topNo, Collection<SkdOrderUse> skdOrderUseData);

}
