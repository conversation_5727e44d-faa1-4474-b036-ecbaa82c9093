package com.lds.oneplanning.skd.controller;

import com.lds.oneplanning.skd.domain.bo.SkdSaleBo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleVo;
import com.lds.oneplanning.skd.service.ISkdSaleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(value = "SkdSaleController", tags = "SKD销售单（主计划）")
@RestController
@AllArgsConstructor
@RequestMapping("/skd/sale")
public class SkdSaleController {

    @Resource
    ISkdSaleService saleService;
    @ApiOperation(value = "查询销售单主计划", notes = "查询销售单主计划")
    @PostMapping("/list")
    public List<SkdSaleVo> querySaleList(@RequestBody SkdSaleBo bo){
       return saleService.queryList(bo);
    }
}
