package com.lds.oneplanning.skd.domain.bo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@Data
public class SkdOrderUseSubManualReq {

    @NotEmpty(message = "topNoAndUseNoList不能为空")
    List<SkdOrderUseSubManualBo> topNoAndUseNoList;
    /**
     * 分配数量
     */
    @ApiModelProperty(value = "分配数量")
    private Double manualDistributionQty;

    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    private Date manualSendTime;

    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    private String manualTransportMode;

    /**
     * 使用方式
     */
    @ApiModelProperty(value = "使用方式")
    private String useType;

}
