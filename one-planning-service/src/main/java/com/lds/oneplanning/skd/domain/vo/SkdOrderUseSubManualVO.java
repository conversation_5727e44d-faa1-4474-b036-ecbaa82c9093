package com.lds.oneplanning.skd.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 使用订单子表人工填写数据(SkdOrderUseSubManual)视图实体类
 *
 * <AUTHOR>
 * @since 2025-05-24 17:25:07
 */
@Data
@ApiModel(value = "SkdOrderUseSubManual对象", description = "使用订单子表人工填写数据")
public class SkdOrderUseSubManualVO {

    private Long id;

    /**
     * 顶层单号
     */
    @ApiModelProperty(value = "顶层单号")
    private String topNo;

    /**
     * 使用单号(销售单号）
     */
    @ApiModelProperty(value = "使用单号(销售单号）")
    private String useNo;

    /**
     * 分配数量
     */
    @ApiModelProperty(value = "分配数量")
    private Double distributionQty;

    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    private Date sendTime;

    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    private String transportMode;

}
