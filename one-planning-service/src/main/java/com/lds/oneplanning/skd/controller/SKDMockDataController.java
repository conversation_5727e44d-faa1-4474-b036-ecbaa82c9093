package com.lds.oneplanning.skd.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.esb.datafetch.model.EsbProductionOrderData;
import com.lds.oneplanning.esb.datafetch.model.EsbShipmentData;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.skd.constants.AtpCompletionStatusEnum;
import com.lds.oneplanning.skd.constants.TransportMode;
import com.lds.oneplanning.skd.job.SyncSkdDataHandler;
import com.lds.oneplanning.skd.service.ISkdOrderMaterialService;
import com.lds.oneplanning.skd.service.ISkdOrderProductService;
import com.lds.oneplanning.skd.service.SkdPurchaseLogisticsService;
import com.lds.oneplanning.skd.utils.SKDFHUtil;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.warning.workbench.TempMockData;
import com.lds.wts.thtask.api.IThTaskLogisticsInfoApi;
import com.lds.wts.thtask.vo.req.ThTaskLogisticsInfoQueryReq;
import com.lds.wts.thtask.vo.resp.ThTaskLogisticsInfoResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(value = "SKDMockDataController", tags = "SKD模拟数据")
@RestController
@AllArgsConstructor
@RequestMapping("/skd/mock")
public class SKDMockDataController {
    private  final IThTaskLogisticsInfoApi thTaskLogisticsInfoApi;
    private  final EsbDataFetchService esbDataFetchService;
    private final ISkdOrderProductService skdOrderProductService;
    private final ISkdOrderMaterialService skdOrderMaterialService;
    private final SkdPurchaseLogisticsService skdPurchaseLogisticsService;
    private final SyncSkdDataHandler syncSkdDataHandler;
    @Resource
    private IFactoryService factoryService;
    @ApiOperation(value = "生成SKD发货评估评估模拟数据", notes = "生成SKD发货评估模拟数据")
    @GetMapping("/generateFHData")
    public Map<String,Object> generateFHData(@ApiParam(value = "需求日期") @RequestParam("dueDate") LocalDate dueDate,
                                             @ApiParam(value = "最新需求日期")@RequestParam("latestDueDate")LocalDate latestDueDate,
                                             @ApiParam(value = "收货处理时间")@RequestParam("receiveProcessTime")int receiveProcessTime,
                                             @ApiParam(value = "原运输方式")@RequestParam("transportMode")TransportMode transportMode,
                                             @ApiParam(value = "发货日期（人工填写）")@RequestParam(value ="shippingTime",required = false)LocalDate shippingTime,
                                             @ApiParam(value = "交泰方式")@RequestParam("toThaiType")String toThaiType,
                                             @ApiParam(value = "计划开始时间")@RequestParam("planStartDate")LocalDate planStartDate,
                                             @ApiParam(value = "是否都在3317仓库")@RequestParam("isAllIn")boolean isAllIn,
                                             @ApiParam(value = "是否不齐套发货")@RequestParam("isSendUnReady")boolean isSendUnReady,
                                             @ApiParam(value = "人工填写运输方式")@RequestParam(value = "transportModeManual",required = false)TransportMode transportModeManual,
                                             @ApiParam(value = "工厂code")@RequestParam("factoryCode")String factoryCode,
                                             @ApiParam(value = "使用类型")@RequestParam("useType")String useType,
                                             @ApiParam(value = "是否已放舱")@RequestParam("isLoaded")boolean isLoaded
    ) {
        log.info("开始生成SKD发货评估模拟数据");
//        SKDFHUtil skdFHUtil = new SKDFHUtil(dueDate,latestDueDate,receiveProcessTime,transportMode,shippingTime,transportModeManual,isLoaded,useType,factoryCode);
//        Map<String,Object> result = Maps.newHashMap();
//        result.put("泰国需求发货日期（基于最佳运输方式）",skdFHUtil.getThaiSendDueDate());
//        result.put("需要提拉天数",skdFHUtil.getPullDays());
//        result.put("预计到泰入库日期",skdFHUtil.getThaiArrivalDate());
//        result.put("预计海运最快泰国入库日期",skdFHUtil.getThaiArrivalDateBySEA());
//        result.put("最终建议运输方式",skdFHUtil.getFinalTransportMode());
//        result.put("预计中国发货时间",skdFHUtil.getChinaShippingTime());
//        result.put("差异天数",skdFHUtil.getGap());
//        result.put("泰国需求发运方式",skdFHUtil.getTransportMode());
//        result.put("是否能满足泰国上线需求（连空运也满足不了，要调整计划）",skdFHUtil.isReady());
//        result.put("是否可发状态",skdFHUtil.getStatus(toThaiType,planStartDate,isAllIn,isSendUnReady));
        return Maps.newHashMap();
    }

    @ApiOperation(value = "wts查询销售单物流", notes = "wts查询销售单物流")
    @GetMapping("/querySalesOrderLogistics")
    public List<ThTaskLogisticsInfoResp>  querySalesOrderLogistics(@ApiParam(value = "销售单号") @RequestParam("salesOrderNo") String salesOrderNo,
                                                       @ApiParam(value = "销售单行项目号") @RequestParam("salesOrderItemNo") String  salesOrderItemNo){
        log.info("开始查询销售单物流");
        ThTaskLogisticsInfoQueryReq thTaskLogisticsInfoQueryReq = new ThTaskLogisticsInfoQueryReq();
        thTaskLogisticsInfoQueryReq.setOutgoingOrderNo(salesOrderNo);
        thTaskLogisticsInfoQueryReq.setOutgoingOrderItemNo(salesOrderItemNo);
        List<ThTaskLogisticsInfoResp> thTaskLogisticsInfoResps = skdPurchaseLogisticsService.queryLogisticsInfo(Lists.newArrayList(thTaskLogisticsInfoQueryReq));
        return thTaskLogisticsInfoResps;
    }
    @ApiOperation(value = "esb查询销售单物流", notes = "esb查询销售单物流")
    @GetMapping("/querySalesOrderLogisticsByEsb")
    public List<EsbShipmentData> querySalesOrderLogisticsByEsb(@ApiParam(value = "销售单号") @RequestParam("salesOrderNo") String salesOrderNo,
                                                               @ApiParam(value = "销售单行项目号") @RequestParam("salesOrderItemNo") String  salesOrderItemNo){
        log.info("开始查询销售单物流");
        // 使用批量处理方式，为未来可能的批量调用做准备
        EsbShipmentData esbShipmentData = new EsbShipmentData();
        esbShipmentData.setVbeln(salesOrderNo);
        esbShipmentData.setPosnr(salesOrderItemNo);
        List<EsbShipmentData> result = esbDataFetchService.getShipmentDateData(Lists.newArrayList(esbShipmentData));
        return result != null ? result : Lists.newArrayList();
    }
    @ApiOperation(value = "查询订单信息", notes = "查询订单信息")
    @PostMapping("/queryProductionOrderData")
    public List<EsbProductionOrderData> queryProductionOrderData(@RequestBody EsbProductionOrderData esbProductionOrderData){
        return esbDataFetchService.getProductionOrderData(Lists.newArrayList(esbProductionOrderData));
    }

    @ApiOperation(value = "查询齐套信息", notes = "查询齐套信息")
    @GetMapping("/getOrderCompletionInfoData")
    public Map<String, AtpCompletionStatusEnum>  getOrderCompletionInfoData(@ApiParam(value = "计划单号") @RequestParam("topNo") String topNo){
        log.info("查询齐套信息");
        return skdOrderProductService.getAtpCompletionStatusMap(Lists.newArrayList(topNo));
    }

    @ApiOperation(value = "同步WPS订单", notes = "同步WPS订单")
    @GetMapping("/syncWpsOrder")
    public List<WpsRowData> syncWpsOrder(){
        //获取泰国工厂列表
        List<String> factoryCode = factoryService.getThaiFactoryCodes();
        return skdOrderProductService.syncFromWPS(factoryCode);
    }

    @GetMapping("/testJob")
    public void testJob() throws Exception {
        syncSkdDataHandler.execute("{\"factoryCode\":\"2602\"}");
    }

    @GetMapping("/queryLogisticsInfo")
    public void queryLogisticsInfo() throws Exception {
        List<ThTaskLogisticsInfoQueryReq> data = TempMockData.getCtx4();
        skdPurchaseLogisticsService.queryLogisticsInfo(data);
    }
}
