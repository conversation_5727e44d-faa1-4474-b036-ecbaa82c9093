package com.lds.oneplanning.skd.constants;

public enum TransportMode {
    SEA("SEA", "海运",3),
    LAND("LAND", "陆运",2),
    AIR("AIR", "空运",1),
    UNKNOWN("99", "未知",99);

    private final String code;
    private final String description;
    private  int priority;

    TransportMode(String code, String description,int priority) {
        this.code = code;
        this.description = description;
        this.priority = priority;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取对应的枚举值
    public static TransportMode fromCode(String code) {
        for (TransportMode mode : TransportMode.values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        return UNKNOWN;
    }
    //根据description 获取对应的枚举值
    public static TransportMode fromDescription(String description) {
        for (TransportMode mode : TransportMode.values()){
            if (mode.getDescription().equals(description)){
                return mode;
            }
        }
        return UNKNOWN;
    }

    /**
     * 是否优先
     * @param mode
     * @return
     */
    public boolean isPriority(TransportMode mode) {
        return this.priority < mode.priority;
    }
}
