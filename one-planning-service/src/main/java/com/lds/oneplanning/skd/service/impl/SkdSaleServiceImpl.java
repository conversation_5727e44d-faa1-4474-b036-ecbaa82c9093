package com.lds.oneplanning.skd.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.skd.domain.SkdOrderProduct;
import com.lds.oneplanning.skd.domain.SkdSale;
import com.lds.oneplanning.skd.domain.bo.SkdSaleBo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleVo;
import com.lds.oneplanning.skd.mapper.SkdSaleMapper;
import com.lds.oneplanning.skd.service.ISkdSaleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * SKD销售单(SkdSaleServiceImpl)服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
@AllArgsConstructor
public class SkdSaleServiceImpl extends ServiceImpl<SkdSaleMapper, SkdSale> implements ISkdSaleService {
    @Override
    public List<SkdSaleVo> queryList(SkdSaleBo bo) {
        List<SkdSale> res = baseMapper.selectList(buildQueryWrapper(bo));
        if (CollUtil.isEmpty(res)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(res, SkdSaleVo.class);
    }

    public SkdSale queryByCoverSoNo(String coverSoNo) {
        LambdaQueryWrapper<SkdSale> lqw = Wrappers.lambdaQuery();
        lqw.eq(SkdSale::getCoverSoNo, coverSoNo);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 保存
     * @param skdSale
     * @return
     */
    @Override
    public SkdSale saveSale(SkdSale skdSale) {
        if (Objects.isNull(skdSale) || StringUtils.isBlank(skdSale.getCoverSoNo())) {
            return skdSale;
        }

        SkdSale dbSale = this.queryByCoverSoNo(skdSale.getCoverSoNo());
        if (Objects.nonNull(dbSale)) {
            skdSale.setId(dbSale.getId());
            baseMapper.updateById(skdSale);
        } else {
            baseMapper.insert(skdSale);
        }
        return skdSale;
    }

    @Override
    public int deleteAll() {
        return baseMapper.delete(Wrappers.lambdaQuery());
    }

    @Override
    public int insertFromSelect() {
        return baseMapper.insertFromSelect();
    }

    private LambdaQueryWrapper<SkdSale> buildQueryWrapper(SkdSaleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SkdSale> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTopNo()), SkdSale::getTopNo, bo.getTopNo());
        lqw.like(StringUtils.isNotBlank(bo.getCoverSoNo()), SkdSale::getCoverSoNo, bo.getCoverSoNo());
        lqw.in( bo.getCoverSoNoList() != null && !bo.getCoverSoNoList().isEmpty(), SkdSale::getCoverSoNo, bo.getCoverSoNoList());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerCode()), SkdSale::getCustomerCode, bo.getCustomerCode());
        lqw.between(params.get("beginPlanStartDate") != null && params.get("endPlanStartDate") != null,
                SkdSale::getPlanStartDate ,params.get("beginPlanStartDate"), params.get("endPlanStartDate"));
        lqw.between(params.get("beginPlanEndDate") != null && params.get("endPlanEndDate") != null,
                SkdSale::getPlanEndDate ,params.get("beginPlanEndDate"), params.get("endPlanEndDate"));
        lqw.between(params.get("beginShipTime") != null && params.get("endShipTime") != null,
                SkdSale::getShipTime ,params.get("beginShipTime"), params.get("endShipTime"));
        lqw.eq(StringUtils.isNotBlank(bo.getPlant()), SkdSale::getPlant, bo.getPlant());
        lqw.between(params.get("beginPlanReadyDate") != null && params.get("endPlanReadyDate") != null,
                SkdSale::getPlanReadyDate ,params.get("beginPlanReadyDate"), params.get("endPlanReadyDate"));
        lqw.between(params.get("beginSkdReadyDate") != null && params.get("endSkdReadyDate") != null,
                SkdSale::getSkdReadyDate ,params.get("beginSkdReadyDate"), params.get("endSkdReadyDate"));
        lqw.between(params.get("beginSkdReadyDateShip") != null && params.get("endSkdReadyDateShip") != null,
                SkdSale::getSkdReadyDateShip ,params.get("beginSkdReadyDateShip"), params.get("endSkdReadyDateShip"));
        lqw.between(params.get("beginSkdReadyDateNonShip") != null && params.get("endSkdReadyDateNonShip") != null,
                SkdSale::getSkdReadyDateNonShip ,params.get("beginSkdReadyDateNonShip"), params.get("endSkdReadyDateNonShip"));
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SkdSale::getStatus, bo.getStatus());
        return lqw;
    }
}
