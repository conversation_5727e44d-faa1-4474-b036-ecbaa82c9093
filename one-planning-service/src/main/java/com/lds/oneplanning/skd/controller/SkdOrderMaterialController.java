package com.lds.oneplanning.skd.controller;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.skd.domain.bo.SkdOrderMaterialBo;
import com.lds.oneplanning.skd.domain.bo.SkdOrderUseBo;
import com.lds.oneplanning.skd.domain.vo.SkdOrderMaterialVo;
import com.lds.oneplanning.skd.domain.vo.SkdOrderUseVo;
import com.lds.oneplanning.skd.service.ISkdOrderMaterialService;
import com.lds.oneplanning.skd.service.ISkdOrderUseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@Api(value = "SkdOrderMaterialController", tags = "SKD订单物料")
@RestController
@AllArgsConstructor
@RequestMapping("/skd/order/material")
public class SkdOrderMaterialController {

    @Resource
    ISkdOrderMaterialService orderMaterialService;
    @Resource
    ISkdOrderUseService orderUseService;

    @ApiOperation(value = "分页查询", notes = "SKD订单物料分页查询")
    @PostMapping("/page")
    public Page<SkdOrderMaterialVo> page(@RequestBody SkdOrderMaterialBo bo) {
        return orderMaterialService.queryPage(bo);
    }

    @ApiOperation(value = "excel导出 SKD订单物料", notes = "excel导出 SKD订单物料")
    @PostMapping("/export")
    public void export(@RequestBody SkdOrderMaterialBo bo, HttpServletResponse response) {
        orderMaterialService.export(bo, response);
    }

    @ApiOperation(value = "查询物料关联采购单列表", notes = "查询物料关联采购单列表")
    @PostMapping("/purchase/list")
    public List<SkdOrderUseVo> queryPurchaseList(@RequestBody SkdOrderUseBo bo){
        return orderUseService.queryList(bo);
    }

    @ApiOperation(value = "查询物料关联采购单列表 分页", notes = "查询物料关联采购单列表 分页")
    @PostMapping("/purchase/page")
    public Page<SkdOrderUseVo> queryPurchasePage(@RequestBody SkdOrderUseBo bo){
        return orderUseService.queryPage(bo);
    }
}
