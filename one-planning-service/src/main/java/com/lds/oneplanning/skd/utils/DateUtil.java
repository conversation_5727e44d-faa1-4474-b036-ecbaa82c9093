package com.lds.oneplanning.skd.utils;

import org.jetbrains.annotations.NotNull;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

public class DateUtil {

    public static Date getDate(LocalDate date){
        if(date == null) return null;
        return Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate getLocalDate(Date date){
        if(date == null) return null;
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 如果比当前日期晚则取原值，否则取当前日期
     * @param date
     * @return
     */
    public static LocalDate getAfterToday(LocalDate date) {
        if(date == null) return null;
        return date.isAfter(LocalDate.now()) ? date : LocalDate.now();
    }

    /**
     * 周末顺延
     * @Description
     * 遇周六周日顺延到周一
     */
    @NotNull
    public static LocalDate postponedOnWeekends(LocalDate shippingTime) {
        if(shippingTime != null){
            if (shippingTime.getDayOfWeek().equals(DayOfWeek.SATURDAY)) {
                shippingTime = shippingTime.plusDays(2);
            }else if(shippingTime.getDayOfWeek().equals(DayOfWeek.SUNDAY)){
                shippingTime = shippingTime.plusDays(1);
            }
        }
        return shippingTime;
    }
}
