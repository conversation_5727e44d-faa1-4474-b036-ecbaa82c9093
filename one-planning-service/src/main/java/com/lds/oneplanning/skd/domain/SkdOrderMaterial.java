package com.lds.oneplanning.skd.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 订单物料对象 skd_order_material
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("skd_order_material")
public class SkdOrderMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    /**
     * 订单产品Id
     */
    private Long orderProductId;

    /**
     * 顶层单号
     */
    private String topNo;

    /**
     * 销售单号Id
     */
    private String coverSoId;

    /**
     * 销售单号
     */
    private String coverSoNo;

    /**
     * 销售单项次
     */
    private String coverSoLine;

    /**
     * 计划单号
     */
    private String workNo;

    /**
     * 计划单项次
     */
    private String workLine;

    /**
     * 子件物料编码
     */
    private String materialItemNo;

    /**
     * 子件物料名称
     */
    private String materialItemName;

    /**
     * 子件工厂
     */
    private String deliveryPlant;

    /**
     * 子件前置期
     */
    private Integer materialLeadTimeDays;

    /**
     * 跨工厂状态
     */
    private String mstae;

    /**
     * 物料组
     */
    private String itemGroup;

    /**
     * 库位
     */
    private String defPlace;

    /**
     * 库位描述
     */
    private String defPlaceName;

    /**
     * po库位
     */
    private String place;

    /**
     * po库位描述
     */
    private String placeName;

    /**
     * 包材物料库位
     */
    private String itemPackPlace;

    /**
     * 物料采购类型
     */
    private String poType;

    /**
     * 子件需求日期
     */
    private Date materialNeedDate;

    /**
     * 子件物料需求数量
     */
    private Double needQty;

    /**
     * 泰国在库分配满足数量
     */
    private Double inventoryQty;

    /**
     * 剩余需求量
     */
    private Double remainingQty;

    /**
     * 在途数量
     */
    private Double transitQty;

    /**
     * PO剩余待中国出货
     */
    private Double unTransitQty;

    /**
     * 状态
     */
    private String status;

    public String getKey() {
        return workNo + workLine + materialItemNo;
    }

    // 同步时有些字段已经剔除，如果需要更新请记得增加进来！！！
    @Override
    public int hashCode() {
        return Objects.hash(orderProductId, topNo, coverSoId, coverSoNo, coverSoLine, workNo, workLine, materialItemNo, materialItemName, deliveryPlant, materialLeadTimeDays, mstae, itemGroup, defPlace, defPlaceName, place, placeName, itemPackPlace, poType, materialNeedDate, needQty);
    }
}
