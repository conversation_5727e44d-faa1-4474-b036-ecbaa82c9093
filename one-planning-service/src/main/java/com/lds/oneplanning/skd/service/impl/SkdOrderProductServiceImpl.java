package com.lds.oneplanning.skd.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.enums.SysParamCfgEnum;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.ISysParamCfgService;
import com.lds.oneplanning.esb.datafetch.model.EsbOrderCompletionInfoData;
import com.lds.oneplanning.esb.datafetch.model.EsbProductionOrderData;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.skd.constants.AtpCompletionStatusEnum;
import com.lds.oneplanning.skd.constants.Constant;
import com.lds.oneplanning.skd.constants.OrderStatusEnum;
import com.lds.oneplanning.skd.domain.SkdOrderProduct;
import com.lds.oneplanning.skd.domain.bo.SkdOrderProductBo;
import com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO;
import com.lds.oneplanning.skd.domain.vo.SkdOrderProductVo;
import com.lds.oneplanning.skd.mapper.SkdOrderProductMapper;
import com.lds.oneplanning.skd.service.ISkdOrderProductService;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessContext;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单产品Service接口
 *
 * <AUTHOR>
 * @since 2025-05-21
 */

@Service
@Slf4j
@AllArgsConstructor
public class SkdOrderProductServiceImpl extends ServiceImpl<SkdOrderProductMapper, SkdOrderProduct> implements ISkdOrderProductService {

    private final IFactoryService factoryService;
    private final WpsOrderProcessContext wpsOrderProcessContext;
    private final IEsbDataFetchService esbDataFetchService;
    private final ISysParamCfgService sysParamCfgService;

    @Override
    public List<String> getUnCompleteTopNos() {
        LambdaQueryWrapper<SkdOrderProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(SkdOrderProduct::getStatus, OrderStatusEnum.UN_COMPLETE.getCode());
        lqw.select(SkdOrderProduct::getTopNo);
        List<SkdOrderProduct> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(SkdOrderProduct::getTopNo).collect(Collectors.toList());
    }

    @Override
    public List<SkdOrderProductVo> list(SkdOrderProductBo bo) {

        LambdaQueryWrapper<SkdOrderProduct> lqw = buildQueryWrapper(bo);

        List<SkdOrderProduct> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(list, SkdOrderProductVo.class);
    }

    @Override
    public Page<SkdOrderProductVo> queryPage(SkdOrderProductBo bo) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SkdOrderProduct> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(bo.getPage(), bo.getPageSize());
        LambdaQueryWrapper<SkdOrderProduct> lqw = buildQueryWrapper(bo);
        entityPage = baseMapper.selectPage(entityPage, lqw);

        Page<SkdOrderProductVo> resultPage = new Page<>(bo.getPage(), bo.getPageSize());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.copyProperties(entityPage, Page.class);
            List<SkdOrderProductVo> results = BeanUtil.copyToList(entityPage.getRecords(), SkdOrderProductVo.class);
            resultPage.setPageNum(bo.getPage());
            resultPage.setPageSize(bo.getPageSize());

            resultPage.setResult(results);
        }

        return resultPage;
    }

    @Override
    public Map<String, SkdOrderProduct> getMapByTopNo(String topNo) {
        LambdaQueryWrapper<SkdOrderProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(SkdOrderProduct::getTopNo, topNo);
        List<SkdOrderProduct> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(item -> item.getKey(), e -> e, (e1, e2) -> e2));
    }

    @Override
    public Map<Long, SkdOrderProduct> getByIds(Collection<Long> ids) {
        List<SkdOrderProduct> list = super.listByIds(ids);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(SkdOrderProduct::getId, e -> e, (e1, e2) -> e2));
    }

    private LambdaQueryWrapper<SkdOrderProduct> buildQueryWrapper(SkdOrderProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SkdOrderProduct> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTopNo()), SkdOrderProduct::getTopNo, bo.getTopNo());
        lqw.like(StringUtils.isNotBlank(bo.getCoverSoNo()), SkdOrderProduct::getCoverSoNo, bo.getCoverSoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverSoLine()), SkdOrderProduct::getCoverSoLine, bo.getCoverSoLine());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerCode()), SkdOrderProduct::getCustomerCode, bo.getCustomerCode());
        lqw.like(StringUtils.isNotBlank(bo.getWorkNo()), SkdOrderProduct::getWorkNo, bo.getWorkNo());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkLine()), SkdOrderProduct::getWorkLine, bo.getWorkLine());
        lqw.between(params.get("beginPlanDate") != null && params.get("endPlanDate") != null,
                SkdOrderProduct::getPlanDate ,params.get("beginPlanDate"), params.get("endPlanDate"));
        lqw.like(StringUtils.isNotBlank(bo.getItemNo()), SkdOrderProduct::getItemNo, bo.getItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPlant()), SkdOrderProduct::getPlant, bo.getPlant());
        lqw.like(StringUtils.isNotBlank(bo.getWorkTypeName()), SkdOrderProduct::getWorkTypeName, bo.getWorkTypeName());
        lqw.eq(bo.getPlanQitaoDate() != null, SkdOrderProduct::getPlanQitaoDate, bo.getPlanQitaoDate());
        lqw.eq(bo.getPlanAllQitaoDate() != null, SkdOrderProduct::getPlanAllQitaoDate, bo.getPlanAllQitaoDate());
        lqw.eq(bo.getAllQitaoDate() != null, SkdOrderProduct::getAllQitaoDate, bo.getAllQitaoDate());
        lqw.eq(bo.getNotSkdQitaoDate() != null, SkdOrderProduct::getNotSkdQitaoDate, bo.getNotSkdQitaoDate());
        lqw.eq(bo.getSkdQitaoDate() != null, SkdOrderProduct::getSkdQitaoDate, bo.getSkdQitaoDate());
        lqw.eq(StringUtils.isNotBlank(bo.getHasNotShipTransport()), SkdOrderProduct::getHasNotShipTransport, bo.getHasNotShipTransport());
        lqw.eq(bo.getSkdQitaoDateShip() != null, SkdOrderProduct::getSkdQitaoDateShip, bo.getSkdQitaoDateShip());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SkdOrderProduct::getStatus, bo.getStatus());
        return lqw;
    }


    @Override
    public List<WpsRowData> syncFromWPS(List<String> factoryCode) {
        List<WpsRowData> resultList = Lists.newArrayList();
        //从wps获取工厂订单
        String futureDays =  sysParamCfgService.getValueByCode(SysParamCfgEnum.SCHEDULE_DAYS.getCode());
        // 订单类型
        List<String> orderTypeList = Lists.newArrayList("计划订单", "生产订单");
        for (String code : factoryCode) {
            try{
                List<WpsRowData> wpsRowDataList = wpsOrderProcessContext.queryFuturePlanOrders(code, orderTypeList, Integer.parseInt(futureDays));
                List<WpsRowData> orders = this.getUnCompleteWithUpdateStatus(code, wpsRowDataList);
                if(!CollectionUtils.isEmpty(orders)){
                    resultList.addAll(orders);
                }
            } catch (Exception e) {
                log.error("syncFromWPS factoryCode: {}, error:{}", code, e.getMessage(), e);
            }
        }
        return resultList;
    }

    @NotNull
    private List<WpsRowData> getUnCompleteWithUpdateStatus(String factoryCode, List<WpsRowData> wpsRowDataList) {
        Set<String> orderNos = wpsRowDataList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toSet());
        LambdaQueryWrapper<SkdOrderProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SkdOrderProduct::getId, SkdOrderProduct::getTopNo, SkdOrderProduct::getStatus);
        queryWrapper.eq(SkdOrderProduct::getPlant, factoryCode);
        List<SkdOrderProduct> skdOrderProductList = baseMapper.selectList(queryWrapper);
        Set<String> productTopNos = skdOrderProductList.stream().map(SkdOrderProduct::getTopNo).collect(Collectors.toSet());
        Set<String> endTopNos = skdOrderProductList.stream().filter(
                e -> OrderStatusEnum.COMPLETE.getCode().equals(e.getStatus()) ||
                        OrderStatusEnum.DELETED.getCode().equals(e.getStatus())).map(SkdOrderProduct::getTopNo).collect(Collectors.toSet());
        // 取已经不存在的数据，直接删除
        Collection<String> delProductCollection = CollUtil.subtract(productTopNos, orderNos);

        // 取并集，新查到的数据和数据库的数据取并集
        orderNos.addAll(productTopNos);
        // 取差集，过滤出数据库不是【未完成】的数据
        orderNos.removeAll(endTopNos);

        // 获取最新的状态
        Map<String, String> topNoStatusMap = this.fetchOrderStatus(orderNos);
        List<SkdOrderProduct> updateProductList = new ArrayList<>();
        // 更新数据库中存在并且状态发生改变的数据
        skdOrderProductList.forEach(item -> {
            if (delProductCollection.contains(item.getTopNo())) {
                item.setUpdateTime(new Date());
                item.setStatus(OrderStatusEnum.UNKNOWN.getCode());
                updateProductList.add(item);
            } else if (topNoStatusMap.containsKey(item.getTopNo()) && !item.getStatus().equals(topNoStatusMap.get(item.getTopNo()))) {
                item.setUpdateTime(new Date());
                item.setStatus(topNoStatusMap.get(item.getTopNo()));
                updateProductList.add(item);
            }
        });

        if (CollUtil.isNotEmpty(updateProductList)) {
            super.updateBatchById(updateProductList);
        }

        return wpsRowDataList.stream().filter(wpsRowData -> orderNos.contains(wpsRowData.getOrderNo())).collect(Collectors.toList());
    }

    /**
     * 从SAP查询订单状态
     * @Description
     * 取出的订单对应的状态有包含以下几个：
     * I0045、I0046、IFO07、I0076、I0012 已完成，
     * I0013 ： 删除
     * @param topNos
     * @return
     */
    private Map<String, String> fetchOrderStatus(Collection<String> topNos) {
        if (CollUtil.isEmpty(topNos)) {
            return new HashMap<>();
        }
        List<EsbProductionOrderData> params = new ArrayList<>();
        topNos.forEach(topNo -> {
            EsbProductionOrderData productionOrderData = new EsbProductionOrderData();
            productionOrderData.setAufnr(topNo);
            params.add(productionOrderData);
        });

        List<EsbProductionOrderData> productionOrderDataList = esbDataFetchService.getProductionOrderData(params);
        if(CollectionUtils.isEmpty(productionOrderDataList)){
            return new HashMap<>();
        }

        Map<String, String> topNoStatusMap = new HashMap<>();
        productionOrderDataList.forEach(item -> {
            if (StringUtils.isNotBlank(item.getZstat())) {
                topNoStatusMap.put(item.getAufnr(), getStatus(item.getZstat()));
            }
        });
       return topNoStatusMap;
    }

    @Override
    public void saveBatchByTopNo(String topNo, Collection<SkdOrderProduct> skdOrderProductData) {
        if (CollUtil.isEmpty(skdOrderProductData)) {
            return;
        }

        List<SkdOrderProduct> dbList = baseMapper.selectList(Wrappers.<SkdOrderProduct>lambdaQuery().eq(SkdOrderProduct::getTopNo, topNo));
        Map<String, SkdOrderProduct> dbMap = new HashMap<>();
        if (CollUtil.isNotEmpty(dbList)) {
            dbMap = dbList.stream().collect(Collectors.toMap(item -> item.getKey() , e -> e, (e1, e2) -> e2));
        }

        Set<SkdOrderProduct> addSet = new HashSet<>();
        Set<SkdOrderProduct> updateSet = new HashSet<>();
        for (SkdOrderProduct skdOrderProduct : skdOrderProductData) {
            String key = skdOrderProduct.getKey();
            if (dbMap.containsKey(key)) {
                if (skdOrderProduct.getHashCode() == dbMap.get(key).getHashCode()) {
                    continue;
                }
                skdOrderProduct.setId(dbMap.get(key).getId());
                skdOrderProduct.setUpdateTime(DateUtil.date());
                updateSet.add(skdOrderProduct);
            } else {
                addSet.add(skdOrderProduct);
            }
        }

        if (CollUtil.isNotEmpty(updateSet)) {
            super.updateBatchById(updateSet);
        }

        if (CollUtil.isNotEmpty(addSet)) {
            super.saveBatch(addSet);
        }
    }

    @Override
    public void delByTopNos(Collection<String> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        log.info("skdOrderProduct delByTopNos: {}", list);
        LambdaUpdateWrapper<SkdOrderProduct> lqw = new LambdaUpdateWrapper<>();
        lqw.in(SkdOrderProduct::getTopNo, list);
        lqw.set(SkdOrderProduct::getStatus, OrderStatusEnum.UNKNOWN.getCode());
        super.update(lqw);
    }

    @Override
    public Map<String, AtpCompletionStatusEnum> getAtpCompletionStatusMap(List<String> topNos){
        List<EsbOrderCompletionInfoData> esbSingleMaterialRespList = esbDataFetchService.getOrderCompletionInfoData(topNos);
        Map<String, String> atpSingleMaterialRespMap = esbSingleMaterialRespList.stream().collect(Collectors.toMap(item ->String.valueOf(Long.parseLong(item.getAufnr())), EsbOrderCompletionInfoData::getZsfjt,  (v1, v2) -> v1));
        Map<String, AtpCompletionStatusEnum> atpCompletionStatusMap = new HashMap<>();
        topNos.forEach(item -> {
            String zsfjt = atpSingleMaterialRespMap.get(item);
            atpCompletionStatusMap.put(item, AtpCompletionStatusEnum.fromCode(zsfjt));
        });
        return atpCompletionStatusMap;
    }

    @Override
    public void update(List<SalesOrderInfoVO> list) {
        list.forEach(item -> {
            LambdaUpdateWrapper<SkdOrderProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SkdOrderProduct::getTopNo, item.getTopNo());
            updateWrapper.eq(SkdOrderProduct::getWorkNo, item.getWorkNo());
            updateWrapper.eq(StringUtils.isNotBlank(item.getWorkLine()),SkdOrderProduct::getWorkLine, item.getWorkLine());
            updateWrapper.set(SkdOrderProduct::getPlanAllQitaoDate, item.getFullSetReadyDate3317());
            updateWrapper.set(SkdOrderProduct::getSkdQitaoDateShip, item.getEstThailandInboundFullSeaOnly());
            updateWrapper.set(SkdOrderProduct::getAllQitaoDate, item.getEstThailandInboundFullWithLandAir());
            this.update(updateWrapper);
        });

    }

    private String getStatus(String status) {
        for(String state : Constant.COMPLETE_ORDER_STATUS){
            if(status.contains(state)){
                return OrderStatusEnum.COMPLETE.getCode();
            }
        }
        for(String state : Constant.DELETE_ORDER_STATUS){
            if(status.contains(state)){
                return OrderStatusEnum.DELETED.getCode();
            }
        }

        return OrderStatusEnum.UN_COMPLETE.getCode();
    }
}
