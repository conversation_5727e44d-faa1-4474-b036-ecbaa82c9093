package com.lds.oneplanning.skd.domain.bo;

import com.lds.oneplanning.basedata.model.base.BasePageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 订单产品业务对象 skd_order_product
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SkdOrderProductBo extends BasePageEntity {

    private Long id;

    /**
     * 顶层单号
     */
    private String topNo;

    /**
     * 销售单号
     */
    private String coverSoNo;

    /**
     * 销售单项次
     */
    private String coverSoLine;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 计划单号
     */
    private String workNo;

    /**
     * 计划单项次
     */
    private String workLine;

    /**
     * 计划开始日期
     */
    private Date planDate;

    /**
     * 计划完工日期
     */
    private Date plantFinish;

    /**
     * 订单交期
     */
    private Date orderDelivery;

    /**
     * 船期
     */
    private Date shipTime;

    /**
     * 订单数量
     */
    private Double qty;

    /**
     * 产品编码
     */
    private String itemNo;

    /**
     * 订单工厂
     */
    private String plant;

    /**
     * 类型
     */
    private String workTypeName;

    /**
     * 需求最晚齐套时间
     */
    private Date planQitaoDate;

    /**
     * 计划总齐套日期
     */
    private Date planAllQitaoDate;

    /**
     * 总齐套日期
     */
    private Date allQitaoDate;

    /**
     * 本直齐套日期
     */
    private Date notSkdQitaoDate;

    /**
     * skd物料齐套日期
     */
    private Date skdQitaoDate;

    /**
     * 是否有非海运
     */
    private String hasNotShipTransport;

    /**
     * skd海运物料齐套日期
     */
    private Date skdQitaoDateShip;

    /**
     * 状态
     */
    private String status;
    /**
     * 是否不齐套发货
     */
    private Integer isCanSendUnReady;
}
