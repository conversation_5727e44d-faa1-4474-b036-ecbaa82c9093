package com.lds.oneplanning.skd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.skd.constants.AtpCompletionStatusEnum;
import com.lds.oneplanning.skd.domain.SkdOrderProduct;
import com.lds.oneplanning.skd.domain.bo.SkdOrderProductBo;
import com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO;
import com.lds.oneplanning.skd.domain.vo.SkdOrderProductVo;
import com.lds.oneplanning.wps.model.WpsRowData;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 订单产品Service接口
 *
 * <AUTHOR>
 * @since 2025-05-21
 */

public interface ISkdOrderProductService extends IService<SkdOrderProduct> {

    List<String> getUnCompleteTopNos();

    List<SkdOrderProductVo> list(SkdOrderProductBo bo);

    Page<SkdOrderProductVo> queryPage(SkdOrderProductBo bo);

    Map<String, SkdOrderProduct> getMapByTopNo(String topNo);

    Map<Long, SkdOrderProduct> getByIds(Collection<Long> ids);

    /**
     * 同步WPS订单
     * @return 订单列表
     */
    List<WpsRowData> syncFromWPS(List<String> factoryCode);

    void saveBatchByTopNo(String topNo, Collection<SkdOrderProduct> list);

    void delByTopNos(Collection<String> list);

    Map<String, AtpCompletionStatusEnum> getAtpCompletionStatusMap(List<String> topNos);

    void update(List<SalesOrderInfoVO> list);

}
