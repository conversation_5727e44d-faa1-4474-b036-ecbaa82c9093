package com.lds.oneplanning.skd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.skd.domain.SkdSale;
import com.lds.oneplanning.skd.domain.bo.SkdSaleBo;
import com.lds.oneplanning.skd.domain.vo.SkdSaleVo;

import java.util.List;

public interface ISkdSaleService extends IService<SkdSale> {

    List<SkdSaleVo> queryList(SkdSaleBo bo);

    SkdSale saveSale(SkdSale skdSale);

    int deleteAll();

    int insertFromSelect();
}
