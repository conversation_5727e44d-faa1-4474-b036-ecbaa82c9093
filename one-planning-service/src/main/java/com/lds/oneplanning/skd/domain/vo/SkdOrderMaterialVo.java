package com.lds.oneplanning.skd.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 订单物料视图对象 skd_order_material
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SkdOrderMaterialVo {

    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    private Long id;

    /**
     * 顶层单号
     */
    @ExcelProperty("顶层单号")
    private String topNo;

    /**
     * 销售单号Id
     */
    @ExcelIgnore
    private String coverSoId;

    /**
     * 销售单号
     */
    @ExcelProperty("销售单号")
    private String coverSoNo;

    /**
     * 销售单项次
     */
    @ExcelProperty("销售单项次")
    private String coverSoLine;

    /**
     * 计划单号
     */
    @ExcelProperty("计划单号")
    private String workNo;

    /**
     * 计划单项次
     */
    @ExcelProperty("计划单项次")
    private String workLine;

    /**
     * 客户代码
     */
    @ExcelIgnore
    private String customerCode;

    /** 订单数量 */
    @ExcelIgnore
    private Double qty;

    /** 产品编码 */
    @ExcelIgnore
    private String itemNo;

    /** 子件物料编码 */
    @ExcelProperty("子件物料编码")
    private String materialItemNo;

    /** 子件物料名称 */
    @ExcelProperty("子件物料名称")
    private String materialItemName;

    /** 物料组 */
    @ExcelProperty("物料组")
    private String itemGroup;

    /** 子件工厂 */
    @ExcelProperty("子件工厂")
    private String deliveryPlant;

    /** 库位 */
    @ExcelProperty("库位")
    private String defPlace;

    /** 库位描述 */
    @ExcelProperty("库位描述")
    private String defPlaceName;

    /** po库位 */
    @ExcelProperty("po库位")
    private String place;

    /** po库位描述 */
    @ExcelProperty("po库位描述")
    private String placeName;

    /** 物料采购类型 */
    @ExcelProperty("物料采购类型")
    private String poType;

    /** 包材物料库位 */
    @ExcelProperty("包材物料库位")
    private String itemPackPlace;

    /** 子件物料需求数量 */
    @ExcelProperty("子件物料需求数量")
    private Double needQty;

    /** 泰国在库分配满足数量 */
    @ExcelProperty("泰国在库分配满足数量")
    private Double inventoryQty;

    /** 剩余需求量 */
    @ExcelProperty("剩余需求量")
    private Double remainingQty;

    /** 在途数量 */
    @ExcelProperty("在途数量")
    private Double transitQty;

    /** PO剩余待中国出货 */
    @ExcelProperty("PO剩余待中国出货")
    private Double unTransitQty;

    /** 计划开始日期 */
    @ExcelIgnore
    private LocalDate planDate;

    /** 计划完工日期 */
    @ExcelIgnore
    private LocalDate plantFinish;

    /** 订单交期 */
    @ExcelIgnore
    private LocalDate orderDelivery;

    /** 船期 */
    @ExcelIgnore
    private LocalDate shipTime;

    /** 订单工厂 */
    @ExcelIgnore
    private String plant;

    /** 类型 */
    @ExcelIgnore
    private String workTypeName;

    /** 需求最晚齐套时间 */
    @ExcelIgnore
    private LocalDate planQitaoDate;

    /** 计划总齐套日期 */
    @ExcelIgnore
    private LocalDate planAllQitaoDate;

    /** 总齐套日期 */
    @ExcelIgnore
    private LocalDate allQitaoDate;

    /** 订单产品Id */
    @ExcelIgnore
    private Long orderProductId;

    /** 跨工厂状态 */
    @ExcelIgnore
    private String mstae;

    /** 子件需求日期 */
    @ExcelIgnore
    private LocalDate materialNeedDate;

    /**
     * 状态
     */
    @ExcelIgnore
    private String status;

}
