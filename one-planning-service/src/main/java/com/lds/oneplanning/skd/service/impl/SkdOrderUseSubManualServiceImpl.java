package com.lds.oneplanning.skd.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.oneplanning.esb.datafetch.model.EsbShipmentData;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.skd.domain.SkdOrderMaterial;
import com.lds.oneplanning.skd.domain.SkdOrderUseSub;
import com.lds.oneplanning.skd.domain.SkdOrderUseSubManual;
import com.lds.oneplanning.skd.mapper.SkdOrderUseSubManualMapper;
import com.lds.oneplanning.skd.service.SkdOrderUseSubManualService;
import com.lds.oneplanning.skd.service.SkdOrderUseSubService;
import com.lds.oneplanning.skd.utils.DateUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 使用订单子表人工填写数据(SkdOrderUseSubManual)服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-24 17:25:07
 */
@Service
@AllArgsConstructor
public class SkdOrderUseSubManualServiceImpl extends ServiceImpl<SkdOrderUseSubManualMapper,SkdOrderUseSubManual> implements SkdOrderUseSubManualService {

    private final SkdOrderUseSubService skdOrderUseSubService;
    private final IEsbDataFetchService esbService;
    @Override
    public List<SkdOrderUseSubManual> queryList(List<String> useNoList) {
        if(CollectionUtils.isEmpty(useNoList)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<SkdOrderUseSubManual> query = new  LambdaQueryWrapper<>();
        query.in(SkdOrderUseSubManual::getUseNo,useNoList);
        return baseMapper.selectList(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateManual(SkdOrderUseSubManual skdOrderUseSubManual) {
        SkdOrderUseSubManual oldSkdOrderUseSubManual = getSkdOrderUseSubManual(skdOrderUseSubManual.getUseNo(),skdOrderUseSubManual.getTopNo(),skdOrderUseSubManual.getUseType());
        if(oldSkdOrderUseSubManual != null){
            oldSkdOrderUseSubManual.setSendTime(skdOrderUseSubManual.getSendTime());
            oldSkdOrderUseSubManual.setTransportMode(skdOrderUseSubManual.getTransportMode());
            oldSkdOrderUseSubManual.setDistributionQty(skdOrderUseSubManual.getDistributionQty());
            baseMapper.updateById(oldSkdOrderUseSubManual);
        }else{
            baseMapper.insert(skdOrderUseSubManual);
        }
        SkdOrderUseSub old = skdOrderUseSubService.getSubOrderUseSub(skdOrderUseSubManual.getTopNo(),skdOrderUseSubManual.getUseNo(),skdOrderUseSubManual.getUseType());
        EsbShipmentData shipmentData = null;
        if(!Objects.isNull(old)){
            // 准备批量查询参数
            EsbShipmentData data = new EsbShipmentData();
            data.setVbeln(old.getOutboundDeliveryNo());
            data.setPosnr(old.getOutboundDeliveryLine());

            // 批量查询
            List<EsbShipmentData> esbShipmentDataList = esbService.getShipmentDateData(Lists.newArrayList(data));
            if(!CollectionUtils.isEmpty(esbShipmentDataList)){
                shipmentData = esbShipmentDataList.get(0);
            }
        }
        SkdOrderMaterial skdOrderMaterial = skdOrderUseSubService.getSkdOrderMaterial(skdOrderUseSubManual.getTopNo(),skdOrderUseSubManual.getUseNo());
        SkdOrderUseSub newSkdOrderUseSub = skdOrderUseSubService.fillData(
            DateUtil.getLocalDate(skdOrderMaterial.getMaterialNeedDate()),
            old,
            old,
            skdOrderUseSubManual,
            shipmentData
        );
        return skdOrderUseSubService.updateById(newSkdOrderUseSub);
    }

    @Override
    public Boolean batchSaveOrUpdateManual(List<SkdOrderUseSubManual> skdOrderUseSubManuals) {
        skdOrderUseSubManuals.stream().forEach(skdOrderUseSubManual -> {
            saveOrUpdateManual(skdOrderUseSubManual);
        });
        return true;
    }

    private SkdOrderUseSubManual getSkdOrderUseSubManual(String useNo,String topNo,String useType) {
        LambdaQueryWrapper<SkdOrderUseSubManual> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SkdOrderUseSubManual::getUseNo,useNo);
        queryWrapper.eq(SkdOrderUseSubManual::getTopNo,topNo);
        queryWrapper.eq(SkdOrderUseSubManual::getUseType,useType);
        return baseMapper.selectOne(queryWrapper);
    }
}
