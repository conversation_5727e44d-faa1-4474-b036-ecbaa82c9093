package com.lds.oneplanning.skd.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.skd.domain.SkdSale;
import com.lds.oneplanning.skd.domain.SkdSaleOrder;
import com.lds.oneplanning.skd.domain.bo.SkdSaleOrderBo;
import com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO;
import com.lds.oneplanning.skd.domain.vo.SkdSaleOrderVo;
import com.lds.oneplanning.skd.mapper.SkdSaleOrderMapper;
import com.lds.oneplanning.skd.service.ISkdSaleOrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * SKD销售订单(SkdSaleOrderServiceImpl)服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
@AllArgsConstructor
public class SkdSaleOrderServiceImpl extends ServiceImpl<SkdSaleOrderMapper, SkdSaleOrder> implements ISkdSaleOrderService {

    @Override
    public List<SkdSaleOrderVo> queryList(SkdSaleOrderBo bo) {
        List<SkdSaleOrder> res = baseMapper.selectList(buildQueryWrapper(bo));
        if (CollUtil.isEmpty(res)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(res, SkdSaleOrderVo.class);
    }

    @Override
    public void export(SkdSaleOrderBo bo, HttpServletResponse response) {
        List<SkdSaleOrderVo> list = this.queryList(bo);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=SKDSaleOrder.xlsx");
        try {
            this.extentInfo(list);
            EasyExcel.write(response.getOutputStream(), SkdSaleOrderVo.class)
                    .sheet("SKD整灯齐套信息")
                    .doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public int deleteAll() {
        return baseMapper.delete(Wrappers.lambdaQuery());
    }

    @Override
    public int insertFromSelect() {
        return baseMapper.insertFromSelect();
    }

    private LambdaQueryWrapper<SkdSaleOrder> buildQueryWrapper(SkdSaleOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SkdSaleOrder> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTopNo()), SkdSaleOrder::getTopNo, bo.getTopNo());
        lqw.like(StringUtils.isNotBlank(bo.getCoverSoId()), SkdSaleOrder::getCoverSoId, bo.getCoverSoId());
        lqw.in(CollUtil.isNotEmpty(bo.getCoverSoNoList()), SkdSaleOrder::getCoverSoNo, bo.getCoverSoNoList());
        lqw.like(StringUtils.isNotBlank(bo.getCoverSoNo()), SkdSaleOrder::getCoverSoNo, bo.getCoverSoNo());
        lqw.like(StringUtils.isNotBlank(bo.getOrderId()), SkdSaleOrder::getOrderId, bo.getOrderId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerCode()), SkdSaleOrder::getCustomerCode, bo.getCustomerCode());
        lqw.between(params.get("beginPlanStartDate") != null && params.get("endPlanStartDate") != null,
                SkdSaleOrder::getPlanStartDate ,params.get("beginPlanStartDate"), params.get("endPlanStartDate"));
        lqw.between(params.get("beginPlanEndDate") != null && params.get("endPlanEndDate") != null,
                SkdSaleOrder::getPlanEndDate ,params.get("beginPlanEndDate"), params.get("endPlanEndDate"));
        lqw.between(params.get("beginShipTime") != null && params.get("endShipTime") != null,
                SkdSaleOrder::getShipTime ,params.get("beginShipTime"), params.get("endShipTime"));
        lqw.eq(StringUtils.isNotBlank(bo.getPlant()), SkdSaleOrder::getPlant, bo.getPlant());
        lqw.between(params.get("beginPlanQitaoDate") != null && params.get("endPlanQitaoDate") != null,
                SkdSaleOrder::getPlanReadyDate ,params.get("beginPlanReadyDate"), params.get("endPlanReadyDate"));
        lqw.between(params.get("beginSkdReadyDate") != null && params.get("endSkdReadyDate") != null,
                SkdSaleOrder::getSkdReadyDate ,params.get("beginSkdReadyDate"), params.get("endSkdReadyDate"));
        lqw.between(params.get("beginSkdReadyDateShip") != null && params.get("endSkdReadyDateShip") != null,
                SkdSaleOrder::getSkdReadyDateShip ,params.get("beginSkdReadyDateShip"), params.get("endSkdReadyDateShip"));
        lqw.between(params.get("beginSkdReadyDateNonShip") != null && params.get("endSkdReadyDateNonShip") != null,
                SkdSaleOrder::getSkdReadyDateNonShip ,params.get("beginSkdReadyDateNonShip"), params.get("endSkdReadyDateNonShip"));
        return lqw;
    }

    private void extentInfo(List<SkdSaleOrderVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        list.forEach(item -> {
            if (1 == item.getSkdAllSend()) {
                item.setSkdAllSendStr("是");
            } else {
                item.setSkdAllSendStr("否");
            }
        });
    }
}
