package com.lds.oneplanning.skd.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.skd.domain.SkdOrderUse;
import com.lds.oneplanning.skd.domain.SkdPurchaseLogistics;
import com.lds.oneplanning.skd.domain.bo.SkdOrderUseBo;
import com.lds.oneplanning.skd.domain.vo.SkdOrderUseVo;
import com.lds.oneplanning.skd.mapper.SkdOrderUseMapper;
import com.lds.oneplanning.skd.service.ISkdOrderUseService;
import com.lds.oneplanning.skd.service.SkdPurchaseLogisticsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SkdOrderUseServiceImpl extends ServiceImpl<SkdOrderUseMapper, SkdOrderUse> implements ISkdOrderUseService {

    @Resource
    SkdPurchaseLogisticsService skdPurchaseLogisticsService;

    @Override
    public List<SkdOrderUseVo> queryList(SkdOrderUseBo bo) {
        LambdaQueryWrapper<SkdOrderUse> lqw = buildQueryWrapper(bo);
        List<SkdOrderUse> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(list, SkdOrderUseVo.class);
    }

    @Override
    public Page<SkdOrderUseVo> queryPage(SkdOrderUseBo bo) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SkdOrderUse> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(bo.getPage(), bo.getPageSize());
        LambdaQueryWrapper<SkdOrderUse> lqw = buildQueryWrapper(bo);
        entityPage = baseMapper.selectPage(entityPage, lqw);

        Page<SkdOrderUseVo> resultPage = new Page<>(bo.getPage(), bo.getPageSize());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.copyProperties(entityPage, Page.class);
            List<SkdOrderUseVo> results = BeanUtil.copyToList(entityPage.getRecords(), SkdOrderUseVo.class);
            resultPage.setPageNum(bo.getPage());
            resultPage.setPageSize(bo.getPageSize());

            this.extendInfo(results);

            resultPage.setResult(results);
        }

        return resultPage;
    }

    private LambdaQueryWrapper<SkdOrderUse> buildQueryWrapper(SkdOrderUseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SkdOrderUse> lqw = Wrappers.lambdaQuery();
        lqw.eq(Objects.nonNull(bo.getOrderMaterialId()), SkdOrderUse::getOrderMaterialId, bo.getOrderMaterialId());
        lqw.like(StringUtils.isNotBlank(bo.getTopNo()), SkdOrderUse::getTopNo, bo.getTopNo());
        lqw.like(StringUtils.isNotBlank(bo.getCoverSoNo()), SkdOrderUse::getCoverSoNo, bo.getCoverSoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverSoLine()), SkdOrderUse::getCoverSoLine, bo.getCoverSoLine());
        lqw.like(StringUtils.isNotBlank(bo.getWorkNo()), SkdOrderUse::getWorkNo, bo.getWorkNo());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkLine()), SkdOrderUse::getWorkLine, bo.getWorkLine());
        lqw.like(StringUtils.isNotBlank(bo.getMaterialItemNo()), SkdOrderUse::getMaterialItemNo, bo.getMaterialItemNo());
        lqw.like(StringUtils.isNotBlank(bo.getUseTypeName()), SkdOrderUse::getUseTypeName, bo.getUseTypeName());
        lqw.like(StringUtils.isNotBlank(bo.getUseNo()), SkdOrderUse::getUseNo, bo.getUseNo());
        lqw.eq(StringUtils.isNotBlank(bo.getUseNote()), SkdOrderUse::getUseNote, bo.getUseNote());
        lqw.like(StringUtils.isNotBlank(bo.getSupply()), SkdOrderUse::getSupply, bo.getSupply());
        lqw.eq(StringUtils.isNotBlank(bo.getUnit()), SkdOrderUse::getUnit, bo.getUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getPoGroup()), SkdOrderUse::getPoGroup, bo.getPoGroup());
        lqw.eq(StringUtils.isNotBlank(bo.getPoGroupR()), SkdOrderUse::getPoGroupR, bo.getPoGroupR());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SkdOrderUse::getStatus, bo.getStatus());
        return lqw;
    }

    public Map<String, Long> queryMapByTopNo(String topNo) {
        List<SkdOrderUse> list = baseMapper.selectList(Wrappers.<SkdOrderUse>lambdaQuery().eq(SkdOrderUse::getTopNo, topNo));
        Map<String, Long> map = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            map = list.stream().collect(Collectors.toMap(item -> item.getKey(), SkdOrderUse::getId));
        }
        return map;
    }

    @Override
    public void saveBatchByTopNo(String topNo, Collection<SkdOrderUse> skdOrderUseData) {
        if (CollUtil.isEmpty(skdOrderUseData)) {
            return;
        }

        // 执行删除
        this.deleteByTopNo(topNo);

        super.saveBatch(skdOrderUseData);
    }

    private void deleteByTopNo(String topNo) {
        LambdaQueryWrapper<SkdOrderUse> lqw = Wrappers.lambdaQuery();
        lqw.like(SkdOrderUse::getTopNo, topNo);
        baseMapper.delete(lqw);
    }

    private void extendInfo(List<SkdOrderUseVo> list) {
        List<SkdPurchaseLogistics> logisticsBoList = new ArrayList<>();
        list.forEach(item -> {
            if (StringUtils.isNotBlank(item.getUseNo())) {
                SkdPurchaseLogistics logistics = new SkdPurchaseLogistics();
                logistics.setPurchaseNo(item.getUseNo());
                logisticsBoList.add(logistics);
            }
        });

        Map<String, SkdPurchaseLogistics> logisticsMap = skdPurchaseLogisticsService.getExistPurchaseMap(logisticsBoList);
        list.forEach(item -> {
            if (logisticsMap.containsKey(item.getUseNo())) {
                item.setHasLogistics("Y");
            } else {
                item.setHasLogistics("N");
            }
        });
    }
}
