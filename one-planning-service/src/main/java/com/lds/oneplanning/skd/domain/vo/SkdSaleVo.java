package com.lds.oneplanning.skd.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 销售单视图对象 skd_sale
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SkdSaleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 顶层单号
     */
    private String topNo;

    /**
     * 销售单号
     */
    private String coverSoNo;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 计划开始日期
     */
    private LocalDate planStartDate;

    /**
     * 计划完工日期
     */
    private LocalDate planEndDate;

    /**
     * 订单数量
     */
    private Double qty;

    /**
     * 船期
     */
    private LocalDate shipTime;

    /**
     * 订单工厂
     */
    private String plant;

    /**
     * 需求最晚齐套时间
     */
    private LocalDate planReadyDate;

    /**
     * skd可发货日期
     */
    private LocalDate skdReadyDate;

    /**
     * 海运入库时间
     */
    private LocalDate skdReadyDateShip;

    /**
     * 非海运入库时间
     */
    private LocalDate skdReadyDateNonShip;

    /**
     * 陆运笔数
     */
    private int landNum;

    /**
     * 空运笔数
     */
    private int airNum;

    /**
     * 状态
     */
    private String status;

    /**
     * skd是否全发货
     */
    private int skdAllSend;
}
