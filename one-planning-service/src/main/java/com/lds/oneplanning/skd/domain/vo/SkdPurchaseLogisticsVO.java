package com.lds.oneplanning.skd.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.lds.oneplanning.skd.utils.converter.IntegerConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 采购物流表(SkdPurchaseLogistics)视图实体类
 *
 * <AUTHOR>
 * @since 2025-05-23 11:49:46
 */
@Data
@ApiModel(value = "SkdPurchaseLogistics对象", description = "采购物流表")
public class SkdPurchaseLogisticsVO {

    @ExcelIgnore
    private Long id;

    @ApiModelProperty(value = "顶层单号")
    @ExcelProperty(value = "顶层单号")
    private String topNo;

    /**
     * 销售单号
     */
    @ApiModelProperty(value = "销售单号")
    @ExcelProperty(value = "销售单号")
    private String coverSoNo;

    /**
     * 计划单号
     */
    @ApiModelProperty(value = "计划单号")
    @ExcelProperty(value = "计划单号")
    private String workNo;

    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    @ExcelProperty(value = "采购单号")
    private String purchaseNo;

    /**
     * 子件物料编码
     */
    @ApiModelProperty(value = "子件物料编码")
    @ExcelProperty(value = "子件物料编码")
    private String materialItemNo;

    /**
     * 子件物料名称
     */
    @ApiModelProperty(value = "子件物料名称")
    @ExcelProperty(value = "子件物料名称")
    private String materialItemName;

    /**
     * 库位
     */
    @ApiModelProperty(value = "库位")
    @ExcelProperty(value = "库位")
    private String defPlace;

    /**
     * 库位描述
     */
    @ApiModelProperty(value = "库位描述")
    @ExcelProperty(value = "库位描述")
    private String defPlaceName;

    /**
     * po库位
     */
    @ApiModelProperty(value = "po库位")
    @ExcelProperty(value = "po库位")
    private String place;

    /**
     * po库位描述
     */
    @ApiModelProperty(value = "po库位描述")
    @ExcelProperty(value = "po库位描述")
    private String placeName;

    /**
     * 包材物料库位
     */
    @ApiModelProperty(value = "包材物料库位")
    @ExcelProperty(value = "包材物料库位")
    private String itemPackPlace;

    /**
     * 物料采购类型
     */
    @ApiModelProperty(value = "物料采购类型")
    @ExcelProperty(value = "物料采购类型")
    private String poType;

    /**
     * 外向单
     */
    @ApiModelProperty(value = "外向单")
    @ExcelProperty(value = "外向单")
    private String outboundDeliveryNo;

    /**
     * 外向单项次
     */
    @ApiModelProperty(value = "外向单项次")
    @ExcelProperty(value = "外向单项次")
    private String outboundDeliveryLine;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量")
    private Double transitQty;

    /**
     * 关联物流单号
     */
    @ApiModelProperty(value = "关联物流单号")
    @ExcelProperty(value = "关联物流单号")
    private String logisticsNo;

    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    @ExcelProperty(value = "运输方式")
    private String transportMode;

    /**
     * 发出日期
     */
    @ApiModelProperty(value = "发出日期")
    @ExcelProperty(value = "发出日期")
    private String sendDate;

    /**
     * 目前状态
     */
    @ApiModelProperty(value = "目前状态")
    @ExcelProperty(value = "目前状态")
    private String status;

    /**
     * 系统ETA
     */
    @ApiModelProperty(value = "系统ETA")
    @ExcelProperty(value = "系统ETA")
    private String eta;

    @ApiModelProperty("预计到门")
    @ExcelProperty(value = "预计到门")
    private String planArriveFactoryDate;


    @ApiModelProperty("实际到门时间")
    @ExcelProperty(value = "实际到门时间")
    private String arriveFactoryDate;

    /**
     * 到泰日期
     */
    @ApiModelProperty(value = "到泰日期")
    @ExcelProperty(value = "到泰日期")
    private String thaiArriveDate;

    /**
     * 是否异常
     */
    @ApiModelProperty(value = "是否异常")
    @ExcelProperty(value = "是否异常",converter = IntegerConverter.class)
    private Integer isAbnormal;

    /**
     * 距离需求入库时间
     */
    @ApiModelProperty(value = "距离需求入库时间")
    @ExcelProperty(value = "距离需求入库时间")
    private Integer gapDays;

    /**
     * 是否需要加急清关
     */
    @ApiModelProperty(value = "是否需要加急清关")
    @ExcelProperty(value = "是否需要加急清关",  converter = IntegerConverter.class)
    private Integer isNeedUrgent;




    /**
     * 销售单项次
     */
    @ApiModelProperty(value = "销售单项次")
    @ExcelIgnore
    private String coverSoLine;

    /**
     * 客户代码
     */
    @ApiModelProperty(value = "客户代码")
    @ExcelIgnore
    private String customerCode;



    /**
     * 计划单项次
     */
    @ApiModelProperty(value = "计划单项次")
    @ExcelIgnore
    private String workLine;

    /**
     * 计划开始日期
     */
    @ApiModelProperty(value = "计划开始日期")
    @ExcelIgnore
    private String planDate;

    /**
     * 计划完工日期
     */
    @ApiModelProperty(value = "计划完工日期")
    @ExcelIgnore
    private String plantFinish;




}
