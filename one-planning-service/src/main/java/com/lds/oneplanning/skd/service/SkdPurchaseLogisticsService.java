package com.lds.oneplanning.skd.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.skd.domain.SkdPurchaseLogistics;
import com.lds.oneplanning.skd.domain.bo.SkdPurchaseLogisticsBo;
import com.lds.oneplanning.skd.domain.vo.SkdPurchaseLogisticsVO;
import com.lds.wts.thtask.vo.req.ThTaskLogisticsInfoQueryReq;
import com.lds.wts.thtask.vo.resp.ThTaskLogisticsInfoResp;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 采购物流表(SkdPurchaseLogistics)服务接口
 *
 * <AUTHOR>
 * @since 2025-05-23 11:49:49
 */
public interface SkdPurchaseLogisticsService extends IService<SkdPurchaseLogistics> {

    Map<String, SkdPurchaseLogistics> getExistPurchaseMap(List<SkdPurchaseLogistics> params);
    /**
     * 查询物流信息
     * @Description
     *
     * @param req
     * @return
     */
    List<ThTaskLogisticsInfoResp> queryLogisticsInfo(List<ThTaskLogisticsInfoQueryReq>  req);

    List<SkdPurchaseLogistics> calculateAndFilledData(List<SkdPurchaseLogistics> list);

    /**
     * 计算并填充数据
     * @param list 外向单列表
     * @param dueDate 需求日期
     * @param topNo 顶层单号
     * @return
     */
    List<SkdPurchaseLogistics> calculateAndFilledData(List<SkdPurchaseLogistics> list,String topNo);

    /**
     * 同步物流信息
     * @param topNo 顶层单号
     * @param list 物流外向单及行号 信息
     */
    void syncLogisticsInfo(String topNo,List<SkdPurchaseLogistics> list);

    void batchSaveData(List<SkdPurchaseLogistics> list);

    Page<SkdPurchaseLogisticsVO> queryPage(SkdPurchaseLogisticsBo bo);

   void export(SkdPurchaseLogisticsBo bo, HttpServletResponse response);
}
