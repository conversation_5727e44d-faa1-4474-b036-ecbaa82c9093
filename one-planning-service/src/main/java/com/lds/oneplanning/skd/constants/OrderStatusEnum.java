package com.lds.oneplanning.skd.constants;

import java.util.Arrays;
import java.util.Collections;

public enum OrderStatusEnum {

    COMPLETE("1", "已完成"),
    DELETED("-1", "已删除"),
    UN_COMPLETE("0", "未完成"),
    UNKNOWN("99", "未知");

    private final String code;
    private final String desc;

    OrderStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static OrderStatusEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(e -> e.code.equals(code))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
