package com.lds.oneplanning.skd.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.esb.datafetch.model.EsbShipmentData;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.skd.constants.TransportMode;
import com.lds.oneplanning.skd.domain.SkdPurchaseLogistics;
import com.lds.oneplanning.skd.domain.bo.SkdPurchaseLogisticsBo;
import com.lds.oneplanning.skd.domain.vo.SalesOrderInfoVO;
import com.lds.oneplanning.skd.domain.vo.SkdPurchaseLogisticsVO;
import com.lds.oneplanning.skd.mapper.SkdPurchaseLogisticsMapper;
import com.lds.oneplanning.skd.service.SkdPurchaseLogisticsService;
import com.lds.oneplanning.skd.utils.DateUtil;
import com.lds.oneplanning.skd.utils.SKDWLUtil;
import com.lds.wts.thtask.api.IThTaskLogisticsInfoApi;
import com.lds.wts.thtask.vo.req.ThTaskLogisticsInfoQueryReq;
import com.lds.wts.thtask.vo.resp.ThTaskLogisticsInfoResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 采购物流表(SkdPurchaseLogistics)服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-23 11:49:50
 */
@Service
@AllArgsConstructor
@Slf4j
public class SkdPurchaseLogisticsServiceImpl extends ServiceImpl<SkdPurchaseLogisticsMapper,SkdPurchaseLogistics> implements SkdPurchaseLogisticsService {

    private final IThTaskLogisticsInfoApi thTaskLogisticsInfoApi;
    private final EsbDataFetchService esbDataFetchService;

    /**
     * 获取存在的采购Map
     * @param params
     * @return
     */
    public Map<String, SkdPurchaseLogistics> getExistPurchaseMap(List<SkdPurchaseLogistics> params) {
        if (CollUtil.isEmpty(params)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<SkdPurchaseLogistics> lqw = new LambdaQueryWrapper<>();
        lqw.select(SkdPurchaseLogistics::getPurchaseNo);
        lqw.and(wrapper -> {
            for (SkdPurchaseLogistics vo : params) {
                wrapper.or().eq(SkdPurchaseLogistics::getPurchaseNo, vo.getPurchaseNo());
            }
        });
        List<SkdPurchaseLogistics> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(SkdPurchaseLogistics::getPurchaseNo, e -> e, (e1, e2) -> e1));
    }

    /**
     * 根据请求查询物流信息
     *
     * @param reqList 请求的物流信息查询请求列表
     * @return 返回物流信息查询响应列表
     */
    @Override
    public List<ThTaskLogisticsInfoResp> queryLogisticsInfo(List<ThTaskLogisticsInfoQueryReq> reqList) {
        reqList.removeIf(Objects::isNull);
        reqList.removeIf(e -> StringUtils.isEmpty(e.getOutgoingOrderItemNo()) && StringUtils.isEmpty(e.getOutgoingOrderNo()));

        int batchSize = 20;
        log.info("开始查询物流信息，请求参数长度：{}，按{}个为一批次进行查询", reqList.size(), batchSize);

        return Lists.partition(reqList, batchSize)
                .parallelStream()
                .map(this::queryApiIgnoreException)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    private List<ThTaskLogisticsInfoResp> queryApiIgnoreException(List<ThTaskLogisticsInfoQueryReq> reqList) {
        try {
            return queryApi(reqList);
        } catch (Exception e) {
            log.error("查询物流信息失败", e);
            return Lists.newArrayList();
        }
    }
    private List<ThTaskLogisticsInfoResp> queryApi(List<ThTaskLogisticsInfoQueryReq> reqList) {
        // 查询物流信息列表
        List<ThTaskLogisticsInfoResp> list = thTaskLogisticsInfoApi.queryThTaskLogisticsInfo(reqList);

        // 将物流信息列表转换为以订单号和物品号作为键的Map
        Map<String, ThTaskLogisticsInfoResp> map = list.stream().collect(Collectors.toMap(item -> item.getOutgoingOrderNo() + item.getDeliveryItem(), item -> item, (v1, v2) -> v1));

        // 收集所有需要查询的EsbShipmentData
        // 过滤出需要查询的物流信息
        List<EsbShipmentData> esbQueryList = reqList.stream()
            // 过滤条件：物流信息为空或者当前节点为空
            .filter(req -> map.get(req.getOutgoingOrderNo() + req.getOutgoingOrderItemNo()) == null
                    || StringUtils.isEmpty(map.get(req.getOutgoingOrderNo() + req.getOutgoingOrderItemNo()).getCurrentNode()))
            // 转换为EsbShipmentData对象
            .map(req -> {
                EsbShipmentData esb = new EsbShipmentData();
                esb.setVbeln(req.getOutgoingOrderNo());
                esb.setPosnr(req.getOutgoingOrderItemNo());
                return esb;
            })
            .collect(Collectors.toList());

        // 批量查询
        // 初始化查询结果Map
        Map<String, EsbShipmentData> shipmentStatusMap = Maps.newHashMap();
        // 判断是否需要查询
        if (CollUtil.isNotEmpty(esbQueryList)) {
            // 查询物流状态数据
            List<EsbShipmentData> esbShipmentDataList = esbDataFetchService.getShipmentDateData(esbQueryList);
            // 判断查询结果是否为空
            if (CollUtil.isNotEmpty(esbShipmentDataList)) {
                // 将查询结果转换为以订单号和物品号作为键的Map
                shipmentStatusMap = esbShipmentDataList.stream()
                    .collect(Collectors.toMap(
                        data -> data.getVbeln() + data.getPosnr(),
                        Function.identity(),
                        (v1, v2) -> v1
                    ));
            }
        }

        // 构建结果
        // 将查询结果Map赋值给finalShipmentStatusMap
        Map<String, EsbShipmentData> finalShipmentStatusMap = shipmentStatusMap;
        // 根据请求列表构建响应列表
        return reqList.stream().map(req -> {
            // 获取物流信息响应对象
            ThTaskLogisticsInfoResp resp = map.get(req.getOutgoingOrderNo() + req.getOutgoingOrderItemNo());
            // 判断物流信息响应对象是否为空
            if (resp == null) {
                // 如果为空，则创建新的响应对象
                resp = new ThTaskLogisticsInfoResp();
                resp.setOutgoingOrderNo(req.getOutgoingOrderNo());
                resp.setDeliveryItem(req.getOutgoingOrderItemNo());
            }
            EsbShipmentData esbShipmentData = finalShipmentStatusMap.get(req.getOutgoingOrderNo() + req.getOutgoingOrderItemNo());
            if(esbShipmentData != null){

                // 判断当前节点是否为空
                if (StringUtils.isEmpty(resp.getCurrentNode())) {
                    // 如果为空，则从查询结果Map中获取当前节点状态
                    resp.setCurrentNode(esbShipmentData.getZState());
                }
                if(Objects.isNull(resp.getETA())){
                    resp.setTransportType(esbShipmentData.getTransportMode());
                    try {
                        resp.setIssueDate(new SimpleDateFormat("yyyy-MM-DD").parse(esbShipmentData.getKzDate()));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            // 返回响应对象
            return resp;
        }).collect(Collectors.toList());
    }


    @Override
    public List<SkdPurchaseLogistics> calculateAndFilledData(List<SkdPurchaseLogistics> list) {
        if(CollUtil.isEmpty(list)){
            return list;
        }
        Map<String, SkdPurchaseLogistics> map = list.stream().distinct()
                .collect(Collectors.toMap(item -> item.getOutboundDeliveryNo() + item.getOutboundDeliveryLine(), item -> item, (v1, v2) -> v1));

        List<ThTaskLogisticsInfoQueryReq> reqList = list.stream().distinct()
                .map(item -> {
                    ThTaskLogisticsInfoQueryReq req = new ThTaskLogisticsInfoQueryReq();
                    req.setOutgoingOrderNo(item.getOutboundDeliveryNo());
                    req.setOutgoingOrderItemNo(item.getOutboundDeliveryLine());
                    return req;
                })
                .collect(Collectors.toList());

        List<ThTaskLogisticsInfoResp> respList = queryLogisticsInfo(reqList);
        return respList.stream().map(resp -> covertData(resp, map)).collect(Collectors.toList());
    }

    @NotNull
    private static SkdPurchaseLogistics covertData(ThTaskLogisticsInfoResp resp, Map<String, SkdPurchaseLogistics> map) {
        SkdPurchaseLogistics skdPurchaseLogistics = map.get(resp.getOutgoingOrderNo() + resp.getDeliveryItem());
        skdPurchaseLogistics.setLogisticsNo(resp.getBillOfLoadingNo());
        skdPurchaseLogistics.setStatus(resp.getCurrentNode());
        skdPurchaseLogistics.setEta(resp.getETA());
        skdPurchaseLogistics.setSendDate(resp.getIssueDate());
        skdPurchaseLogistics.setTransportMode(resp.getTransportType());
        skdPurchaseLogistics.setPlanArriveFactoryDate(resp.getPlanArriveFactoryDate());
        skdPurchaseLogistics.setArriveFactoryDate(resp.getArriveFactoryDate());

        SKDWLUtil skdWLUtil = new SKDWLUtil(DateUtil.getLocalDate(skdPurchaseLogistics.getMaterialNeedDate()),
                DateUtil.getLocalDate(skdPurchaseLogistics.getEta()),
                DateUtil.getLocalDate(skdPurchaseLogistics.getPlanArriveFactoryDate()),
                DateUtil.getLocalDate(skdPurchaseLogistics.getArriveFactoryDate()),
                DateUtil.getLocalDate(skdPurchaseLogistics.getSendDate()),
                TransportMode.fromDescription(resp.getTransportType()));
        skdPurchaseLogistics.setThaiArriveDate( DateUtil.getDate(skdWLUtil.getThaiArrivalDate()));
        skdPurchaseLogistics.setGapDays(skdWLUtil.getPullDays());
        skdPurchaseLogistics.setIsNeedUrgent(skdWLUtil.isNeedUrgentClearance() ? 1 : 0);
        skdPurchaseLogistics.setThaiArriveInboundDate(DateUtil.getDate(skdWLUtil.getThaiArrivalInboundDate()));

        return skdPurchaseLogistics;
    }

    @Override
    public List<SkdPurchaseLogistics> calculateAndFilledData(List<SkdPurchaseLogistics> list,String topNo) {
        if(CollUtil.isEmpty(list)){
            return list;
        }
        Map<String, SkdPurchaseLogistics> map = list.stream().filter(item -> StringUtils.isNotEmpty(item.getOutboundDeliveryNo()) && StringUtils.isNotEmpty(item.getOutboundDeliveryLine())).collect(Collectors.toMap(item -> item.getOutboundDeliveryNo() + item.getOutboundDeliveryLine(), item -> item, (v1, v2) -> v1));

        List<ThTaskLogisticsInfoQueryReq> reqList = list.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOutboundDeliveryLine()) && StringUtils.isNotEmpty(e.getOutboundDeliveryLine()))
                .map(item -> {
                    ThTaskLogisticsInfoQueryReq req = new ThTaskLogisticsInfoQueryReq();
                    req.setOutgoingOrderNo(item.getOutboundDeliveryNo());
                    req.setOutgoingOrderItemNo(item.getOutboundDeliveryLine());
                    return req;
                })
                .collect(Collectors.toList());

        List<ThTaskLogisticsInfoResp> respList = queryLogisticsInfo(reqList);
        return respList.stream().map(resp -> {
            SkdPurchaseLogistics skdPurchaseLogistics = covertData(resp, map);
            skdPurchaseLogistics.setTopNo(topNo);
            return skdPurchaseLogistics;
        }).collect(Collectors.toList());
    }

    @Override
    public void syncLogisticsInfo(String topNo, List<SkdPurchaseLogistics> list) {
        list = calculateAndFilledData(list, topNo);
        deleteByTopNo(topNo);
        saveBatch(list);
    }

    @Override
    public void batchSaveData(List<SkdPurchaseLogistics> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        delete(list.stream().distinct().collect(Collectors.toList()));
        saveBatch(list);
    }

    @Override
    public Page<SkdPurchaseLogisticsVO> queryPage(SkdPurchaseLogisticsBo bo) {
        Page<SkdPurchaseLogisticsVO> result = new Page<>(bo.getPage(), bo.getPageSize());
        baseMapper.queryPage(result,bo);
        result.setRecords(dataConvert(result.getRecords()));
        return result;
    }

    @NotNull
    private static List<SkdPurchaseLogisticsVO> dataConvert(List<SkdPurchaseLogisticsVO> data) {
        return data.stream().peek(item -> {
            for (java.lang.reflect.Field field : item.getClass().getDeclaredFields()) {
                if (field.getType() == String.class) {
                    field.setAccessible(true);
                    try {
                        String value = (String) field.get(item);
                        if (org.springframework.util.StringUtils.hasLength(value)) {
                            field.set(item, value.replace(" 00:00:00", ""));
                        }
                    } catch (IllegalAccessException e) {
                        log.error("反射获取属性值失败：{}", field.getName(), e);
                    }
                }
            }
        }).collect(Collectors.toList());
    }

    @Override
    public void export(SkdPurchaseLogisticsBo bo, HttpServletResponse response) {
       List<SkdPurchaseLogisticsVO> list = baseMapper.queryList(bo);
       list = dataConvert(list);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=SKDPurchaseLogisticsDetail.xlsx");
        try {
            EasyExcel.write(response.getOutputStream(), SkdPurchaseLogisticsVO.class)
                    .sheet("SKD物流明细")
                    .doWrite(list);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private boolean deleteByTopNo(String topNo){
        if(StringUtils.isEmpty(topNo)){
            return false;
        }
        LambdaQueryWrapper<SkdPurchaseLogistics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SkdPurchaseLogistics::getTopNo,topNo);
        return baseMapper.delete(queryWrapper) > 0;
    }
    private boolean delete(List<SkdPurchaseLogistics> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        LambdaQueryWrapper<SkdPurchaseLogistics> queryWrapper = new LambdaQueryWrapper<>();
        list.forEach(item -> {
            queryWrapper.or().eq(SkdPurchaseLogistics::getOutboundDeliveryNo, item.getOutboundDeliveryNo())
                    .eq(SkdPurchaseLogistics::getOutboundDeliveryLine,item.getOutboundDeliveryLine());
        });
        return baseMapper.delete(queryWrapper) > 0;
    }

    private boolean deleteByTopNo(List<String> topNos){
        if(CollectionUtils.isEmpty(topNos)){
            return false;
        }
        LambdaQueryWrapper<SkdPurchaseLogistics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SkdPurchaseLogistics::getTopNo,topNos);
        return baseMapper.delete(queryWrapper) > 0;
    }

}
