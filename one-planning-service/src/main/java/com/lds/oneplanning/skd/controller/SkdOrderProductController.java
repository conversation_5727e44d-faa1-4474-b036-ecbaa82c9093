package com.lds.oneplanning.skd.controller;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.skd.domain.bo.SkdOrderProductBo;
import com.lds.oneplanning.skd.domain.vo.SkdOrderProductVo;
import com.lds.oneplanning.skd.service.ISkdOrderProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@Api(value = "SkdOrderProductController", tags = "SKD订单产品（整机层次）")
@RestController
@AllArgsConstructor
@RequestMapping("/skd/order/product")
public class SkdOrderProductController {

    @Resource
    ISkdOrderProductService orderProductService;

    @ApiOperation(value = "分页查询", notes = "SKD订单产品分页查询（整机层次）")
    @GetMapping("/page")
    public Page<SkdOrderProductVo> page(SkdOrderProductBo bo) {
        return orderProductService.queryPage(bo);
    }

    /**
     * 编辑订单
     */

}
