package com.lds.oneplanning.skd.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.lds.oneplanning.common.service.impl.LocalDateStringConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 销售订单视图对象 skd_sale_order
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SkdSaleOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    private Long id;

    /**
     * 顶层单号
     */
    @ExcelIgnore
    private String topNo;

    /**
     * 销售单Id
     */
    @ExcelProperty("销售单号（整机）")
    private String coverSoId;

    /**
     * 销售单号
     */
    @ExcelIgnore
    private String coverSoNo;

    /**
     * 订单Id
     */
    @ExcelProperty("计划订单/生产订单（整灯/组件/部件）")
    private String orderId;

    /**
     * 客户代码
     */
    @ExcelProperty("客户编码")
    private String customerCode;


    /**
     * 订单数量
     */
    @ExcelProperty("订单数量")
    private Double qty;

    /**
     * 产品编码
     */
    @ExcelProperty("产品编码")
    private String productCode;

    /**
     * 计划开始日期
     */
    @ExcelProperty(value = "计划开始日期", converter = LocalDateStringConverter.class)
    private LocalDate planStartDate;

    /**
     * 计划完工日期
     */
    @ExcelProperty(value = "计划完工日期", converter = LocalDateStringConverter.class)
    private LocalDate planEndDate;

    /**
     * 船期
     */
    @ExcelProperty(value = "船期", converter = LocalDateStringConverter.class)
    private LocalDate shipTime;

    /**
     * 订单工厂
     */
    @ExcelProperty("订单工厂")
    private String plant;

    /**
     * 需求最晚齐套时间
     */
    @ExcelProperty(value = "需求最晚齐套时间", converter = LocalDateStringConverter.class)
    private LocalDate planReadyDate;

    /**
     * skd可发货日期
     */
    @ExcelProperty(value = "3317整单齐套可发货时间", converter = LocalDateStringConverter.class)
    private LocalDate skdReadyDate;

    /**
     * skd是否全发货
     */
    @ExcelIgnore
    private int skdAllSend;

    /**
     * skd是否全发货
     */
    @ExcelProperty("3317是否已全部发货")
    private String skdAllSendStr;

    /**
     * 海运入库时间
     */
    @ExcelProperty(value = "整单预计到泰入库配套时间（仅海运）", converter = LocalDateStringConverter.class)
    private LocalDate skdReadyDateShip;

    /**
     * 非海运入库时间
     */
    @ExcelProperty(value = "整单预计到泰入库配套时间（含陆运空运）", converter = LocalDateStringConverter.class)
    private LocalDate skdReadyDateNonShip;

    /**
     * 陆运笔数
     */
    @ExcelProperty("陆运笔数")
    private int landNum;

    /**
     * 空运笔数
     */
    @ExcelProperty("空运笔数")
    private int airNum;

    /**
     * 状态
     */
    @ExcelIgnore
    private String status;

}
