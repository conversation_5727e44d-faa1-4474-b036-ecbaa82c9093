package com.lds.oneplanning.skd.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.skd.domain.SkdPurchaseLogistics;
import com.lds.oneplanning.skd.domain.bo.SkdPurchaseLogisticsBo;
import com.lds.oneplanning.skd.domain.vo.SkdPurchaseLogisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购物流表(SkdPurchaseLogistics)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-23 11:49:48
 */
@Mapper
public interface SkdPurchaseLogisticsMapper extends BaseMapper<SkdPurchaseLogistics> {

    Page<SkdPurchaseLogisticsVO> queryPage(Page<SkdPurchaseLogisticsVO> result, @Param("bo")SkdPurchaseLogisticsBo bo);
    List<SkdPurchaseLogisticsVO> queryList(@Param("bo")SkdPurchaseLogisticsBo bo);
}
