package com.lds.oneplanning.skd.utils;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.collect.Sets;
import com.lds.oneplanning.skd.constants.TransportMode;
import org.jetbrains.annotations.NotNull;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * SKD发货计算
 *  1、泰国需求发货日期（基于最佳运输方式）
 * 	2、需要提拉天数
 * 	3、泰国需求发运方式
 * 	4、预计中国发货时间
 * 	5、预计到泰入库日期
 * 	6、是否能满足泰国上线需求（连空运也满足不了，要调整计划）
 * 	7、预计海运最快泰国入库日期
 * 	8、泰国需求入库日期
 * 	9、gap
 * 	10、最新建议运输方式
 * 	11、是否可发状态
 */
public class SKDFHUtil {
    /**
     * 需求日期（泰国需求日期、到泰需求日期、泰国需求入库日期、到泰需求入库日期、需求日期、物料需求日期、子件物料需求时间）
     */
    private LocalDate dueDate;
    /**
     *最新交期
     */
    private LocalDate originalLatestDueDate;

    /**
     * 收货处理时间(天）
     */
    private int receiveProcessTime;

    /**
     * 发货时间(人工填写）
     */
    private LocalDate shippingTime;

    /**
     * 原运输方式
     */
    private TransportMode transportMode;

    /**
     *  人工填写的运输方式
     */
    private TransportMode transportModeManual;

    /**
     * 是否已放舱
     */
    private boolean isLoaded;

    /**
     * 使用类型
     */
    private String useType;

    /**
     * 厂代码
     */
    private String factoryCode;
    /**
     * 物料需求 lead time 天数
     */
    private Integer materialLeadTimeDays;

    /**
     * 构造器
     * @param dueDate 需求日期
     * @param originalLatestDueDate  最新交期
     * @param receiveProcessTime  收货处理时间(天）
     * @param transportMode 原运输方式
     * @param shippingTime  发货时间(人工填写）
     * @param transportModeManual 人工填写的运输方式
     * @param isLoaded 是否已放舱
     * @param useType 使用类型
     * @param factoryCode 厂代码
     *
     */
    public SKDFHUtil(LocalDate dueDate,LocalDate originalLatestDueDate, int receiveProcessTime,TransportMode transportMode, LocalDate shippingTime,TransportMode transportModeManual, boolean isLoaded, String useType,String factoryCode,Integer materialLeadTimeDays) {
        this.dueDate = dueDate;
        this.receiveProcessTime = receiveProcessTime;
        this.originalLatestDueDate = originalLatestDueDate;
        this.transportMode = transportMode;
        this.shippingTime = shippingTime;
        this.transportModeManual = transportModeManual;
        this.isLoaded = isLoaded;
        this.useType = useType;
        this.factoryCode = factoryCode;
        this.materialLeadTimeDays = materialLeadTimeDays;
    }

    /**
     * 泰国放假天数
     * @return
     */
    public static int getN(LocalDate date) {
        //TODO 需要获取泰国放假日期，并计算date到放假结束的天数
    	return 0;
    }

    /**
     * 获取最新的交期
     * SKD在途：
     * 泰国需求发货日期
     * SKD销售订单：
     * 有采购PO（交泰方式=SKD采购）：
     * 通过PO订单+行项目的SAP-ZMMI0084获取最新交期
     * 无采购PO
     * 3317使用类型 = 库存、限制库存、特殊库存
     * 今天
     * 3317使用类型 = 需要采购
     * 今天 + 物料采购LT
     * @return
     */
    public LocalDate getCalculateLatestDueDate() {

        if(Sets.newHashSet("需要采购").contains(useType)){
            return LocalDate.now().plusDays(materialLeadTimeDays==null?0:materialLeadTimeDays);

        }else if(Sets.newHashSet("库存","限制库存","特殊库存").contains(useType)){
            return LocalDate.now();
        }
        if (originalLatestDueDate == null) {
            return LocalDate.now();
        }
        return originalLatestDueDate;
    }

    public LocalDate getNewCalculateLatestDueDate() {
        LocalDate newCalculateLatestDueDate = getCalculateLatestDueDate();
        return newCalculateLatestDueDate.isAfter(LocalDate.now()) ? newCalculateLatestDueDate : LocalDate.now();
    }

    /**
     * 泰国需求发运方式
     * @Description 等于if到泰需求日期-收货处理时间-today()-N>=21天，海运；if到泰需求日期-today()-N>=9天，陆运；if到泰需求日期-today()-N<9天，空运
     * @return
     */
    public TransportMode getTransportMode() {
        int days = dueDate.minusDays(receiveProcessTime).getDayOfYear() - LocalDate.now().getDayOfYear() - getN(dueDate);
        if (days >= 21) {
            return TransportMode.SEA;
        } else if (days >= 9) {
            return TransportMode.LAND;
        } else {
            return TransportMode.AIR;
        }
    }
    /**
     * 泰国需求发货日期（基于最佳运输方式）
     * @Description 等于IF（=海运，需求到泰日期-收货处理时间-21-N天；=陆运，需求到泰日期-收货处理时间-9-N天；=空运，需求到泰日期-收货处理时间-5-N天；）N为放假日期
     */
    public LocalDate getThaiSendDueDate() {
        TransportMode mode = getTransportMode();
        switch (mode) {
            case SEA:
                return DateUtil.getAfterToday(dueDate.minusDays(receiveProcessTime).minusDays(21).minusDays(getN(dueDate)));
            case LAND:
                return DateUtil.getAfterToday(dueDate.minusDays(receiveProcessTime).minusDays(9).minusDays(getN(dueDate)));
            default:
                return DateUtil.getAfterToday(dueDate.minusDays(receiveProcessTime).minusDays(5).minusDays(getN(dueDate)));
        }
    }

    public static Date getThaiSendDueDate(Date needDate, int receiveProcessTimeParam) {
        if (ObjectUtils.isNull(needDate)) {
            return DateUtil.getDate(LocalDate.now());
        }
        LocalDate dueDateParam = DateUtil.getLocalDate(needDate);
        LocalDate thaiSendDueDate;
        int days = dueDateParam.minusDays(receiveProcessTimeParam).getDayOfYear() - LocalDate.now().getDayOfYear() - getN(dueDateParam);
        if (days >= 21) {
            thaiSendDueDate = dueDateParam.minusDays(receiveProcessTimeParam).minusDays(21).minusDays(getN(dueDateParam));
        } else if(days >= 9){
            thaiSendDueDate = dueDateParam.minusDays(receiveProcessTimeParam).minusDays(9).minusDays(getN(dueDateParam));
        } else if(days >= 5){
            thaiSendDueDate = dueDateParam.minusDays(receiveProcessTimeParam).minusDays(5).minusDays(getN(dueDateParam));
        } else {
            thaiSendDueDate = LocalDate.now();
        }

        return DateUtil.getDate(thaiSendDueDate);
    }

    /**
     * 需要提拉天数
     * @Description 需提拉日期  MAX(0, 【最新交期】-【泰国需求发货日期】)
     */
    public int getPullDays() {
        long daysDifference = getNewCalculateLatestDueDate().getDayOfYear() - getThaiSendDueDate().getDayOfYear();
        return Math.max(0, (int) daysDifference);
    }
    /**
     * 预计海运最快泰国入库日期
     * @Description 等于最新交期+1（遇周末顺延到周1）+21+收货处理时间+N；N为中间碰到的节假日
     */
    public LocalDate getThaiArrivalDateBySEA() {
        LocalDate thaiArrivalDate = getNewCalculateLatestDueDate().plusDays(1);
        thaiArrivalDate = DateUtil.postponedOnWeekends(thaiArrivalDate);
        thaiArrivalDate = thaiArrivalDate.plusDays(21).plusDays(receiveProcessTime);
        return thaiArrivalDate.plusDays(getN(thaiArrivalDate));
    }
    /**
     * 预计陆运最快泰国入库日期
     * @Description 等于最新交期+1（遇周末顺延到周1）+9+收货处理时间+N；N为中间碰到的节假日
     */
    public LocalDate getThaiArrivalDateByLand() {
        LocalDate thaiArrivalDate = getNewCalculateLatestDueDate().plusDays(1);
        thaiArrivalDate = DateUtil.postponedOnWeekends(thaiArrivalDate);
        thaiArrivalDate = thaiArrivalDate.plusDays(9).plusDays(receiveProcessTime);
        return thaiArrivalDate.plusDays(getN(thaiArrivalDate));
    }
    /**
     * 预计空运最快泰国入库日期
     * @Description 等于最新交期+1（遇周末顺延到周1）+5+收货处理时间+N；N为中间碰到的节假日
     */
    public LocalDate getThaiArrivalDateByAir() {
        LocalDate thaiArrivalDate = getNewCalculateLatestDueDate().plusDays(1);
        thaiArrivalDate = DateUtil.postponedOnWeekends(thaiArrivalDate);
        thaiArrivalDate = thaiArrivalDate.plusDays(5).plusDays(receiveProcessTime);
        return thaiArrivalDate.plusDays(getN(thaiArrivalDate));
    }

    /**
     * 差异天数
     * @Description 等于预计海运最快泰国入库日期-泰国需求入库日期
     */
    public int getGap(){
        return getThaiArrivalDateBySEA().getDayOfYear() - getThaiSendDueDate().getDayOfYear();
    }

    /**
     * 最终建议运输方式
     * @Description
     * 按优先级使用下列字段值
     *
     * 【运输方式（人工）】
     * 【最新建议运输方式】
     * 【原运输方式】
     */
    public TransportMode getFinalTransportMode() {
       if(ObjectUtils.isNotNull(transportModeManual)){
           return transportModeManual;
       }
        return getLatestTransportMode();
    }

    /**
     * 最新建议运输方式
     * @Description
        从上往下匹配

        排载总表状态 = 已放舱状态，则取【原运输方式】
        排载总表状态 ！= 已放舱状态，则按下面规则选择
        3317有库存
        海运：【需求日期】- 收获处理时间 - 到泰放假天数 - 当前日期 >= 21
        陆运：【需求日期】- 收获处理时间 - 到泰放假天数 - 当前日期 >= 9
        空运：其他
        3317没库存
        海运：【需求日期】 - 《最快海运预计到泰日期》 > 0
        陆运：【需求日期】 - 《最快陆运预计到泰日期》 > 0
        空运：【需求日期】 - 《最快空运预计到泰日期》 > 0
        空运
        ps: 都不满足取空运
     */
    public TransportMode getLatestTransportMode() {
        if (isLoaded) {
            return transportMode;
        }

        TransportMode mode = TransportMode.AIR;
        if (dueDate.getDayOfYear()-getThaiArrivalDateBySEA().getDayOfYear() > 0) {
            return TransportMode.SEA;
        }
        if(dueDate.getDayOfYear()-getThaiArrivalDateByLand().getDayOfYear() > 0){
            return TransportMode.LAND;
        }
        return mode;
    }

    private int getFinalTransportModeDays() {
        switch (getFinalTransportMode()) {
            case SEA:
                return 21;
            case LAND:
                return 9;
            default:
                return 5;
        }
    }

    /**
     * 预计中国发货时间
     * @Description
     * 按优先级使用下列字段值
     *
     * 1、【发货时间（人工）】
     * 2、当前库存数量满足订单发货需求 or 没有PO时:(("#特殊库存#库存#限制库存#需要采购#计划订单#").indexOf(useType)>0)
     * 【泰国需求入库日期】- 收货处理时间 - 到泰放假天数 - 【最终建议运输方式】对应物流天数（配置表）
     * 3、当前库存不满足发货需求时：
     *  下面多值取最大
     * 【PO最新交期】 + 1 + （遇到周末顺延到周1）
     * 【泰国需求入库日期】- 收货处理时间 - 到泰放假天数 - 【最终建议运输方式】对应物流天数（配置表）
     * 今天 （兼容交期过期，物料未送到的情况）
     */
    public LocalDate getChinaShippingTime() {
        if (shippingTime != null) {
            return DateUtil.getAfterToday(shippingTime);
        }
        LocalDate shippingTime2 = dueDate.minusDays(receiveProcessTime).minusDays(getN(dueDate)).minusDays(getFinalTransportModeDays());

        LocalDate shippingTime = getNewCalculateLatestDueDate().plusDays(1);
        shippingTime = DateUtil.postponedOnWeekends(shippingTime);
        LocalDate maxTime = shippingTime.isAfter(shippingTime2) ? shippingTime : shippingTime2;
        return  maxTime.isAfter(LocalDate.now()) ? maxTime : LocalDate.now();
    }



    /**
     * 预计到泰入库日期
     *
     * @Description
     * 海运：预计发货时间+21+收货处理时间+N（中间碰到的节假日）
     * 陆运：预计发货时间+9+收货处理时间
     * 空运：预计发货时间+5+收货处理时间
     */
    public LocalDate getThaiArrivalDate() {

        switch (this.getLatestTransportMode()) {
            case SEA:
                return getChinaShippingTime().plusDays(21).plusDays(receiveProcessTime);
            case LAND:
                return getChinaShippingTime().plusDays(9).plusDays(receiveProcessTime);
            default:
                return getChinaShippingTime().plusDays(5).plusDays(receiveProcessTime);
        }
    }



    /**
     * 是否能满足泰国上线需求（连空运也满足不了，要调整计划）
     * @Description 等于需求入库日期 - 预计入库日期
     */

    public boolean isReady() {
        return dueDate.getDayOfYear() - getThaiArrivalDate().getDayOfYear() >= 0;
    }

    /**
     * 是否可发状态
     * @Description
     * 从上往下顺序匹配
     *
     *  齐套可发：
     * 物料已在3317仓库
     * 【预计中国发货时间】在近七天
     * 关联顶层订单上线开始时间在21~28天（第4周）
     * 关联顶层待3317发货物料全部入库3317
     * 不齐套可发：
     * 物料已在3317仓库
     * 【预计中国发货时间】在近七天
     * 关联顶层订单上线开始时间在21~28天（第4周）
     * 关联顶层待3317发货物料没有全部入库3317
     * 第一层【是否不齐套发运】为是
     * 不齐套不可发：
     * 【预计中国发货时间】在近七天
     * 关联顶层订单上线开始时间在21~28天（第4周）
     * 关联顶层待3317发货物料没有全部入库3317
     * 第一层【是否不齐套发运】为否
     * 紧急运输可发：
     * 物料已在3317仓库
     * 【预计中国发货时间】在近七天
     * 关联顶层订单开始时间在21天内（3周内）
     *
     * 未到期不可发
     * @Param toThaiType 交泰方式 分3317在库发货，3317Z在途交付
     * @Param planStartDate 需求计划开始时间
     * @Param isAllIn 是否全部入3317库
     * @Param isSendUnReady 是否不齐套发货
     */

    public String getStatus(String toThaiType,LocalDate planStartDate,boolean isAllIn,boolean isSendUnReady) {
        if(getChinaShippingTime().isAfter(LocalDate.now().plusDays(7))){
            return "未到期不可发";
        }
        if("3317在库发货".equals(toThaiType)){
            if(planStartDate.isBefore(LocalDate.now().plusDays(21)) && planStartDate.isAfter(LocalDate.now().plusDays(28))){
                if(isAllIn){
                    if(isSendUnReady){
                        return "不齐套可发";
                    }else{
                        return "齐套可发";
                    }
                }else{
                    return "不齐套不可发";
                }
            }else if(planStartDate.isBefore(LocalDate.now().plusDays(21))){
                return "紧急运输可发";
            }
        }
        return "未到期不可发";
    }
    /**
     * 交泰方式
     * @Description
     * 库存：3317在库发货
     * SKD销售单：中国PO交付
     * SKD在途：3317发货在途
     * 空
     */
    public  String getToThaiWay() {
//        if("3317".equals(factoryCode)){
            if(Sets.newHashSet("库存","限制库存","特殊库存").contains(useType)){
                return "SKD在库";
            }
            if(Sets.newHashSet("计划订单","需要采购","需要生产","采购申请","采购单","限制采购单").contains(useType)){
                return "SKD采购";
            }
//        }
        return "";
    }

    /**
     * 获取原运输方式
     * @return
     */
    public TransportMode getOriginalTransportMode() {
        if (transportMode != null) {
            return transportMode;
        }
        return getLatestTransportMode();
    }
    /**
     * 特殊运输方式是否本版本新增
     * SKD新版本的【最终建议运输方式】比原版本更加紧急，则为是，反之则为否
     *
     * 紧急关系对比：空运 > 陆运 > 海运
     */
    public boolean isNewSpecialTransportMode() {
        return getFinalTransportMode().isPriority(getOriginalTransportMode());
    }

    /**
     * 	紧急需求原因标记（只有陆运、空运需要），按顺序判断：
     * 订单提前，最新版本顶层订单上线开始时间对比上一版本提前；
     * 紧急插单或超损(挪单)，最新版本顶层单号（生产订单 or 销售订单）与上一版本不一样；
     * 接单LT不足，原始交货日期-采购下达日期<=min(物料采购LT,21天)，订单周期不足；
     * 物料LT偏长，物料标准LT＞21天；
     * SKD逾期；
     */
    public String getEmergencyReason() {
        if(Sets.newHashSet(TransportMode.LAND,TransportMode.AIR).contains(getFinalTransportMode())){

        }
        return "";
    }
}
