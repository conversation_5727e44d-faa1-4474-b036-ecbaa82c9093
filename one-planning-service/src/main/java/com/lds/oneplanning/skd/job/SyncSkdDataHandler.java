package com.lds.oneplanning.skd.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.lds.coral.job.annotation.JobRegister;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.esb.model.EsbPurchaseOrderVo;
import com.lds.oneplanning.esb.model.EsbSkdDataVo;
import com.lds.oneplanning.skd.constants.OrderStatusEnum;
import com.lds.oneplanning.skd.domain.*;
import com.lds.oneplanning.skd.service.*;
import com.lds.oneplanning.skd.utils.SKDFHUtil;
import com.lds.oneplanning.skd.utils.StringUtil;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.utils.TraceIdUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@JobRegister(value = "SyncSkdDataHandler", jobName = "SyncSkdDataJob", cron = "0 0 1,8 * * ? *")
@Component
@Slf4j
public class SyncSkdDataHandler extends IJobHandler {

    @Resource
    private IEsbDataFetchService esbDataFetchService;
    @Resource
    private ISkdOrderProductService productService;
    @Resource
    private ISkdOrderMaterialService materialService;
    @Resource
    private ISkdOrderUseService useService;
    @Resource
    private IFactoryService factoryService;
    @Resource
    private SkdPurchaseLogisticsService skdPurchaseLogisticsService;
    @Resource
    private SkdOrderUseSubService useSubService;
    @Resource
    private ISkdSaleOrderService saleOrderService;
    @Resource
    private ISkdSaleDetailService saleDetailService;
    @Resource
    private ISkdSaleService saleService;
    @Resource
    private SkdPurchaseLogisticsService purchaseLogisticsService;



    @Override
    public ReturnT<String> execute(String paramsJson) throws Exception {

        ReturnT<String> returnInfo = SUCCESS;
        try {
            String tid = TraceIdUtils.setTraceId();
            XxlJobLogger.log("SyncSkdDataHandler xxl-job start:" + tid);
            JSONObject params = JSONUtil.parseObj(paramsJson);
            List<String> factoryCode = new ArrayList<>();
            if (params.containsKey("factoryCode") && StringUtils.isNotBlank(params.getStr("factoryCode"))) {
                String[] factoryCodes = params.getStr("factoryCode").split(",");
                factoryCode.addAll(Arrays.stream(factoryCodes).collect(Collectors.toList()));
            } else {
                //获取泰国工厂列表
                factoryCode = factoryService.getThaiFactoryCodes();
                // factoryCode = Lists.newArrayList("2602");
            }

            final List<String> finalFactoryCode = factoryCode;

            List<WpsRowData> topNoList = productService.syncFromWPS(factoryCode);
            log.info("SyncSkdDataHandler xxl-job syncFromWPS topNos size: {}", topNoList.size());
            // topNoList = topNoList.stream().filter(item -> Lists.newArrayList("107670032", "106806191", "106806058").contains(item.getOrderNo())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(topNoList)) {

                ListUtil.partition(topNoList, 100).parallelStream().forEach(item -> syncDataPartition(finalFactoryCode, item));

                this.syncBatchSqlProcess();
            }
        } catch (Exception e) {
            log.error("SyncSkdDataHandler exec error: {}", e.getMessage(), e);
            returnInfo = ReturnT.FAIL;
        }
        XxlJobLogger.log("=========================  SyncSkdDataHandler job execute end ======================");
        return returnInfo;
    }

    /**
     * 分批请求skdData
     * @param factoryCode
     * @param topNoList
     */
    private void syncDataPartition(List<String> factoryCode, List<WpsRowData> topNoList) {
        Integer pageNo = 0;
        Integer pageSize = 5000;
        Map<String, WpsRowData> mapWpsRowData = topNoList.stream().collect(Collectors.toMap(WpsRowData::getOrderNo, e -> e, (e1, e2) -> e2));
        List<String> topNos = topNoList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toList());

        List<EsbSkdDataVo> skdDataAll = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            long skdStartTime = System.currentTimeMillis();
            // 分页查询
            List<EsbSkdDataVo> skdData = esbDataFetchService.getSkdData(topNos, pageNo * pageSize + 1, (pageNo + 1) * pageSize);
            long skdEndTime = System.currentTimeMillis();
            log.info("SyncSkdDataHandler xxl-job topNos:{}, pageNo: {}, fetchSkdData size: {}, cost time: {}", topNos, pageNo, skdData.size(), skdEndTime - skdStartTime);
            if (CollUtil.isEmpty(skdData)) {
                break;
            }

            skdDataAll.addAll(skdData);
            Long totalRows = skdData.get(0).getTotalRows();
            if (totalRows <= (pageNo + 1) * pageSize) {
                break;
            }
            pageNo ++;
        }

        this.delProductProcess(topNos, skdDataAll);

        if (CollUtil.isEmpty(skdDataAll)) {
            return;
        }

        this.processByBatch(factoryCode, skdDataAll, mapWpsRowData);
    }

    private void processByBatch(List<String> factoryCode, List<EsbSkdDataVo> skdData, Map<String, WpsRowData> mapWpsRowData) {
        // 查询采购单交期 OP交期, 查询3317工厂的采购单信息
        List<String> orderNoList = skdData.stream().filter(e -> StringUtils.isNotBlank(e.getUseNo()) && !factoryCode.contains(e.getPlant())).map(EsbSkdDataVo::getUseNo).collect(Collectors.toList());
        long skdStartTime = System.currentTimeMillis();
        Map<String, EsbPurchaseOrderVo> purchaseOrderMap = esbDataFetchService.getPurchaseOrderData(orderNoList);
        long skdEndTime = System.currentTimeMillis();
        log.info("SyncSkdDataHandler xxl-job orderNoList:{}, fetchPurchaseOrderData size: {}, cost time: {}", orderNoList, purchaseOrderMap.size(), skdEndTime - skdStartTime);

        Set<String> sdkToNos = skdData.stream().filter(e -> StringUtils.isNotBlank(e.getTopNo())).map(EsbSkdDataVo::getTopNo).collect(Collectors.toSet());

        List<SkdPurchaseLogistics> purchaseLogisticsList = new ArrayList<>();

        for (String topNo : sdkToNos) {
            List<EsbSkdDataVo> skdDataSub = skdData.stream().filter(e -> topNo.equals(e.getTopNo())).collect(Collectors.toList());
            WpsRowData wpsRowData = mapWpsRowData.get(topNo);
            try {
                Map<String, SkdOrderMaterial> materialMap = this.processByTopNo(factoryCode, wpsRowData, skdDataSub, purchaseOrderMap);
                purchaseLogisticsList.addAll(this.convertToOrderLogistics(wpsRowData, skdDataSub, materialMap));
            } catch (Exception e) {
                log.error("SyncSkdDataHandler xxl-job topNo: {}, process error: {}", topNo, e.getMessage(), e);
            }
        }


        purchaseLogisticsList = purchaseLogisticsList.stream().distinct()
            .collect(Collectors.toList());

        // 物流批量处理
        this.syncLogisticsBatch(purchaseLogisticsList);
    }

    private Map<String, SkdOrderMaterial> processByTopNo(List<String> tFactoryCodes, WpsRowData wpsRowData, List<EsbSkdDataVo> skdData, Map<String, EsbPurchaseOrderVo> purchaseOrderMap) {
        String topNo = wpsRowData.getOrderNo();
        log.info("SyncSkdDataHandler xxl-job processByTopNo: {}, skdData size: {}", topNo, skdData.size());
        List<EsbSkdDataVo> tSkdData = new ArrayList<>();
        List<EsbSkdDataVo> zSkdData = new ArrayList<>();
        List<String> zFactoryCodes = Lists.newArrayList("3317");
        skdData.forEach(item -> {
            if (tFactoryCodes.contains(item.getPlant())) {
                tSkdData.add(item);
            } else if (zFactoryCodes.contains(item.getPlant())) {
                zSkdData.add(item);
            }
        });

        long skdStartTime = System.currentTimeMillis();
        Map<String, SkdOrderMaterial> materialMap = this.tProcess(wpsRowData, tSkdData);
        long skdEndTime = System.currentTimeMillis();
        this.zProcess(wpsRowData, zSkdData, purchaseOrderMap, materialMap);
        if (skdEndTime - skdStartTime > 4000) {
            log.error("SyncSkdDataHandler xxl-job processByTopNo: {}, error tProcess cost time: {}, zProcess cost time: {}", topNo,
                    skdEndTime - skdStartTime, System.currentTimeMillis() - skdEndTime);
        } else {
            log.info("SyncSkdDataHandler xxl-job processByTopNo: {}, tProcess cost time: {}, zProcess cost time: {}", topNo,
                    skdEndTime - skdStartTime, System.currentTimeMillis() - skdEndTime);
        }

        return materialMap;
    }

    /**
     * 主订单处理（2602）
     * @param wpsRowData
     * @param skdData
     */
    private Map<String, SkdOrderMaterial> tProcess(WpsRowData wpsRowData, List<EsbSkdDataVo> skdData) {
        if (CollUtil.isEmpty(skdData)) {
            return new HashMap<>();
        }
        String topNo = wpsRowData.getOrderNo();
        log.info("SyncSkdDataHandler xxl-job tProcess topNo: {}, skdData: {}", topNo, skdData.size());
        // 1.计划单
        // 1.1 订单产品 保存
        productService.saveBatchByTopNo(topNo, this.convertToProduct(wpsRowData, skdData));
        // 1.2 订单产品 查询
        Map<String, SkdOrderProduct> productMap = productService.getMapByTopNo(topNo);
        // 1.3 订单物料 保存
        materialService.saveBatchByTopNo(topNo, this.convertToMaterial(productMap, skdData));
        // 1.4 订单物料 查询
        Map<String, SkdOrderMaterial> materialMap = materialService.queryMapByTopNo(topNo);
        // 2.采购单（本采+直采、SKD采购单）
        useService.saveBatchByTopNo(topNo, this.convertToOrderUse(materialMap, skdData));

        // 5.汇总并更新订单物料

        // 6.汇总并更新订单产品

        // 给下层处理
        return materialMap;
    }

    private List<SkdPurchaseLogistics> convertToOrderLogistics(WpsRowData wpsRowData, List<EsbSkdDataVo> skdData,Map<String, SkdOrderMaterial> materialMap) {

        return skdData.stream().filter(e -> StringUtils.isNotBlank(e.getOutboundDeliveryNo())).map(item -> {
            SkdPurchaseLogistics purchaseLogistics = new SkdPurchaseLogistics();
            purchaseLogistics.setOutboundDeliveryNo(item.getOutboundDeliveryNo());
            purchaseLogistics.setOutboundDeliveryLine(item.getOutboundDeliveryLine());
            purchaseLogistics.setTransitQty(item.getUseQty());
            purchaseLogistics.setCoverSoNo(item.getCoverSoNo());
            purchaseLogistics.setCoverSoLine(item.getCoverSoLine());
            purchaseLogistics.setPurchaseNo(item.getUseNo());
            purchaseLogistics.setTopNo(item.getTopNo());
            SkdOrderMaterial material = BeanUtil.copyProperties(item, SkdOrderMaterial.class);
            String materialKey = material.getKey();
            if (materialMap.containsKey(materialKey)) {
                purchaseLogistics.setMaterialNeedDate(materialMap.get(materialKey).getMaterialNeedDate());
            }
            return purchaseLogistics;
        }).collect(Collectors.toList());
    }

    /**
     * 外采订单处理（3317）
     * @param wpsRowData
     * @param skdData
     * @param purchaseOrderMap
     */
    private void zProcess(WpsRowData wpsRowData,
                          List<EsbSkdDataVo> skdData,
                          Map<String, EsbPurchaseOrderVo> purchaseOrderMap,
                          Map<String, SkdOrderMaterial> materialMap) {
        String topNo = wpsRowData.getOrderNo();
        log.info("SyncSkdDataHandler xxl-job zProcess topNo:{}, skdData: {}", topNo, skdData.size());

        useSubService.syncData(topNo, this.covertToOrderUseSub(skdData, purchaseOrderMap, materialMap));
    }

    /**
     * 逻辑删除
     * @param topNos
     * @param skdDataAll
     */
    private void delProductProcess(List<String> topNos, List<EsbSkdDataVo> skdDataAll) {
        Set<String> topNoSet = CollUtil.newHashSet(topNos);
        if (CollUtil.isNotEmpty(skdDataAll)) {
            Set<String> existTopNoSet = skdDataAll.stream().map(EsbSkdDataVo::getTopNo).collect(Collectors.toSet());
            topNoSet.removeAll(existTopNoSet);
        }

        productService.delByTopNos(topNoSet);
    }

    private void syncLogisticsBatch(List<SkdPurchaseLogistics> purchaseLogisticsList) {
        try {
            if (CollUtil.isEmpty(purchaseLogisticsList)) {
                return;
            }
            long skdLogisticsStartTime = System.currentTimeMillis();
            purchaseLogisticsList = skdPurchaseLogisticsService.calculateAndFilledData(purchaseLogisticsList);
            purchaseLogisticsList = purchaseLogisticsList.stream().collect(Collectors.toList());
            skdPurchaseLogisticsService.batchSaveData(purchaseLogisticsList);
            long skdLogisticsEndTime = System.currentTimeMillis();
            log.info("SyncSkdDataHandler xxl-job purchaseLogistics process size: {} cost time: {}", purchaseLogisticsList.size(), skdLogisticsEndTime - skdLogisticsStartTime);
        } catch (Exception e) {
            log.error("SyncSkdDataHandler xxl-job syncLogisticsBatch: {}, error: {}", purchaseLogisticsList, e.getMessage(), e);
        }
    }

    /**
     * 数据转成订单产品信息
     * @param wpsRowData
     * @param skdData
     * @return
     */
    private Collection<SkdOrderProduct> convertToProduct(WpsRowData wpsRowData, List<EsbSkdDataVo> skdData) {
        Set<SkdOrderProduct> skdOrderProductSet = new HashSet<>();
        for (EsbSkdDataVo skdDataVo : skdData) {
            SkdOrderProduct skdOrderProduct = BeanUtil.copyProperties(skdDataVo, SkdOrderProduct.class);
            skdOrderProduct.setCustomerCode(wpsRowData.getCustomerCode());
            skdOrderProduct.setPlantFinish(wpsRowData.getProductEndTime());
            skdOrderProduct.setPlanQitaoDate(DateUtil.offsetDay(skdOrderProduct.getPlanDate(), -1));
            skdOrderProduct.setOrderDelivery(Objects.isNull(wpsRowData.getEstFinishTime()) ?
                    wpsRowData.getOriginalFinishTime() : wpsRowData.getEstFinishTime());
            skdOrderProduct.setShipTime(Objects.isNull(wpsRowData.getFinalShipTime()) ?
                    wpsRowData.getOriginalShipTime() : wpsRowData.getFinalShipTime());
            skdOrderProduct.setStatus(OrderStatusEnum.UN_COMPLETE.getCode());

            skdOrderProductSet.add(skdOrderProduct);
        }
        return skdOrderProductSet;
    }

    /**
     * 数据转成订单物料信息
     * @param productMap
     * @param skdData
     * @return
     */
    private Collection<SkdOrderMaterial> convertToMaterial(Map<String, SkdOrderProduct> productMap, List<EsbSkdDataVo> skdData) {
        Map<String, SkdOrderMaterial> skdOrderMaterialMap = new HashMap<>();
        for (EsbSkdDataVo skdDataVo : skdData) {
            SkdOrderProduct skdOrderProduct = BeanUtil.copyProperties(skdDataVo, SkdOrderProduct.class);
            String productKey = skdOrderProduct.getKey();
            if (!productMap.containsKey(productKey)) {
                continue;
            }

            SkdOrderProduct dbProduct = productMap.get(productKey);
            SkdOrderMaterial skdOrderMaterial = BeanUtil.copyProperties(skdDataVo, SkdOrderMaterial.class);
            skdOrderMaterial.setOrderProductId(dbProduct.getId());
            skdOrderMaterial.setCoverSoId(StringUtil.getIdByNoAndLine(skdOrderMaterial.getCoverSoNo(),
                    skdOrderMaterial.getCoverSoLine()));
            String materialKey = skdOrderMaterial.getKey();
            if (skdOrderMaterialMap.containsKey(materialKey)) {
                BeanUtil.copyProperties(skdOrderMaterial, skdOrderMaterialMap.get(materialKey));
            } else {
                skdOrderMaterialMap.put(materialKey, skdOrderMaterial);
            }
        }
        return skdOrderMaterialMap.values();
    }

    /**
     * 数据转成订单关联单
     * 采购单、在途等
     * @param materialMap
     * @param skdData
     * @return
     */
    private Collection<SkdOrderUse> convertToOrderUse(Map<String, SkdOrderMaterial> materialMap, List<EsbSkdDataVo> skdData) {
        Set<SkdOrderUse> skdOrderUseSet = new HashSet<>();
        for (EsbSkdDataVo skdDataVo : skdData) {
            SkdOrderUse skdOrderUse = BeanUtil.copyProperties(skdDataVo, SkdOrderUse.class);

            SkdOrderMaterial material = BeanUtil.toBean(skdDataVo, SkdOrderMaterial.class);
            String materialKey = material.getKey();
            if (materialMap.containsKey(materialKey)) {
                skdOrderUse.setOrderMaterialId(materialMap.get(materialKey).getId());
            }

            if (StringUtils.isNotBlank(skdDataVo.getUseNo())) {
                skdOrderUse.setOrderUseId(
                    StrUtil.concat(true,
                            skdDataVo.getTopNo(),
                            skdDataVo.getWorkNo(),
                            skdDataVo.getWorkLine(),
                            skdDataVo.getUseNo()
                        ));
            }

            skdOrderUseSet.add(skdOrderUse);
        }
        return skdOrderUseSet;
    }

    /**
     * 数据转成订单使用子数据
     * @param skdData
     * @return
     */
    private List<SkdOrderUseSub> covertToOrderUseSub(List<EsbSkdDataVo> skdData,
                                                     Map<String, EsbPurchaseOrderVo> purchaseOrderMap,
                                                     Map<String, SkdOrderMaterial> materialMap) {
        List<SkdOrderUseSub> skdOrderUseSubList = new ArrayList<>();
        for (EsbSkdDataVo skdDataVo : skdData) {
            SkdOrderUseSub skdOrderUseSub = BeanUtil.copyProperties(skdDataVo, SkdOrderUseSub.class);
            SkdOrderMaterial material = BeanUtil.copyProperties(skdDataVo, SkdOrderMaterial.class);
            String materialKey = material.getKey();
            if (materialMap.containsKey(materialKey)) {
                skdOrderUseSub.setMaterialNeedDate(materialMap.get(materialKey).getMaterialNeedDate());
                skdOrderUseSub.setMaterialLeadTimeDays(materialMap.get(materialKey).getMaterialLeadTimeDays());
            }
            skdOrderUseSub.setUseType(skdDataVo.getUseTypeName());
            skdOrderUseSub.setFactoryCode(skdDataVo.getDeliveryPlant());
            // 没有采购PO类型的处理：#特殊库存#库存#限制库存#需要采购#计划订单#
            if (purchaseOrderMap.containsKey(skdDataVo.getUseNo())) {
                EsbPurchaseOrderVo purchaseOrderVo = purchaseOrderMap.get(skdDataVo.getUseNo());
                Date latestDeliveryDate = purchaseOrderVo.getLatestDeliveryDate();
                if (Objects.nonNull(latestDeliveryDate)) {
                    skdOrderUseSub.setOriginalLatestDueDate(latestDeliveryDate);
                }
                skdOrderUseSub.setPurchaseQty(purchaseOrderVo.getPurchaseQty());
                skdOrderUseSub.setOnOrderQty(purchaseOrderVo.getOnOrderQty());
            }

            if (StringUtils.isNotBlank(skdOrderUseSub.getCoverSoNo())){
                String coverSoId = skdOrderUseSub.getCoverSoNo();
                if (StringUtils.isNotBlank(skdOrderUseSub.getCoverSoLine())) {
                    coverSoId += "_" + skdOrderUseSub.getCoverSoLine();
                }
                skdOrderUseSub.setCoverSoId(coverSoId);
                skdOrderUseSub.setOrderUseId(
                        StrUtil.concat(true,
                        skdDataVo.getTopNo(),
                        skdDataVo.getWorkNo(),
                        skdDataVo.getWorkLine(),
                        coverSoId
                        ));
            }
            skdOrderUseSubList.add(skdOrderUseSub);
        }
        return skdOrderUseSubList;
    }

    /**
     * 执行sql进行批量汇总更新 相关同步处理
     */
    void syncBatchSqlProcess() {
        //更新产品表
        productService.update(useSubService.aggregateQueriesSalesOrderInfo());
        // 更新物料的数量汇总
        materialService.updateFromSelect();

        // 销售相关先从底层开始往上汇总
        // 1.销售订单
        saleOrderService.deleteAll();
        saleOrderService.insertFromSelect();

        // 2.销售明细单
        saleDetailService.deleteAll();
        saleDetailService.insertFromSelect();

        // 3.销售单
        saleService.deleteAll();
        saleService.insertFromSelect();
    }
}
