package com.lds.oneplanning.po.service;

import cn.hutool.core.bean.BeanUtil;
import com.lds.oneplanning.po.domain.entity.CallOffOrder;
import com.lds.oneplanning.po.domain.entity.DemandOrder;
import com.lds.oneplanning.po.strategy.LockDateStrategy;
import com.lds.oneplanning.po.strategy.LockDateStrategyFactory;
import com.lds.oneplanning.po.strategy.helper.CallOffCalculationHelper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 叫料计算服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Getter
public class CallOffCalculationService {
    private final LockDateStrategyFactory lockDateStrategyFactory;

    /**
     * 计算叫料计划
     *
     * @param demandOrders  需求单列表
     * @param callOffOrders 叫料单列表
     * @param currentDate   当前日期
     * @return 计算后的叫料单列表
     */
    public List<CallOffOrder> calculateCallOffPlan(List<DemandOrder> demandOrders,
                                                   List<CallOffOrder> callOffOrders,
                                                   final LocalDate currentDate) {
        // 参数校验
        if (demandOrders == null || callOffOrders == null || currentDate == null) {
            log.error("参数校验失败: 参数不能为空 - demandOrders: {}, callOffOrders: {}, currentDate: {}",
                    demandOrders, callOffOrders, currentDate);
            throw new IllegalArgumentException("参数不能为空");
        }

        log.info("开始计算叫料计划，需求单数量: {}, 叫料单数量: {}, 当前日期: {}",
                demandOrders.size(),
                callOffOrders.size(),
                currentDate);


        if (currentDate.isAfter(LocalDate.now())) {
            log.error("参数校验失败: 当前日期{}不能大于系统日期{}", currentDate, LocalDate.now());
            throw new IllegalArgumentException("当前日期不能大于系统日期");
        }

        // 处理锁定状态，并且去掉非锁定状态的叫料单，注意这里算是以昨天的角度来看，天数要-1
        processLockedCallOffs(callOffOrders, currentDate.minusDays(1));


        List<CallOffOrder> result = new ArrayList<>();

        // 1. 获取前一天的叫料单并处理
        processPreviousDayCallOffs(callOffOrders, currentDate, result);

        // 2. 按工厂+供应商+物料ID分组
        Map<String, List<DemandOrder>> demandGroups = demandOrders.stream()
                .collect(Collectors.groupingBy(this::getGroupKey));
        Map<String, List<CallOffOrder>> callOffGroups = callOffOrders.stream()
                .collect(Collectors.groupingBy(this::getGroupKey));

        // 3. 对每个分组进行计算
        for (String groupKey : demandGroups.keySet()) {
            List<DemandOrder> groupDemands = demandGroups.get(groupKey);
            List<CallOffOrder> groupCallOffs = callOffGroups.getOrDefault(groupKey, new ArrayList<>());

            // 4. 计算锁定日期
            calculateLockDates(groupDemands, currentDate);

            // 6. 按天处理数据
            result.addAll(processDailyData(groupDemands, groupCallOffs, currentDate));
        }

        return result;
    }

    /**
     * 处理前一天的叫料单
     * 修改逻辑，改成根据差额直接生成叫料单
     */
    private void processPreviousDayCallOffs(List<CallOffOrder> callOffOrders,
                                            LocalDate currentDate, List<CallOffOrder> result) {
        if (callOffOrders == null || result == null) {
            return;
        }

        log.debug("处理前一天的叫料单，日期: {}", currentDate.minusDays(1));

        callOffOrders.stream()
                .filter(c -> c.getCallOffDate() != null && c.getCallOffDate().equals(currentDate.minusDays(1)))
                .forEach(callOff -> {
                    int received = ObjectUtils.defaultIfNull(callOff.getReceivedQuantity(), 0);
                    int callOffQty = ObjectUtils.defaultIfNull(callOff.getCallOffQuantity(), 0);

                    if (received < callOffQty) {
                        int difference = callOffQty - received;
                        CallOffOrder newCallOff = BeanUtil.copyProperties(callOff, CallOffOrder.class);
                        newCallOff.setId(null);
                        newCallOff.setCallOffQuantity(difference);
                        newCallOff.setCallOffDate(currentDate);
                        log.info("创建新叫料单[{}]，差额:{}", callOff.getOrderNo(), difference);
                        result.add(newCallOff);
                    }
                });
    }

    /**
     * 处理锁定状态
     */
    private void processLockedCallOffs(List<CallOffOrder> callOffOrders,
                                       LocalDate currentDate) {
        log.debug("开始处理锁定状态的叫料单，初始数量: {}", callOffOrders.size());

        calculateLockDates(callOffOrders, currentDate);

        // 更新锁定状态
        for (CallOffOrder callOff : callOffOrders) {
            callOff.setLocked(CallOffCalculationHelper.isLocked(callOff.getCallOffDate(), callOff.getLockDate()));
        }

        // 删除非锁定状态的叫料单
        callOffOrders.removeIf(callOff -> !isLocked(callOff));
        log.debug("过滤后锁定状态的叫料单数量: {}", callOffOrders.size());
    }

    private boolean isLocked(CallOffOrder order) {
        return Boolean.TRUE.equals(order.getLocked());
    }

    /**
     * 获取分组键
     */
    private String getGroupKey(DemandOrder order) {
        return String.format("%s_%s_%s", order.getFactoryCode(), order.getSupplier(), order.getMaterialId());
    }

    /**
     * 获取分组键
     */
    private String getGroupKey(CallOffOrder order) {
        return String.format("%s_%s_%s", order.getFactoryCode(), order.getSupplier(), order.getMaterialId());
    }

    /**
     * 计算锁定日期
     */
    private void calculateLockDates(List<? extends DemandOrder> demandOrders, LocalDate currentDate) {
        for (DemandOrder order : demandOrders) {
            LockDateStrategy strategy = lockDateStrategyFactory.getStrategy(order.getTransportType());
            order.setLockDate(strategy.calculateLockDate(order, currentDate));
        }
    }

    /**
     * 按天处理需求单和叫料单数据，生成新的叫料单列表。
     *
     * @param demandOrders  需求单列表
     * @param callOffOrders 叫料单列表
     * @param currentDate   当前日期
     * @return 处理后生成的叫料单列表
     */
    private List<CallOffOrder> processDailyData(List<DemandOrder> demandOrders,
                                                List<CallOffOrder> callOffOrders,
                                                LocalDate currentDate) {
        long startTime = System.currentTimeMillis();
        List<CallOffOrder> result = new ArrayList<>();
        log.info("开始按天处理数据，当前日期: {}，需求单数量: {}，叫料单数量: {}", currentDate, demandOrders.size(), callOffOrders.size());

        if (demandOrders.isEmpty()) {
            log.info("需求单列表为空，直接返回空结果");
            return result;
        }

        // 准备数据，例如排序、过滤等
        prepareData(demandOrders, callOffOrders);

        // 获取最后的需求日期
        LocalDate lastDemandDate = demandOrders.get(demandOrders.size() - 1).getDemandDate();
        // 从当前日期开始，按天处理
        LocalDate processDate = demandOrders.get(0).getDemandDate();
        ;
        log.info("最后需求日期: {}", lastDemandDate);

        // 增加边界检查：如果最后需求日期早于当前日期，直接返回空结果
        if (lastDemandDate.isBefore(currentDate)) {
            log.warn("最后需求日期{}早于当前日期{}，不进行计算", lastDemandDate, currentDate);
            return result;
        }


        while (!processDate.isAfter(lastDemandDate)) {
            final LocalDate currentProcessDate = processDate;
            log.info("正在处理日期: {}", currentProcessDate);

            // 获取当天的需求单
            List<DemandOrder> dailyDemands = demandOrders.stream()
                    .filter(d -> d.getDemandDate().equals(currentProcessDate))
                    .collect(Collectors.toList());
            log.info("当天{}有{}条需求单", currentProcessDate, dailyDemands.size());

            //创建数据备份，以免修改到原始数据
            dailyDemands = BeanUtil.copyToList(dailyDemands, DemandOrder.class);

            // 计算当天总需求数量
            int totalDemand = dailyDemands.stream()
                    .mapToInt(DemandOrder::getDemandQuantity)
                    .sum();

            if (totalDemand == 0) {
                // 如果当天总需求数量为0，则直接跳过
                processDate = processDate.plusDays(1);
                continue;
            }

            if (totalDemand > 0) {
                // 获取当天锁定的叫料单（非锁定的不参与计算）
                List<CallOffOrder> dailyCallOff = callOffOrders.stream()
                        .filter(c -> c.getCallOffDate().equals(currentProcessDate))
                        .filter(this::isLocked)
                        //TODO 只拿一个来计算可能会逻辑bug
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(dailyCallOff)) {
                    //锁定的直接加入到结果中
                    result.addAll(dailyCallOff);

                    CallOffOrder callOff = dailyCallOff.get(0);
                    // 计算差额
                    int lockedQuantity = dailyCallOff.stream().mapToInt(e -> intVal(e.getCallOffQuantity())).sum();
                    int difference = totalDemand - lockedQuantity;

                    //UPDATE 2025-6-9 策略区分，JIT当天可以补充叫料（只增不减），PO2当前不允许任何改动
                    if (difference != 0) {
                        LockDateStrategy strategy = lockDateStrategyFactory.getStrategy(callOff.getTransportType());

                        // 创建虚拟需求单
                        lastDemandDate = strategy.createVirtualDemand(
                                demandOrders,
                                dailyDemands,
                                currentProcessDate,
                                difference,
                                lastDemandDate,
                                callOff,
                                result);
                    }
                } else {
                    // 如果不存在叫料单，创建新的叫料单，需要计算差额，有可能存在负数的情况
                    CallOffCalculationHelper.createCallOffOrder(dailyDemands, currentProcessDate, result);
                    log.info("创建新的叫料单，日期: {}", currentProcessDate);
                }
            } else {
                // 需求数量为负数的情况
                // 判断剩余未处理的需求是不是都为负数，如果是直接跳出循环，如果否则创建虚拟的需求单，传递给下一次循环处理

                //获取剩余的需求
                boolean hasRemainingDemand = dailyDemands.stream()
                        .anyMatch(d -> d.getDemandDate().isAfter(currentProcessDate) && d.getDemandQuantity() > 0);
                if (!hasRemainingDemand) {
                    // 所有剩余需求都为负数，跳出循环
                    break;
                }

                lastDemandDate = CallOffCalculationHelper.createNegativeVirtualDemand(demandOrders, dailyDemands, currentProcessDate, totalDemand, lastDemandDate);
                log.warn("处理负数需求，总需求: {}", totalDemand);
            }

            processDate = processDate.plusDays(1);
        }

        long endTime = System.currentTimeMillis();
        log.info("按天处理完成，共生成{}条叫料单，耗时: {}ms", result.size(), (endTime - startTime));
        return result;
    }


    /**
     * 准备数据，例如排序、过滤等
     */
    private void prepareData(List<DemandOrder> demandOrders, List<CallOffOrder> callOffOrders) {
        long startTime = System.currentTimeMillis();
        int demandSize = demandOrders.size();
        int callOffSize = callOffOrders.size();
        log.info("数据准备前, demand orders: {}, call off orders: {}", demandSize, callOffSize);
        //去掉需求日期为空的数据，以免出现NullPointerException
        boolean invalidDemand = demandOrders.removeIf(d -> d.getDemandDate() == null);
        if (invalidDemand) {
            log.warn("移除了{}条需求日期为空的需求单", demandSize - demandOrders.size());
        }

        boolean invalidCallOff = callOffOrders.removeIf(c -> c.getCallOffDate() == null);
        if (invalidCallOff) {
            log.warn("移除了{}条叫料日期为空的叫料单", callOffSize - callOffOrders.size());
        }

        //设置需求数量，防止空指针
        demandOrders.forEach(d -> d.setDemandQuantity(intVal(d.getDemandQuantity())));


        // 按需求日期排序
        demandOrders.sort(Comparator.comparing(DemandOrder::getDemandDate));
        callOffOrders.sort(Comparator.comparing(CallOffOrder::getCallOffDate));
        long endTime = System.currentTimeMillis();
        log.info("完成数据准备, 剩下 demand orders: {}, call off orders: {}, 耗时: {}ms",
                demandOrders.size(), callOffOrders.size(), (endTime - startTime));
    }


    private int intVal(Integer intObj) {
        return intObj != null ? intObj : 0;
    }

} 