package com.lds.oneplanning.po.strategy.helper;

import com.lds.oneplanning.po.domain.entity.CallOffOrder;
import com.lds.oneplanning.po.domain.entity.DemandOrder;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@UtilityClass
@Slf4j
public class CallOffCalculationHelper {


    /**
     * 当前无法调整数量，所以创建虚拟的需求单，传递给下一次循环处理
     * <p>
     * 今天锁定了10个，需求为2,3，多出了5个，创建虚拟需求给明天-5个
     * 今天锁定了10个，需求为1,4,10，少了5个，创建虚拟需求给明天+5个
     * <p>
     * 此方法旨在处理每日需求与锁定数量之间的差异，通过创建虚拟需求单来平衡差异，并将处理结果传递给下一天
     *
     * @param demandOrders       当前所有的需求订单列表
     * @param dailyDemands       当日的需求订单列表
     * @param currentProcessDate 当前处理的日期
     * @param difference         当日锁定数量与需求总量的差异
     * @param lastDemandDate     最后一个需求日期
     * @return 返回处理后的日期
     */
    public LocalDate createVirtualDemand(List<DemandOrder> demandOrders,
                                         List<DemandOrder> dailyDemands,
                                         LocalDate currentProcessDate,
                                         int difference,
                                         LocalDate lastDemandDate) {
        //如果差额为负数，直接创建虚拟需求单
        if (difference < 0) {
            return createNegativeVirtualDemand(demandOrders, dailyDemands, currentProcessDate, difference, lastDemandDate);
        }

        //计算当日总需求量
        int totalDemand = dailyDemands.stream()
                .mapToInt(DemandOrder::getDemandQuantity)
                .sum();

        //计算锁定的数量，即总需求量减去差异
        int lockedQuantity = totalDemand - difference;

        //先合并差异
        dailyDemands = consolidationDifference(dailyDemands);

        //抹平差异，先从数量少的开始处理
        dailyDemands.sort(Comparator.comparingInt(DemandOrder::getDemandQuantity));
        List<DemandOrder> remainingDemands = new ArrayList<>();
        for (DemandOrder demand : dailyDemands) {
            //如果锁定数量足够满足当前需求，则减少锁定数量，并将当前需求量设置为0
            if (lockedQuantity >= demand.getDemandQuantity()) {
                lockedQuantity -= demand.getDemandQuantity();
                demand.setDemandQuantity(0);
                continue;
            }
            //如果锁定数量不足以满足当前需求，则将剩余需求量添加到未处理的需求列表中
            demand.setDemandQuantity(demand.getDemandQuantity() - lockedQuantity);
            lockedQuantity = 0;
            if (demand.getDemandQuantity() != 0) {
                remainingDemands.add(demand);
            }
        }
        //将未处理的需求传递给下一天处理，并返回下一天的日期
        return moveToNextDay(demandOrders, currentProcessDate, lastDemandDate, remainingDemands);
    }

    public LocalDate moveToNextDay(List<DemandOrder> demandOrders, LocalDate currentProcessDate, LocalDate lastDemandDate, List<DemandOrder> positiveDemands) {
        LocalDate nextProcessDate = currentProcessDate.plusDays(1);
        for (DemandOrder demand : positiveDemands) {
            demand.setDemandDate(nextProcessDate);
            demandOrders.add(demand);
        }
        if (nextProcessDate.isAfter(lastDemandDate)) {
            lastDemandDate = nextProcessDate;
        }
        log.info("处理负数需求，移动到下个计算周期的数量: {}，新最后需求日期: {}", positiveDemands, lastDemandDate);
        return lastDemandDate;
    }


    public LocalDate createNegativeVirtualDemand(List<DemandOrder> demandOrders,
                                                 List<DemandOrder> dailyDemands,
                                                 LocalDate currentProcessDate,
                                                 int difference,
                                                 LocalDate lastDemandDate) {
        DemandOrder virtual = createVirtualDemand(dailyDemands.get(0), currentProcessDate.plusDays(1), difference);
        demandOrders.add(virtual);

        //更新最后需求日期
        if (virtual.getDemandDate().isAfter(lastDemandDate)) {
            lastDemandDate = virtual.getDemandDate();
        }
        log.info("差额{}为负数，直接创建虚拟需求单, 最后需求日期: {}", difference, lastDemandDate);
        return lastDemandDate;
    }


    /**
     * 创建虚拟需求单
     */
    public DemandOrder createVirtualDemand(DemandOrder template, LocalDate demandDate, int quantity) {
        return DemandOrder.builder()
                .orderNo(template.getOrderNo())
                .demandDate(demandDate)
                .actualDemandDate(demandDate)
                .materialId(template.getMaterialId())
                .supplier(template.getSupplier())
                .factoryCode(template.getFactoryCode())
                .demandQuantity(quantity)
                .transportType(template.getTransportType())
                .transportTime(template.getTransportTime())
                .build();
    }

    /**
     * 创建叫料单
     */
    public CallOffOrder createCallOffOrder(DemandOrder demand, LocalDate callOffDate, int quantity) {
        CallOffOrder order = new CallOffOrder();
        BeanUtils.copyProperties(demand, order);
        order.setDemandDate(ObjectUtils.defaultIfNull(demand.getActualDemandDate(), demand.getDemandDate()));
        order.setCallOffDate(callOffDate);
        order.setCallOffQuantity(quantity);
        order.setReceivedQuantity(0);

        return order;
    }


    public void createCallOffOrder(List<DemandOrder> dailyDemands, LocalDate currentProcessDate, List<CallOffOrder> result) {
        List<DemandOrder> positiveDemands = consolidationDifference(dailyDemands);

        // 单独处理剩余正需求
        positiveDemands.stream()
                .filter(d -> d.getDemandQuantity() > 0)
                .forEach(demand -> {
                    CallOffOrder newCallOff = createCallOffOrder(demand,
                            currentProcessDate,
                            demand.getDemandQuantity());
                    result.add(newCallOff);
                });
    }


    /**
     * 合并差异
     *
     * @param dailyDemands 当天需求单列表
     * @return 合并后的需求单列表，正数和负数已经合并，正数为0的会被移除
     */
    public List<DemandOrder> consolidationDifference(List<DemandOrder> dailyDemands) {
        int sum = dailyDemands.stream().mapToInt(DemandOrder::getDemandQuantity).sum();
        if (sum >= 0) {
            return consolidationPositiveDifference(dailyDemands);
        } else {
            return consolidationNegativeDifference(dailyDemands);
        }
    }


    /**
     * 合并差异，总结果为正的情况
     *
     * @param dailyDemands 当天需求单列表
     * @return 合并后的需求单列表，正数和负数已经合并，正数为0的会被移除
     */
    public List<DemandOrder> consolidationPositiveDifference(List<DemandOrder> dailyDemands) {
        //按正数负数分组
        List<DemandOrder> positiveDemands = dailyDemands.stream()
                .filter(d -> d.getDemandQuantity() > 0)
                .collect(Collectors.toList());
        int negativeNum = dailyDemands.stream()
                .filter(d -> d.getDemandQuantity() < 0)
                .mapToInt(DemandOrder::getDemandQuantity)
                .sum();


        // 假设1：需求情况为：-5,4,5,1，抹平差异后，最终添加的叫料单为：4,1
        // 假设2：需求情况为：-1,4,5,1，抹平差异后，最终添加的叫料单为：3,5,1
        // 假设3：需求情况为：1,4,5,1，抹平差异后，最终添加的叫料单为：1,4,5,1
        int remainingNegative = Math.abs(negativeNum);
        for (DemandOrder demand : positiveDemands) {
            if (remainingNegative <= 0) break;

            int qty = Math.min(demand.getDemandQuantity(), remainingNegative);
            remainingNegative -= qty;
            demand.setDemandQuantity(demand.getDemandQuantity() - qty);
        }
        return positiveDemands;
    }

    /**
     * 合并差异，总结果为负的情况
     *
     * @param dailyDemands 当天需求单列表
     * @return 合并后的需求单列表，正数和负数已经合并，正数为0的会被移除
     */
    public List<DemandOrder> consolidationNegativeDifference(List<DemandOrder> dailyDemands) {
        // 将每个需求单的需求量取反
        dailyDemands.forEach(e -> e.setDemandQuantity(e.getDemandQuantity() * -1));
        // 调用合并正数差异的方法，将需求单列表进行合并
        dailyDemands = consolidationPositiveDifference(dailyDemands);
        // 再次将每个需求单的需求量取反
        dailyDemands.forEach(e -> e.setDemandQuantity(e.getDemandQuantity() * -1));
        return dailyDemands;
    }

    public static boolean isLocked(LocalDate callOffDate, LocalDate lockDate) {
        if (lockDate == null) {
            return false;
        }

        //callOffDate晚于lockDate，就没锁定，比如叫料日期5.12，锁定5.11，就没锁定
        return !callOffDate.isAfter(lockDate);
    }
}
