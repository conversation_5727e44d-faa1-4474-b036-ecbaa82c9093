package com.lds.oneplanning.consumer.rocket;

import com.lds.coral.mq.rocketmq.listener.AbstractPullRocketListener;
import com.lds.coral.mq.rocketmq.properties.PullConsumeProperties;
import com.lds.oneplanning.wps.constants.RocketMQConstants;
import com.lds.oneplanning.wps.req.WpsPlanWarningMQReq;
import com.lds.oneplanning.wps.warning.WpsPlanWarningManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
@RocketMQMessageListener(topic = RocketMQConstants.LDX_OP_WPS_WARNING_TOPIC,
        consumerGroup = "LDX_OP_WPS_WARNING_CONSUMER_GROUP")
public class WpsPlanWarningNotifyListener extends AbstractPullRocketListener<WpsPlanWarningMQReq> {

    @Autowired
    private WpsPlanWarningManager wpsPlanWarningManager;

    @Override
    protected void processMessage(List<WpsPlanWarningMQReq> wpsPlanWarningMQReqList) {
        if (CollectionUtils.isEmpty(wpsPlanWarningMQReqList)) {
            return;
        }
        for (WpsPlanWarningMQReq req : wpsPlanWarningMQReqList) {
            wpsPlanWarningManager.processMessage(req);
            log.info("Successfully processed message, factoryCode: {}, userId: {}, planType: {}", req.getFactoryCode()
                    , req.getUserId(), req.getPlanTpe());
        }
    }

    @Override
    protected PullConsumeProperties getProperties() {
        return PullConsumeProperties.builder()
                .consumeThreadMin(2)
                .consumeThreadMax(10)
                .consumeMessageBatchMaxSize(100)
                .pullBatchSize(100)
                .build();
    }

    @Override
    protected boolean isBatchProcessMessage() {
        return true;
    }
}