package com.lds.oneplanning;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.iot.common.constant.SystemConstants;
import com.iot.common.exception.CustomErrorDecoder;
import com.lds.remote.channel.InkfishChannel;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.Bean;
import org.springframework.core.convert.converter.Converter;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.Date;
import java.util.Properties;
import java.util.TimeZone;

@Slf4j
@SpringBootApplication(scanBasePackages = {"com.iot", "com.lds"},exclude = {DataSourceAutoConfiguration.class, DruidDataSourceAutoConfigure.class})
@EnableDiscoveryClient
@EnableAsync
@EnableHystrix
@EnableFeignClients(basePackages = {"com.iot", "com.lds"})
@MapperScan(basePackages = {"com.lds.oneplanning.**.mapper"})
//@EnableBinding(value = {InkfishChannel.class})
public class OnePlanningServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(OnePlanningServiceApplication.class, args);
        log.debug("start oneplanning service....");
    }

    public ErrorDecoder errorDecoder() {
        return new CustomErrorDecoder();
    }

    @PostConstruct
    void started() {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
    }

    @Bean
    public Converter<String, Date> dateConvert() {
        return new Converter<String, Date>() {
            @Override
            public Date convert(String dateStr) {
                long lt = Long.parseLong(dateStr);
                return new Date(lt);
            }
        };
    }

    @PostConstruct
    public void createDefaultFile(){
        try {
            String windows = "Windows";
            Properties props=System.getProperties();
            String osName = props.getProperty("os.name");
            if(!osName.startsWith(windows)) {
                File file = new File(SystemConstants.DEFAULT_UPLOAD_PATH);
                if(!file.exists()){
                    file.mkdirs();
                }
            }
        } catch (Exception e) {
            log.error("user-service createDefaultFile error {}", e);
        }
    }
}
