package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.entity.WpsOrderDispatch;
import com.lds.oneplanning.wps.model.WpsOrderDispatchDTO;

import java.util.Collection;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
public interface IWpsOrderDispatchService extends IService<WpsOrderDispatch> {

    Page<WpsOrderDispatchDTO> page(String keyword, Integer pageNum, Integer pageSize);

    WpsOrderDispatchDTO detail(Long id);

    void dispatch(WpsOrderDispatchDTO dto);

    /**
     * 查询分配给自己的订单，
     * @param empNo
     * @param targetOrders
     * @return
     */
    Set<String> getDispatchOrders(String empNo,Collection<String> targetOrders);

    /**
     * 给别人的订单 要减掉
     * @param empNo
     * @return
     */
    Set<String> getMinusOrders(String empNo, Collection<String> sourceList);
}
