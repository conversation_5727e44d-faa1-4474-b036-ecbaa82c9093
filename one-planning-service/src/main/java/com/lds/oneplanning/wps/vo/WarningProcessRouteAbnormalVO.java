package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-6-05
 */
@Data
@ApiModel(value="工艺路线异常表单", description="工艺路线异常表单")
@TableHeader(type = WpsOrderWarningTypeEnum.PROCESS_ROUTE_ABNORMAL, source = {ViewSource.PC,ViewSource.NPI})
public class WarningProcessRouteAbnormalVO extends AffectsPlan{

    private Long id;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "排产线体")
    private String productionLine;

    @ApiModelProperty(value = "线体名称")
    private String lineName;

    @ApiModelProperty(value = "计划订单")
    private String plannedOrder;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "计划上线时间")
    private LocalDate plannedOnlineTime;

    @ApiModelProperty(value = "距离计划上线时间剩余天数")
    private Integer days;

    @ApiModelProperty(value = "生产工厂")
    private String factory;

    @ApiModelProperty(value = "灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "是否维护工艺路线")
    private String sfwhgylx;

    @ApiModelProperty(value = "是否触发待办到责任人")
    private String pushStatus;

}
