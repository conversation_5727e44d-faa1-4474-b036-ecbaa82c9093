package com.lds.oneplanning.wps.warning.auto.handlers;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.service.ILineCapacityService;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WpsWarningFillLineUphHandler implements IWpsAutoPlanWarningHandler {

    @Autowired
    private IWpsOrderCommonService wpsOrderCommonService;

    @Autowired
    private ILineCapacityService lineCapacityService;

    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<String> lineUuids = getLineUuids(context);
        if (CollectionUtils.isEmpty(lineUuids)) {
            return;
        }
        Map<String, Map<String, Float>> orderLineUphMap = getOrderLineUphMap(context);
        if (MapUtils.isEmpty(orderLineUphMap)) {
            return;
        }
        WpsRowData wpsRowData = orderList.get(0);
        Set<LocalDate> allDates = wpsRowData.getScheduleDataMap().keySet();
        Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap = context.getDailyProductionLineMap();
        if (MapUtils.isEmpty(dailyProductionLineMap)) {
            initDailyProductionLineMap(context, new ArrayList<>(allDates));
        }
        log.info("WPS告警检查, 开始填充产线UPH, 订单UPH列表，userId：{},orderLineUphMap:{}.", context.getUserId(),
                JSON.toJSONString(orderLineUphMap));
    }

    @Override
    public int getOrder() {
        return 0;
    }

    /**
     * 获取产线代码
     *
     * @param context 上下文
     * @return 产线代码列表
     */
    private List<String> getLineUuids(WpsAutoScheduleContext context) {
        List<String> lineUuids = context.getLineUuids();
        if (CollectionUtils.isEmpty(lineUuids)) {
            lineUuids = context.getOrderList().stream()
                    .map(WpsRowData::getLineUuid)
                    .distinct()
                    .collect(Collectors.toList());
            context.setLineUuids(lineUuids);
        }
        return lineUuids;
    }

    /**
     * 获取订单可匹配的产线类
     *
     * @param context
     * @return Map<orderNo, Map<lineUuid, uph>>
     */
    private Map<String, Map<String, Float>> getOrderLineUphMap(WpsAutoScheduleContext context) {
        Map<String, Map<String, Float>> orderLineUphMap = context.getOrderLineUphMap();
        if (MapUtils.isNotEmpty(orderLineUphMap)) {
            return orderLineUphMap;
        }
        List<String> lineUuids = context.getLineUuids();
        if (CollectionUtils.isEmpty(context.getProductIds())) {
            return orderLineUphMap;
        }
        Map<String, List<String>> dpsLineUphRequests = createDpsLineUphRequests(context.getProductIds(), lineUuids);
        Map<String, Map<String, Float>> lineProductUphMap = wpsOrderCommonService.getLineProductUphMap(dpsLineUphRequests);
        if (MapUtils.isEmpty(lineProductUphMap)) {
            return orderLineUphMap;
        }
        context.getOrderList().forEach(order -> {
            String orderNo = order.getOrderNo();
            lineUuids.forEach(lineUuid -> {
                Optional.ofNullable(lineProductUphMap.get(lineUuid))
                        .map(map -> map.get(order.getProductId()))
                        .filter(uph -> uph > 0)
                        .ifPresent(uph -> orderLineUphMap.computeIfAbsent(orderNo, k -> Maps.newHashMap()).put(lineUuid, uph));
            });
        });
        return orderLineUphMap;
    }

    private Map<String, List<String>> createDpsLineUphRequests(List<String> productIds, List<String> lineUuids) {
        return lineUuids.stream()
                .collect(Collectors.toMap(
                        lineUuid -> lineUuid,
                        lineUuid -> new ArrayList<>(new HashSet<>(productIds))
                ));
    }

    private void initDailyProductionLineMap(WpsAutoScheduleContext context, List<LocalDate> allDates) {
        List<String> lineUuids = context.getLineUuids();
        List<LocalDate> localDates = allDates.parallelStream()
                .collect(Collectors.toList());
        Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap = lineCapacityService
                .getDailyProductionLineMap(BaseDataConstant.CONFIG_TYPE_LINE, lineUuids, null,localDates);
        if (MapUtils.isNotEmpty(dailyProductionLineMap)) {
            context.setDailyProductionLineMap(dailyProductionLineMap);
            log.info("WPS告警, 产线日计划数据初始化成功, userId：{}, dailyProductionLineMap:{}.",
                    context.getUserId(), JSON.toJSONString(dailyProductionLineMap));
        }
    }
}