package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WarningTodoList;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.req.TodoHandleReq;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【warning_todo_list】的数据库操作Service
 * @createDate 2025-05-16 09:58:56
 */
public interface WarningTodoListService extends IService<WarningTodoList> {

    /**
     * 保存数据
     *
     * @param warningType 警告类型
     * @param todoList    待办事项清单
     */
    void saveData(WpsOrderWarningTypeEnum warningType, List<WarningTodoList> todoList);

    /**
     * 推送LCP消息
     */
    void pushLcp();

    /**
     * 消除警报
     *
     * @param bizIds      要删除ID
     * @param warningType 警告类型
     */
    void eliminateAlarms(Collection<Long> bizIds, WpsOrderWarningTypeEnum warningType);

    /**
     * 处理
     *
     * @param req req
     */
    void handle(@Valid TodoHandleReq req);

    void firstUpgrade();

    void secondUpgrade();

    void thirdUpgrade();

    void fourthUpgrade();

    /**
     * 力推LCP
     *
     * @param type   WPS订单警告类型枚举
     * @param bizIds 业务ID集合
     */
    void forcePushLcp(WpsOrderWarningTypeEnum type, Set<Long> bizIds);
}
