package com.lds.oneplanning.wps.controller;


import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.req.ShipBookingUrgentReq;
import com.lds.oneplanning.wps.service.IWarningShipBookingUrgentAbnormalService;
import com.lds.oneplanning.wps.utils.PageHelper;
import com.lds.oneplanning.wps.vo.ShipBookingUrgentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api("船期临近未订舱异常")
@RestController
@RequestMapping("/wps/warning/shipBookingUrgent")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningShipBookingUrgentAbnormalController {

    private final IWarningShipBookingUrgentAbnormalService warningShipBookingUrgentAbnormalService;
    private final IUserInfoService userInfoService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public Page<ShipBookingUrgentVO> page(@RequestParam(value = "source", required = false) ViewSource viewSource,
                                          @RequestBody ShipBookingUrgentReq req) {
        ViewSource source = userInfoService.getOrDefaultUserType(viewSource);
        return PageHelper.cover(warningShipBookingUrgentAbnormalService.queryPage(source, req));
    }
}
