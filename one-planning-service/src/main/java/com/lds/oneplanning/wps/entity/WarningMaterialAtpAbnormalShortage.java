package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

@TableName(value = "warning_material_atp_abnormal_shortage")
@Data
public class WarningMaterialAtpAbnormalShortage {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "异常ID")
    private Long abnormalId;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "欠料组")
    private String shortageGroup;

    @ApiModelProperty(value = "欠料ID")
    private String shortageId;

    @ApiModelProperty(value = "物料描述")
    private String shortageDescription;

    @ApiModelProperty(value = "需求时间")
    private LocalDate requiredTime;

    @ApiModelProperty(value = "可得日期")
    private LocalDate availableDate;

    @ApiModelProperty(value = "齐套日期")
    private LocalDate qitaoDate;

    @ApiModelProperty(value = "欠料数量")
    private Integer shortageQuantity;

    @ApiModelProperty(value = "GAP天数")
    private Integer gapDays;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "采购交期")
    private LocalDate purchaseDeliveryTime;
    @ApiModelProperty(value = "最新采购交期")
    private LocalDate latestPurchaseDeliveryTime;

    /**
     * 应该改成未收数量
     */
    @ApiModelProperty(value = "采购未收数量")
    private Integer unReceivedQtyNum;

    @ApiModelProperty(value = "采购单号")
    private String poNo;
    @ApiModelProperty(value = "采购行项目")
    private String poItemNo;

    @ApiModelProperty(value = "采购人员")
    private String purchaseInCharge;

    @ApiModelProperty(value = "采购组")
    private String groupInCharge;
    /**
     *
     */
    @TableField(value = "created_at")
    private Date createdAt;
    /**
     *
     */
    @TableField(value = "updated_at")
    private Date updatedAt;

    @TableField(value = "created_by")
    private Long createdBy;
    @TableField(value = "updated_by")
    private Long updatedBy;
}