package com.lds.oneplanning.wps.service;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.req.InProductionAbnormalReq;
import com.lds.oneplanning.wps.vo.ProcessWorkOrderVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
public interface IMesProcessWorkOrderService extends IService<MesProcessWorkOrder> {

    /**
     * 分页查询
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<MesProcessWorkOrder> findListByPage(int pageNum, int pageSize);

    /**
     * 删除订单号列表
     */
    void removeByOrderNoList(List<String> validOrderNoList);

    /**
     * 查询在制工单异常
     * @param req
     * @return
     */
    Page<ProcessWorkOrderVO> queryPage(InProductionAbnormalReq req);

    Map<String,Integer> getReportQtyMap(Collection<String> orderNos);
}
