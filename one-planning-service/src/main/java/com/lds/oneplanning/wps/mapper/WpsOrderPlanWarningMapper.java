package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.vo.WpsOrderPlanWarningCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-28
 */
public interface WpsOrderPlanWarningMapper extends BaseMapper<WpsOrderPlanWarning> {

    /**
     * 物料不齐套异常消警
     */
    void eliminateAtpAlarms();

    /**
     * 来料检验异常
     */
    void eliminateAtpAlarmsLlyc();
    /**
     * 冻结解冻异常
     */
    void eliminateAtpAlarmsDjjd();
    /**
     * 工艺路线异常
     */
    void eliminateAtpAlarmsGylx();

    /**
     * 交期异常警告消除
     */
    void eliminateDeliveryDateAbnormalAlarms();

    /**
     * 船期临近未订舱警告消除
     */
    void eliminateShipBookingUrgentAlarms();

    /**
     * 按订单计数计数
     *
     * @param status   处理状态
     * @param orderNos 订购编号
     */
    List<WpsOrderPlanWarningCountVO> countByOrderNos(@Param("orderNos") List<String> orderNos);
}
