package com.lds.oneplanning.wps.utils;

import org.apache.commons.collections4.CollectionUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/25 15:40
 */
public class WpsDateUtil {
    private WpsDateUtil() {
    }

    public static List<LocalDate> getDateStacks(LocalDate startDate,int plusDays){
        startDate = startDate ==null ? LocalDate.now() : startDate;
        LocalDate endDate = startDate.plusDays(89);
        LocalDate dateIndex = startDate;
        List<LocalDate> dateStacks = com.google.common.collect.Lists.newArrayList();
        while (!dateIndex.isAfter(endDate)){
            dateStacks.add(dateIndex);
            dateIndex = dateIndex.plusDays(1);
        }
        return dateStacks;
    }

    /**
     * 排产第一天
     *
     * @param scheduleDataMap 包含日期和数值的映射表，键为LocalDate类型，值为Number类型
     * @return 如果映射表中存在值为正数的记录，则返回该记录对应的日期；否则返回null
     */
    public static LocalDate getStartScheduleDate(Map<LocalDate,Number> scheduleDataMap){
        if (scheduleDataMap == null) {
            return null;
        }
        for(Map.Entry<LocalDate,Number> entry : scheduleDataMap.entrySet()){
            if (entry.getValue() != null  &&  entry.getValue().intValue() >0) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 排产最后一天。
     *
     * @param scheduleDates 包含日期的列表
     * @return 如果列表为空或所有日期均为null，则返回null；否则返回列表中最晚的日期
     */
    public static LocalDate lastDate(List<LocalDate> scheduleDates) {
        if (CollectionUtils.isEmpty(scheduleDates)) {
            return null;
        }
        return scheduleDates.stream().max(LocalDate::compareTo).orElse(null);
    }
}