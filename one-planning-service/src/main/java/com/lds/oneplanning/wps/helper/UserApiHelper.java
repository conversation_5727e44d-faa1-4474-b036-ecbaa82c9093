package com.lds.oneplanning.wps.helper;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.lds.basic.account.user.api.UserApi2;
import com.lds.basic.account.user.dto.UserDto;
import com.lds.coral.auth.core.context.UserContextUtils;
import lombok.experimental.UtilityClass;
import lombok.var;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.TimeUnit;

@UtilityClass
public class UserApiHelper {
    private final static Cache<Long, String> userLoginNameCache = CacheBuilder.newBuilder()
            .maximumSize(256)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();

    public static String getUserLoginName() {
        Long userId = UserContextUtils.getUserId();
        return getUserLoginName(userId);
    }

    public static String getUserLoginName(Long userId) {
        if (userId == null) {
            return "";
        }

        String loginName = userLoginNameCache.getIfPresent(userId);
        if (loginName != null) {
            return loginName;
        }

        UserApi2 userApi = SpringUtil.getBean(UserApi2.class);
        UserDto params = new UserDto();
        params.setId(userId);

        var userBo = userApi.getDto(params);
        if (userBo == null) {
            return "";
        }

        loginName = userBo.getLoginName();
        if (StringUtils.isEmpty(loginName)) {
            throw new RuntimeException("用户登录名为空");
        }
        userLoginNameCache.put(userId, loginName);
        return loginName;
    }
}
