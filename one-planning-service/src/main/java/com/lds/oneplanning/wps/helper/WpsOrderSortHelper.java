package com.lds.oneplanning.wps.helper;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.wps.entity.WpsRowExt;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsRowExtService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/26 15:42
 */
@Component
public class WpsOrderSortHelper {

    @Resource
    private IWpsRowExtService wpsWeekPlanExtService;

    private int nullStrCompare(String s1,String s2){
        if (s1 == null  && s2 == null) {
            return 0;
        }else if(s1 ==null && s2!=null ){
            return -1;
        }else if(s1!=null && s2==null){
            return 1;
        }else{
            return s1.compareTo(s2);
        }
    }
    private int nullLocalDateCompare(LocalDate d1,LocalDate d2){
        if (d1 == null  && d2 == null) {
            return 0;
        }else if(d1 ==null && d2!=null ){
            return -1;
        }else if(d1!=null && d2==null){
            return 1;
        }else{
            return d1.compareTo(d2);
        }
    }
    public void sortBeforeAutoSchedule(List<WpsRowData> dirtyList) {
        // 根据预排上线日期 ASC、上线截止日期，排产序号（有排产保存过&7天内才加入计算） ASC、排产算法（正向排产 > 逆向排产）、产品组、产品ID、销售订单号+行项目号、最终完工日期 ASC
        dirtyList.sort((o1, o2) -> {
            int preOnlineCompare= this.nullLocalDateCompare(o1.getPreOnlineTime(), o2.getPreOnlineTime());
            if (preOnlineCompare != 0) {
                // 预排产时间存在且不等 直接返回
                return preOnlineCompare;
            } else {
                int endProductPeriodCompare = this.nullLocalDateCompare(o1.get_endProductPeriod(),o2.get_endProductPeriod());
                if (endProductPeriodCompare !=0) {
                    return endProductPeriodCompare;
                }else {
                    // 进入排序方向对比
                    if (!o1.getScheduleDirection().equals(o2.getScheduleDirection())) {
                        // 排产算法不等，用排产算法
                        return o1.getScheduleDirection().compareTo(o2.getScheduleDirection());
                    } else {
                        // 排产算法相等  ,进入产品组对比
                        int productGroupCodeCompare= this.nullStrCompare(o1.getProductGroupCode(), o2.getProductGroupCode());
                        if (productGroupCodeCompare != 0) {
                            return productGroupCodeCompare;
                        } else {
                            // 产品组相等，使用产品id
                            int productIdCompare = this.nullStrCompare(o1.getProductId(), o2.getProductId());
                            if (productIdCompare != 0) {
                                return productIdCompare;
                            } else {
                                //进入订单销售号排序
                                int sellOrderAndRowItemCompare = this.nullStrCompare(o1.getSellOrderAndRowItemNo(), o2.getSellOrderAndRowItemNo());
                                if (sellOrderAndRowItemCompare != 0) {
                                    return sellOrderAndRowItemCompare;
                                } else {
                                    //进入计算完工日期排序
                                    return this.nullLocalDateCompare(o1.getCalculateFinishTime(), o2.getCalculateFinishTime());
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     *  线体内排序 这么多重排序  排序规则 https://confluence.leedarson.com/pages/viewpage.action?pageId=178296687
     *  1、根据预排上线日期 ASC、
     *  2、根据截止日期 ASC、
     *  2、排产序号（有排产保存过&7天内才加入计算） ASC、
     *  3、排产算法（正向排产 > 逆向排产）、
     *  4、产品组、
     *  5、产品ID、
     *  6、销售订单号+行项目号、
     *  7、最终完工日期 ASC
     * @param wpsRowDatas
     */
    public void sortInLine(List<WpsRowData> wpsRowDatas) {
        Set<String> bizIds = wpsRowDatas.stream().map(WpsRowData::getOrderNo).collect(Collectors.toSet());
        List<WpsRowExt> planExts = wpsWeekPlanExtService.listByBizIds(bizIds);

        Map<String, LocalDate> dateSortMap = planExts.stream()
                .collect(Collectors.toMap(WpsRowExt::getBizId, WpsRowExt::getScheduleDate, (v1, v2) -> v2));

        Map<String, Long> seqSortMap = planExts.stream()
                .collect(Collectors.toMap(WpsRowExt::getBizId, WpsRowExt::getScheduleSeq, (v1, v2) -> v2));

        wpsRowDatas.sort((o1, o2) -> {
            LocalDate preOnlineTime1 = dateSortMap.getOrDefault(o1.getOrderNo(), o1.getPreOnlineTime());
            LocalDate preOnlineTime2 = dateSortMap.getOrDefault(o2.getOrderNo(), o2.getPreOnlineTime());
            int preOnlineCompare= this.nullLocalDateCompare(preOnlineTime1, preOnlineTime2);
            if (preOnlineCompare != 0) {
                // 预排产时间存在且不等 直接返回
                return preOnlineCompare;
            } else {
                int endProductPeriodCompare = this.nullLocalDateCompare(o1.get_endProductPeriod(),o2.get_endProductPeriod());
                if (endProductPeriodCompare !=0) {
                    return endProductPeriodCompare;
                }else{
                    // 进入排产序号
                    Long seq1 =  seqSortMap.getOrDefault(o1.getOrderNo(),-1L);
                    Long seq2 = seqSortMap.getOrDefault(o2.getOrderNo(),-1L);
                    if(seq1.compareTo(seq2) != 0 ){
                        return seq1.compareTo(seq2);
                    }else{
                        // 进入排产算法比对
                        if (!o1.getScheduleDirection().equals(o2.getScheduleDirection())) {
                            // 排产算法不等，用排产算法
                            return o1.getScheduleDirection().compareTo(o2.getScheduleDirection());
                        } else {
                            // 排产算法相等  ,进入产品组对比
                            int productGroupCodeCompare= this.nullStrCompare(o1.getProductGroupCode(), o2.getProductGroupCode());
                            if (productGroupCodeCompare != 0) {
                                return productGroupCodeCompare;
                            } else {
                                // 产品组相等，使用产品id
                                int productIdCompare = this.nullStrCompare(o1.getProductId(), o2.getProductId());
                                if (productIdCompare != 0) {
                                    return productIdCompare;
                                } else {
                                    //进入订单销售号排序
                                    int sellOrderAndRowItemCompare = this.nullStrCompare(o1.getSellOrderAndRowItemNo(), o2.getSellOrderAndRowItemNo());
                                    if (sellOrderAndRowItemCompare != 0) {
                                        return sellOrderAndRowItemCompare;
                                    } else {
                                        //进入计算完工日期排序
                                        return this.nullLocalDateCompare(o1.getCalculateFinishTime(), o2.getCalculateFinishTime());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
    }
    public List<WpsRowData> listAutoScheduleData(Map<String, Map<LocalDate, Map<String, Integer>>> orderDailyScheduleDataMap ,
                                                 WpsRowData wpsRowData,List<LineInfo> lineInfos,Map<String,String> workshoeNameMap){
        List<WpsRowData> resList = Lists.newArrayList();
        String bizId = wpsRowData.getOrderNo();

        if (orderDailyScheduleDataMap.get(bizId)!=null) {
            // 日期  线体(注意，已经改成lineUuid) 排产数量
            Map<LocalDate, Map<String, Integer>> dateMap = orderDailyScheduleDataMap.get(bizId);

            for (Map.Entry<LocalDate, Map<String, Integer>> entry : dateMap.entrySet()){
                // 排产日期
                LocalDate scheduleDate = entry.getKey();
                // 不同线体数量
                Map<String, Integer> lineMap = entry.getValue();

                // 线体，不同排产数量
                Map<String,List<Integer>> dateTargetMap = Maps.newLinkedHashMap();
                for (Map.Entry<String, Integer> lineEntry : lineMap.entrySet()){
                    String lineUuid = lineEntry.getKey();
                    if (dateTargetMap.containsKey(lineUuid)){
                        dateTargetMap.get(lineUuid).add(lineEntry.getValue());
                    }else{
                        dateTargetMap.put(lineUuid,Lists.newArrayList(lineEntry.getValue()));
                    }
                }
                Map<String,String> workshopMap = lineInfos.stream().filter(lineInfo -> lineInfo.getWorkshopCode()!=null).collect(Collectors.toMap(LineInfo::getLineUuid, LineInfo::getWorkshopCode,(s, s2) -> s2));
                Map<String,String> codeMap = lineInfos.stream().filter(lineInfo -> lineInfo.getCode()!=null).collect(Collectors.toMap(LineInfo::getLineUuid, LineInfo::getCode,(s, s2) -> s2));
                Map<String,String> nameMap = lineInfos.stream().filter(lineInfo -> lineInfo.getName()!=null).collect(Collectors.toMap(LineInfo::getLineUuid, LineInfo::getName,(s, s2) -> s2));
                for (Map.Entry<String,List<Integer>>  qtyEntry:  dateTargetMap.entrySet()){
                    String lineUuid = qtyEntry.getKey();
                    String lineCode = codeMap.get(lineUuid);
                    wpsRowData.setLineUuid(lineUuid);
                    wpsRowData.setLineCode(lineCode);
                    wpsRowData.setLineName(nameMap.get(lineUuid));
                    wpsRowData.setWorkshopCode(workshopMap.get(lineUuid));
                    wpsRowData.setWorkshopName(workshoeNameMap.get(wpsRowData.getWorkshopCode()));
                    List<Integer> qtyList = qtyEntry.getValue();
                    if (CollectionUtils.isEmpty(qtyList)) {
                        resList.add(wpsRowData);
                    }else{
                        qtyList.stream().forEach(qty -> {
                            if (resList.contains(wpsRowData)) {
                                // 注意 订单编码和产线编码一致就是一条记录 注意不要修改 wpsRowData的值
                                // 注意 找出ScheduleData记录 并进行设置，
                                resList.stream().filter(target -> target.equals(wpsRowData)).findAny().get().getScheduleDataMap().put(scheduleDate,qty);
                            }else{
                                WpsRowData copy = BeanUtil.map(wpsRowData, WpsRowData.class);
                                copy.getScheduleDataMap().put(scheduleDate,qty);
                                resList.add(copy);
                            }
                        });
                    }
                }

            }

        }else{
            resList.add(wpsRowData);
        }
        return resList;
    }
}
