package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.req.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-06-05
 */
@Data
public class WarningProcessRouteParams extends BasePageReq {

    // PC查询条件
    @ApiModelProperty(value = "计划角色：NPI")
    private ViewSource  viewRole;

    @ApiModelProperty(value = "工厂")
    private String factory;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "计划订单")
    private String plannedOrder;

    @ApiModelProperty(value = "物料id")
    private String materialId;

    @ApiModelProperty(value = "npi人员userid")
    private Long npiUserid;

    @ApiModelProperty(value = "工号")
    private String gh;
    private String processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

}
