/**/package com.lds.oneplanning.wps.service.facade.impl;

import cn.hutool.core.date.StopWatch;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.mps.model.RowSaveData;
import com.lds.oneplanning.wps.entity.WpsDayPlan;
import com.lds.oneplanning.wps.entity.WpsFormInfo;
import com.lds.oneplanning.wps.entity.WpsRowExt;
import com.lds.oneplanning.wps.filter.read.WpsOrderReadFilter;
import com.lds.oneplanning.wps.helper.WpsOrderSortHelper;
import com.lds.oneplanning.wps.model.OrderScheduleDTO;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsDayPlanService;
import com.lds.oneplanning.wps.service.IWpsFormInfoService;
import com.lds.oneplanning.wps.service.IWpsRowExtService;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import com.lds.oneplanning.wps.service.facade.WpsLineScheduleService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessContext;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessEnum;
import com.lds.oneplanning.wps.utils.LineScheduleBizUtils;
import com.lds.oneplanning.wps.utils.WpsTransUtils;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;





/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/8 16:53
 */
@Slf4j
@Service
public class WpsRowDataFacadeServiceImpl implements WpsRowDataFacadeService {

    @Resource
    private IWpsFormInfoService wpsFormInfoService;
    @Resource
    private IWpsRowExtService wpsRowExtService;
    @Resource
    private IWpsDayPlanService wpsDayPlanService;
    @Resource
    private IEsbDataFetchService dataFetchService;
    @Resource
    private IPlannerBaseService  plannerBaseService;
    @Resource
    private IWpsOrderCommonService wpsOrderCommonService;
    @Resource
    private WpsOrderSortHelper wpsOrderSortHelper;
    @Resource
    private List<WpsOrderReadFilter> wpsOrderReadFilterList;
    @Resource
    private WpsOrderProcessContext orderProcessContext;
    @Resource
    private WpsLineScheduleService wpsLineScheduleService;

    @Override
    public List<WpsRowData> customListOrder(List<WpsOrderProcessEnum> processEnums, Long userId, LocalDate startTime, LocalDate endTime, String factoryCode,boolean cacheFlag,Map<String,Object> params) {
        return orderProcessContext.processList(processEnums,userId,startTime, endTime,factoryCode,cacheFlag,params);
    }

    @Override
    public List<WpsRowData> listUserApiRowData(Long userId, Date startTime, Date endTime,String inputFactoryCode) {
        StopWatch stopWatch = new StopWatch();
        // step 1  fetch orders from esb api
        log.info("listUserApiRowData 数据准备耗时={}",stopWatch.prettyPrint(TimeUnit.MILLISECONDS ));
        stopWatch.start("listUserApiRowData 请求接口");
        List<WpsRowData> dirtyList = dataFetchService.fetchWpsOrderList(LocalDateTimeUtil.dateToLocalDate(startTime), LocalDateTimeUtil.dateToLocalDate(endTime), Lists.newArrayList(inputFactoryCode));
        stopWatch.stop();
        log.info("listUserApiRowData 请求接口 耗时={}",stopWatch.prettyPrint(TimeUnit.MILLISECONDS ));

        // step 2  filter all rule and set the filed
        wpsOrderReadFilterList.sort(Comparator.comparing(WpsOrderReadFilter::filterSeq));
        // 执行各种过滤
        StopWatch stopWatch4Filter = new StopWatch();
        for (WpsOrderReadFilter filter : wpsOrderReadFilterList){
            String filterName = filter.getClass().getName();
            stopWatch4Filter.start(String.format("业务过滤器%s",filterName));

            dirtyList  = filter.filter(userId, inputFactoryCode, dirtyList,false);

            stopWatch4Filter.stop();
            log.info(String.format("业务过滤器%s",filterName)+"耗时{}",stopWatch4Filter.prettyPrint(TimeUnit.MILLISECONDS ));
        }

        return dirtyList ;
    }

    @Override
    public List<WpsRowData> sortWpsRowData(List<WpsRowData> wpsRowDatas, Integer datasource) {
        if (wpsRowDatas == null || wpsRowDatas.isEmpty()) {
            return new ArrayList<>();
        }

        // Step 1: 分组 线体数据为空的组
        Map<String, List<WpsRowData>> groupByLineCode = wpsRowDatas.stream().filter(wpsRowData -> StringUtils.isNotBlank(wpsRowData.getLineCode()))
                .collect(Collectors.groupingBy(WpsRowData::getLineCode));
        // STEP2: key 值排序
        Comparator<String> customKeyComparator = (k1, k2) -> {
            boolean k1Blank = k1 == null || k1.isEmpty();
            boolean k2Blank = k2 == null || k2.isEmpty();

            if (!k1Blank && !k2Blank) {
                return k1.compareTo(k2); // 都不为空时按自然顺序排序
            } else if (k1Blank && k2Blank) {
                return 0; // 都为 null 或 ""，视为相等
            } else if (k1Blank) {
                return 1; // k1 为空，放在后面
            } else {
                return -1; // k2 为空，把 k2 放后面
            }
        };
        Map<String, List<WpsRowData>> sortedMap = groupByLineCode.entrySet()
                .stream()
                .sorted((e1, e2) -> customKeyComparator.compare(e1.getKey(), e2.getKey()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue, // merge function（一般不会冲突）
                        LinkedHashMap::new // 保证插入顺序
                ));

        List<WpsRowData> resList = new ArrayList<>();
        for (Map.Entry<String, List<WpsRowData>> entry : sortedMap.entrySet()) {
            String lineCode = entry.getKey();
            List<WpsRowData> targetList = entry.getValue();
            wpsOrderSortHelper.sortInLine(targetList);
            resList.addAll(targetList);
        }
        // 再根据线体名称进行排序
        resList.sort((o1, o2) -> LineScheduleBizUtils.getLineNameComparator().compare(o1.getLineName(),o2.getLineName()));
        List<WpsRowData> lineEmptyList=  wpsRowDatas.stream().filter(wpsRowData -> StringUtils.isBlank(wpsRowData.getLineCode())).collect(Collectors.toList());
        resList.addAll(lineEmptyList);
        return resList;
    }


    @Override
    public void saveRowData(List<RowSaveData> wpsRowDatas, Date date,Long userId) {
        String empNo = plannerBaseService.getEmpNoByUserId(userId);
        wpsRowDatas.stream().forEach(rowSaveData -> rowSaveData.setEmpNo(empNo));
        // save frominfo
        List<WpsFormInfo> wpsFormInfos = WpsTransUtils.wpsRowDatasToFormList(wpsRowDatas);
        wpsFormInfoService.saveOrUpdateBatchByBizId(wpsFormInfos);
        // <productId,<linCode,uph>>
        Map<String, Map<String, Float>> lineProductUphMap = buildLineProductUphMap(wpsRowDatas);
        // save plandata
        List<WpsDayPlan> wpsDayPlans = Lists.newArrayList();
        for (RowSaveData mpsRow  : wpsRowDatas){
            // 一行就变成一个list数组
            List<WpsDayPlan> rowList = WpsTransUtils.wpsRowDataToWpsWeekPlanList(mpsRow, lineProductUphMap);
            wpsDayPlans.addAll(rowList);
        }
        wpsDayPlanService.batchSaveByBizId(wpsDayPlans);

        List<WpsRowExt> totalWpsRowExts = Lists.newArrayList();
        for (RowSaveData mpsRow  : wpsRowDatas){
            // 一行就变成一个list数组
            WpsRowExt planExt = WpsTransUtils.wpsRowDataToWpsWeekPlanExt(mpsRow,date);
            totalWpsRowExts.add(planExt);
        }
        wpsRowExtService.batchSaveByBizId(totalWpsRowExts);
    }


    @Override
    public void saveData(List<WpsRowData> wpsRowDatas, Long userId) {
        List<OrderScheduleDTO> dtos = Lists.newArrayList();
        for (WpsRowData wpsRowData : wpsRowDatas){
            if (StringUtils.isBlank(wpsRowData.getLineCode()) || MapUtils.isEmpty(wpsRowData.getScheduleDataMap())) {
                continue;
            }
            OrderScheduleDTO dto = new OrderScheduleDTO();

            dto.setFactoryCode(wpsRowData.getFactory());
            dto.setLineUuid(wpsRowData.getLineUuid());
            dto.setLineCode(wpsRowData.getLineCode());
            dto.setOrderType(wpsRowData.getOrderType());
            dto.setOrderNo(wpsRowData.getOrderNo());
            dto.setSellOrderNo(wpsRowData.getSellOrderNo());
            dto.setRowItem(wpsRowData.getRowItem());
            dto.setProductGroupCode(wpsRowData.getProductGroupCode());
            dto.setProductGroupName(wpsRowData.getProductGroupName());
            dto.setOrderUnitQty(wpsRowData.getOrderUnitQty());
            dto.setOrderPcsQty(wpsRowData.getOrderPcsQty());
            dto.setCalculateFinishTime(wpsRowData.getCalculateFinishTime());
            dto.setOnlineTime(wpsRowData.getOnlineTime());
            dto.setProductId(wpsRowData.getProductId());
            dto.setCommodityId(wpsRowData.getCommodityId());
            dto.setFrozenStatus(wpsRowData.get_frozenStatus());
            dto.setScheduleDataMap(wpsRowData.getScheduleDataMap());
            dtos.add(dto);
            log.info("saveData->orderNo:{},scheduleData:{}",dto.getOrderNo(),dto.getScheduleDataMap());
        }
        wpsLineScheduleService.saveScheduleData(dtos,userId);
    }

    @Override
    public List<WpsRowData> listRowDataByDate(Long userId, LocalDate startDate, LocalDate endDate, List<String> factoryCodes) {
        if (null == userId || null == startDate || null == endDate || CollectionUtils.isEmpty(factoryCodes)) {
            return Collections.emptyList();
        }
        return dataFetchService.fetchWpsOrderList(startDate, endDate, factoryCodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStartScheduleDate(String orderNo, LocalDate scheduleDate) {
        // 更新 开始排产时间，把之后的数量全部放在这天，删除原来的排产信息，并将其冻结
        List<WpsDayPlan> targetList = wpsDayPlanService.list(Wrappers.<WpsDayPlan>lambdaQuery()
                .eq(WpsDayPlan::getBizId, orderNo).ge(WpsDayPlan::getScheduleDate, LocalDate.now()));
        if (targetList.isEmpty()) {
            return;
        }
        Integer sumPrePlanQuantity = targetList.stream().filter(wpsDayPlan -> wpsDayPlan.getPrePlanQuantity() != null).collect(Collectors.summingInt(WpsDayPlan::getPrePlanQuantity));
        WpsDayPlan dayPlan = BeanUtil.map(targetList.get(0), WpsDayPlan.class);
        dayPlan.setBizId(orderNo);
        dayPlan.setScheduleDate(scheduleDate);
        dayPlan.setPrePlanQuantity(sumPrePlanQuantity);
        dayPlan.setCreateBy(-1L);
        dayPlan.setUpdateBy(-1L);
        wpsDayPlanService.save(dayPlan);
        wpsDayPlanService.removeByIds(targetList.stream().map(WpsDayPlan::getId).collect(Collectors.toSet()));
        // 冻结该暑假
        LambdaUpdateWrapper<WpsRowExt> updateWrapper =  new LambdaUpdateWrapper<>();
        updateWrapper.set(WpsRowExt::getFrozenStatus,1);
        updateWrapper.eq(WpsRowExt::getBizId,orderNo);
//        updateWrapper.eq(WpsRowExt::getLineUuid,dayPlan.getLineUuid());
        wpsRowExtService.update(updateWrapper);

    }

    private Map<String, Map<String, Float>> buildLineProductUphMap(List<RowSaveData> wpsRowDatas) {
        List<String> lineCodes = wpsRowDatas.stream().map(RowSaveData::getLineCode)
                .distinct().collect(Collectors.toList());
        List<String> productIds = wpsRowDatas.stream().map(RowSaveData::getProductId)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lineCodes) || CollectionUtils.isEmpty(productIds)) {
            return Maps.newHashMap();
        }
        Map<String, List<String>> lineProductIdMap = lineCodes.stream()
                .collect(Collectors.toMap(
                        lineCode -> lineCode,
                        lineCode -> new ArrayList<>(new HashSet<>(productIds))
                ));
        return wpsOrderCommonService.getLineProductUphMap(lineProductIdMap);
    }
}
