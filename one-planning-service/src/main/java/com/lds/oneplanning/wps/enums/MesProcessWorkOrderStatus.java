package com.lds.oneplanning.wps.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/5/28
 */
@AllArgsConstructor
@Getter
public enum MesProcessWorkOrderStatus {
    UN_SYNC(0, "未同步"),
    IN_PRODUCTION_EXCEPTION(1, "在制异常"),
    ALARM_CLEARED(2, "已消警"),
    STOCKED(3, "已入库"),
    COMPLETED(4, "已完结");
    private final int code;
    private final String message;
}
