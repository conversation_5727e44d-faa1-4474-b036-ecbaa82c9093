package com.lds.oneplanning.wps.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
@Data
@ApiModel(value="质检人员待办表单", description="质检人员待办表单")
@TableHeader(type = WpsOrderWarningTypeEnum.MATERIAL_INSPECTION_ABNORMAL, source = ViewSource.IQC)
public class WarningIncomeMaterialAtpAbnormalIqcVO {

    @ApiModelProperty(value = "灯色")
    private LightColor color;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "销售订单+行项目")
    private String xsddhxm;

    @ApiModelProperty(value = "采购合同")
    private String purchaseContract;

    @ApiModelProperty(value = "行项目")
    private String purchaseHxm;

    @ApiModelProperty(value = "工厂")
    private String factory;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "数量")
    private Integer planQuantity;

    @ApiModelProperty(value = "判定结果")
    private String judgeResult;

    @ApiModelProperty(value = "IQC负责人")
    private String iqcCharge;

    @ApiModelProperty(value = "影响生产订单")
    private String orderNo;

    @ApiModelProperty(value = "对应计划上线时间")
    private String plannedLaunchTime;

    @ApiModelProperty(value = "物料最晚需要入库时间")
    private String latestStorageTime;

    @ApiModelProperty(value = "距离最晚需求入库时间天数")
    private long days;
    private OrderWarningHandleStatusEnum processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;
}
