package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MesProcessWorkOrderProcedure对象", description="")
public class MesProcessWorkOrderProcedure implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "工序编码")
    private String procedureCode;

    @ApiModelProperty(value = "工序名称")
    private String procedureName;

    @ApiModelProperty(value = "报工数量")
    private Integer qty;

    @ApiModelProperty(value = "排序")
    private Integer sortNo;

    @ApiModelProperty(value = "排产设备")
    private String schedulingEquipment;

    @ApiModelProperty(value = "排产模具")
    private String schedulingMold;
}
