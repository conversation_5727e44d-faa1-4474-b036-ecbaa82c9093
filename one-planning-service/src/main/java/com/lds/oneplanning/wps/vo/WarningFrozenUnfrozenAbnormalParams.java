package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.req.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *  冻结解冻
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
@Data
public class WarningFrozenUnfrozenAbnormalParams extends BasePageReq {

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "工厂")
    private String factory;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "计划上线时间-开始时间")
    private String startDate;

    @ApiModelProperty(value = "计划上线时间-结束时间")
    private String endDate;

    @ApiModelProperty(value = "人员角色")
    private ViewSource source;

    private String processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

    @ApiModelProperty(value = "QPL人员userid")
    private Long qplUserid;

    @ApiModelProperty(value = "NPI人员userid")
    private Long npiUserid;

}
