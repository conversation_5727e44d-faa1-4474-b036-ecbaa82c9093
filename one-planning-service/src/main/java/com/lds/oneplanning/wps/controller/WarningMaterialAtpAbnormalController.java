package com.lds.oneplanning.wps.controller;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Maps;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.enums.WpsPlanTypeEnum;
import com.lds.oneplanning.wps.model.EsbPoData;
import com.lds.oneplanning.wps.model.EsbPoParams;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.req.FactoryCodesReq;
import com.lds.oneplanning.wps.req.MaterialAtpAbnormalReq;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.WarningMaterialAtpAbnormalService;
import com.lds.oneplanning.wps.service.WarningMaterialAtpAbnormalShortageService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessContext;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessEnum;
import com.lds.oneplanning.wps.utils.PageHelper;
import com.lds.oneplanning.wps.utils.WspScheduleDebuggingFilesUtils;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalShortageVO;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalVO;
import com.lds.oneplanning.wps.warning.workbench.TempMockData;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningPipeline;
import com.lds.oneplanning.wps.warning.workbench.handlers.MaterialAtpAbnormalHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Api("物料不齐套异常")
@RestController
@RequestMapping("/wps/warning/atp")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningMaterialAtpAbnormalController {
    private final WarningMaterialAtpAbnormalService warningMaterialAtpAbnormalService;
    private final WarningMaterialAtpAbnormalShortageService warningMaterialAtpAbnormalShortageService;
    private final MaterialAtpAbnormalHandler materialAtpAbnormalHandler;
    private final IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    private final IUserInfoService userInfoService;
    private final WpsRowDataFacadeService wpsRowDataFacadeService;
    private final WpsOrderProcessContext wpsOrderProcessContext;
    private final EsbDataFetchService esbDataFetchService;
    private final WarningTodoListService warningTodoListService;

    @PostMapping("/page")
    @ApiOperation("物料不齐套异常分页查询")
    public Page<?> page(@RequestParam(value = "source", required = false) ViewSource viewSource,
                        @RequestBody MaterialAtpAbnormalReq vo) {
        ViewSource source = userInfoService.getOrDefaultUserType(viewSource);
        return PageHelper.cover(warningMaterialAtpAbnormalService.queryPage(source, vo));
    }

    @ApiOperation("欠料详情查询")
    @GetMapping("/shortage")
    public List<MaterialAtpAbnormalShortageVO> getShortageList(@RequestParam Long id) {
        return warningMaterialAtpAbnormalShortageService.getShortageList(id);
    }



    @GetMapping("/rePushMessage")
    @ApiOperation("物料不齐套重新推送消息")
    public void rePushMessage(@RequestParam Long id) {
        List<MaterialAtpAbnormalShortageVO> shortageList = warningMaterialAtpAbnormalShortageService.getShortageList(id);
        if (CollectionUtils.isEmpty(shortageList)) {
            return;
        }
        Set<Long> bizIds = shortageList.stream().map(MaterialAtpAbnormalShortageVO::getId).collect(Collectors.toSet());
        warningTodoListService.forcePushLcp(WpsOrderWarningTypeEnum.ATP_EXCEPTION, bizIds);
    }

    @PostMapping("/update")
    @ApiOperation("物料不齐套异常更新")
    public void update(@RequestBody MaterialAtpAbnormalVO vo) {
        warningMaterialAtpAbnormalService.updateData(vo);
    }


    @GetMapping("/test/generateData")
    public void testGenerateData() {
        log.info("测试数据生成中...");
        WpsAutoScheduleContext ctx = TempMockData.getCtx3();

        WpsWorkbenchWarningContext context = new WpsWorkbenchWarningContext();
        context.setFactoryCode(ctx.getCurrentFactoryCode());
        context.setOrders(ctx.getOrderList());
        context.setProductType(WpsPlanTypeEnum.WHOLE_MACHINE);

        List<WpsOrderPlanWarning> list = materialAtpAbnormalHandler.execute(context, Maps.newHashMap());
        if (CollectionUtils.isNotEmpty(list)) {
            wpsOrderPlanWarningService.batchSaveUnHandlerWarning(list);
        }

        log.info("测试数据生成完成");
    }

    @GetMapping("/test/pushLcp")
    public void pushLcp() {
        SpringUtil.getBean(WarningTodoListService.class).pushLcp();
    }

    @GetMapping("/test/startAnalyzeAbnormal")
    public void startAnalyzeAbnormal(@RequestParam String factoryCode) {
        Long userId = 1367766865382349738L;
        List<WpsRowData> orders = SpringUtil.getBean(WpsOrderProcessContext.class)
                .process(WpsOrderProcessEnum.READ_STORAGE, userId, null, null, factoryCode, true,Maps.newHashMap());

        WpsAutoScheduleContext context = new WpsAutoScheduleContext();
        context.setCurrentFactoryCode(factoryCode);
        context.setOrderList(orders);

        SpringUtil.getBean(WpsWorkbenchWarningPipeline.class)
                .execute(factoryCode, userId, context, WpsPlanTypeEnum.WHOLE_MACHINE);

    }

    @GetMapping("/test/queryLeader")
    public Object queryLeader() {

        return SpringUtil.getBean(EsbDataFetchService.class)
                .fetchLeader(Arrays.asList("11004010", "11004376"));

    }

    @GetMapping("/test/downloadResultFile")
    public void downloadResultFile(HttpServletResponse response) {
        // 下载结果文件
        String scheduleResult = WspScheduleDebuggingFilesUtils.getScheduleResult();
        String fileName = "scheduleResult.json";

        // 设置响应头
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.setContentType("application/json; charset=UTF-8"); // 指定编码格式

        try {
            response.setCharacterEncoding("UTF-8"); // 确保输出使用 UTF-8 编码
            response.getWriter().write(scheduleResult);
        } catch (IOException e) {
            log.error("下载结果文件失败", e);
        }
    }

    @PostMapping("/test/fetchPoInfo")
    public EsbPoData fetchPoInfo(@RequestBody EsbPoParams req) {
        log.info("查询PO信息: {}", req);
        return esbDataFetchService.fetchPoInfo(req);
    }

    @PostMapping("/test/updatePoInfo")
    public void updatePoInfo() {
        warningMaterialAtpAbnormalShortageService.updatePoInfo();
    }

    @PostMapping("/productionScheduling")
    public void productionScheduling(@RequestBody FactoryCodesReq req) {
        Long userId = UserContextUtils.getUserId();

        //获取当前年份的第一天作为开始时间
        for (String factoryCode : req.getFactoryCodes()) {
            try {
                List<WpsRowData> wpsRowDatas = wpsOrderProcessContext.process(WpsOrderProcessEnum.AUTO_SCHEDULE, userId, factoryCode, true,Maps.newHashMap());
                //保存自动排产数据
                if (org.apache.commons.collections.CollectionUtils.isEmpty(wpsRowDatas)) {
                    continue;
                }
                wpsRowDataFacadeService.saveData(wpsRowDatas, userId);
            } catch (Exception e) {
                log.error("工厂{}生产排产失败", factoryCode, e);
            }
        }


    }

}
