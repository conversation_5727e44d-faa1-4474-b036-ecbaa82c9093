package com.lds.oneplanning.wps.filter.write;

import com.lds.oneplanning.wps.model.WpsRowData;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuang<PERSON>ayin
 * @Email: <EMAIL>
 * @Date: 2025/3/26 15:21
 */
public interface WpsOrderWriteFilter {


    Integer filterSeq();

    void setNext(WpsOrderWriteFilter writeFilter);

    List<WpsRowData> filter(Long userId,Integer datasource, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag, Map<String,Object> params);
}
