package com.lds.oneplanning.wps.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/5/29
 */
@TableHeader(type = WpsOrderWarningTypeEnum.PROCESS_WORK_ORDER, source = {ViewSource.DEFAULT})
@Data
public class ProcessWorkOrderVO implements Serializable {

    private Long id;

    @ApiModelProperty(value = "销售订单-行项目")
    private String associatedOrderNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "排产日期")
    private LocalDate schedulingDate;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "生产车间")
    private String productionWorkshop;

    @ApiModelProperty(value = "生产线体")
    private String productionLine;

    @ApiModelProperty(value = "生产课长")
    private String productionForeman;

    @ApiModelProperty(value = "生产线长")
    private String productionLineLeader;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "物料下架日期")
    private LocalDate materialOffShelfDate;

    @ApiModelProperty(value = "上线日期")
    private LocalDate onlineDate;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialName;

    @ApiModelProperty(value = "计划数量")
    private Integer plannedQuantity;

//    @ApiModelProperty(value = "在制天数")
//    private Integer productionDays;

    @ApiModelProperty(value = "实际投入数量")
    private Integer actualInputQuantity;

    @ApiModelProperty(value = "gap")
    private Integer actualInputQuantityGap;

    @ApiModelProperty(value = "实际报工数量")
    private Integer actualReportingQuantity;

    @ApiModelProperty(value = "实际报工gap")
    private Integer actualReportedQuantityGap;

    @ApiModelProperty(value = "报工状态")
    private String reportStatusStr;

}
