package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lds.oneplanning.common.utils.OrderArithUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.helper.WpsAutoScheduleOrderHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;

/**
 * 订单截止时间自动排产处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsEndProductPeriodOrderHandlerV2 implements IWpsAutoScheduleHandler {

    @Autowired
    WpsAutoScheduleOrderHelper wpsAutoScheduleOrderHelper;
    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        orderList.forEach(order -> {
            if (Optional.ofNullable(order.get_frozenStatus()).orElse(0) == 0) {
                processOrder(context, order);
            }
        });
        log.info("订单截止时间自动排产处理器->userId:{},dailyProductionLineMap:{},orderDailyScheduleDataMap:{}",context.getUserId(),context.getDailyProductionLineMap(),context.getOrderDailyScheduleDataMap());
    }

    private void processOrder(WpsAutoScheduleContext context, WpsRowData order) {
        String orderNo = order.getOrderNo();
        int waitingOrderQty = order.getWaitingOrderQty();
        Map<String, Map<LocalDate, Map<String, Integer>>> orderDailyScheduleDataMap = context.getOrderDailyScheduleDataMap();
        if (waitingOrderQty > 0) {
            String productGroupCode = order.get_productGroupCode();
            Set<String> lineUuids = getLineUuids(context.getProductGroupPriorityMap(), productGroupCode);
            if (CollectionUtils.isNotEmpty(lineUuids)) {
                String lineUuid = lineUuids.iterator().next();
                //获取填充日期
                LocalDate endProductPeriod =  wpsAutoScheduleOrderHelper.getFillOrderDate(order);
                if (null == endProductPeriod) {
                    return;
                }
                Map<String, Map<String, Float>> orderLineUphMap = context.getOrderLineUphMap();
                Float uph = Optional.ofNullable(orderLineUphMap.get(orderNo))
                        .map(lineUphMap -> lineUphMap.get(lineUuid))
                        .orElse(null);
                if (null == uph) {
                    return;
                }
                Map<LocalDate, Map<String, Integer>> dailyScheduleDataMap = orderDailyScheduleDataMap.computeIfAbsent(orderNo, k -> Maps.newHashMap());
                Map<String, Integer> lineUuidQtyMap = dailyScheduleDataMap.computeIfAbsent(endProductPeriod, k -> Maps.newHashMap());
                updateLineUuidQuantity(lineUuidQtyMap, lineUuid, waitingOrderQty);
                updateProductionLine(context, lineUuid, endProductPeriod, order.getOrderNo(), waitingOrderQty, uph);
                order.setWaitingOrderQty(0);
                log.info("WpsEndProductPeriodOrderHandlerV2->WPS排产,endProductPeriodOrder,orderNo:{},date:{},lineUuid:{},waitingOrderQty:{}.",
                        orderNo, endProductPeriod, lineUuid, waitingOrderQty);
                //增加线体换款时长，若有换款需要增加换款时长
                addChangeLineProductGroup(context, lineUuid, endProductPeriod, order);
            }
        }
    }

    private Set<String> getLineUuids(Map<String, Map<Integer, Set<String>>> productGroupPriorityMap, String productGroupCode) {
        Map<Integer, Set<String>> priorityMap = productGroupPriorityMap.get(productGroupCode);
        if (MapUtils.isEmpty(priorityMap)) {
            return Sets.newLinkedHashSet();
        }
        for (int i = WpsConstants.MIN_PRODUCT_GROUP_PRIORITY_SEQ; i <= WpsConstants.MAX_PRODUCT_GROUP_PRIORITY_SEQ; i++) {
            if (priorityMap.containsKey(i)) {
                return priorityMap.get(i);
            }
        }
        return Sets.newLinkedHashSet();
    }

    private void updateLineUuidQuantity(Map<String, Integer> lineUuidQtyMap, String lineUuid, int waitingOrderQty) {
        lineUuidQtyMap.merge(lineUuid, waitingOrderQty, Integer::sum);
    }

    private void updateProductionLine(WpsAutoScheduleContext context, String lineUuid, LocalDate endProductPeriod,
                                      String orderNo, float waitingOrderQty, Float uph) {
        Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap = context.getDailyProductionLineMap();
        Map<LocalDate, WpsProductionLine> localDateWpsProductionLineMap = dailyProductionLineMap.get(lineUuid);
        if (MapUtils.isEmpty(localDateWpsProductionLineMap)) {
            return;
        }
        WpsProductionLine wpsProductionLine = localDateWpsProductionLineMap.get(endProductPeriod);
        if (null != wpsProductionLine && null != uph) {
            wpsProductionLine.setWaitingScheduleHours(0F);
            float waitingOrderHours = OrderArithUtil.floatDivide(waitingOrderQty, uph);
            float scheduledHours = OrderArithUtil.floatAdd(wpsProductionLine.getScheduledHours(), waitingOrderHours);
            wpsProductionLine.setScheduledHours(scheduledHours);
            wpsProductionLine.getOrderScheduledHoursMap().merge(orderNo, waitingOrderHours, Float::sum);
            log.info("doEndProductPeriodOrderHandler,orderNo:{},date:{},lineUuid:{},uph:{},waitingOrderHour:{},waitingOrderQty:{}.",
                    orderNo, endProductPeriod, lineUuid, uph, waitingOrderHours, waitingOrderQty);
        }
    }
    /**
     * 验证线体是否有换产品组生产，如果有换款则需要增加换线时长
     * @param context
     * @param lineUuid
     * @param order
     */
    private void addChangeLineProductGroup(WpsAutoScheduleContext context, String lineUuid,LocalDate scheduleDate, WpsRowData order) {
        float waitingOrderHours = wpsAutoScheduleOrderHelper.lineChangeProductGroupHour(lineUuid,scheduleDate,order,context);
        if(waitingOrderHours<=0){
            return;
        }
        Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap = context.getDailyProductionLineMap();
        Map<LocalDate, WpsProductionLine> localDateWpsProductionLineMap = dailyProductionLineMap.get(lineUuid);
        if (MapUtils.isEmpty(localDateWpsProductionLineMap)) {
            return;
        }
        WpsProductionLine wpsProductionLine = localDateWpsProductionLineMap.get(scheduleDate);
        if (null != wpsProductionLine) {
            float scheduledHours = OrderArithUtil.floatAdd(wpsProductionLine.getScheduledHours(), waitingOrderHours);
            wpsProductionLine.setScheduledHours(scheduledHours);
            wpsProductionLine.getOrderScheduledHoursMap().merge(order.getOrderNo(), waitingOrderHours, Float::sum);
            context.getLineProductGroupMap().computeIfAbsent(lineUuid,k->Maps.newHashMap()).put(scheduleDate,order.getProductGroupCode());
            //设置有换线
            order.setLineChangeProductGroup(true);
            log.info("对排产不完的订单,进行换款产品组增加换款时长->addChangeLineProductGroup,orderNo:{},date:{},lineUuid:{},waitingOrderHour:{}",
                    order.getOrderNo(), scheduleDate, lineUuid, waitingOrderHours);

        }

    }

    @Override
    public int getOrder() {
        return 8;
    }

    @Override
    public int getRound() {
        return 1;
    }
}