package com.lds.oneplanning.wps.warning.workbench.handlers;

import com.lds.oneplanning.esb.datafetch.model.EsbAbnormalVO;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.entity.WarningQualityHistoryRisk;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.entity.WpsOrderWarningCfg;
import com.lds.oneplanning.wps.enums.WpsOrderWarningCategoryEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IProductQualityHistoryService;
import com.lds.oneplanning.wps.service.IWarningQualityHistoryRiskService;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/4
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class QualityHistoryRiskHandler extends AbstractWpsWorkbenchWarningHandler {
    private final EsbDataFetchService esbDataFetchService;
    private final IWarningQualityHistoryRiskService warningQualityHistoryRiskService;
    private final IProductQualityHistoryService productQualityHistoryService;
    @Override
    public List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext ctx, Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap) {
        log.info("开始执行品质履历信息处理");
        try {
            return doExecute(ctx);
        } finally {
            log.info("品质履历信息处理完成");
        }
    }

    private List<WpsOrderPlanWarning> doExecute(WpsWorkbenchWarningContext ctx) {
        if (ctx == null || CollectionUtils.isEmpty(ctx.getOrders())) {
            return Collections.emptyList();
        }

        List<String> productIdList = ctx.getOrders().stream().map(WpsRowData::getProductId).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        log.info("产品id数量:{}", productIdList.size());

        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.emptyList();
        }
//        productIdList.add("1070312171");
//        productIdList.add("2064425014");
//        productIdList.add("1120600180");
//        productIdList.add("1072702016");
        List<EsbAbnormalVO> getabnormallist = esbDataFetchService.getabnormallist(productIdList);
        if (CollectionUtils.isEmpty(getabnormallist)){
            return Collections.emptyList();
        }

        //申请日期为一年以内的数据,sqrq是yyyy-MM-dd hh:mm:ss格式
        List<EsbAbnormalVO> esbAbnormalVOList = getabnormallist.stream().filter(esbAbnormalVO ->
                StringUtils.isNotEmpty(esbAbnormalVO.getSqrq()) &&
                        esbAbnormalVO.getSqrq().contains("-") && esbAbnormalVO.getSqrq().length()>=10 &&
                LocalDate.parse(esbAbnormalVO.getSqrq().substring(0,10)).isAfter(LocalDate.now().minusYears(1))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(esbAbnormalVOList)){
            return Collections.emptyList();
        }
        productQualityHistoryService.saveOrUpdate(esbAbnormalVOList);
        Set<String> cpidList = esbAbnormalVOList.stream()
                .map(EsbAbnormalVO::getCpid).collect(Collectors.toSet());

        List<WarningQualityHistoryRisk> riskList = ctx.getOrders().stream().filter(wpsRowData -> cpidList.contains(wpsRowData.getProductId()))
                .map(wpsRowData -> {
                    return WarningQualityHistoryRisk.builder()
                            .planTime(wpsRowData.getPlanLoadDate())
                            .productionWorkshop(wpsRowData.getWorkshopName())
                            .factoryCode(wpsRowData.getFactory())
                            .orderNo(wpsRowData.getOrderNo())
                            .productId(wpsRowData.getProductId())
                            .planQty(wpsRowData.getOrderPcsQty())
                            .productCategory(wpsRowData.getProductLineLevel5())
                            .build();
                }).collect(Collectors.toList());

        warningQualityHistoryRiskService.saveOrUpdate(riskList);
        return Collections.emptyList();
    }

    @Override
    public WpsOrderWarningCategoryEnum getWarningCategory() {
        return WpsOrderWarningCategoryEnum.DEFAULT;
    }

    @Override
    public WpsOrderWarningTypeEnum getWarningType() {
        return WpsOrderWarningTypeEnum.QUALITY_HISTORY_RISK;
    }
}
