package com.lds.oneplanning.wps.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransportDaysParams {
    private String busAccount = "100000";
    private String interfaceCode = "supplierDeliveryDayListServiceImpl";
    private Info data;


    public TransportDaysParams addSupplierCode(String supplierCode) {
        if (data == null) {
            data = new Info();
        }
        if (data.supplierCodes == null) {
            data.supplierCodes = new HashSet<>();
        }
        if (StringUtils.isEmpty(supplierCode)) {
            return this;
        }
        data.supplierCodes.add(supplierCode);
        return this;
    }

    public TransportDaysParams addPurchaseOrg(String purchaseOrg) {
        if (data == null) {
            data = new Info();
        }
        if (data.purchaseOrgs == null) {
            data.purchaseOrgs = new HashSet<>();
        }
        if (StringUtils.isEmpty(purchaseOrg)) {
            return this;
        }
        data.purchaseOrgs.add(purchaseOrg);
        return this;
    }


    @Data
    @Builder
    public static class Info {
        /**
         * 供应商编码
         */
        private Set<String> supplierCodes;
        /**
         * 采购组织
         */
        private Set<String> purchaseOrgs;

        public Info() {
            this.supplierCodes = new HashSet<>();
            this.purchaseOrgs = new HashSet<>();
        }

        public Info(Set<String> supplierCodes, Set<String> purchaseOrgs) {
            this.supplierCodes = supplierCodes;
            this.purchaseOrgs = purchaseOrgs;
        }
    }


}
