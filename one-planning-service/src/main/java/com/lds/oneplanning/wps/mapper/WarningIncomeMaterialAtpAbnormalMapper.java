package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningIncomeMaterialAtpAbnormal;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lds.oneplanning.wps.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
public interface WarningIncomeMaterialAtpAbnormalMapper extends BaseMapper<WarningIncomeMaterialAtpAbnormal> {

    IPage<WarningIncomeMaterialAtpAbnormalVO2> selectPage(Page<WarningIncomeMaterialAtpAbnormalVO2> page, @Param("params")WarningIncomeMaterialAtpAbnormalParams params);
    IPage<WarningIncomeMaterialAtpAbnormalVO> selectPageIqc(Page<WarningIncomeMaterialAtpAbnormalVO> page, @Param("dealResult") String dealResult, @Param("params")WarningIncomeMaterialAtpAbnormalParams params);
    IPage<WarningIncomeMaterialAtpAbnormalVO> selectPageMc(Page<WarningIncomeMaterialAtpAbnormalVO> page, @Param("dealResult") String dealResult, @Param("params")WarningIncomeMaterialAtpAbnormalParams params);
}
