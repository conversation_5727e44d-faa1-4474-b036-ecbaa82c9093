package com.lds.oneplanning.wps.service;

import com.lds.oneplanning.wps.entity.WarningIncomeMaterialPoAtpAbnormal;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormalShortage;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
public interface IWarningIncomeMaterialPoAtpAbnormalService extends IService<WarningIncomeMaterialPoAtpAbnormal> {

    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningMaterialAtpAbnormal }>
     */
    List<WarningIncomeMaterialPoAtpAbnormal> queryUnHandleData();

    void updateIncomePoInfo();

    void queryIncomePoInfo(List<WarningIncomeMaterialPoAtpAbnormal> poList, List<Long> undoList);

    void createTodoList(List<WarningIncomeMaterialPoAtpAbnormal> warningList);
}
