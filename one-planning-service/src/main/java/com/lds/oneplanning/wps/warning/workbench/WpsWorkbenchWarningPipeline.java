package com.lds.oneplanning.wps.warning.workbench;

import org.springframework.util.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.entity.WpsOrderWarningCfg;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.enums.WpsPlanTypeEnum;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.IWpsOrderWarningCfgService;
import com.lds.oneplanning.wps.utils.TraceIdUtils;
import com.lds.oneplanning.wps.utils.WspScheduleDebuggingFilesUtils;
import com.lds.oneplanning.wps.warning.workbench.handlers.IWpsWorkbenchWarningHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WpsWorkbenchWarningPipeline {

    private final Map<WpsOrderWarningTypeEnum, IWpsWorkbenchWarningHandler> handlerMap = Maps.newHashMap();

    @Autowired
    private IWpsOrderWarningCfgService wpsOrderWarningCfgService;

    @Autowired
    private IWpsOrderPlanWarningService wpsOrderPlanWarningService;

    @Autowired
    public WpsWorkbenchWarningPipeline(List<IWpsWorkbenchWarningHandler> handlers) {
        if (CollectionUtils.isEmpty(handlers)) {
            return;
        }
        for (IWpsWorkbenchWarningHandler handler : handlers) {
            handlerMap.put(handler.getWarningType(), handler);
        }
    }

    public void execute(String factoryCode, Long userId, WpsAutoScheduleContext wpsAutoScheduleContext, WpsPlanTypeEnum wpsPlanTypeEnum) {
        if (StringUtils.isEmpty(factoryCode) || null == userId
                || null == wpsAutoScheduleContext || CollectionUtils.isEmpty(wpsAutoScheduleContext.getOrderList())) {
            log.warn("invalid input params, factoryCode:{}, userId:{}, wpsAutoScheduleContext:{}", factoryCode, userId, wpsAutoScheduleContext != null);
            return;
        }

        log.info("执行工作台预警, factoryCode:{}, userId:{}, wpsPlanTypeEnum:{}", factoryCode, userId, wpsPlanTypeEnum);

        // 调试阶段，写入结果到文件中
        WspScheduleDebuggingFilesUtils.saveScheduleResult(wpsAutoScheduleContext);

        // 准备数据
        List<WpsRowData> orderList = wpsAutoScheduleContext.getOrderList();
        Map<String, List<WpsOrderWarningCfg>> wpsOrderWarningCfgMap = prepareWarningConfigs(factoryCode);

        // 处理所有handler
        List<WpsOrderPlanWarning> wpsOrderPlanWarningList = Lists.newCopyOnWriteArrayList();

        StopWatch stopWatch = new StopWatch();
        Arrays.stream(WpsOrderWarningTypeEnum.values())
//                .parallel() - 并行，先关闭方便调试
                .forEach(wpsOrderWarningTypeEnum -> {
                    IWpsWorkbenchWarningHandler wpsWorkbenchWarningHandler = handlerMap.get(wpsOrderWarningTypeEnum);
                    TraceIdUtils.setSpanId(wpsOrderWarningTypeEnum.getCode());
                    if (wpsWorkbenchWarningHandler == null) {
                        log.warn("handler not found for warningType:{}", wpsOrderWarningTypeEnum.getCode());
                        return;
                    }

                    WpsWorkbenchWarningContext context = new WpsWorkbenchWarningContext();
                    context.setUserId(userId);
                    context.setFactoryCode(factoryCode);
                    context.setProductType(wpsPlanTypeEnum);
                    context.setOrders(orderList);
                    context.setDailyProductionLineMap(wpsAutoScheduleContext.getDailyProductionLineMap());
                    log.info("execute wpsWorkbenchWarning, warningType:{}, factoryCode:{}, userId:{}, rowDataListSize:{}",
                            wpsOrderWarningTypeEnum.getCode(), factoryCode, userId, orderList.size());
                    stopWatch.start("WpsWorkbenchWarningPipeline-" + wpsOrderWarningTypeEnum.getCode());
                    processSingleHandler(wpsOrderWarningTypeEnum,
                            wpsOrderWarningCfgMap,
                            wpsWorkbenchWarningHandler,
                            context,
                            wpsOrderPlanWarningList);
                    log.info("finish wpsWorkbenchWarning, warningType:{}", wpsOrderWarningTypeEnum.getCode());
                    stopWatch.stop();
                });
        log.info("WpsWorkbenchWarningPipeline处理完成,userId:{}, factoryCode:{}, ,执行耗时={}.", userId, factoryCode, stopWatch.prettyPrint());

        // 保存结果
        if (CollectionUtils.isNotEmpty(wpsOrderPlanWarningList)) {
            log.info("saving warnings, size:{}", wpsOrderPlanWarningList.size());
            wpsOrderPlanWarningService.batchSaveUnHandlerWarning(wpsOrderPlanWarningList);
        }
    }

    /**
     * 处理单个预警处理器
     *
     * @param wpsOrderWarningTypeEnum    预警类型枚举
     * @param wpsOrderWarningCfgMap      预警配置映射
     * @param wpsWorkbenchWarningHandler 工作台预警处理器
     * @param context                    工作台预警上下文
     * @param wpsOrderPlanWarningList    预警计划列表
     */
    private void processSingleHandler(WpsOrderWarningTypeEnum wpsOrderWarningTypeEnum, Map<String, List<WpsOrderWarningCfg>> wpsOrderWarningCfgMap, IWpsWorkbenchWarningHandler wpsWorkbenchWarningHandler, WpsWorkbenchWarningContext context, List<WpsOrderPlanWarning> wpsOrderPlanWarningList) {
        try {
            Map<Integer, WpsOrderWarningCfg> levelCfgMap = getWarningLevelConfigs(wpsOrderWarningTypeEnum, wpsOrderWarningCfgMap);

            List<WpsOrderPlanWarning> warnings = wpsWorkbenchWarningHandler.execute(context, levelCfgMap);
            if (CollectionUtils.isNotEmpty(warnings)) {
                wpsOrderPlanWarningList.addAll(warnings);
            }
        } catch (Exception e) {
            log.error("执行工作台预警handler异常, warningType:{}", wpsOrderWarningTypeEnum.getCode(), e);
        }
    }

    /**
     * 准备警告配置
     *
     * @param factoryCode 工厂代码
     * @return 按警告类型分组的警告配置映射
     */
    private Map<String, List<WpsOrderWarningCfg>> prepareWarningConfigs(String factoryCode) {
        List<WpsOrderWarningCfg> wpsOrderWarningCfgs = wpsOrderWarningCfgService.listByFactoryCode(factoryCode);
        return CollectionUtils.isEmpty(wpsOrderWarningCfgs)
                ? Maps.newHashMap()
                : wpsOrderWarningCfgs.stream()
                .collect(Collectors.groupingBy(WpsOrderWarningCfg::getWarningType));
    }


    /**
     * 根据警告类型获取对应的警告级别配置
     *
     * @param warningType   警告类型枚举
     * @param warningCfgMap 警告配置映射，键为警告类型代码，值为警告配置列表
     * @return 警告级别配置映射，键为警告级别，值为警告配置对象
     */
    private Map<Integer, WpsOrderWarningCfg> getWarningLevelConfigs(
            WpsOrderWarningTypeEnum warningType, Map<String, List<WpsOrderWarningCfg>> warningCfgMap) {

        List<WpsOrderWarningCfg> cfgList = warningCfgMap.get(warningType.getCode());
        return CollectionUtils.isEmpty(cfgList)
                ? Maps.newHashMap()
                : cfgList.stream().collect(Collectors.toMap(WpsOrderWarningCfg::getWarningLevel, cfg -> cfg));
    }


}