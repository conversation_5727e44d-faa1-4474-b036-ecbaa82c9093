package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 独立备料SO未转正
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daish<PERSON>kun</a>
 * @since 2025/5/13 16:20
 */
@ApiModel(description = "独立备料SO未转正")
@TableHeader(type = WpsOrderWarningTypeEnum.INDEPENDENT_PREPARE_MATERIAL_ABNORMAL, source = ViewSource.OM)
@Data
public class IndependentPrepareMaterialAbnormalOmVO {
    private Long id;

    @ApiModelProperty(value = "预警日期")
    private LocalDate updatedAt;

    @ApiModelProperty(value = "首次创建日期")
    private LocalDate createdAt;

    @ApiModelProperty(value = "整灯风险备料单号")
    private String plannedOrderNumber;

    @ApiModelProperty(value = "责任人")
    private String responsiblePerson;

    @ApiModelProperty(value = "初次预计转正日期")
    private LocalDate initialEstimatedNormalizationDate;

    @ApiModelProperty(value = "初次预计出货日期")
    private LocalDate initialEstimatedShipmentDate;

    @ApiModelProperty(value = "计划单号")
    private String orderNumber;

    // 新增字段: 工厂
    @ApiModelProperty(value = "工厂")
    private String factoryCode;

    @ApiModelProperty(value = "商品ID")
    private String productId;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    @ApiModelProperty(value = "订单数量")
    private Integer orderQuantity;

    // 新增字段: 上线日期
    @ApiModelProperty(value = "上线日期")
    private LocalDate plannedOnlineTime;

    @ApiModelProperty(value = "原始完工")
    private LocalDate originalCompletionDate;

    @ApiModelProperty(value = "延迟后转正")
    private LocalDate delayedNormalizationDate;

    @ApiModelProperty(value = "调整后上线日期")
    private LocalDate adjustedOnlineDate;

    @ApiModelProperty(value = "计划单是否取消")
    private Boolean plannedOrderCancelled;

    @ApiModelProperty(value = "物料是否可消耗")
    private Boolean materialChangeOrCancellationCompleted;

    @ApiModelProperty(value = "最新预计转正日期")
    private LocalDate latestEstimatedNormalizationDate;

    @ApiModelProperty(value = "最新预计出货日期")
    private LocalDate latestEstimatedShipmentDate;

    @ApiModelProperty(value = "已变更次数")
    private Integer changeCount;

    @ApiModelProperty(value = "距离需求日期天数")
    private Integer daysToRequiredDate;

    @ApiModelProperty(value = "灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "类别")
    private String category;

    @ApiModelProperty(value = "销售订单-行项目")
    private String salesOrderNumber;

    @ApiModelProperty(value = "采购订单-行项目")
    private String purchaseOrderNumber;

    private String lineNumber;


    private String createdBy;
    private String updatedBy;

}
