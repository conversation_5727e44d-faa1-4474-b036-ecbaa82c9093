package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.vo.WpsOrderPlanWarningCountVO;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-28
 */
public interface IWpsOrderPlanWarningService extends IService<WpsOrderPlanWarning> {

    void batchSaveUnHandlerWarning(List<WpsOrderPlanWarning> wpsOrderPlanWarningList);

    List<WpsOrderPlanWarning> listByOrderNosAndDates(List<String> orderNos, LocalDate startDate, LocalDate endDate);

    List<WpsOrderPlanWarning> listByWarningTypeAndOrderNoAndDate(List<String> orderNos, String warningType, LocalDate startDate, LocalDate endDate);

    /**
     * 消警
     *
     * @param warningType 警告类型
     */
    void eliminateAlarms(WpsOrderWarningTypeEnum warningType);

    /**
     * 消警
     *
     * @param warningType
     * @param orderNoList
     */
    void eliminateAlarms(WpsOrderWarningTypeEnum warningType, List<String> orderNoList);

    /**
     * 按订单计数计数
     *
     * @param orderNos 订购编号
     * @return {@link List }<{@link WpsOrderPlanWarning }>
     */
    List<WpsOrderPlanWarningCountVO> countByOrderNos(List<String> orderNos);
}