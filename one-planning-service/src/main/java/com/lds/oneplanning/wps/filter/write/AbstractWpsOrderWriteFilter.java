package com.lds.oneplanning.wps.filter.write;

import com.lds.oneplanning.wps.model.WpsRowData;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuang<PERSON><PERSON>in
 * @Email: zhuang<PERSON><EMAIL>
 * @Date: 2025/3/25 17:11
 */
public abstract class AbstractWpsOrderWriteFilter implements WpsOrderWriteFilter {
    protected WpsOrderWriteFilter next;
    @Override
    public void setNext(WpsOrderWriteFilter filter) {
        this.next = filter;
    }

    @Override
    public List<WpsRowData> filter(Long userId,Integer datasource,String factoryCode,List<WpsRowData> dirtyList,boolean cacheFlag, Map<String,Object> params) {
        if (CollectionUtils.isEmpty(dirtyList)) {
            return dirtyList;
        }
        List<WpsRowData> filteredOrders = doFilter(userId,datasource,factoryCode,dirtyList,cacheFlag,params);
        if (next != null) {
            return next.filter(userId,datasource,factoryCode,filteredOrders,cacheFlag,params);
        }
        return filteredOrders;
    }

    protected abstract List<WpsRowData> doFilter(Long userId,Integer datasource,String factoryCode,List<WpsRowData> dirtyList,boolean cacheFlag, Map<String,Object> params);
}
