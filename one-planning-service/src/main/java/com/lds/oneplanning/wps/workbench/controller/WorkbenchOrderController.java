package com.lds.oneplanning.wps.workbench.controller;

import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.wps.workbench.req.HandleWarningOrderReq;
import com.lds.oneplanning.wps.workbench.req.WorkbenchOrderWarningReq;
import com.lds.oneplanning.wps.workbench.resp.*;
import com.lds.oneplanning.wps.workbench.service.IWorkbenchOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "WorkbenchOrderController", tags = "wps告警预警记录")
@RestController
@RequestMapping("/workbench/order")
public class WorkbenchOrderController {

    @Autowired
    private IWorkbenchOrderService workbenchOrderService;

    /**
     * 排产比例
     *
     * @return
     */
    @GetMapping("/production-ratio")
    public List<ProductionRatioResp> productionRatio() {
        Long userId = UserContextUtils.getUserId();
        return workbenchOrderService.productionRatio(userId);
    }

    /**
     * 订单情况
     *
     * @return
     */
    @GetMapping("/status")
    public List<OrderStatusResp> status() {
        Long userId = UserContextUtils.getUserId();
        return workbenchOrderService.listOrderStatus(userId);
    }

    /**
     * 排产未匹配订单列表
     */
    @ApiOperation(value = "排产未匹配订单列表", notes = "排产未匹配订单列表")
    @PostMapping("/listUnMatchOrder")
    public List<UnMatchOrderResp> listUnMatchOrder(@RequestBody @Validated WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        Long userId = UserContextUtils.getUserId();
        return workbenchOrderService.listUnMatchOrder(userId, workbenchOrderWarningReq);
    }

    @ApiOperation(value = "入库未达成", notes = "入库未达成")
    @PostMapping("/listUnStoreWarningOrder")
    public List<UnStoreOrderResp> listUnStoreWarningOrder(@RequestBody @Validated WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        Long userId = UserContextUtils.getUserId();
        return workbenchOrderService.listUnStoreWarningOrder(userId, workbenchOrderWarningReq);
    }

    /**
     * 验货预警订单列表
     *
     * @param workbenchOrderWarningReq
     * @return
     */
    @PostMapping("/listInspectionWarningOrder")
    public List<InspectionWarningOrderResp> listInspectionWarningOrder(@RequestBody @Validated WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        Long userId = UserContextUtils.getUserId();
        return workbenchOrderService.listInspectionWarningOrder(userId, workbenchOrderWarningReq);
    }

    @ApiOperation(value = "包材无版面", notes = "包材无版面")
    @PostMapping("/listUnPackingPrintWarningOrder")
    public List<PackingNoPrintResp> listUnPackingPrintWarningOrder(@RequestBody @Validated WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        Long userId = UserContextUtils.getUserId();
        return workbenchOrderService.listUnPackingPrintWarningOrder(userId, workbenchOrderWarningReq);
    }

    /**
     * 物料信息不齐套订单列表
     *
     * @param workbenchOrderWarningReq
     * @return
     */
    @PostMapping("/listAtpExceptionOrder")
    public List<AptExceptionOrderResp> listAtpExceptionOrder(@RequestBody @Validated WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        Long userId = UserContextUtils.getUserId();
        return workbenchOrderService.listAtpExceptionOrder(userId, workbenchOrderWarningReq);
    }

    /**
     * 处理预警订单
     */
    @PostMapping("/handleWarningOrder")
    public void handleWarningOrder(@RequestBody @Validated HandleWarningOrderReq handleWarningOrderReq) {
        Long userId = UserContextUtils.getUserId();
        workbenchOrderService.handleWarningOrder(userId, handleWarningOrderReq);
    }

    @ApiOperation(value = "船期临近未订舱", notes = "船期临近未订舱")
    @PostMapping("/listBookingUrgentWarningOrder")
    public List<BookingUrgentResp> listBookingUrgentWarningOrder(@RequestBody @Validated WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        Long userId = UserContextUtils.getUserId();
        return workbenchOrderService.listBookingUrgentWarningOrder(userId, workbenchOrderWarningReq);
    }

    @ApiOperation(value = "区间负荷异常", notes = "区间负荷异常")
    @PostMapping("/listSectionPayloadWarningOrder")
    public List<SectionPayloadWarningOrderResp> listSectionPayloadWarningOrder(@RequestBody @Validated WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        Long userId = UserContextUtils.getUserId();
        return workbenchOrderService.listSectionPayloadWarningOrder(userId, workbenchOrderWarningReq);
    }

}