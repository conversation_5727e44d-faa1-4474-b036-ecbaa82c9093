package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.enums.SysParamCfgEnum;
import com.lds.oneplanning.basedata.service.ISysParamCfgService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.common.utils.OrderArithUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.helper.WpsAutoScheduleOrderHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsOrderPublishStatusEnum;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * 过期订单排产
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsExpireOrderHandlerV2 implements IWpsAutoScheduleHandler {

    @Autowired
    WpsAutoScheduleOrderHelper wpsAutoScheduleOrderHelper;
    @Autowired
    ISysParamCfgService sysParamCfgService;
    @Override
    public void execute(WpsAutoScheduleContext context) {
        Long expireDays = getDays();
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        orderList.forEach(order -> {
            if(checkOrder(order, expireDays,context)){
                processOrder(context, order);
            }
        });
    }

    private void processOrder(WpsAutoScheduleContext context, WpsRowData order) {
        String orderNo = order.getOrderNo();
        int waitingOrderQty = order.getWaitingOrderQty();
        Map<String,Float> orderLineUphMap = context.getOrderLineUphMap().get(order.getOrderNo());
        if(MapUtils.isEmpty(orderLineUphMap)){
            log.info("WpsExpireOrderHandlerV2->订单未设置UPH，orderNo:{}",orderNo);
            return;
        }
        String lineUuid =getLineUuid(context,orderLineUphMap,orderNo);
        if (StringUtils.isBlank(lineUuid)) {
            return;
        }
        LocalDate scheduleDate = getScheduleDate(order, context);
        Float uph = orderLineUphMap.get(lineUuid);
        if(Objects.isNull(uph)){
            return;
        }
        //获取可排产的线体
        Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap = context.getDailyProductionLineMap();
        Map<LocalDate, WpsProductionLine> localDateWpsProductionLineMap = dailyProductionLineMap.get(lineUuid);
        if(Objects.isNull(localDateWpsProductionLineMap) || !localDateWpsProductionLineMap.containsKey(scheduleDate)){
            log.info("WpsExpireOrderHandlerV2->WPS排产过期订单没有预排产数据,orderNo:{},lineUuid:{},scheduleDate:{}", orderNo,lineUuid,scheduleDate,localDateWpsProductionLineMap);
            return;
        }
        Map<String, Map<LocalDate, Map<String, Integer>>> orderDailyScheduleDataMap = context.getOrderDailyScheduleDataMap();
        Map<LocalDate, Map<String, Integer>>  dailyScheduleDataMap = orderDailyScheduleDataMap.computeIfAbsent(orderNo, k -> Maps.newHashMap());
        Map<String, Integer> lineUuidQtyMap = dailyScheduleDataMap.computeIfAbsent(scheduleDate, k -> Maps.newHashMap());
        //更新线体排产数量
        updateLineUuidQuantity(lineUuidQtyMap, lineUuid, waitingOrderQty);
        //更新线体时长
        updateProductionLine(context, lineUuid, scheduleDate, orderNo, waitingOrderQty, uph);
        order.setWaitingOrderQty(0);
        order.setScheduleDates(Lists.newArrayList(scheduleDate));
    }

    private String getLineUuid(WpsAutoScheduleContext context,Map<String, Float> uphMap,String orderNo) {
        Map<Integer, Set<String>> orderLinePriorityMap = context.getOrderLinePriorityMap().get(orderNo);
        if(MapUtils.isEmpty(orderLinePriorityMap) || MapUtils.isEmpty(uphMap)){
            return null;
        }
        for (int i = WpsConstants.MIN_PRODUCT_GROUP_PRIORITY_SEQ; i <= WpsConstants.MAX_PRODUCT_GROUP_PRIORITY_SEQ; i++) {
            if (!orderLinePriorityMap.containsKey(i)) {
               continue;
            }
            Set<String> lineUuids = orderLinePriorityMap.get(i);
            String returnLineUuid = lineUuids.stream().filter(lineUuid -> uphMap.containsKey(lineUuid)).findFirst().orElse(null);
            if(StringUtils.isNotBlank(returnLineUuid)){
                return returnLineUuid;
            }
        }
        log.info("WpsExpireOrderHandlerV2->getLineUuid订单未找到排产的线体，orderNo:{},uphMap:{},orderLinePriorityMap:{}", orderNo,uphMap,orderLinePriorityMap);
        return null;
    }

    private void updateLineUuidQuantity(Map<String, Integer> lineUuidQtyMap, String lineUuid, int waitingOrderQty) {
        lineUuidQtyMap.merge(lineUuid, waitingOrderQty, Integer::sum);
    }

    private void updateProductionLine(WpsAutoScheduleContext context, String lineUuid, LocalDate endProductPeriod,
                                      String orderNo, float waitingOrderQty, Float uph) {
        Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap = context.getDailyProductionLineMap();
        Map<LocalDate, WpsProductionLine> localDateWpsProductionLineMap = dailyProductionLineMap.get(lineUuid);
        if (MapUtils.isEmpty(localDateWpsProductionLineMap)) {
            return;
        }
        WpsProductionLine wpsProductionLine = localDateWpsProductionLineMap.get(endProductPeriod);
        if (null != wpsProductionLine && null != uph) {
            float waitingOrderHours = OrderArithUtil.floatDivide(waitingOrderQty, uph);
            float scheduledHours = OrderArithUtil.floatAdd(wpsProductionLine.getScheduledHours(), waitingOrderHours);
            wpsProductionLine.setScheduledHours(scheduledHours);
            float waitingHours = OrderArithUtil.floatSubtract(wpsProductionLine.getWaitingScheduleHours(), waitingOrderHours);
            if(waitingHours>0){
                wpsProductionLine.setWaitingScheduleHours(waitingHours);
            }else{
                wpsProductionLine.setWaitingScheduleHours(0F);
            }
            wpsProductionLine.getOrderScheduledHoursMap().merge(orderNo, waitingOrderHours, Float::sum);
            log.info("WpsExpireOrderHandlerV2->填充过期订单的排产时长 orderNo:{},date:{},lineUuid:{},uph:{},waitingOrderHour:{},waitingOrderQty:{}",orderNo, endProductPeriod, lineUuid, uph, waitingOrderHours, waitingOrderQty);
        }
    }

    /**
     * 验证是否有效
     * @param order
     * @param days
     * @param context
     * @return
     */
    private Boolean checkOrder(WpsRowData order,Long days,WpsAutoScheduleContext context) {
        //冻结的订单不处理
        if (Objects.equals(order.get_frozenStatus(),1)) {
            return false;
        }
        //发布订单不处理
        if(Objects.equals(WpsOrderPublishStatusEnum.PUBLISHED.getValue(),order.get_publishStatus())){
            return false;
        }
        //订单数量为0不处理
        if(order.getWaitingOrderQty()<=0){
            return false;
        }
        //排除自动排产的订单
        if(CollectionUtils.isNotEmpty(order.getScheduleDates())){
            return false;
        }
        //排除没有产品组的订单
        if(StringUtils.isBlank(order.get_productGroupCode())){
            log.info("WpsExpireOrderHandlerV2->WPS排产,过期订单没有产品组,orderNo:{}", order.getOrderNo());
            return false;
        }
        //订单过期不处理
        LocalDate finishDate = getFinishDate(order);
        if(Objects.isNull(finishDate)){
            return false;
        }
        //超过指定天数的订单不处理
        LocalDate lastDate = context.getCurrentDate().plusDays(-days);
        //订单过期且在规定的时间内需要安排排产
        if(finishDate.compareTo(lastDate)>=0 && context.getCurrentDate().compareTo(finishDate)>=0){
            return true;
        }
        //完成时间未过期的需要进入排产
        if(context.getCurrentDate().compareTo(order.get_endProductPeriod())>0 && context.getCurrentDate().compareTo(finishDate)<=0){
            return true;
        }
        return false;
    }
    /**
     * TODO 过期或未排产的订单都排在当天生产（后续可能调整）
     * @param order
     * @param context
     * @return
     */
    private LocalDate getScheduleDate(WpsRowData order,WpsAutoScheduleContext context) {
        return context.getCurrentDate();
    }


    /**
     * 获取指定的过期的订单天数
     * @return
     */
    private Long getDays() {
        String orderExpireDays = sysParamCfgService.getValueByCode(SysParamCfgEnum.ORDER_EXPIRE_DAYS.getCode());
        //默认返回0
        if (StringUtils.isBlank(orderExpireDays)) {
            return 0L;
        }
        return new BigDecimal(orderExpireDays).longValue();
    }

    /**
     * 订单完成日期
     * 订单完成日期优先取推估需求完工日期没值取原始完工日期其次是生产结束日期
     * @param order
     * @return
     */
    private LocalDate getFinishDate(WpsRowData order){
        Date date = Objects.nonNull(order.getEstFinishTime())?order.getEstFinishTime():order.getOriginalFinishTime();
        date = date == null ? order.getProductEndTime():date;
        if(Objects.isNull(date)){
            return null;
        }
        return LocalDateTimeUtil.dateToLocalDate(date);
    }
    @Override
    public int getOrder() {
        return 9;
    }

    @Override
    public int getRound() {
        return 1;
    }
}