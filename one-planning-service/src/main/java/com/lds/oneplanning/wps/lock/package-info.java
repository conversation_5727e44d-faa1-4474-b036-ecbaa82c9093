/**
 * 分布式锁包
 * <p>
 * 本包提供基于Redisson实现的分布式锁功能，主要包括：
 * <ul>
 *     <li>分布式锁注解 {@link com.lds.oneplanning.wps.lock.DistributedLock}：用于在方法上标注需要加锁的操作</li>
 *     <li>分布式锁切面 {@link com.lds.oneplanning.wps.lock.aspect.DistributedLockAspect}：处理注解的AOP实现</li>
 *     <li>锁键生成器 {@link com.lds.oneplanning.wps.lock.support.LockKeyGenerator}：用于生成分布式锁的键</li>
 * </ul>
 * </p>
 * <p>
 * 使用示例：
 * <pre>
 *
 * // 使用固定键
 * {@literal @}DistributedLock(key = "myLock")
 * public void doSomething() {
 *     // 业务逻辑
 * }
 *
 * // 使用SpEL表达式，引用方法参数
 * {@literal @}DistributedLock(key = "user:#{#userId}")
 * public void processUser(String userId) {
 *     // 业务逻辑
 * }
 *
 * // 使用SpEL表达式，引用对象属性
 * {@literal @}DistributedLock(key = "order:#{#order.id}")
 * public void processOrder(Order order) {
 *     // 业务逻辑
 * }
 * </pre>
 * </p>
 */
package com.lds.oneplanning.wps.lock;