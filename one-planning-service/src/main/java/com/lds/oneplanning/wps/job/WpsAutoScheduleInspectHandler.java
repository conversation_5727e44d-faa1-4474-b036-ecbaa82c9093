package com.lds.oneplanning.wps.job;

import com.google.common.collect.Maps;
import com.lds.coral.job.annotation.JobRegister;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.service.WpsExcelService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * 自动排产异常检测
 */
@JobRegister(value = "WpsAutoScheduleInspectHandler", jobName = "WpsAutoScheduleInspectJob", cron = "0 0 1 * * ? *")
@Component
@Slf4j
public class WpsAutoScheduleInspectHandler extends IJobHandler {

    @Autowired
    private WpsExcelService wpsExcelService;

    @Autowired
    private IPlannerDataPermissionService plannerDataPermissionService;

    @Autowired
    WpsRowDataFacadeService wpsRowDataFacadeService;
    @Autowired
    IPlannerBaseService plannerBaseService;
    @Autowired
    RedissonClient redissonClient;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        ReturnT<String> returnInfo = SUCCESS;
        Long start = System.currentTimeMillis();
        try {
            Map<String, Set<Long>> factoryUserMap = plannerDataPermissionService.getFactoryUserMap();
            if (MapUtils.isEmpty(factoryUserMap)) {
                XxlJobLogger.log("No user factory code data found.");
                return SUCCESS;
            }
            for(Map.Entry<String,Set<Long>> entry:factoryUserMap.entrySet()){
                processUserData(entry);
            }
        } catch (Exception e) {
            log.error("WpsAutoScheduleInspectHandler 执行失败 -->{}", e.getMessage(), e);
            returnInfo = ReturnT.FAIL;
        }
        Long end = System.currentTimeMillis();
        log.info("=========================  WpsAutoScheduleInspectHandler job execute end ,耗时：{}======================",end-start);
        return returnInfo;
    }

    private void processUserData(Map.Entry<String, Set<Long>> entry) {
        try {
            String factoryCode = entry.getKey();
            Set<Long> userIds = entry.getValue();
            Date startTime = LocalDateTimeUtil.localDateToDate(LocalDate.of(LocalDate.now().getYear(),1,1));
            Date endTime = new Date();
            Map<String,Object> params = Maps.newHashMap();
            params.put(WpsConstants.IS_SAVE_SCHEDULE_PLANLOG_PARAM,true);
            for(Long userId:userIds){
               wpsExcelService.getDataWithLock(userId, WpsConstants.DATA_SOURCE_AUTO, startTime, endTime, factoryCode,false,params);
            }
        } catch (Exception e) {
            log.error("WpsAutoScheduleInspectHandler处理用户数据失败: userId={}, error={}", entry.getKey(), e.getMessage(), e);
            XxlJobLogger.log("WpsAutoScheduleInspectHandler处理用户数据失败: userId=" + entry.getKey() + ", error=" + e.getMessage());
        }
    }
}
