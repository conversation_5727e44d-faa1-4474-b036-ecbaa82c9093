package com.lds.oneplanning.wps.utils;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.io.File;

@UtilityClass
public class WspScheduleDebuggingFilesUtils {
    private static final String TEMP_FILE_PATH = "/tmp/wps_schedule_result.txt";

    public static synchronized void saveScheduleResult(Object result) {
        FileUtil.writeUtf8String(JSON.toJSONString(result), TEMP_FILE_PATH);
    }

    public static String getScheduleResult() {
        if (!new File(TEMP_FILE_PATH).exists()) {
            return "{}";
        }
        return StringUtils.defaultIfEmpty(FileUtil.readUtf8String(TEMP_FILE_PATH), "{}");
    }
}
