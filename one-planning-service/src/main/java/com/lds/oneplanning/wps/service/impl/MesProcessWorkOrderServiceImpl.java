package com.lds.oneplanning.wps.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.lds.basic.common.enums.BooleanEnum;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrder;
import com.lds.oneplanning.wps.enums.PlanTypeEnum;
import com.lds.oneplanning.wps.enums.ReportStatusEnum;
import com.lds.oneplanning.wps.mapper.MesProcessWorkOrderMapper;
import com.lds.oneplanning.wps.req.InProductionAbnormalReq;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderProcedureService;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderService;
import com.lds.oneplanning.wps.vo.ProcessWorkOrderVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
@Service
public class MesProcessWorkOrderServiceImpl extends ServiceImpl<MesProcessWorkOrderMapper, MesProcessWorkOrder> implements IMesProcessWorkOrderService {

    @Resource
    private IMesProcessWorkOrderProcedureService mesProcessWorkOrderProcedureService;

    @Override
    public List<MesProcessWorkOrder> findListByPage(int pageNum, int pageSize) {
        return baseMapper.selectList(Wrappers.lambdaQuery(MesProcessWorkOrder.class)
                .eq(MesProcessWorkOrder::getPlanType, PlanTypeEnum.WHOLE_MACHINE.getCode())
                .in(MesProcessWorkOrder::getIsCompleted, BooleanEnum.FALSE.code)
                //排产日期是昨天之前的数据,yyyy-MM-dd
                .le(MesProcessWorkOrder::getMaterialOffShelfDate, LocalDate.now().minusDays(1))
                .orderByDesc(MesProcessWorkOrder::getId)
                .last(" limit " + (pageNum - 1) * pageSize + "," + pageSize));
    }

    @Override
    public void removeByOrderNoList(List<String> validOrderNoList) {
        baseMapper.delete(new QueryWrapper<MesProcessWorkOrder>().lambda()
                .in(MesProcessWorkOrder::getOrderNo, validOrderNoList));
    }

    @Override
    public Page<ProcessWorkOrderVO> queryPage(InProductionAbnormalReq req) {
        Page<ProcessWorkOrderVO> result = new Page<>();
        PageHelper.startPage(req.getPage(), req.getPageSize());
        LambdaQueryWrapper<MesProcessWorkOrder> queryWrapper = Wrappers.lambdaQuery(MesProcessWorkOrder.class)
                .eq(MesProcessWorkOrder::getPlanType, PlanTypeEnum.WHOLE_MACHINE.getCode())
                .between(req.getSchedulingStartDate() != null && req.getSchedulingEndDate() != null, MesProcessWorkOrder::getSchedulingDate, req.getSchedulingStartDate(), req.getSchedulingEndDate())
                .like(StringUtils.isNotEmpty(req.getMaterialId()), MesProcessWorkOrder::getMaterialId, req.getMaterialId())
                .like(StringUtils.isNotEmpty(req.getOrderNo()), MesProcessWorkOrder::getOrderNo, req.getOrderNo())
                .like(StringUtils.isNotEmpty(req.getWorkOrderNo()), MesProcessWorkOrder::getWorkOrderNo, req.getWorkOrderNo())
                .eq(req.getIsCompleted() != null, MesProcessWorkOrder::getIsCompleted, req.getIsCompleted())
                .in(CollUtil.isNotEmpty(req.getFactoryCodeList()), MesProcessWorkOrder::getFactoryCode, req.getFactoryCodeList());
        if (StringUtils.isNotEmpty(req.getFactoryCodes())){
            queryWrapper.in(MesProcessWorkOrder::getFactoryCode, Arrays.asList(req.getFactoryCodes().split(",")));
        }
        //报工状态
        // 如果是“未完成”状态，则增加 plannedQuantity > actualReportedQuantity 条件
        if (Objects.equals(req.getReportStatus(), ReportStatusEnum.UNFINISHED.getCode())) {
            queryWrapper.apply("planned_quantity > actual_reporting_quantity");
        } else if (Objects.equals(req.getReportStatus(), ReportStatusEnum.FINISHED.getCode())) {
            queryWrapper.apply("planned_quantity = actual_reporting_quantity");
        }
        queryWrapper.orderByDesc(MesProcessWorkOrder::getSchedulingDate)
                .orderByAsc(MesProcessWorkOrder::getProductionLine)
                .orderByDesc(MesProcessWorkOrder::getId);
        PageInfo<MesProcessWorkOrder> pageInfo = new PageInfo<>(baseMapper.selectList(queryWrapper));
        if (CollUtil.isNotEmpty(pageInfo.getList())) {
            List<ProcessWorkOrderVO> resultVoList = BeanUtil.copyToList(pageInfo.getList(), ProcessWorkOrderVO.class);
            resultVoList.forEach(item -> {
                //计算actualInputQuantityGap
                if (item.getPlannedQuantity() != null && item.getActualInputQuantity() != null){
                    item.setActualInputQuantityGap(item.getPlannedQuantity() - item.getActualInputQuantity());
                }
                item.setReportStatusStr(ReportStatusEnum.UNFINISHED.getName());
                if (item.getPlannedQuantity() != null && item.getActualReportingQuantity() != null) {
                    item.setActualReportedQuantityGap(item.getPlannedQuantity() - item.getActualReportingQuantity());
                    if (item.getActualReportedQuantityGap() == 0) {
                        item.setReportStatusStr(ReportStatusEnum.FINISHED.getName());
                    }
                }
            });
            result.setResult(resultVoList);
        }
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(req.getPage());
        result.setPageSize(req.getPageSize());
        return result;
    }

    @Override
    public Map<String, Integer> getReportQtyMap(Collection<String> orderNos) {
        if (orderNos == null || orderNos.isEmpty()) {
            return Maps.newLinkedHashMap();
        }
        LambdaQueryWrapper<MesProcessWorkOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(MesProcessWorkOrder::getOrderNo,MesProcessWorkOrder::getActualReportingQuantity);
        queryWrapper.in(MesProcessWorkOrder::getOrderNo,orderNos);
        List<MesProcessWorkOrder> mesProcessWorkOrders = baseMapper.selectList(queryWrapper);
        return mesProcessWorkOrders.stream().filter(mesProcessWorkOrder -> mesProcessWorkOrder.getOrderNo()!=null && mesProcessWorkOrder.getActualReportingQuantity()!=null)
                .collect(Collectors.toMap(MesProcessWorkOrder::getOrderNo,MesProcessWorkOrder::getActualReportingQuantity,(integer, integer2) -> integer2));
    }
}
