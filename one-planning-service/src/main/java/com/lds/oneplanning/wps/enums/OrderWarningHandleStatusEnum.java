package com.lds.oneplanning.wps.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public enum OrderWarningHandleStatusEnum {
    /**
     * 未处理
     */
    UN_HANDLE(1, "未处理"),
    /**
     * 已处理
     */
    HANDLED(2, "已处理"),
    /**
     * 已关闭
     */
    CLOSED(3, "已关闭");

    private final Integer code;
    private final String name;

    public static OrderWarningHandleStatusEnum getByCode(Integer handleStatus) {
        for (OrderWarningHandleStatusEnum statusEnum : OrderWarningHandleStatusEnum.values()) {
            if (statusEnum.getCode().equals(handleStatus)) {
                return statusEnum;
            }
        }
        return null;
    }
}