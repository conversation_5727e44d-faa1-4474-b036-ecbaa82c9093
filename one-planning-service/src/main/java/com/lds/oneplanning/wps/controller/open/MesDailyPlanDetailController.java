package com.lds.oneplanning.wps.controller.open;


import com.lds.oneplanning.wps.req.MesDailyPlanDetailReq;
import com.lds.oneplanning.wps.service.IMesDailyPlanDetailService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-24
 */
@RestController
@RequestMapping("/esb-api/wps/mesDailyPlanDetail")
public class MesDailyPlanDetailController {

    @Autowired
    private IMesDailyPlanDetailService mesDailyPlanDetailService;

    @ApiOperation(value = "MES回写日计划明细", notes = "MES回写日计划明细")
    @PostMapping("/writeBack")
    public void writeBack(@RequestBody @Validated List<MesDailyPlanDetailReq> mesDailyPlanDetails) {
       mesDailyPlanDetailService.saveByPlanDateAndProductionLine(mesDailyPlanDetails);
    }
}