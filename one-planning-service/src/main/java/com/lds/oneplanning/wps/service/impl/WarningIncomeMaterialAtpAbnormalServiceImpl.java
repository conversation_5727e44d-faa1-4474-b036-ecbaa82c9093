package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.model.UserInfoDTO;
import com.lds.oneplanning.basedata.model.UserInfoQueryDTO;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.wps.entity.WarningIncomeMaterialAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningIncomeMaterialPoAtpAbnormal;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.mapper.WarningIncomeMaterialAtpAbnormalMapper;
import com.lds.oneplanning.wps.service.AffectsPlanService;
import com.lds.oneplanning.wps.service.IWarningIncomeMaterialAtpAbnormalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.wps.service.IWarningIncomeMaterialPoAtpAbnormalService;
import com.lds.oneplanning.wps.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
@Service
@Slf4j
public class WarningIncomeMaterialAtpAbnormalServiceImpl extends ServiceImpl<WarningIncomeMaterialAtpAbnormalMapper, WarningIncomeMaterialAtpAbnormal> implements IWarningIncomeMaterialAtpAbnormalService {

    @Resource
    private WarningIncomeMaterialAtpAbnormalMapper warningIncomeMaterialAtpAbnormalMapper;

    @Resource
    private IFactoryFacadeService factoryFacadeService;

    @Resource
    private IUserInfoService userInfoService;

    @Resource
    private AffectsPlanService affectsPlanService;

    @Resource
    private IWarningIncomeMaterialPoAtpAbnormalService poAtpAbnormalService;

    @Override
    public IPage<?> selectPage(ViewSource source, WarningIncomeMaterialAtpAbnormalParams params) {
        Long userId = UserContextUtils.getUserId();
        if (userId != null){
            UserInfoQueryDTO userInfoQueryDTO = new UserInfoQueryDTO();
            userInfoQueryDTO.setUserId(userId);
            List<UserInfoDTO> userInfoDTOS = userInfoService.listByCondition(userInfoQueryDTO);
            if (!userInfoDTOS.isEmpty()){
                params.setGh(userInfoDTOS.get(0).getEmpNo());
            }
        }
        if (ViewSource.PC.equals(source)) {
            if (StrUtil.isBlank(params.getFactory())){
                List<Factory> factories = factoryFacadeService.listByUser(userId);
                String factoryCodes = factories.stream()
                        .map(Factory::getCode)
                        .collect(Collectors.joining(","));
                params.setFactory(factoryCodes);
            }
            Page<WarningIncomeMaterialAtpAbnormalVO2> page2 = new Page<>(params.getPage(), params.getPageSize());
            return this.selectPagePc(page2,params);
        }
        if (ViewSource.IQC.equals(source)) {
            log.info("进入IQC分支={}", source);
            Page<WarningIncomeMaterialAtpAbnormalVO> page = new Page<>(params.getPage(), params.getPageSize());
            //IQC角色
            IPage<WarningIncomeMaterialAtpAbnormalVO> iqcPage = this.selectPageIqc(page, "不合格,待检", params);
            List<WarningIncomeMaterialAtpAbnormalVO> records = iqcPage.getRecords();
            List<WarningIncomeMaterialAtpAbnormalIqcVO> iqcVOS = new ArrayList<>();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            records.forEach(item -> {
                WarningIncomeMaterialAtpAbnormalIqcVO iqcVO = new WarningIncomeMaterialAtpAbnormalIqcVO();
                iqcVO.setSupplierName(item.getSupplyName());
                String[] split = item.getPurchasePo().split("_");
                iqcVO.setPurchaseContract(split[0]);
                iqcVO.setPurchaseHxm(split[1]);
                iqcVO.setMaterialId(item.getShortageMaterialId());
                iqcVO.setMaterialDesc(item.getShortageMaterialDesc());
                iqcVO.setPlanQuantity(item.getCheckQuantity());
                iqcVO.setJudgeResult(item.getDealResult());
                iqcVO.setIqcCharge(item.getQualityInspectors());
                iqcVO.setColor(calculateLightColor(item.getPlanDate()));
                iqcVO.setOrderNo(item.getOrderNo());
                iqcVO.setPlannedLaunchTime(item.getOnlineTime());
                if (StrUtil.isNotEmpty(item.getOnlineTime())){
                    LocalDate date = LocalDate.parse(item.getOnlineTime(), formatter);
                    LocalDate newDate = date.minusDays(3);
                    String result = newDate.format(formatter);
                    iqcVO.setLatestStorageTime(result);
                }
                iqcVO.setXsddhxm(item.getXsddhxm());
                iqcVO.setFactory(item.getFactory());
                iqcVO.setProcessStatusName(item.getProcessStatusName());
                iqcVO.setProcessStatus(item.getProcessStatus());
                if (StrUtil.isNotEmpty(iqcVO.getLatestStorageTime())){
                    LocalDate now = LocalDate.now();
                    long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(now ,LocalDate.parse(iqcVO.getLatestStorageTime()));
                    iqcVO.setDays(Math.abs(daysBetween));
                }
                iqcVOS.add(iqcVO);
            });
            IPage<WarningIncomeMaterialAtpAbnormalIqcVO> resultPage = new Page<>();
            resultPage.setRecords(iqcVOS);
            resultPage.setTotal(iqcPage.getTotal());
            resultPage.setPages(iqcPage.getPages());
            resultPage.setCurrent(iqcPage.getCurrent());
            resultPage.setSize(iqcPage.getSize());
            log.info("查到IQC对应数据条数={}", iqcVOS.size());
            return resultPage;
        }
        if (ViewSource.MC.equals(source)) {
            //PO角色
            log.info("进入MC分支={}", source);
            Page<WarningIncomeMaterialAtpAbnormalVO> page = new Page<>(params.getPage(), params.getPageSize());
            IPage<WarningIncomeMaterialAtpAbnormalVO> iqcPage = this.selectPageMc(page, "退货,报废", params);
            List<WarningIncomeMaterialAtpAbnormalVO> records = iqcPage.getRecords();
            List<WarningIncomeMaterialAtpAbnormalPoVO> poVOS = new ArrayList<>();
            records.forEach(item -> {
                WarningIncomeMaterialAtpAbnormalPoVO poVO = new WarningIncomeMaterialAtpAbnormalPoVO();
                poVO.setSupplierName(item.getSupplyName());
                String[] split = item.getPurchasePo().split("_");
                poVO.setPurchaseContract(split[0]);
                poVO.setPurchaseHxm(split[1]);
                poVO.setMaterialId(item.getShortageMaterialId());
                poVO.setMaterialDesc(item.getShortageMaterialDesc());
                poVO.setPlanQuantity(item.getCheckQuantity());
                poVO.setDeliveryProcurement(item.getPurchaser());
                poVO.setDealResult(item.getDealResult());
                poVO.setOrderNo(item.getOrderNo());
                poVO.setProcessStatusName(item.getProcessStatusName());
                poVO.setProcessStatus(item.getProcessStatus());
                //若导致3天内欠料为红灯；
                //若导致7天内欠料，为黄灯；否则不亮
                //当前日期-计划日期作为判断点
                poVO.setColor(calculateLightColor(item.getPlanDate()));
//                poVO.setOrderNo(item.getOrderNo());
                poVO.setNextEarliestDhsj(item.getNextArrivalDate());
                poVO.setDealResult(item.getDealResult());
                poVOS.add(poVO);
            });
            IPage<WarningIncomeMaterialAtpAbnormalPoVO> resultPage = new Page<>();
            resultPage.setRecords(poVOS);
            resultPage.setTotal(iqcPage.getTotal());
            resultPage.setPages(iqcPage.getPages());
            resultPage.setCurrent(iqcPage.getCurrent());
            resultPage.setSize(iqcPage.getSize());
            log.info("查到IQC对应数据条数={}", poVOS.size());
            return  resultPage;
        }
        return null;
    }
    // 计算灯色
    public static LightColor calculateLightColor(LocalDate planDate) {
        if (planDate == null) {
            return LightColor.BLANK;
        }
        LocalDate now = LocalDate.now();
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(now ,planDate);

        if (daysBetween <= 3 && daysBetween > 0) {
            // 小于3天，红灯
            return LightColor.RED;
        } else if (daysBetween > 3 && daysBetween <= 7) {
            // 3~7天内，黄灯
            return LightColor.YELLOW;
        } else {
            return LightColor.BLANK;
        }
    }

    @Override
    public IPage<WarningIncomeMaterialAtpAbnormalVO2> selectPagePc(Page<WarningIncomeMaterialAtpAbnormalVO2> page, WarningIncomeMaterialAtpAbnormalParams params) {
        IPage<WarningIncomeMaterialAtpAbnormalVO2> pageList = warningIncomeMaterialAtpAbnormalMapper.selectPage(page, params);
        if (CollectionUtils.isNotEmpty(pageList.getRecords())) {
            page.getRecords()
                    .forEach(e -> {
                        List<WarningIncomeMaterialPoAtpAbnormal> dataList = poAtpAbnormalService.lambdaQuery()
                                .eq(WarningIncomeMaterialPoAtpAbnormal::getOrderNo, e.getOrderNo())
                                .list();
                        Optional<LocalDate> min = dataList.stream()
                                .map(item -> {
                                    try {
                                        return LocalDate.parse(item.getSureReplanDate());
                                    } catch (Exception exception) {
                                        return null;
                                    }
                                })
                                .filter(date -> date != null)
                                .min(LocalDate::compareTo);
                         //min 在加一天
                        e.setExpectedCompletionDate(min.orElse(null).plusDays(1));
                        e.setSchedulingDate(e.getPlanDate());
                        e.setPlanQuantity(e.getPlanQuantity());
                        Optional.ofNullable(e.getProcessStatus()).ifPresent(s -> e.setProcessStatusName(s.getName()));
                    });
            affectsPlanService.buildAffectsPlan(page.getRecords(),WarningIncomeMaterialAtpAbnormalVO2::getExpectedCompletionDate);
        }

        return pageList;
    }

    @Override
    public IPage<WarningIncomeMaterialAtpAbnormalVO> selectPageIqc(Page<WarningIncomeMaterialAtpAbnormalVO> page, String dealResult, WarningIncomeMaterialAtpAbnormalParams params) {
        IPage<WarningIncomeMaterialAtpAbnormalVO> pageList = warningIncomeMaterialAtpAbnormalMapper.selectPageIqc(page, dealResult, params);
        if (CollectionUtils.isNotEmpty(pageList.getRecords())) {
            page.getRecords()
                    .forEach(e -> {
                        Optional.ofNullable(e.getProcessStatus()).ifPresent(s -> e.setProcessStatusName(s.getName()));
                    });
        }
        return pageList;
    }

    @Override
    public IPage<WarningIncomeMaterialAtpAbnormalVO> selectPageMc(Page<WarningIncomeMaterialAtpAbnormalVO> page, String dealResult, WarningIncomeMaterialAtpAbnormalParams params) {
        IPage<WarningIncomeMaterialAtpAbnormalVO> pageList = warningIncomeMaterialAtpAbnormalMapper.selectPageMc(page, dealResult, params);
        if (CollectionUtils.isNotEmpty(pageList.getRecords())) {
            page.getRecords()
                    .forEach(e -> {
                        Optional.ofNullable(e.getProcessStatus()).ifPresent(s -> e.setProcessStatusName(s.getName()));
                    });
        }
        return pageList;
    }
}
