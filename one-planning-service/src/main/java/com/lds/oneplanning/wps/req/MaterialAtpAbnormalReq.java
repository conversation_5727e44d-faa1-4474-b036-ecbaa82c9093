package com.lds.oneplanning.wps.req;

import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * 物料不齐异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daish<PERSON><PERSON>n</a>
 * @since 2025/5/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MaterialAtpAbnormalReq extends MaterialAtpAbnormalVO {
    private String factoryCodes;
    private List<String> factoryCodeList;

    private LocalDate startDate;
    private LocalDate endDate;
}
