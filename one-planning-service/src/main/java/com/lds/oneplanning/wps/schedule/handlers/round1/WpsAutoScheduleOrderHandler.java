package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.common.utils.OrderArithUtil;
import com.lds.oneplanning.mps.date.HalfWeekMap;
import com.lds.oneplanning.mps.date.WeekMap;
import com.lds.oneplanning.mps.date.YearWeekMap;
import com.lds.oneplanning.mps.utils.MpsDateUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsOrderPublishStatusEnum;
import com.lds.oneplanning.wps.schedule.enums.WpsOrderTypeEnum;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import com.lds.oneplanning.wps.service.facade.IWpsOrderPublishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * WPS自动排产
 * 1. 产线-产品，优先排序
 * 2. 循环生产线列表，根据订单的产品，捞取优先级最高的生产线列表
 * 3. 线体起始时间，结束时间，需要扣除休息日，再与排产周期做交集
 * 4. 订单预排产量：产线排产时长*排产比例*uph
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsAutoScheduleOrderHandler implements IWpsAutoScheduleHandler {

    /**
     * 允许超产比例
     */
    private static final float ALLOW_OVER_PRODUCTION_RATIO = 0.05F;

    @Autowired
    private IWpsOrderCommonService wpsOrderCommonService;

    @Autowired
    private IWpsOrderPublishService wpsOrderPublishService;

    @Override
    public void execute(WpsAutoScheduleContext context) {
        LocalDate currentDate = context.getCurrentDate();
        int weeksToPush = context.getWeeksToPush();
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        if (MapUtils.isEmpty(context.getOrderLineUphMap())) {
            return;
        }
        this.runWeekSchedule(currentDate, weeksToPush, context);
        log.info("WPS排产,自动排产逻辑,userId:{}, currentDate:{}, weeksToPush:{}, orderList size:{}, context:{}.",
                context.getUserId(), currentDate, weeksToPush, orderList.size(), JSON.toJSONString(context));
    }

    /**
     * 运行自然周排产
     *
     * @param localDate
     * @param weeksToPush
     * @param context
     */
    private void runWeekSchedule(LocalDate localDate, Integer weeksToPush, WpsAutoScheduleContext context) {
        Date currentDate = Date.from(localDate.atStartOfDay(MpsDateUtil.ZONE_ID).toInstant());
        YearWeekMap yearWeekMap = MpsDateUtil.getAllDatesFromCurrentDate(currentDate, weeksToPush);
        Map<Integer, WeekMap> yearMap = yearWeekMap.getYearMap();
        for (Map.Entry<Integer, WeekMap> yearEntry : yearMap.entrySet()) {
            WeekMap weekMap = yearEntry.getValue();
            for (Map.Entry<Integer, HalfWeekMap> weekEntry : weekMap.getWeekMap().entrySet()) {
                HalfWeekMap halfWeekMap = weekEntry.getValue();
                for (Map.Entry<Integer, List<Date>> halfWeekEntry : halfWeekMap.getHalfWeekMap().entrySet()) {
                    // 半周期排产
                    doHalfWeekSchedule(halfWeekEntry, context);
                }
            }
        }
    }

    /**
     * 处理半周排产
     *
     * @param halfWeekEntry
     * @param context
     */
    private void doHalfWeekSchedule(Map.Entry<Integer, List<Date>> halfWeekEntry, WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        List<Date> dates = halfWeekEntry.getValue();
        if (CollectionUtils.isEmpty(dates)) {
            return;
        }
        List<LocalDate> localDates = dates.stream().map(date -> date.toInstant()
                .atZone(MpsDateUtil.ZONE_ID).toLocalDate()).collect(Collectors.toList());
        context.getScheduleDates().addAll(localDates);
        // 处理已发布订单，已发布订单不参与自动排产
        wpsOrderPublishService.publish(context, localDates);
        // 处理订单
        Map<WpsOrderTypeEnum, List<WpsRowData>> wpsOrderTypeEnumListMap = categorizeOrders(orderList);
        wpsOrderTypeEnumListMap.forEach((type, orders) -> {
            if (CollectionUtils.isNotEmpty(orders)) {
                loopByProductPriority(orders, context, type, localDates);
            }
        });
    }

    private Map<WpsOrderTypeEnum, List<WpsRowData>> categorizeOrders(List<WpsRowData> orderList) {
        Map<WpsOrderTypeEnum, List<WpsRowData>> categorizedOrders = Maps.newHashMap();
        categorizedOrders.put(WpsOrderTypeEnum.FROZEN, Lists.newArrayList());
        categorizedOrders.put(WpsOrderTypeEnum.NORMAL, Lists.newArrayList());
        orderList.forEach(order -> {
            if (Optional.ofNullable(order.get_frozenStatus()).orElse(0) == 1) {
                categorizedOrders.get(WpsOrderTypeEnum.FROZEN).add(order);
            } else {
                categorizedOrders.get(WpsOrderTypeEnum.NORMAL).add(order);
            }
        });
        return categorizedOrders;
    }

    private void loopByProductPriority(List<WpsRowData> orders, WpsAutoScheduleContext context, WpsOrderTypeEnum wpsOrderTypeEnum, List<LocalDate> localDates) {
        if (WpsOrderTypeEnum.FROZEN.equals(wpsOrderTypeEnum)) {
            wpsOrderCommonService.doFrozenOrders(context, orders, localDates);
        } else {
            // 值越小，优先级越高
            int minPrioritySeq = WpsConstants.MIN_PRODUCT_GROUP_PRIORITY_SEQ;
            int maxPrioritySeq = WpsConstants.MAX_PRODUCT_GROUP_PRIORITY_SEQ;
            IntStream.rangeClosed(minPrioritySeq, maxPrioritySeq).forEach(prioritySeq -> {
                this.loopOrders(orders, context, wpsOrderTypeEnum, localDates, prioritySeq);
            });
        }
    }

    private void loopOrders(List<WpsRowData> orders, WpsAutoScheduleContext context, WpsOrderTypeEnum wpsOrderTypeEnum,
                            List<LocalDate> localDates, Integer prioritySeq) {
        for (WpsRowData order : orders) {
            // 订单开始排产日期
            LocalDate startProductPeriod = order.get_startProductPeriod();
            // 订单截止排产日期
            LocalDate endProductPeriod = order.get_endProductPeriod();
            // 待排产数量小于等于0，则跳过
            if (order.getWaitingOrderQty() <= 0 || null == endProductPeriod) {
                continue;
            }
            // 如果已发布订单，则跳过
            Integer publishStatus = order.get_publishStatus();
            if (null != publishStatus && publishStatus == WpsOrderPublishStatusEnum.PUBLISHED.getValue()) {
                log.info("WPS排产,orderNo:{},已发布.", order.getOrderNo());
                continue;
            }
            if (WpsOrderTypeEnum.NORMAL.equals(wpsOrderTypeEnum) && startProductPeriod.isAfter(localDates.get(localDates.size() - 1))) {
                continue;
            }
            // 计算预排产的日期列表
            List<LocalDate> scheduleDates = localDates.stream()
                    .filter(date -> !date.isBefore(startProductPeriod) && !date.isAfter(endProductPeriod))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(scheduleDates)) {
                continue;
            }
            doOrderSchedule(context, order, prioritySeq, scheduleDates);
        }
    }

    private void doOrderSchedule(WpsAutoScheduleContext context, WpsRowData order, Integer prioritySeq, List<LocalDate> scheduleDates) {
        String orderNo = order.getOrderNo();
        Map<Integer, Set<String>> orderLinePriorityMap = context.getOrderLinePriorityMap().get(orderNo);
        if (MapUtils.isEmpty(orderLinePriorityMap)) {
            return;
        }
        Set<String> suitableLineUuids = orderLinePriorityMap.get(prioritySeq);
        if (CollectionUtils.isEmpty(suitableLineUuids)) {
            return;
        }
        log.info("WPS排产,prioritySeq:{},orderNo:{},suitableLineUuids:{}.", prioritySeq, orderNo, suitableLineUuids);
        // 订单->产线UUID->uph映射
        Map<String, Map<String, Float>> orderLineUphMap = context.getOrderLineUphMap();
        Map<String, Float> uphMap = orderLineUphMap.get(orderNo);
        if (MapUtils.isEmpty(uphMap)) {
            return;
        }
        suitableLineUuids.forEach(lineUuid -> {
            Float uph = uphMap.get(lineUuid);
            if (null == uph) {
                return;
            }
            // 订单待排产时长
            Map<LocalDate, WpsProductionLine> dailyProductionLineMap = context.getDailyProductionLineMap().get(lineUuid);
            if (MapUtils.isEmpty(dailyProductionLineMap)) {
                return;
            }
            dailyProductionLineMap.forEach((date, productionLine) -> {
                LocalDate currentDate = context.getCurrentDate();
                if (date.isBefore(currentDate)) {
                    return;
                }
                if (!scheduleDates.contains(date)) {
                    return;
                }
                int waitingOrderQty = order.getWaitingOrderQty();
                float waitingLineHour = productionLine.getWaitingScheduleHours();
                if (waitingOrderQty == 0 || waitingLineHour == 0F) {
                    return;
                }
                int scheduledQty;
                // 订单待排产数量
                float waitingOrderHour = OrderArithUtil.floatDivide(waitingOrderQty, uph);
                // 订单待排产时长大于等于线体排产时长
                if (waitingOrderHour >= waitingLineHour) {
                    productionLine.setWaitingScheduleHours(0F);
                    // 当天排完后剩余订单量不超过该线体生产时长的10%, 则把剩余的数量放在当天完成
                    if (OrderArithUtil.floatSubtract(waitingOrderHour, waitingLineHour) <=
                            OrderArithUtil.floatMultiply(waitingLineHour, ALLOW_OVER_PRODUCTION_RATIO)) {
                        scheduledQty = waitingOrderQty;
                        productionLine.setScheduledHours(OrderArithUtil.floatAdd(productionLine.getScheduledHours(), waitingOrderHour));
                    } else {
                        scheduledQty = (int) Math.ceil(waitingLineHour * uph);
                        productionLine.setScheduledHours(OrderArithUtil.floatAdd(productionLine.getScheduledHours(), waitingLineHour));
                    }
                    updateScheduledHours(productionLine, orderNo, waitingLineHour);
                } else {
                    scheduledQty = waitingOrderQty;
                    productionLine.setWaitingScheduleHours(OrderArithUtil.floatSubtract(waitingLineHour, waitingOrderHour));
                    productionLine.setScheduledHours(OrderArithUtil.floatAdd(productionLine.getScheduledHours(), waitingOrderHour));
                    updateScheduledHours(productionLine, orderNo, waitingOrderHour);
                }
                order.setWaitingOrderQty(waitingOrderQty - scheduledQty);
                log.info("WPS排产,doOrderSchedule,orderNo:{},date:{},lineUuid:{},uph:{},waitingLineHour:{}," +
                                "waitingOrderHour:{},waitingOrderQty:{},scheduledQty:{}.",
                        orderNo, date, lineUuid, uph, waitingLineHour, waitingOrderHour, waitingOrderQty, scheduledQty);
                wpsOrderCommonService.updateDailyScheduleData(context, orderNo, date, lineUuid, scheduledQty);
            });
        });
    }

    private void updateScheduledHours(WpsProductionLine productionLine, String orderNo, float hours) {
        productionLine.getOrderScheduledHoursMap()
                .merge(orderNo, hours, Float::sum);
    }

    @Override
    public int getOrder() {
        return 7;
    }

    @Override
    public int getRound() {
        return 1;
    }
}