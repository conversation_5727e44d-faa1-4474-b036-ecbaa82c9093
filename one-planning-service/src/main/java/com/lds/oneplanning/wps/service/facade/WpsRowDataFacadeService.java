package com.lds.oneplanning.wps.service.facade;

import com.google.common.collect.Maps;
import com.lds.oneplanning.mps.model.RowSaveData;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessEnum;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/8 16:53
 */
public interface WpsRowDataFacadeService {

    List<WpsRowData> customListOrder(List<WpsOrderProcessEnum> processEnums, Long userId, LocalDate startTime, LocalDate endTime, String factoryCode, boolean cacheFlag, Map<String,Object> params);
    /**
     * api行数据，没有填充排产数据
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    List<WpsRowData> listUserApiRowData(Long userId,Date startTime,Date endTime,String factoryCode);
    List<WpsRowData> sortWpsRowData(List<WpsRowData> mpsRowDatas,Integer datasource);

    void saveRowData(List<RowSaveData> mpsRowDatas, Date date,Long userId);

    List<WpsRowData> listRowDataByDate(Long userId, LocalDate startDate, LocalDate endDate, List<String> factoryCodes);

    void saveData(List<WpsRowData> wpsRowDatas, Long userId);

    void changeStartScheduleDate(@NotNull String orderNo, @NotNull LocalDate scheduleDate);
}
