package com.lds.oneplanning.wps.helper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.coral.common.util.date.DateUtil;
import com.lds.oneplanning.basedata.service.ILineChangeCfgService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.common.utils.OrderArithUtil;
import com.lds.oneplanning.wps.enums.ScheduleDirectionEnum;
import com.lds.oneplanning.wps.enums.SchedulePlanErrorCodeEnum;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单自动排产辅助类
 */
@Slf4j
@Component
public class WpsAutoScheduleOrderHelper {

    @Autowired
    ILineChangeCfgService lineChangeCfgService;

    @Autowired
    WpsSchedulePlanLogHelper wpsSchedulePlanLogHelper;

    /**
     * 允许超产比例
     */
    private static final float ALLOW_OVER_PRODUCTION_RATIO = 0.05F;

    /**
     * 获取有效的排产时间
     * @param wpsRowData
     * @param dailyProductionLineMap
     * @return
     */
    public List<LocalDate> getScheduleDate(WpsRowData wpsRowData,String lineUuid,Map<LocalDate, WpsProductionLine> dailyProductionLineMap){
        //设置排产区间
        setScheduleRange(wpsRowData,dailyProductionLineMap);
        List<LocalDate> scheduleDates =  getScheduleDate(wpsRowData);
        List<LocalDate> dailyProductionLineDates =  dailyProductionLineMap.entrySet().stream().map(data->data.getKey()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(dailyProductionLineDates)){
            return Lists.newArrayList();
        }
        if(wpsRowData.get_startProductPeriod().compareTo(wpsRowData.get_endProductPeriod())>0){
            log.info("排产开始时间大于结束时间，orderNo:{},_startProductPeriod：{}，_endProductPeriod：{}",wpsRowData.getOrderNo(),wpsRowData.get_startProductPeriod(),wpsRowData.get_endProductPeriod());
            return Lists.newArrayList();
        }
        log.info("getScheduleDate->订单Id:{},排产时间：{}，lineUuid:{}",wpsRowData.getOrderNo(),scheduleDates,lineUuid);
        scheduleDates.removeIf(date -> !dailyProductionLineDates.contains(date));
        log.info("getScheduleDate->订单Id:{},有效排查时间：{}，lineUuid:{}",wpsRowData.getOrderNo(),scheduleDates,lineUuid);
        return scheduleDates;
    }

    /**
     * 获取订单可排产的线体
     * @return
     */
    public  String getScheduleLine(Set<String> suitableLineUuids,WpsRowData order,WpsAutoScheduleContext context, Map<String, Float> uphMap){
        int waitingOrderQty = order.getWaitingOrderQty();
        String returnLineUuid=null;
        String firstLineUuid = null;
        Boolean isFirst=false;
        for(String lineUuid:suitableLineUuids){
            //线体为空，跳过
            Float uph = uphMap.get(lineUuid);
            if (null == uph) {
                log.info("getScheduleLine获取uph为空，orderNo:{},lineUUID:{}",order.getOrderNo(),lineUuid);
                context.getSchedulePlanLogMap().putAll(wpsSchedulePlanLogHelper.createWpsSchedulePlanLog(order, lineUuid, SchedulePlanErrorCodeEnum.LINE_UPH_NOT_EXIST,context));
                continue;
            }
            // 订单待排产时长
            Map<LocalDate, WpsProductionLine> dailyProductionLineMap = context.getDailyProductionLineMap().get(lineUuid);
            if (MapUtils.isEmpty(dailyProductionLineMap)) {
                log.info("getScheduleLine获取WpsProductionLine为空，orderNo:{},lineUUID:{}",order.getOrderNo(),lineUuid);
                context.getSchedulePlanLogMap().putAll(wpsSchedulePlanLogHelper.createWpsSchedulePlanLog(order, lineUuid, SchedulePlanErrorCodeEnum.LINE_CAPACITY_NOT_EXIST,context));
                continue;
            }
            //获取有效的排产日期
            List<LocalDate> scheduleDates =  getScheduleDate(order,lineUuid,dailyProductionLineMap);
            if(CollectionUtils.isEmpty(scheduleDates)){
                continue;
            }
            //默认未匹配到线体的情况下，直接放在第一个线体
            if(!isFirst){
                firstLineUuid = lineUuid;
                isFirst=true;
            }
            // 订单待排产小时
            float waitingOrderHour = OrderArithUtil.floatDivide(waitingOrderQty, uph);
            //线体待排产小时
            float waitingLineHour =dailyProductionLineMap.entrySet().stream().filter(data -> scheduleDates.contains(data.getKey()))
                    .map(Map.Entry::getValue).map(WpsProductionLine::getWaitingScheduleHours).filter(Objects::nonNull).reduce(0.0f,Float::sum);
            //计划时长
            float planningHour = dailyProductionLineMap.entrySet().stream().filter(data -> scheduleDates.contains(data.getKey()))
                    .map(Map.Entry::getValue).map(WpsProductionLine::getPlanScheduleHours).filter(Objects::nonNull).reduce(0.0f,Float::sum);
            log.info("getScheduleLine计算线体是否订单排产：orderNo:{},lineUuid:{},waitingOrderHour:{},waitingLineHour:{},planningHour:{},waitingOrderQty:{},uph:{}",order.getOrderNo(),lineUuid,waitingOrderHour,waitingLineHour,planningHour,waitingOrderQty,uph);
            if(waitingLineHour>=waitingOrderHour){
                returnLineUuid = lineUuid;
                break;
            }
        }
        returnLineUuid=StringUtils.isNotBlank(returnLineUuid)?returnLineUuid:firstLineUuid;
        log.info("getScheduleLine订单排产到该线体：orderNo:{},lineUuid:{}，firstLineUuid:{}",order.getOrderNo(),returnLineUuid,firstLineUuid);
        return returnLineUuid;
    }

    /**
     * 设置排产区间范围
     * @param wpsRowData
     * @param dailyProductionLineMap
     */
    private void setScheduleRange(WpsRowData wpsRowData,Map<LocalDate, WpsProductionLine> dailyProductionLineMap){
        //增加排产时间必须在上线时间之后，如果是临界点是休息日需要去掉休息日
        wpsRowData.set_startProductPeriodTemp(wpsRowData.get_startProductPeriod());
        wpsRowData.set_endProductPeriodTemp(wpsRowData.get_endProductPeriod());
        LocalDate productStartLocalDate = Optional.ofNullable(LocalDateTimeUtil.dateToLocalDate(wpsRowData.getProductStartTime())).orElse(wpsRowData.get_startProductPeriod());
        if(productStartLocalDate.isAfter(wpsRowData.get_startProductPeriod()) && productStartLocalDate.compareTo(wpsRowData.get_endProductPeriod())<=0){
            wpsRowData.set_startProductPeriodTemp(productStartLocalDate);
            wpsRowData.set_endProductPeriodTemp(wpsRowData.get_endProductPeriod());
            log.info("设置排产可排的时间区间，orderNo:{},原来可排区间：{}--{} 现在可排区间：{}--{}",wpsRowData.getOrderNo(),wpsRowData.get_startProductPeriod(),wpsRowData.get_endProductPeriod(),wpsRowData.get_startProductPeriodTemp(),wpsRowData.get_endProductPeriodTemp());
        }else if(productStartLocalDate.isAfter(wpsRowData.get_endProductPeriod())){
           LocalDate endDate = wpsRowData.get_endProductPeriod();
           while (true){
               if(endDate.isBefore(wpsRowData.get_startProductPeriod())){
                   break;
               }
               if(endDate.compareTo(wpsRowData.get_startProductPeriod())>=0){
                  if(dailyProductionLineMap.containsKey(endDate)){
                      wpsRowData.set_startProductPeriodTemp(endDate);
                      wpsRowData.set_endProductPeriodTemp(endDate);
                      log.info("设置排产可排的时间区间，orderNo:{},原来区间：{}--{} 现在：{}--{}",wpsRowData.getOrderNo(),wpsRowData.get_startProductPeriod(),wpsRowData.get_endProductPeriod(),endDate,endDate);
                      break;
                  }
               }
               endDate = endDate.plusDays(-1);
           }
        }
    }
    /**
     * 线体换产品需要增加换线时长
     * @param lineUuid
     * @param scheduleDate
     * @param wpsRowData
     * @param context
     * @return
     */
    public float lineChangeProductGroupHour(String lineUuid,LocalDate scheduleDate,WpsRowData wpsRowData,WpsAutoScheduleContext context){
         //初始化线体，换线时长为0
         context.getLineProductGroupMap().putIfAbsent(lineUuid,new LinkedHashMap<LocalDate,String>());
         Map<LocalDate,String> lineProductGroupMap =  context.getLineProductGroupMap().get(lineUuid);
         if(!lineProductGroupMap.containsKey(scheduleDate)){
             return 0f;
         }
         //生产的产品组不变的情况下，不需要增加换线时长
         if(Objects.equals(lineProductGroupMap.get(scheduleDate),wpsRowData.get_productGroupCode())){
           return 0f;
         }
         //线体出现换产品组生产时，需要增加换线时长
         return context.getChangeLineHour();
    }
    /**
     * 设置满单时，需要填充的日期
     *    * 1-7逆向1的算法
     *      * 1、开始时间1号，结束时间8号， 排产时间6、5、4、3、2、1、7、8   最后排不完的全部放到1号
     *      * 2、开始时间1号，结束时间6号 ，排产时间4、3、2、1、5、6        最后排不完的全部放到1号
     *      * 3、开始时间6号，结束时间6号， 排产时间6                    最后排不完的全部放到6号
     *      * 4、开始时间5号，结束时间6号， 排产时间5、6                 最后排不完的全部放到5号
     *      * 5、开始时间4号，结束时间6号， 排产时间4、5、6                最后排不完的全部放到4号
     *      * 6、开始时间3号，结束时间6号， 排产时间4、3、5、6              最后排不完的全部放到3号
     *      * 7、开始时间2号，结束时间6号， 排产时间4、3、2、5、6           最后排不完的全部放到2号
     *     正向填充最后一天
     *     逆向1填充日期按上面规则
     *     逆向2填充最后一天
     * @param wpsRowData
     * @return
     */
    public LocalDate getFillOrderDate(WpsRowData wpsRowData){
        if(CollectionUtils.isEmpty(wpsRowData.getScheduleDates())){
           return null;
        }
        List<LocalDate> localDates = wpsRowData.getScheduleDates();
        //正向、逆向2
        if(wpsRowData.getScheduleDirection().equals(ScheduleDirectionEnum.Forward.getCode())||  wpsRowData.getScheduleDirection().equals(ScheduleDirectionEnum.Backward2.getCode())){
            return localDates.get(localDates.size()-1);
        }else if(wpsRowData.getScheduleDirection().equals(ScheduleDirectionEnum.Backward.getCode())){
            //逆1填充日期
            if(localDates.size()>=3){
                return localDates.get(localDates.size()-3);
            }else if(localDates.size()==2){
                return localDates.get(localDates.size()-2);
            }else{
                return localDates.get(localDates.size()-1);
            }
        }
        log.info("订单未设置排产算法orderNO:{}",wpsRowData.getOrderNo());
        return null;
    }
    /**
     * 获取订单的排产时间（上层业务传进来的结束时间已经减去2,以下算法都要加2）
     * 正向排产：顺序排产从N-7 排到N-2，最后剩余的堆在N-2
     * 逆向排产1：从N-4排产到N-7，再排N-3 ~ N-2，最后剩余的堆在N-7
     * 逆向排产2(提供给前置组件使用)：从N-2排到N-7，最后剩余的堆在N-7
     *
     * @param wpsRowData
     * @return
     */
    public List<LocalDate> getScheduleDate(WpsRowData wpsRowData) {
        LocalDate startLocalDate = wpsRowData.get_startProductPeriodTemp();
        LocalDate endLocalDate = wpsRowData.get_endProductPeriodTemp();
        List<LocalDate> allLocalDates = Lists.newArrayList();
        if (ScheduleDirectionEnum.Forward.getCode().equals(wpsRowData.getScheduleDirection())) {
            allLocalDates = getBetweenDateList(startLocalDate, endLocalDate);
        } else if (ScheduleDirectionEnum.Backward.getCode().equals(wpsRowData.getScheduleDirection())) {
            //超过N-5天取开始时间，避免有些天数没有被排到 如：开始时间1号，结束时间8号， 排产时间6、5、4、3、1、2、7、8（避免1,2没有排）
            LocalDate startCondition = endLocalDate.plusDays(-5);
            LocalDate endCondition = endLocalDate.plusDays(-2);
            startCondition = startCondition.compareTo(startLocalDate) > 0 ? startLocalDate : startCondition;
            //逆向排产
            while (startCondition.compareTo(endCondition) <= 0) {
                if (startCondition.compareTo(startLocalDate) < 0) {
                    startCondition = startLocalDate;
                    continue;
                }
                allLocalDates.add(endCondition);
                endCondition = endCondition.plusDays(-1);
            }
            //再正向排
            startCondition = endLocalDate.plusDays(-1);
            while (startCondition.compareTo(endLocalDate) <= 0) {
                if (startCondition.compareTo(startLocalDate) < 0) {
                    startCondition = startCondition.plusDays(1);
                    continue;
                }
                allLocalDates.add(startCondition);
                startCondition = startCondition.plusDays(1);
            }
        } else if (ScheduleDirectionEnum.Backward2.getCode().equals(wpsRowData.getScheduleDirection())) {
            allLocalDates = getBetweenDateList(startLocalDate, endLocalDate);
            Collections.reverse(allLocalDates);
        }
        return allLocalDates;
    }


    public List<LocalDate> getBetweenDateList(LocalDate startDate, LocalDate endDate){
        List<LocalDate> dateList = new ArrayList<>();
        while (startDate.compareTo(endDate)<=0){
            dateList.add(startDate);
            startDate = startDate.plusDays(1);
        }
        return dateList;
    }
    /**
     * 设置满单时，需要填充的日期
     *
     * @param allLocalDateMap
     * @param fullOrderFillDate
     */
    private void setFullOrderFillDate(Map<LocalDate, Boolean> allLocalDateMap, LocalDate fullOrderFillDate) {
        for (LocalDate date : allLocalDateMap.keySet()) {
            if (Objects.equals(date, fullOrderFillDate)) {
                allLocalDateMap.put(date, true);
            }
        }
    }

    public static void main(String[] args) {
        WpsRowData wpsRowData = new WpsRowData();
        wpsRowData.setScheduleDirection(1);
        wpsRowData.set_startProductPeriod(LocalDate.of(2025, 6, 9));
        wpsRowData.set_endProductPeriod(LocalDate.of(2025, 6, 15));
        wpsRowData.setProductStartTime(DateUtil.asDate(LocalDate.of(2025, 9, 15)));
        WpsAutoScheduleOrderHelper wpsAutoScheduleOrderHelper = new WpsAutoScheduleOrderHelper();
//        System.out.println(wpsAutoScheduleOrderHelper.getScheduleDate(wpsRowData));
        Map<LocalDate, WpsProductionLine> dailyProductionLineMap = Maps.newHashMap();
        dailyProductionLineMap.put(LocalDate.of(2025, 6, 14),new WpsProductionLine());
        dailyProductionLineMap.put(LocalDate.of(2025, 6, 13),new WpsProductionLine());
       System.out.println(wpsAutoScheduleOrderHelper.getScheduleDate(wpsRowData,"11111",dailyProductionLineMap)) ;

    }
}
