package com.lds.oneplanning.wps.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EsbMainPoOrder {
    /**
     * 采购订单号
     */
    @JSONField(name = "EBELN")
    @JsonProperty("EBELN")
    private String ebeln;
    /**
     * 删除标识符
     */
    @JSONField(name = "LOEKZ")
    @JsonProperty("LOEKZ")
    private String loekz;
    /**
     * 公司代码
     */
    @JSONField(name = "BUKRS")
    @JsonProperty("BUKRS")
    private String bukrs;
    /**
     * 采购凭证类别
     */
    @JSONField(name = "BSTYP")
    @JsonProperty("BSTYP")
    private String bstyp;
    /**
     * 采购凭证类型
     */
    @JSONField(name = "BSART")
    @JsonProperty("BSART")
    private String bsart;
    /**
     * 供应商科目编号
     */
    @JSONField(name = "LIFNR")
    @JsonProperty("LIFNR")
    private String supplierCode;

    /**
     * 付款条件代码
     */
    @JSONField(name = "ZTERM")
    @JsonProperty("ZTERM")
    private String zterm;
    /**
     * 采购组织
     */
    @JSONField(name = "EKORG")
    @JsonProperty("EKORG")
    private String org;
    /**
     * 采购组
     */
    @JSONField(name = "EKGRP")
    @JsonProperty("EKGRP")
    private String poGroup;
    /**
     * 采购组的描述
     */
    @JSONField(name = "EKNAM")
    @JsonProperty("EKNAM")
    private String poGroupName;
    /**
     * 采购订单日期
     */
    @JSONField(name = "BEDAT")
    @JsonProperty("BEDAT")
    private String orderDate;
    /**
     * 货币码
     */
    @JSONField(name = "WAERS")
    @JsonProperty("WAERS")
    private String waers;
}
