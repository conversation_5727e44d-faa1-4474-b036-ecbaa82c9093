package com.lds.oneplanning.wps.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交期异常影响类型枚举
 */
@Getter
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public enum DeliveryDateAbnormalImpactType {
    /*影响出货/影响整灯计划/影响上线/影响组件&部件上线*/
    AFFECTS_SHIPMENT("影响出货", LightColor.RED),
    AFFECTS_WHOLE_LIGHT_PLAN("影响整灯计划", LightColor.YELLOW),
    AFFECTS_ONLINE("影响上线", LightColor.RED),
    AFFECTS_COMPONENT_AND_PART_ONLINE("影响组件&部件上线", LightColor.YELLOW);

    private final String name;
    private final LightColor lightColor;
}
