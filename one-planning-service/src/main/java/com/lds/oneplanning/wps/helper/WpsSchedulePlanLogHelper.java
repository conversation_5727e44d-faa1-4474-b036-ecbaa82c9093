package com.lds.oneplanning.wps.helper;

import com.google.common.collect.Maps;
import com.lds.oneplanning.wps.entity.WpsSchedulePlanLog;
import com.lds.oneplanning.wps.enums.SchedulePlanErrorCodeEnum;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class WpsSchedulePlanLogHelper {
    /**
     * 创建自动排产的错误日志
     *
     * @param wpsRowData
     * @param lineUuid
     * @param schedulePlanErrorCodeEnum
     * @return
     */
    public Map<String,WpsSchedulePlanLog> createWpsSchedulePlanLog(WpsRowData wpsRowData, String lineUuid, SchedulePlanErrorCodeEnum schedulePlanErrorCodeEnum, WpsAutoScheduleContext context) {
        Map<String,WpsSchedulePlanLog> schedulePlanLogMap = Maps.newHashMap();
        WpsSchedulePlanLog wpsSchedulePlanLog = new WpsSchedulePlanLog();
        wpsSchedulePlanLog.setFactoryCode(wpsRowData.getFactory());
        wpsSchedulePlanLog.setOrderType(wpsRowData.getOrderType());
        wpsSchedulePlanLog.setSellOrderNo(wpsRowData.getSellOrderNo());
        wpsSchedulePlanLog.setOrderNo(wpsRowData.getOrderNo());
        wpsSchedulePlanLog.setRowItem(wpsRowData.getRowItem());
        wpsSchedulePlanLog.setProductId(wpsRowData.getProductId());
        wpsSchedulePlanLog.setCommodityId(wpsRowData.getCommodityId());
        wpsSchedulePlanLog.setErrorCode(schedulePlanErrorCodeEnum.getCode());
        lineUuid = StringUtils.isNotBlank(lineUuid) ? lineUuid : "";
        String lineCode = context.getLineCodeMap().containsKey(lineUuid)?  context.getLineCodeMap().get(lineUuid) : "";
        String productGroupCode = StringUtils.isNotBlank(wpsRowData.getProductGroupCode()) ? wpsRowData.getProductGroupCode() : "";
        String customerCode = StringUtils.isNotBlank(wpsRowData.getCustomerCode()) ? wpsRowData.getCustomerCode() : "";
        wpsSchedulePlanLog.setRemark(schedulePlanErrorCodeEnum.getName() + "，线体ID:" + lineUuid + ",线体编码:"+lineCode+",产品组编码:" + productGroupCode+",客户编码:"+customerCode);
        schedulePlanLogMap.put(wpsRowData.getOrderNo()+"-"+schedulePlanErrorCodeEnum.getCode(),wpsSchedulePlanLog);
        return schedulePlanLogMap;
    }

}
