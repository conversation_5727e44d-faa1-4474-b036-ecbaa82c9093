package com.lds.oneplanning.wps.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class EsbPoData {
    @JSONField(name = "IT_DATA")
    @JsonProperty("IT_DATA")
    private List<Detail> ItData;
    @JSONField(name = "OT_DATA")
    @JsonProperty("OT_DATA")
    private List<Detail> otData;
    @JSONField(name = "IT_DATA2")
    @JsonProperty("IT_DATA2")
    private List<EsbPoParams.MaterialInfo> materialInfoList;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Detail {

        /**
         * 采购订单号
         */
        @JSONField(name = "EBELN")
        @JsonProperty("EBELN")
        private String poNo;
        /**
         * 采购订单行项目
         */
        @JSONField(name = "EBELP")
        @JsonProperty("EBELP")
        private String poRowItem;
        /**
         * 采购订单数量
         */
        @JSONField(name = "MENGE")
        @JsonProperty("MENGE")
        private String poQty;
        /**
         * 收货数量
         */
        @JSONField(name = "WEMNG")
        @JsonProperty("WEMNG")
        private String receivedQty;
        /**
         * 未收数量
         */
        @JSONField(name = "ZWSSL")
        @JsonProperty("ZWSSL")
        private String unReceivedQty;
        private int unReceivedQtyNum;
        /**
         * 原采购交期
         */
        @JSONField(name = "EKETT")
        @JsonProperty("EKETT")
        private String deliveryDate;
        /**
         * 最新采购交期
         */
        @JSONField(name = "EKEST")
        @JsonProperty("EKEST")
        private String confirmedDeliveryDate;
        /**
         * 物料编号
         */
        @JSONField(name = "MATNR")
        @JsonProperty("MATNR")
        private String materialId;
        /**
         * 工厂
         */
        @JSONField(name = "WERKS")
        @JsonProperty("WERKS")
        private String factoryCode;
        /**
         * 供应商名称
         */
        @JSONField(name = "NAME1")
        @JsonProperty("NAME1")
        private String supplierName;
        /**
         * 采购组
         */
        @JSONField(name = "EKGRP")
        @JsonProperty("EKGRP")
        private String poGroup;
        /**
         * 采购组的描述
         */
        @JSONField(name = "EKNAM")
        @JsonProperty("EKNAM")
        private String poGroupDesc;
        /**
         * 供应商科目编号
         */
        @JSONField(name = "LIFNR")
        @JsonProperty("LIFNR")
        private String supplierCode;
    }


}
