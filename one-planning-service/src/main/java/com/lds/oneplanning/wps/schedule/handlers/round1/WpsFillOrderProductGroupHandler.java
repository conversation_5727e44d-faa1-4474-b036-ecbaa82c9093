package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.entity.ProductGroup;
import com.lds.oneplanning.basedata.entity.ProductGroupRel;
import com.lds.oneplanning.basedata.service.IProductGroupRelService;
import com.lds.oneplanning.basedata.service.IProductGroupService;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单填充产品组信息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsFillOrderProductGroupHandler implements IWpsAutoScheduleHandler {

    @Autowired
    private IProductGroupService productGroupService;

    @Autowired
    private IProductGroupRelService productGroupRelService;

    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        String currentFactoryCode = context.getCurrentFactoryCode();
        if (StringUtils.isEmpty(currentFactoryCode) || CollectionUtils.isEmpty(orderList)) {
            return;
        }
        Set<String> productIds = getProductIds(orderList);
        Map<String, ProductGroup> productGroupByProductIdMap = getProductGroupByProductIds(currentFactoryCode, productIds);
        if (MapUtils.isEmpty(productGroupByProductIdMap)) {
            return;
        }
        this.fillOrderProductGroup(orderList, productGroupByProductIdMap);
    }

    private Set<String> getProductIds(List<WpsRowData> orderList) {
        return orderList.stream()
                .flatMap(order -> Stream.of(order.getProductId(), order.getCommodityId()))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
    }

    private void fillOrderProductGroup(List<WpsRowData> orderList, Map<String, ProductGroup> productGroupByProductIdMap) {
        orderList.forEach(rowData -> {
            String productId = rowData.getProductId();
            String commodityId = rowData.getCommodityId();
            // 优先使用商品ID获取产品组信息
            ProductGroup productGroup = getProductGroup(productGroupByProductIdMap, commodityId);
            if (null != productGroup) {
                setProductGroupData(rowData, productGroup);
            } else {
                productGroup = getProductGroup(productGroupByProductIdMap, productId);
                if (null != productGroup) {
                    setProductGroupData(rowData, productGroup);
                }
            }
        });
    }

    private ProductGroup getProductGroup(Map<String, ProductGroup> productGroupByProductIdMap, String id) {
        return StringUtils.isNotEmpty(id) ? productGroupByProductIdMap.get(id) : null;
    }

    private void setProductGroupData(WpsRowData rowData, ProductGroup productGroup) {
        rowData.set_productGroupCode(productGroup.getCode());
        rowData.set_productGroupName(productGroup.getName());
    }

    private Map<String, ProductGroup> getProductGroupByProductIds(String factoryCode, Set<String> productIds) {
        Map<String, ProductGroup> productGroupByProductIdMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(productIds)) {
            return productGroupByProductIdMap;
        }
        log.info("WPS排产,获取订单中所有的产品ID: {}.", productIds);
        List<ProductGroupRel> productGroupRels = productGroupRelService.listByProductIds(productIds);
        List<String> productGroupCodes = productGroupRels.stream()
                .map(ProductGroupRel::getProductGroupCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productGroupCodes)) {
            return productGroupByProductIdMap;
        }
        List<ProductGroup> productGroups = productGroupService.listByFactoryCodeAndCodes(factoryCode, productGroupCodes);
        if (CollectionUtils.isEmpty(productGroups)) {
            return productGroupByProductIdMap;
        }
        // <产品组编码, 产品组>
        Map<String, ProductGroup> productGroupMap = productGroups.stream()
                .collect(Collectors.toMap(ProductGroup::getCode, productGroup -> productGroup, (existing, replacement) -> existing));
        productGroupRels.forEach(productGroupRel -> {
            String productId = productGroupRel.getProductId();
            String productGroupCode = productGroupRel.getProductGroupCode();
            ProductGroup productGroup = productGroupMap.get(productGroupCode);
            if (productGroup != null) {
                productGroupByProductIdMap.put(productId, productGroup);
            }
        });
        return productGroupByProductIdMap;
    }

    @Override
    public int getOrder() {
        return 2;
    }

    @Override
    public int getRound() {
        return 1;
    }
}