package com.lds.oneplanning.wps.workbench.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class PackingNoPrintResp implements Serializable {

    private static final long serialVersionUID = 2533591261724714015L;

    /**
     * 客户
     */
    private String customer;

    /**
     * 工厂
     */
    private String factory;

    /**
     * 销售订单号
     */
    private String saleOrderNo;

    /**
     * 行项目号
     */
    private String lineItemNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单数量
     */
    private Integer orderQuantity;

    /**
     * 最早上线日期
     */
    private LocalDate earlyOnlineTime;


    /**
     * 版面维护描述
     */
    private String printDesc;

    /**
     * 处理状态
     */
    private Integer processStatus;
}