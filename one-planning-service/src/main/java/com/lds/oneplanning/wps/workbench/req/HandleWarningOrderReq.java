package com.lds.oneplanning.wps.workbench.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class HandleWarningOrderReq implements Serializable {

    private static final long serialVersionUID = 3399797609514443812L;

    @NotEmpty
    private String orderNo;

    @NotEmpty
    private String warningType;

    /**
     * 处理状态:1-未处理，2-已处理，3-已关闭
     */
    @NotNull
    private Integer handleStatus;

    /**
     * 处理备注
     */
    private String handleContent;
}
