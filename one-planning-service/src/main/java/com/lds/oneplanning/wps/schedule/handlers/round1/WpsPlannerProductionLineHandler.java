package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询计划员的排产产线
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsPlannerProductionLineHandler implements IWpsAutoScheduleHandler {

    @Autowired
    private IWpsOrderCommonService wpsOrderCommonService;

    @Override
    public void execute(WpsAutoScheduleContext context) {
        Long userId = context.getUserId();
        List<LineInfo> lineInfos = wpsOrderCommonService.getLineInfosByUserId(userId);
        if (CollectionUtils.isEmpty(lineInfos)) {
            return;
        }
        List<String> lineUuids = lineInfos.stream()
                .map(LineInfo::getLineUuid).distinct().collect(Collectors.toList());
        context.setLineUuids(lineUuids);
        // 设置虚拟线体
        setVirtualLineUuid(context, lineInfos);
        //设置线体对应的线体编码
        context.setLineCodeMap(lineInfos.stream().collect(Collectors.toMap(LineInfo::getLineUuid, LineInfo::getCode,(k1,k2)->k1)));
        log.info("WPS排产,订单可匹配的产线列表, userId:{}, lineUuids:{}.", userId, lineUuids);
    }

    private void setVirtualLineUuid(WpsAutoScheduleContext context, List<LineInfo> lineInfos) {
        List<LineInfo> virtualLineInfos = lineInfos.stream()
                .filter(lineInfo -> lineInfo.getVirtualStatus() == 1)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(virtualLineInfos)) {
            context.setVirtualLineUuid(virtualLineInfos.get(0).getLineUuid());
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }

    @Override
    public int getRound() {
        return 1;
    }
}