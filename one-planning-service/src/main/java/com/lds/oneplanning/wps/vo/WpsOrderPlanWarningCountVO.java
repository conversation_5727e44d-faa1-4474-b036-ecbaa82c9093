package com.lds.oneplanning.wps.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.OrderWarningLevelEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WpsOrderPlanWarningCountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "预警小类")
    private WpsOrderWarningTypeEnum warningType;

    @ApiModelProperty(value = "处理状态:1-未处理，2-已处理，3-已关闭")
    private Integer handleStatus;

    @ApiModelProperty
    private OrderWarningHandleStatusEnum statusEnum;

    @ApiModelProperty(value = "预警级别")
    private Integer warningLevel;

    @ApiModelProperty
    private OrderWarningLevelEnum levelEnum;


    @TableField(value = "count(*)", exist = false)
    private Long num;
}