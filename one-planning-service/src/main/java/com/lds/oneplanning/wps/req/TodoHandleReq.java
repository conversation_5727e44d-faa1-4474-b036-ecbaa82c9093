package com.lds.oneplanning.wps.req;

import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(description = "待办处理请求")
public class TodoHandleReq {
    @NotNull(message = "类型不能为空")
    private WpsOrderWarningTypeEnum type;
    @NotEmpty(message = "待办ID不能为空")
    private List<Long> ids;
}
