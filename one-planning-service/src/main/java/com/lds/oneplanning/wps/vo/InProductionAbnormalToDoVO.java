package com.lds.oneplanning.wps.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderAndon;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderProcedure;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 在制工单异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daish<PERSON>kun</a>
 * @since 2025/5/13
 */
@Data
@ApiModel(value = "在制工单异常")
@TableHeader(type = WpsOrderWarningTypeEnum.IN_PRODUCTION_EXCEPTION, source = {ViewSource.PLL,ViewSource.PK,ViewSource.PM})
public class InProductionAbnormalToDoVO implements Serializable {
    private Long id;

    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "销售订单-行项目")
    private String associatedOrderNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "排产日期")
    private LocalDate schedulingDate;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "生产车间")
    private String productionWorkshop;

    @ApiModelProperty(value = "生产线体")
    private String productionLine;

    @ApiModelProperty(value = "生产课长")
    private String productionForeman;

    @ApiModelProperty(value = "生产线长")
    private String productionLineLeader;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "物料下架日期")
    private LocalDate materialOffShelfDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "上线日期")
    private LocalDate onlineDate;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialName;

    @ApiModelProperty(value = "计划数量")
    private Integer plannedQuantity;

    @ApiModelProperty(value = "在制天数")
    private Integer productionDays;

    @ApiModelProperty(value = "实际投入数量")
    private Integer actualInputQuantity;

    @ApiModelProperty(value = "gap")
    private Integer actualInputQuantityGap;

    @ApiModelProperty(value = "实际报工数量")
    private Integer actualReportingQuantity;

    @ApiModelProperty(value = "实际报工gap")
    private Integer actualReportedQuantityGap;

    @ApiModelProperty(value = "SAP已入库数量")
    private Integer inboundQuantity;

    @ApiModelProperty(value = "报工详情")
    private String procedureDetail;

    //    @ApiModelProperty(value = "报工列表")
    List<MesProcessWorkOrderProcedure> procedureList;

    @ApiModelProperty(value = "andon详情")
    private String andonDetail;

    //    @ApiModelProperty(value = "andon列表")
    List<MesProcessWorkOrderAndon> andonList;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "建议再计划日期")
    private LocalDate suggestReplanDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "预计可完工日期", notes = "edit")
    private LocalDate estimatedCompletionDate;

    @ApiModelProperty(value = "未满单原因", notes = "edit")
    private String insufficientOrderReason;

    @ApiModelProperty(value = "原因分类", notes = "edit")
    private String reasonCategory;

    @ApiModelProperty(value = "流程状态")
    private OrderWarningHandleStatusEnum processStatus;

}
