package com.lds.oneplanning.wps.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@ApiModel(value = "影响组件计划")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AffectsAssemblyPlan {
    @ApiModelProperty(value = "PO")
    private String po;
    @ApiModelProperty(value = "ID")
    private String id;
    @ApiModelProperty(value = "计划日期")
    private LocalDate planDate;
    @ApiModelProperty(value = "影响数量")
    private Integer quantity;
}