package com.lds.oneplanning.wps.filter.write.impl;

import com.lds.oneplanning.wps.filter.write.AbstractWpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.utils.WpsDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/26 15:36
 */
@Slf4j
@Service
public class WeekPlanQtyWriteFilter extends AbstractWpsOrderWriteFilter {
    @Override
    public Integer filterSeq() {
        return 4;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, Integer datasource, String factoryCode, List<WpsRowData> wpsRowDatas,boolean cacheFlag, Map<String,Object> params) {
        wpsRowDatas.forEach(rowData -> {
            // 排产数量设值，
            List<LocalDate> dateStacks = WpsDateUtil.getDateStacks(LocalDate.now(), 89);
            for (LocalDate localDate : dateStacks){
                rowData.getScheduleDataMap().put(localDate,null);
            }
            Number schedulePcsQty = rowData.getSchedulePcsQty();
            if (null != schedulePcsQty) {
                rowData.setWaitingOrderQty(schedulePcsQty.intValue());
            }
        });
        return wpsRowDatas;
    }


}
