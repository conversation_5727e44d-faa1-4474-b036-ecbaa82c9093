package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WpsOrderPlanWarning对象", description="")
public class WpsOrderPlanWarning implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "线体编码")
    private String lineCode;

    @ApiModelProperty(value = "线体UUID")
    private String lineUuid;

    @ApiModelProperty(value = "预警大类")
    private String warningCategory;

    @ApiModelProperty(value = "预警小类")
    private String warningType;

    @ApiModelProperty(value = "产品类型")
    private String productType;

    @ApiModelProperty(value = "预警级别:1-低，2-中，3-高")
    private Integer warningLevel;

    @ApiModelProperty(value = "预警内容")
    private String warningContent;

    @ApiModelProperty(value = "排产日期")
    private LocalDate scheduleDate;

    @ApiModelProperty(value = "触发时间")
    private Date triggerTime;

    @ApiModelProperty(value = "处理人id")
    private Long handleUser;

    @ApiModelProperty(value = "处理状态:1-未处理，2-已处理，3-已关闭")
    private Integer handleStatus;

    @ApiModelProperty(value = "处理时间")
    private Date handleTime;

    @ApiModelProperty(value = "处理备注")
    private String handleContent;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "count(*)",exist = false)
    private Long num;

    public String getWarningTypeName() {
        return WpsOrderWarningTypeEnum.getNameByCode(warningType);
    }
}