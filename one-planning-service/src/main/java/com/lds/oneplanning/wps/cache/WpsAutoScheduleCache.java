package com.lds.oneplanning.wps.cache;

import com.lds.coral.website.common.util.RedisCacheUtil;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class WpsAutoScheduleCache {
    public static final String NEW_AUTO_SCHEDULE_KEY= "new:auto:schedule:%s";
    public void changeAutoScheduleCache(Long userId,Boolean isNewAutoSchedule) {
        RedisCacheUtil.valueObjSet(getKey(userId),isNewAutoSchedule);
    }

    /**
     * 默认启用旧版本排产
     * @return
     */
    public Boolean getAutoScheduleCache(Long userId) {
        Boolean isNewAutoSchedule = RedisCacheUtil.valueObjGet(getKey(userId), Boolean.class);
        if(Objects.isNull(isNewAutoSchedule)){
            isNewAutoSchedule=false;
        }
        return isNewAutoSchedule;
    }

    private String getKey(Long userId){
        return  String.format(NEW_AUTO_SCHEDULE_KEY, userId);
    }
}
