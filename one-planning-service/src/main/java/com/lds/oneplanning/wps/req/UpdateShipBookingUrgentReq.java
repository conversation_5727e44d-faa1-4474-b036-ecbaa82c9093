package com.lds.oneplanning.wps.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@ApiModel(description = "船期临近未订舱异常")
@Data
public class UpdateShipBookingUrgentReq implements Serializable {

    private static final long serialVersionUID = -960196808121984874L;

    @ApiModelProperty(value = "异常业务ID")
    private Long id;

    @ApiModelProperty(value = "是否调整出货信息", notes = "edit")
    private LocalDate adjustedShipScheduleDate;
}
