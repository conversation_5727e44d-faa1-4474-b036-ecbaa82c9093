package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.StagnantRiskLevel;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 独立备料SO未转正
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daish<PERSON>kun</a>
 * @since 2025/5/13 16:20
 */
@ApiModel(description = "独立备料SO未转正")
@TableHeader(type = WpsOrderWarningTypeEnum.INDEPENDENT_PREPARE_MATERIAL_ABNORMAL, source = ViewSource.MC)
@Data
public class IndependentPrepareMaterialAbnormalMcVO {
    private Long id;

    @ApiModelProperty(value = "计划单号")
    private String orderNumber;

    @ApiModelProperty(value = "BOM物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    @ApiModelProperty(value = "数量")
    private Integer orderQuantity;

    @ApiModelProperty(value = "物料是否可消耗")
    private Boolean materialChangeOrCancellationCompleted;

    // 新增字段: 工厂
    @ApiModelProperty(value = "工厂")
    private String factoryCode;

    /**
     * 取值：1-6或者>6
     */
    @ApiModelProperty(value = "预计消耗月份")
    private String estimatedConsumptionMonths;

    @ApiModelProperty(value = "呆滞风险等级")
    private StagnantRiskLevel stagnantRiskLevel;

    @ApiModelProperty(value = "N-1")
    private Integer consumptionN1;
    @ApiModelProperty(value = "N-2")
    private Integer consumptionN2;
    @ApiModelProperty(value = "N-3")
    private Integer consumptionN3;
    @ApiModelProperty(value = "N-4")
    private Integer consumptionN4;
    @ApiModelProperty(value = "N-5")
    private Integer consumptionN5;
    @ApiModelProperty(value = "N-6")
    private Integer consumptionN6;

    private String createdBy;
    private String updatedBy;
    private Date updatedAt;
    private Date createdAt;

}
