package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 物料不齐异常 - MC
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daish<PERSON>kun</a>
 * @since 2025/5/13
 */
@Data
@TableHeader(type = WpsOrderWarningTypeEnum.ATP_EXCEPTION, source = ViewSource.MC)
public class MaterialAtpAbnormalMcVO {
    private Long id;

    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "计划上线时间")
    private Date plannedOnlineTime;

    @ApiModelProperty(value = "客户")
    private String customer;

    /**
     * 销售订单
     */
    private String salesOrderNumber;

    @ApiModelProperty(value = "销售订单-行项目")
    private String salesOrderNumberWithLineNumber;

    /**
     * 采购订单
     */
    private String purchaseOrderNumber;

    @ApiModelProperty(value = "采购订单-行项目")
    private String purchaseOrderNumberWithLineNumber;

    private String lineNumber;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "计划数量")
    private Integer onlineQuantity;

    @ApiModelProperty(value = "异常类型")
    private MaterialAtpAbnormalType abnormalType;

    @ApiModelProperty(value = "欠料ID")
    private String shortageId;

    @ApiModelProperty(value = "欠料描述")
    private String shortageDescription;

    @ApiModelProperty(value = "需求时间")
    private Date requiredTime;

    @ApiModelProperty(value = "欠料数量")
    private Integer shortageQuantity;

    @ApiModelProperty(value = "GAP天数")
    private Integer gapDays;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "采购交期")
    private Date purchaseDeliveryDate;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    private OrderWarningHandleStatusEnum processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;
}
