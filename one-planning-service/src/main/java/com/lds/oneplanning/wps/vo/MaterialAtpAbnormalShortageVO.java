package com.lds.oneplanning.wps.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 物料不齐异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daish<PERSON>kun</a>
 * @since 2025/5/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MaterialAtpAbnormalShortageVO extends AffectsPlan {
    private Long id;

    @ApiModelProperty(value = "欠料ID")
    private String shortageId;

    @ApiModelProperty(value = "欠料描述")
    private String shortageDescription;

    @ApiModelProperty(value = "可得日期")
    private LocalDate availableDate;

    @ApiModelProperty(value = "需求时间")
    private LocalDate requiredTime;

    @ApiModelProperty(value = "欠料数量")
    private Integer shortageQuantity;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "采购交期")
    private LocalDate purchaseDeliveryTime;
    @ApiModelProperty(value = "最新采购交期")
    private LocalDate latestPurchaseDeliveryTime;

    @ApiModelProperty(value = "采购人员")
    private String purchaseInCharge;

    @ApiModelProperty(value = "采购组")
    private String groupInCharge;

    @ApiModelProperty(value = "GAP天数")
    private Integer gapDays;


    @ApiModelProperty(value = "是否影响上下层计划", notes = "edit")
    private Boolean affectsUpperLevelPlan;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    @ApiModelProperty(value = "是否发起船期变更")
    private Boolean initiateShipmentChange;

}
