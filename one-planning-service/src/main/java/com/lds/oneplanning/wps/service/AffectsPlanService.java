package com.lds.oneplanning.wps.service;

import com.lds.oneplanning.wps.vo.AffectsPlan;

import java.time.LocalDate;
import java.util.Collection;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2025/5/28
 */
public interface AffectsPlanService {

    /**
     * 构建影响计划
     *
     * @param affectsPlans
     * @param fieldExtractor 预计可完工日期
     */
    <T extends AffectsPlan> void buildAffectsPlan(Collection<T> affectsPlans, Function<T, LocalDate> fieldExtractor);

    /**
     * 构建影响计划
     * @param affectsPlans
     * @param estimatedCompletionDate
     * @param materialId
     * @param schedulingDate
     * @param plannedQuantity
     * @param <T>
     */
    <T extends AffectsPlan> void buildAffectsPlan(Collection<T> affectsPlans,
                                                  Function<T, LocalDate> estimatedCompletionDate,
                                                  Function<T, String> materialId,
                                                  Function<T, LocalDate> schedulingDate,
                                                  Function<T, Integer> plannedQuantity
    );
}
