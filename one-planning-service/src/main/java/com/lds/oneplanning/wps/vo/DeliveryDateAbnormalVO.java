package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 工艺路线异常
 *
 * 搜索
  工厂,订单号,时间（计划上线日期）,客户,物料ID
 *
 * 排序：
 * 灯色+计划上线日期
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13 16:20
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "交期异常")
@TableHeader(type = WpsOrderWarningTypeEnum.DELIVERY_DATE_ABNORMAL, source = ViewSource.DEFAULT)
@Data
public class DeliveryDateAbnormalVO extends AffectsPlan {
    private Long id;
    private LocalDate adjustedOnlineTimeEdited;
    private LocalDate estFinishTimeEdited;

    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "计划上线日期")
    private LocalDate plannedOnlineTime;

    @ApiModelProperty(value = "客户")
    private String customer;

    /**
     * 销售订单
     */
    private String salesOrderNumber;

    @ApiModelProperty(value = "销售订单-行项目")
    private String salesOrderNumberWithLineNumber;

    /**
     * 采购订单
     */
    private String purchaseOrderNumber;

    @ApiModelProperty(value = "采购订单-行项目")
    private String purchaseOrderNumberWithLineNumber;

    private String lineNumber;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    @ApiModelProperty(value = "订单数量")
    private Integer orderUnitQty;

    @ApiModelProperty(value = "原完工日期")
    private LocalDate originalFinishTime;

    @ApiModelProperty(value = "预计最新完工时间")
    private LocalDate estFinishTime;

    @ApiModelProperty(value = "SAP完工日期")
    private LocalDate calculateFinishTime;
    /**
     * 整灯：①影响出货：上线日期-系统完工≤2天；②影响上线：其余影响
     * 组件&部件：①影响整灯计划：组件上线-UB7需求时间≤3天；②影响组件&部件上线：上线时间-需求时间≤5天
     */
    @ApiModelProperty(value = "交期影响类型")
    private String deliveryDateAbnormalImpactType;

    @ApiModelProperty(value = "调整后上线日期", notes = "edit")
    private LocalDate adjustedOnlineTime;

    private OrderWarningHandleStatusEnum processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    @ApiModelProperty(value = "是否发起船期变更")
    private Boolean initiateShipmentChange;
}
