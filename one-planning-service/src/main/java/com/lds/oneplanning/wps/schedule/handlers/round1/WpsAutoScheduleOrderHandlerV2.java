package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.model.LineChangeCfgDTO;
import com.lds.oneplanning.basedata.service.ILineChangeCfgService;
import com.lds.oneplanning.common.utils.OrderArithUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.entity.WpsDayPlan;
import com.lds.oneplanning.wps.enums.SchedulePlanErrorCodeEnum;
import com.lds.oneplanning.wps.helper.WpsAutoScheduleOrderHelper;
import com.lds.oneplanning.wps.helper.WpsSchedulePlanLogHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsOrderPublishStatusEnum;
import com.lds.oneplanning.wps.schedule.enums.WpsOrderTypeEnum;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import com.lds.oneplanning.wps.service.IWpsDayPlanService;
import com.lds.oneplanning.wps.service.IWpsPublishedOrderService;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import com.lds.oneplanning.wps.service.facade.IWpsOrderPublishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * WPS自动排产
 * 1. 产线-产品，优先排序
 * 2. 循环生产线列表，根据订单的产品，捞取优先级最高的生产线列表
 * 3. 线体起始时间，结束时间，需要扣除休息日，再与排产周期做交集
 * 4. 订单预排产量：产线排产时长*排产比例*uph
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsAutoScheduleOrderHandlerV2 implements IWpsAutoScheduleHandler {

    /**
     * 允许超产比例
     */
    private static final float ALLOW_OVER_PRODUCTION_RATIO = 0.05F;

    @Autowired
    private IWpsOrderCommonService wpsOrderCommonService;

    @Autowired
    private IWpsOrderPublishService wpsOrderPublishService;

    @Autowired
    WpsAutoScheduleOrderHelper wpsAutoScheduleOrderHelper;

    @Autowired
    IWpsPublishedOrderService wpsPublishedOrderService;
    @Autowired
    IWpsDayPlanService wpsDayPlanService;
    @Autowired
    ILineChangeCfgService lineChangeCfgService;
    @Autowired
    WpsSchedulePlanLogHelper wpsSchedulePlanLogHelper;

    @Override
    public void execute(WpsAutoScheduleContext context) {
        Long start = System.currentTimeMillis();
        LocalDate currentDate = context.getCurrentDate();
        int weeksToPush = context.getWeeksToPush();
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        if (MapUtils.isEmpty(context.getOrderLineUphMap())) {
            return;
        }
        setChangeLineHour(context);
        this.runOrderSchedule(currentDate, context);
        Long end = System.currentTimeMillis();
        String dailyProductionLineMapStr = MapUtils.isNotEmpty(context.getDailyProductionLineMap()) ? JSON.toJSONString(context.getDailyProductionLineMap()) : "";
        String orderDailyScheduleDataMapStr = MapUtils.isNotEmpty(context.getOrderDailyScheduleDataMap()) ? JSON.toJSONString(context.getOrderDailyScheduleDataMap()) : "";
        String productGroupPriorityMapStr = MapUtils.isNotEmpty(context.getProductGroupPriorityMap()) ? JSON.toJSONString(context.getProductGroupPriorityMap()) : "";
        String orderLinePriorityMapStr = MapUtils.isNotEmpty(context.getOrderLinePriorityMap()) ? JSON.toJSONString(context.getOrderLinePriorityMap()) : "";
        String customerProductGroupPriorityMapStr =   MapUtils.isNotEmpty(context.getCustomerProductGroupPriorityMap()) ? JSON.toJSONString(context.getCustomerProductGroupPriorityMap()) : "";
        log.info("WpsAutoScheduleOrderHandlerV2->WPS自动排产結果,currentDate：{},weeksToPush:{},orderList size:{},userId:{},耗时：{}", currentDate, weeksToPush, orderList.size(), context.getUserId(), end - start);
        log.info("WpsAutoScheduleOrderHandlerV2->WPS自动排产結果,dailyProductionLineMap:{}", dailyProductionLineMapStr);
        log.info("WpsAutoScheduleOrderHandlerV2->WPS自动排产結果,orderDailyScheduleDataMap:{}", orderDailyScheduleDataMapStr);
        log.info("WpsAutoScheduleOrderHandlerV2->WPS自动排产結果,productGroupPriorityMap:{}",productGroupPriorityMapStr);
        log.info("WpsAutoScheduleOrderHandlerV2->WPS自动排产結果,orderLinePriorityMap:{}",orderLinePriorityMapStr);
        log.info("WpsAutoScheduleOrderHandlerV2->WPS自动排产結果,customerProductGroupPriorityMap:{}",customerProductGroupPriorityMapStr);
    }

    /**
     * 运行订单排产
     *
     * @param localDate
     * @param context
     */
    private void runOrderSchedule(LocalDate localDate, WpsAutoScheduleContext context) {
        //排产日期（默认设置90天）
        context.getScheduleDates().addAll(wpsAutoScheduleOrderHelper.getBetweenDateList(localDate, localDate.plusDays(90)));
        //最近7天的订单，不参与自动排产
        setLastSevenOrder(context);
        //处理订单
        Map<WpsOrderTypeEnum, List<WpsRowData>> wpsOrderTypeEnumListMap = categorizeOrders(context.getOrderList());
        wpsOrderTypeEnumListMap.forEach((type, orders) -> {
            if (CollectionUtils.isNotEmpty(orders)) {
                loopByProductPriority(orders, context, type);
            }
        });
    }

    /**
     * 设置最近7天的订单，不参与自动排产
     *
     * @param context
     */
    public void setLastSevenOrder(WpsAutoScheduleContext context) {
        //重置发布订单
        wpsOrderPublishService.resetPublishedOrders(context);
        //获取最近7天已保存的订单不参与保存
        LocalDate start = context.getCurrentDate();
        LocalDate end = start.plusDays(7);
        List<String> orderIds = context.getOrderList().stream().map(WpsRowData::getOrderNo).distinct().collect(Collectors.toList());
        List<String> publishOrderIds = wpsDayPlanService.listByBizIdsAndDates(orderIds, start, end).stream().map(WpsDayPlan::getBizId).distinct().collect(Collectors.toList());
        context.setSevenOrderList(publishOrderIds);
        //获取业务id、产线编码、排产日期、预产数量的map
        Map<String, Map<String, Map<LocalDate, Integer>>> orderDailyPrePlanQuantityMap = context.getOrderDailyPrePlanQuantityMap();
        if (MapUtils.isEmpty(orderDailyPrePlanQuantityMap)) {
            log.info("orderDailyPrePlanQuantityMap为空，无法对7天内的数据进行赋值");
            return;
        }
        context.getOrderList().forEach(order -> {
            //虚拟订单不处理
            if (order.get_virtualLineOrder()) {
                return;
            }
            if (publishOrderIds.contains(order.getOrderNo())) {
                wpsOrderCommonService.processSaveOrder(context, order, orderDailyPrePlanQuantityMap, end);
                order.set_publishStatus(WpsOrderPublishStatusEnum.PUBLISHED.getValue());
            }
        });
    }


    /**
     * 订单分类（冻结/普通）
     *
     * @param orderList
     * @return
     */
    private Map<WpsOrderTypeEnum, List<WpsRowData>> categorizeOrders(List<WpsRowData> orderList) {
        Map<WpsOrderTypeEnum, List<WpsRowData>> categorizedOrders = Maps.newHashMap();
        categorizedOrders.put(WpsOrderTypeEnum.FROZEN, Lists.newArrayList());
        categorizedOrders.put(WpsOrderTypeEnum.NORMAL, Lists.newArrayList());
        orderList.forEach(order -> {
            if (Optional.ofNullable(order.get_frozenStatus()).orElse(0) == 1) {
                categorizedOrders.get(WpsOrderTypeEnum.FROZEN).add(order);
            } else {
                categorizedOrders.get(WpsOrderTypeEnum.NORMAL).add(order);
            }
        });
        return categorizedOrders;
    }

    /**
     * 按优先级进行排产
     *
     * @param orders
     * @param context
     * @param wpsOrderTypeEnum
     */
    private void loopByProductPriority(List<WpsRowData> orders, WpsAutoScheduleContext context, WpsOrderTypeEnum wpsOrderTypeEnum) {
        if (WpsOrderTypeEnum.FROZEN.equals(wpsOrderTypeEnum)) {
            List<LocalDate> localDates = wpsAutoScheduleOrderHelper.getBetweenDateList(context.getCurrentDate(), context.getCurrentDate().plusDays(90));
            doFrozenOrders(context, orders, localDates);
        } else {
            // 值越小，优先级越高
            int minPrioritySeq = WpsConstants.MIN_PRODUCT_GROUP_PRIORITY_SEQ;
            int maxPrioritySeq = WpsConstants.MAX_PRODUCT_GROUP_PRIORITY_SEQ;
            IntStream.rangeClosed(minPrioritySeq, maxPrioritySeq).forEach(prioritySeq -> {
                this.loopOrders(orders, context, wpsOrderTypeEnum, prioritySeq);
            });
        }
    }

    private void loopOrders(List<WpsRowData> orders, WpsAutoScheduleContext context, WpsOrderTypeEnum wpsOrderTypeEnum, Integer prioritySeq) {
        for (WpsRowData order : orders) {
            // 待排产数量小于等于0，则跳过
            if (order.getWaitingOrderQty() <= 0) {
                continue;
            }
            // 如果已发布订单，则跳过
            Integer publishStatus = order.get_publishStatus();
            if (null != publishStatus && publishStatus == WpsOrderPublishStatusEnum.PUBLISHED.getValue()) {
                log.info("WpsAutoScheduleOrderHandlerV2->WPS排产,orderNo:{},已发布.", order.getOrderNo());
                continue;
            }
            doOrderSchedule(context, order, prioritySeq);
        }
    }

    private void doOrderSchedule(WpsAutoScheduleContext context, WpsRowData order, Integer prioritySeq) {
        String orderNo = order.getOrderNo();
        Map<Integer, Set<String>> orderLinePriorityMap = context.getOrderLinePriorityMap().get(orderNo);
        if (MapUtils.isEmpty(orderLinePriorityMap)) {
            return;
        }
        Set<String> suitableLineUuids = orderLinePriorityMap.get(prioritySeq);
        if (CollectionUtils.isEmpty(suitableLineUuids)) {
            return;
        }
        log.info("WpsAutoScheduleOrderHandlerV2->WPS排产,prioritySeq:{},orderNo:{},suitableLineUuids:{}.", prioritySeq, orderNo, suitableLineUuids);
        // 订单->产线UUID->uph映射
        Map<String, Map<String, Float>> orderLineUphMap = context.getOrderLineUphMap();
        Map<String, Float> uphMap = orderLineUphMap.get(orderNo);
        if (MapUtils.isEmpty(uphMap)) {
            log.info("WpsAutoScheduleOrderHandlerV2->订单未配置UPH,orderNo:{},productId:{},commodityId:{}", orderNo, order.getProductId(), order.getCommodityId());
            context.getSchedulePlanLogMap().putAll(wpsSchedulePlanLogHelper.createWpsSchedulePlanLog(order, null, SchedulePlanErrorCodeEnum.ORDER_UPH_NOT_EXIST,context));
            return;
        }
        //获取排产的线体ID
        String lineUuid = wpsAutoScheduleOrderHelper.getScheduleLine(suitableLineUuids, order, context, uphMap);
        if (StringUtils.isBlank(lineUuid)) {
            log.info("WpsAutoScheduleOrderHandlerV2->未找到可排产线,orderNo:{}", orderNo);
            return;
        }
        Float uph = uphMap.get(lineUuid);
        if (null == uph) {
            return;
        }
        // 订单待排产时长
        Map<LocalDate, WpsProductionLine> dailyProductionLineMap = context.getDailyProductionLineMap().get(lineUuid);
        if (MapUtils.isEmpty(dailyProductionLineMap)) {
            return;
        }
        //获取有效的排产日期
        List<LocalDate> scheduleDates = wpsAutoScheduleOrderHelper.getScheduleDate(order, lineUuid, dailyProductionLineMap);
        if (CollectionUtils.isEmpty(scheduleDates)) {
            return;
        }
        order.setScheduleDates(scheduleDates);
        //线体换产品组生产时需要增加换线时长
        this.changeLineProductGroup(lineUuid, scheduleDates, dailyProductionLineMap, order, context);
        int totalcheduledQty = 0;
        for (LocalDate date : scheduleDates) {
            WpsProductionLine productionLine = dailyProductionLineMap.get(date);
            if (Objects.isNull(productionLine)) {
                continue;
            }
            int waitingOrderQty = order.getWaitingOrderQty();
            float waitingLineHour = productionLine.getWaitingScheduleHours();
            if (waitingOrderQty == 0 || waitingLineHour == 0F) {
                continue;
            }
            int scheduledQty;
            // 订单待排产数量
            float waitingOrderHour = OrderArithUtil.floatDivide(waitingOrderQty, uph);
            //计划时长
            float planningHour = productionLine.getPlanScheduleHours();
            // 订单待排产时长大于等于线体排产时长
            if (waitingOrderHour >= waitingLineHour) {
                productionLine.setWaitingScheduleHours(0F);
                // 当天排完后剩余订单量不超过该线体生产时长的10%, 则把剩余的数量放在当天完成
                if (OrderArithUtil.floatSubtract(waitingOrderHour, waitingLineHour) <=
                        OrderArithUtil.floatMultiply(planningHour, ALLOW_OVER_PRODUCTION_RATIO)) {
                    scheduledQty = waitingOrderQty;
                    productionLine.setScheduledHours(OrderArithUtil.floatAdd(productionLine.getScheduledHours(), waitingOrderHour));
                } else {
                    scheduledQty = (int) Math.ceil(waitingLineHour * uph);
                    productionLine.setScheduledHours(OrderArithUtil.floatAdd(productionLine.getScheduledHours(), waitingLineHour));
                }
                updateScheduledHours(productionLine, orderNo, waitingLineHour);
            } else {
                scheduledQty = waitingOrderQty;
                productionLine.setWaitingScheduleHours(OrderArithUtil.floatSubtract(waitingLineHour, waitingOrderHour));
                productionLine.setScheduledHours(OrderArithUtil.floatAdd(productionLine.getScheduledHours(), waitingOrderHour));
                updateScheduledHours(productionLine, orderNo, waitingOrderHour);
            }
            order.setWaitingOrderQty(waitingOrderQty - scheduledQty);
            log.info("WpsAutoScheduleOrderHandlerV2->WPS排产,doOrderSchedule,orderNo:{},date:{},lineUuid:{},uph:{},waitingLineHour:{}," +
                            "waitingOrderHour:{},waitingOrderQty:{},scheduledQty:{}.",
                    orderNo, date, lineUuid, uph, waitingLineHour, waitingOrderHour, waitingOrderQty, scheduledQty);
            wpsOrderCommonService.updateDailyScheduleData(context, orderNo, date, lineUuid, scheduledQty);
            //设置当前日期生产的产品组
            context.getLineProductGroupMap().computeIfAbsent(lineUuid, k -> new HashMap<>()).put(date, order.get_productGroupCode());
            totalcheduledQty += scheduledQty;
        }
        //出现订单在该线体有排过，但是未排完的情况下，需要把剩余数量全部排在该线体最后一天
        if (totalcheduledQty > 0 && order.getWaitingOrderQty() > 0) {
            LocalDate date = order.getScheduleDates().get(order.getScheduleDates().size() - 1);
            float waitingOrderHour = OrderArithUtil.floatDivide(order.getWaitingOrderQty(), uph);
            wpsOrderCommonService.updateProductionLine(context, dailyProductionLineMap, lineUuid, orderNo, date, order.getWaitingOrderQty(), uph);
            log.info("WpsAutoScheduleOrderHandlerV2->WPS排产,doOrderSchedule,当前订单在该线体未排完的统一放在最后一天orderNo:{},date:{},lineUuid:{},uph:{},waitingOrderHour:{},waitingOrderQty:{},scheduledQty:{}",
                    orderNo, date, lineUuid, uph, waitingOrderHour, order.getWaitingOrderQty(), order.getWaitingOrderQty());
            order.setWaitingOrderQty(0);
        }
        log.info("WpsAutoScheduleOrderHandlerV2->WPS排产,prioritySeq:{},orderNo:{},suitableLineUuids:{}，排查日期：{}", prioritySeq, orderNo, suitableLineUuids, order.getScheduleDates());
    }

    /**
     * 判断线体生产的产品组是否有变化，有变化需要增加换线时长
     *
     * @param lineUuid
     * @param scheduleDates
     * @param dailyProductionLineMap
     * @param order
     * @param context
     */
    private void changeLineProductGroup(String lineUuid, List<LocalDate> scheduleDates, Map<LocalDate, WpsProductionLine> dailyProductionLineMap, WpsRowData order, WpsAutoScheduleContext context) {
        float waitingChangeLineHours = 0f;
        for (LocalDate date : scheduleDates) {
            WpsProductionLine productionLine = dailyProductionLineMap.get(date);
            if (Objects.isNull(productionLine)) {
                continue;
            }
            float waitingLineHour = productionLine.getWaitingScheduleHours();
            if (waitingLineHour == 0F) {
                continue;
            }
            //获取线体的换线时长 TODO 增加换线时长操作
            float changeLineHour = wpsAutoScheduleOrderHelper.lineChangeProductGroupHour(lineUuid, date, order, context) + waitingChangeLineHours;
            if (Objects.equals(changeLineHour, 0f)) {
                continue;
            }
            order.setLineChangeProductGroup(true);
            float logHour = 0f;
            // 换线时长超过待排产时长
            if (changeLineHour >= waitingLineHour) {
                productionLine.setWaitingScheduleHours(0F);
                productionLine.setScheduledHours(OrderArithUtil.floatAdd(productionLine.getScheduledHours(), waitingLineHour));
                updateScheduledHours(productionLine, order.getOrderNo(), waitingLineHour);
                waitingChangeLineHours = waitingChangeLineHours + changeLineHour - waitingLineHour;
                logHour = waitingLineHour;
            } else {
                productionLine.setWaitingScheduleHours(OrderArithUtil.floatSubtract(waitingLineHour, changeLineHour));
                productionLine.setScheduledHours(OrderArithUtil.floatAdd(productionLine.getScheduledHours(), changeLineHour));
                waitingChangeLineHours = 0f;
                updateScheduledHours(productionLine, order.getOrderNo(), changeLineHour);
                logHour = changeLineHour;
            }
            wpsOrderCommonService.updateDailyScheduleData(context, order.getOrderNo(), date, lineUuid, 0);
            context.getLineProductGroupMap().computeIfAbsent(lineUuid, k -> new HashMap<>()).put(date, order.get_productGroupCode());
            log.info("线体生产该订单出现换产品组生产需要增加换线时长,日期：{}，lineUuid:{},orderNo:{},productGroupCode:{},productGroupName:{},换线时长：{},当前日期换线时长：{}",
                    date, lineUuid, order.getOrderNo(), order.get_productGroupCode(), order.get_productGroupName(), changeLineHour, logHour);
        }
    }

    private void updateScheduledHours(WpsProductionLine productionLine, String orderNo, float hours) {
        productionLine.getOrderScheduledHoursMap()
                .merge(orderNo, hours, Float::sum);
    }

    /**
     * 冻结订单处理
     *
     * @param context
     * @param frozenOrderList
     * @param localDates
     */
    private void doFrozenOrders(WpsAutoScheduleContext context, List<WpsRowData> frozenOrderList, List<LocalDate> localDates) {
        if (CollectionUtils.isEmpty(frozenOrderList) || CollectionUtils.isEmpty(localDates)) {
            return;
        }
        LocalDate startDate = localDates.get(0);
        LocalDate endDate = localDates.get(localDates.size() - 1);
        List<String> frozenOrderNos = frozenOrderList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toList());
        Map<String, Map<String, Map<LocalDate, Integer>>> frozenOrderMap = wpsDayPlanService.getMapByBizIdsAndDates(frozenOrderNos, startDate, endDate);
        if (MapUtils.isEmpty(frozenOrderMap)) {
            return;
        }
        frozenOrderList.forEach(order -> {
            //排除最近7天订单，避免冻结的订单在7天内被重复处理
            if (context.getSevenOrderList().contains(order.getOrderNo())) {
                return;
            }
            wpsOrderCommonService.processOrder(context, order, WpsOrderTypeEnum.FROZEN, frozenOrderMap, localDates);
        });
    }

    /**
     * 设置换线时长
     * @param context
     */
    private void setChangeLineHour(WpsAutoScheduleContext context){
        LineChangeCfgDTO lineChangeCfgDto = lineChangeCfgService.getCacheLineChangeCfg(context.getCurrentFactoryCode());
        if(Objects.nonNull(lineChangeCfgDto)){
            context.setChangeLineHour(lineChangeCfgDto.getHour());
        }
    }
    @Override
    public int getOrder() {
        return 7;
    }

    @Override
    public int getRound() {
        return 1;
    }
}