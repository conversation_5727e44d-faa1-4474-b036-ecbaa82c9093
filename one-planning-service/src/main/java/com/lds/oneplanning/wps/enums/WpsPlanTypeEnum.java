package com.lds.oneplanning.wps.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public enum WpsPlanTypeEnum {

    WHOLE_MACHINE(0, "整机","WHOLE_MACHINE"),
    COMPONENT(1, "组件","COMPONENT"),
    PART(2, "部件","PART");

    private final Integer code;
    private final String name;
    private final String fullCode;

    public static WpsPlanTypeEnum getEnumByName(String name) {
        for (WpsPlanTypeEnum e : WpsPlanTypeEnum.values()) {
            if (e.getName().equals(name)) {
                return e;
            }
        }
        return null;
    }

    public static WpsPlanTypeEnum getEnumByFullCode(String fullCode) {
        for (WpsPlanTypeEnum e : WpsPlanTypeEnum.values()) {
            if (e.getFullCode().equals(fullCode)) {
                return e;
            }
        }
        return null;
    }
}
