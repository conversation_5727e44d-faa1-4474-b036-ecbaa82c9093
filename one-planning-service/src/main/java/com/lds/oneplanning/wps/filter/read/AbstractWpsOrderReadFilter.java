package com.lds.oneplanning.wps.filter.read;

import com.lds.oneplanning.wps.model.WpsRowData;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/25 17:11
 */
public abstract class AbstractWpsOrderReadFilter implements WpsOrderReadFilter {
    protected WpsOrderReadFilter next;
    @Override
    public WpsOrderReadFilter setNext(WpsOrderReadFilter filter) {
        this.next = filter;
        return filter;
    }

    @Override
    public List<WpsRowData> filter(Long userId,String factoryCode,List<WpsRowData> dirtyList,boolean cacheFlag) {
        if (CollectionUtils.isEmpty(dirtyList)) {
            return dirtyList;
        }
        List<WpsRowData> filteredOrders = doFilter(userId,factoryCode,dirtyList,cacheFlag);
        if (next != null) {
            return next.filter(userId,factoryCode,filteredOrders,cacheFlag);
        }
        return filteredOrders;
    }

    protected abstract List<WpsRowData> doFilter(Long userId,String factoryCode,List<WpsRowData> dirtyList,boolean cacheFlag);

}
