package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * 虚拟线体订单处理
 * 虚拟线体订单不参与排产，放置到第一天进行排产
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsVirtualLineOrderHandler implements IWpsAutoScheduleHandler {

    @Autowired
    private IWpsOrderCommonService wpsOrderCommonService;

    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        String virtualLineUuid = context.getVirtualLineUuid();
        if (CollectionUtils.isEmpty(orderList) || StringUtils.isEmpty(virtualLineUuid)) {
            return;
        }
        processOrders(context, orderList, virtualLineUuid);
    }

    private void processOrders(WpsAutoScheduleContext context, List<WpsRowData> orderList, String virtualLineUuid) {
        for (WpsRowData order : orderList) {
            if (!isVirtualLineOrder(order)) {
                continue;
            }
            LocalDate startProductPeriod = order.get_startProductPeriod();
            if (null == startProductPeriod) {
                continue;
            }
            int waitingOrderQty = order.getWaitingOrderQty();
            if (waitingOrderQty > 0) {
                wpsOrderCommonService.updateDailyScheduleData(context, order.getOrderNo(), startProductPeriod, virtualLineUuid, waitingOrderQty);
                order.setWaitingOrderQty(0);
            }
        }
    }

    private boolean isVirtualLineOrder(WpsRowData order) {
        return null != order && order.get_virtualLineOrder();
    }

    @Override
    public int getOrder() {
        return 5;
    }

    @Override
    public int getRound() {
        return 1;
    }
}