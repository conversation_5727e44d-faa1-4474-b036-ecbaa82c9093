package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工艺路线异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13 16:20
 */
@ApiModel(description = "包材BOM异常")
@TableHeader(type = WpsOrderWarningTypeEnum.PACKAGE_BOM_ABNORMAL, source = ViewSource.DEFAULT)
@Data
public class PackageBomAbnormalVO {
    @ApiModelProperty(value = "采购订单号")
    private String purchaseOrderNumber;

    @ApiModelProperty(value = "行项目")
    private Integer lineItem;

    @ApiModelProperty(value = "需求工厂")
    private String demandPlant;

    @ApiModelProperty(value = "生产订单号")
    private String orderNumber;

    @ApiModelProperty(value = "商品ID")
    private String productId;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    @ApiModelProperty(value = "计划上线时间时间")
    private LocalDateTime plannedOnlineTime;

    @ApiModelProperty(value = "距离计划上线时间剩余天数")
    private Integer daysRemainingBeforeOnline;

    @ApiModelProperty(value = "灯色")
    private String lightColor;

    @ApiModelProperty(value = "订单数量")
    private Integer orderQuantity;

    @ApiModelProperty(value = "包材生产版本")
    private String packagingMaterialProductionVersion;

    @ApiModelProperty(value = "是否触发代办到责任人")
    private Boolean isTriggerTodoToResponsiblePerson;
}
