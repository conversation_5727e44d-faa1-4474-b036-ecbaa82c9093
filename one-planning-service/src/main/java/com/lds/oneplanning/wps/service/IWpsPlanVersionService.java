package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WpsPlanVersion;
import com.lds.oneplanning.wps.enums.WpsPlanSourceEnum;
import com.lds.oneplanning.wps.model.WpsPlanVersionDTO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【wps_plan_version】的数据库操作Service
* @createDate 2025-05-08 11:38:52
*/
public interface IWpsPlanVersionService extends IService<WpsPlanVersion> {



    void savePlanVersion(WpsPlanVersion wpsPlanVersion);

    /**
     * 获取用户指定类型的最新版本
     * @param plannerEmpNo
     * @param source
     * @return
     */
    String getLastPlanVersion(String plannerEmpNo,String factoryCode, WpsPlanSourceEnum source);

    List<WpsPlanVersionDTO> findList(String plannerEmpNo,String factoryCode);

}
