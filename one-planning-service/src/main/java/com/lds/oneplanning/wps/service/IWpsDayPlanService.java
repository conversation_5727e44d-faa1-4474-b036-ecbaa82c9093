package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WpsDayPlan;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-25
 */
public interface IWpsDayPlanService extends IService<WpsDayPlan> {

    Map<String, List<WpsDayPlan>> groupByBizId(@NotNull Collection<String> bizIds, Collection<String> lineCodes);

    void batchSaveByBizId(List<WpsDayPlan> weekPlans);

    List<WpsDayPlan> listByBizIdsAndDates(List<String> bizIds, LocalDate startDate, LocalDate endDate);

    /**
     * 获取业务id、产线UUID、排产日期、预产数量的map
     *
     * @param bizIds
     * @param startDate
     * @param endDate
     * @return
     */
    Map<String, Map<String, Map<LocalDate, Integer>>> getMapByBizIdsAndDates(List<String> bizIds, LocalDate startDate, LocalDate endDate);

    Integer batchSaveOrUpdate4Schedule(List<WpsDayPlan> weekPlans);

    List<WpsDayPlan> listByDates(LocalDate startDate, LocalDate endDate);
}