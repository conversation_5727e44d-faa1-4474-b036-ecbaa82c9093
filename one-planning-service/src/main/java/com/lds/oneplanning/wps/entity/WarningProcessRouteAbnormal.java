package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDate;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.lds.oneplanning.wps.enums.LightColor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WarningProcessRouteAbnormal对象", description="")
public class WarningProcessRouteAbnormal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "排产线体")
    private String productionLine;

    @ApiModelProperty(value = "线体名称")
    private String lineName;

    @ApiModelProperty(value = "计划订单")
    private String plannedOrder;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "计划上线时间")
    private LocalDate plannedOnlineTime;

    @ApiModelProperty(value = "距离计划上线时间剩余天数")
    private Integer days;

    @ApiModelProperty(value = "生产工厂")
    private String factory;

    @ApiModelProperty(value = "灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "是否维护工艺路线")
    private String sfwhgylx;

    @ApiModelProperty(value = "是否触发待办到责任人")
    private String sfcfdbdzrr;

    @ApiModelProperty(value = "NPI人员")
    private String npiPerson;

    @ApiModelProperty(value = "NPI人员工号")
    private String npiPersonGh;

    @ApiModelProperty(value = "NPI人员userid")
    private String npiPersonUserid;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
