package com.lds.oneplanning.wps.helper;

import com.alibaba.fastjson.JSON;
import com.lds.oneplanning.wps.entity.WpsPlanVersion;
import com.lds.oneplanning.wps.entity.WpsSnapshot;
import com.lds.oneplanning.wps.model.WpsRowData;
import org.springframework.stereotype.Component;

@Component
public class WpsSnapshotHelper {

    public WpsSnapshot createWpsSnapshot(WpsRowData wpsRowData,WpsPlanVersion wpsPlanVersion) {
        WpsSnapshot wpsSnapshot = new WpsSnapshot();
        wpsSnapshot.setPlanVersionId(wpsPlanVersion.getId());
        wpsSnapshot.setVersion(wpsPlanVersion.getVersion());
        wpsSnapshot.setSource(wpsPlanVersion.getSource());
        wpsSnapshot.setPlannerEmpNo(wpsPlanVersion.getPlannerEmpNo());
        wpsSnapshot.setBizId(wpsRowData.getOrderNo());
        wpsSnapshot.setLineCode(wpsRowData.getLineCode());
        wpsSnapshot.setLineUuid(wpsRowData.getLineUuid());
        wpsSnapshot.setFactoryCode(wpsRowData.getFactory());
        wpsSnapshot.setWpsData(JSON.toJSONString(wpsRowData));
        return wpsSnapshot;
    }
}
