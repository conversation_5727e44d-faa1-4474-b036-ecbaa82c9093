package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WpsPublishedOrder;
import com.lds.oneplanning.wps.model.WpsDateRange;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface IWpsPublishedOrderService extends IService<WpsPublishedOrder> {

    List<WpsPublishedOrder> listByBizIdsAndPublishTargetAndDates(List<String> bizIds, String publishTarget,
                                                                 LocalDate startDate, LocalDate endDate);

    Map<String, List<WpsDateRange>> getMapByBizIdsAndPublishTargetAndDates(List<String> bizIds, String publishTarget,
                                                                           LocalDate startDate, LocalDate endDate);
}
