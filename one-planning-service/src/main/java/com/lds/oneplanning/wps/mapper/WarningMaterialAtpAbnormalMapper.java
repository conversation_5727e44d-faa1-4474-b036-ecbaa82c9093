package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalMcVO;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【warning_material_atp_abnormal】的数据库操作Mapper
 * @createDate 2025-05-14 17:29:49
 * @Entity com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal
 */
public interface WarningMaterialAtpAbnormalMapper extends BaseMapper<WarningMaterialAtpAbnormal> {

    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningMaterialAtpAbnormal }>
     */
    List<WarningMaterialAtpAbnormal> queryUnHandleData();

    /**
     * 查询页面
     *
     * @param pageParam 页面参数
     * @param userId    用户身份
     * @param vo        vo
     * @return {@link Page }<{@link WarningMaterialAtpAbnormal }>
     */
    Page<MaterialAtpAbnormalVO> queryPage(@Param("pageParam") IPage<MaterialAtpAbnormalVO> pageParam, @Param("userId") String userId, @Param("vo") MaterialAtpAbnormalVO vo);

    /**
     * 查询MC页面
     *
     * @param pageParam 页面参数
     * @param userId    用户身份
     * @param vo        vo
     * @return {@link Page }<{@link ? }>
     */
    Page<MaterialAtpAbnormalMcVO> queryMcPage(@Param("pageParam") IPage<MaterialAtpAbnormalMcVO> pageParam, @Param("userId") String userId, @Param("vo") MaterialAtpAbnormalVO vo);
}




