package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.wps.entity.WpsSchedulePlanLog;
import com.lds.oneplanning.wps.enums.SchedulePlanErrorCodeEnum;
import com.lds.oneplanning.wps.mapper.WpsSchedulePlanLogMapper;
import com.lds.oneplanning.wps.model.WpsSchedulePlanLogDTO;
import com.lds.oneplanning.wps.model.WpsSchedulePlanLogQueryDTO;
import com.lds.oneplanning.wps.service.IWpsSchedulePlanLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class WpsSchedulePlanLogServiceImpl extends ServiceImpl<WpsSchedulePlanLogMapper, WpsSchedulePlanLog> implements IWpsSchedulePlanLogService {
    @Autowired
    private IPlannerDataPermissionService plannerDataPermissionService;
    @Override
    public Page<WpsSchedulePlanLogDTO> findPage(WpsSchedulePlanLogQueryDTO query) {
        Set<String> factoryCodeList = getFacotoryCodes(query.getUserId());
        if(CollectionUtils.isEmpty(factoryCodeList)){
            return new Page<>(query.getPageNum(),query.getPageSize());
        }
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<WpsSchedulePlanLog> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page(query.getPageNum(), query.getPageSize());
        this.baseMapper.selectPage(page,Wrappers.<WpsSchedulePlanLog>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getFactoryCode()),WpsSchedulePlanLog::getFactoryCode, query.getFactoryCode())
                .like(StringUtils.isNotBlank(query.getOrderNo()),WpsSchedulePlanLog::getOrderNo, query.getOrderNo())
                .like(StringUtils.isNotBlank(query.getSellOrderNo()),WpsSchedulePlanLog::getSellOrderNo, query.getSellOrderNo())
                .eq(StringUtils.isNotBlank(query.getErrorCode()),WpsSchedulePlanLog::getErrorCode, query.getErrorCode())
                .ge(Objects.nonNull(query.getStartTime()),WpsSchedulePlanLog::getCreateTime, query.getStartTime())
                .le(Objects.nonNull(query.getEndTime()),WpsSchedulePlanLog::getCreateTime, query.getEndTime())
                .in(WpsSchedulePlanLog::getFactoryCode, factoryCodeList)
                .orderByDesc(WpsSchedulePlanLog::getCreateTime, WpsSchedulePlanLog::getId)
        );
        Page<WpsSchedulePlanLogDTO> returnPage = new Page<>(query.getPageNum(),query.getPageSize());
        List<WpsSchedulePlanLogDTO> records = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(page.getRecords())){
            for(WpsSchedulePlanLog log:page.getRecords()){
                WpsSchedulePlanLogDTO wpsSchedulePlanLogDTO = BeanUtil.map(log,WpsSchedulePlanLogDTO.class);
                wpsSchedulePlanLogDTO.setErrorName(SchedulePlanErrorCodeEnum.getNameByCode(wpsSchedulePlanLogDTO.getErrorCode()));
                records.add(wpsSchedulePlanLogDTO);
            }
        }
        returnPage.setTotal(page.getTotal());
        returnPage.setResult(records);
        return returnPage;
    }

    @Override
    public List<WpsSchedulePlanLogDTO> findList(WpsSchedulePlanLogQueryDTO query) {
        Set<String> factoryCodeList = getFacotoryCodes(query.getUserId());
        if(CollectionUtils.isEmpty(factoryCodeList)){
            return Lists.newArrayList();
        }
        List<WpsSchedulePlanLog> wpsSchedulePlanLogs = this.baseMapper.selectList(Wrappers.<WpsSchedulePlanLog>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getFactoryCode()),WpsSchedulePlanLog::getFactoryCode, query.getFactoryCode())
                .like(StringUtils.isNotBlank(query.getOrderNo()),WpsSchedulePlanLog::getOrderNo, query.getOrderNo())
                .like(StringUtils.isNotBlank(query.getSellOrderNo()),WpsSchedulePlanLog::getSellOrderNo, query.getSellOrderNo())
                .eq(StringUtils.isNotBlank(query.getErrorCode()),WpsSchedulePlanLog::getErrorCode, query.getErrorCode())
                .ge(Objects.nonNull(query.getStartTime()),WpsSchedulePlanLog::getCreateTime, query.getStartTime())
                .le(Objects.nonNull(query.getEndTime()),WpsSchedulePlanLog::getCreateTime, query.getEndTime())
                .in(WpsSchedulePlanLog::getFactoryCode, factoryCodeList)
                .orderByDesc(WpsSchedulePlanLog::getCreateTime, WpsSchedulePlanLog::getId)
        );
        List<WpsSchedulePlanLogDTO> records = Lists.newArrayList();
        for(WpsSchedulePlanLog log:wpsSchedulePlanLogs){
            WpsSchedulePlanLogDTO wpsSchedulePlanLogDTO = BeanUtil.map(log,WpsSchedulePlanLogDTO.class);
            wpsSchedulePlanLogDTO.setErrorName(SchedulePlanErrorCodeEnum.getNameByCode(wpsSchedulePlanLogDTO.getErrorCode()));
            records.add(wpsSchedulePlanLogDTO);
        }
        return records;
    }

    @Override
    public void deleteByFactoryCode(String factoryCode) {
        this.remove(Wrappers.<WpsSchedulePlanLog>lambdaQuery().eq(WpsSchedulePlanLog::getFactoryCode, factoryCode));
    }

    @Override
    public void batchSave(List<WpsSchedulePlanLog> logList, String factoryCode) {
        if(StringUtils.isBlank(factoryCode)){
            log.error("工厂编码为空,无法删除自动排产的异常日志");
            return;
        }
        deleteByFactoryCode(factoryCode);
        saveBatch(logList);
    }

    private Set<String> getFacotoryCodes(Long userId){
        if(Objects.isNull(userId)){
            return Sets.newHashSet();
        }
        return  plannerDataPermissionService.getFactoryCodeByUserId(userId);
    }
}
