package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WarningShipBookingUrgentAbnormal;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.req.ShipBookingUrgentReq;
import com.lds.oneplanning.wps.vo.ShipBookingUrgentVO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
public interface IWarningShipBookingUrgentAbnormalService extends IService<WarningShipBookingUrgentAbnormal> {

    /**
     * 查询未处理数据
     * @return
     */
    List<WarningShipBookingUrgentAbnormal> queryUnHandleData();

    Page<ShipBookingUrgentVO> queryPage(ViewSource source, ShipBookingUrgentReq req);
}
