package com.lds.oneplanning.wps.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EsbMainPoOrderDTO {
    /**
     * 是否剔除前导零
     */
    @JSONField(name = "I_NZERO")
    @JsonProperty("I_NZERO")
    private String removeZeroPrefix;

    /**
     * 是否获取长文本
     */
    @JSONField(name = "I_LTEXT")
    @JsonProperty("I_LTEXT")
    private String containLongText;

    /**
     * 保留已删除数据
     */
    @JSONField(name = "I_LVORM")
    @JsonProperty("I_LVORM")
    private String keepDeletedData;


    @JSONField(name = "PT_TABLE")
    @JsonProperty("PT_TABLE")
    private List<EsbMainPoOrder> data;

    public EsbMainPoOrderDTO(List<EsbMainPoOrder> data) {
        this.removeZeroPrefix = "true";
        this.keepDeletedData = "false";
        this.data = data;
    }
}
