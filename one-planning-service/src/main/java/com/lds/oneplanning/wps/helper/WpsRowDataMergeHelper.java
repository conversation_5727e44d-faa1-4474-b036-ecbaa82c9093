package com.lds.oneplanning.wps.helper;

import cn.hutool.core.bean.BeanUtil;
import com.lds.oneplanning.wps.model.WpsRowData;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@UtilityClass
public class WpsRowDataMergeHelper {

    /**
     * 合并具有相同订单号的WPS行数据列表
     * 
     * @param warningList 待合并的WpsRowData对象列表，可能为null或空
     * @return 合并后的WpsRowData列表。若原列表为空或无重复订单号时返回原列表的浅拷贝
     * 
     * 处理流程：
     * 1. 空列表直接返回
     * 2. 统计各订单号出现次数
     * 3. 无重复订单时返回浅拷贝
     * 4. 按订单号分组处理
     * 5. 对重复订单执行合并操作
     */
    public List<WpsRowData> mergeSameOrder(List<WpsRowData> warningList) {
        if (warningList == null || warningList.isEmpty()) {
            return warningList;
        }

        /*
         * 统计每个订单号的出现次数
         * 使用stream分组统计，性能优于循环遍历
         * key: 订单号, value: 出现次数
         */
        final Map<String, Long> orderCountMap = warningList.stream()
                .collect(Collectors.groupingBy(WpsRowData::getOrderNo, Collectors.counting()));

        if (orderCountMap.values().stream().noneMatch(count -> count > 1)) {
            return new ArrayList<>(warningList);
        }

        /*
         * 按订单号分组存储原始数据
         * 为后续合并操作准备数据结构
         * key: 订单号, value: 该订单号对应的数据列表
         */
        final Map<String, List<WpsRowData>> groupedOrders = warningList.stream()
                .collect(Collectors.groupingBy(WpsRowData::getOrderNo));

        final List<WpsRowData> result = new ArrayList<>();

        groupedOrders.forEach((orderNo, orders) -> {
            /*
             * 合并重复订单的核心逻辑
             * 1. 复制第一个元素的属性作为基准
             * 2. 逐个合并后续元素数据
             * 3. 合并结果存入返回列表
             */
            if (orders.size() == 1) {
                result.add(orders.get(0));
            } else {
                WpsRowData merged = new WpsRowData();
                BeanUtil.copyProperties(orders.get(0), merged);
                for (int i = 1; i < orders.size(); i++) {
                    mergeOrderData(orders.get(i), merged);
                }
                result.add(merged);
            }
        });

        return result;
    }


    /**
     * 合并订单数据
     *
     * @param source 源WpsRowData对象
     * @param target 目标WpsRowData对象
     */
    private void mergeOrderData(WpsRowData source, WpsRowData target) {
        //上线时间
        target.setOnlineTime(minDate(source.getOnlineTime(), target.getOnlineTime()));

        Map<LocalDate, Number> scheduleDataMap = source.getScheduleDataMap();
        Map<LocalDate, Number> targetScheduleDataMap = target.getScheduleDataMap();
        target.setScheduleDataMap(mergeScheduleDataMap(scheduleDataMap, targetScheduleDataMap));

        Set<String> lackOfMaterialIds = source.getLackOfMaterialIds();
        Set<String> targetLackOfMaterialIds = target.getLackOfMaterialIds();
        target.setLackOfMaterialIds(mergeSet(lackOfMaterialIds, targetLackOfMaterialIds));

        Set<String> lackOfInfoMaterialIds = source.getLackOfInfoMaterialIds();
        Set<String> targetLackOfInfoMaterialIds = target.getLackOfInfoMaterialIds();
        target.setLackOfInfoMaterialIds(mergeSet(lackOfInfoMaterialIds, targetLackOfInfoMaterialIds));
    }

    /**
     * 将两个字符串集合合并为一个新的集合
     *
     * @param source 源集合
     * @param target 目标集合
     * @return 合并后的新集合
     */
    private static Set<String> mergeSet(Set<String> source, Set<String> target) {
        if (CollectionUtils.isEmpty(source)) {
            return target;
        }
        if (CollectionUtils.isEmpty(target)) {
            return source;
        }
        Set<String> result = new HashSet<>();
        result.addAll(source);
        result.addAll(target);
        return result;
    }

    /**
     * 合并两个Map<LocalDate, Number>类型的数据，将两个Map中的数据按LocalDate键合并，并将对应的Number值相加。
     *
     * @param d1 第一个待合并的Map
     * @param d2 第二个待合并的Map
     * @return 合并后的Map
     */
    private Map<LocalDate, Number> mergeScheduleDataMap(Map<LocalDate, Number> d1, Map<LocalDate, Number> d2) {
        if (MapUtils.isEmpty(d1)) {
            return d2;
        }
        if (MapUtils.isEmpty(d2)) {
            return d1;
        }

        Map<LocalDate, Number> result = new TreeMap<>();

        for (Map.Entry<LocalDate, Number> entry : d1.entrySet()) {
            if (!result.containsKey(entry.getKey())) {
                result.put(entry.getKey(), entry.getValue());
            }
            Number num1 = result.get(entry.getKey());
            Number num2 = entry.getValue();
            result.put(entry.getKey(), add(num1, num2));
        }

        return result;
    }

    /**
     * 将两个数字相加并返回结果。
     *
     * @param n1 第一个数字
     * @param n2 第二个数字
     * @return 两个数字相加的结果
     */
    private Number add(Number n1, Number n2) {
        if (n1 == null) {
            return n2;
        }
        if (n2 == null) {
            return n1;
        }
        if (n1 instanceof Double || n2 instanceof Double) {
            return n1.doubleValue() + n2.doubleValue();
        }
        if (n1 instanceof Float || n2 instanceof Float) {
            return n1.floatValue() + n2.floatValue();
        }
        if (n1 instanceof Long || n2 instanceof Long) {
            return n1.longValue() + n2.longValue();
        }
        return n1.intValue() + n2.intValue();
    }

    /**
     * 返回两个日期中较早的日期。
     *
     * @param d1 第一个日期
     * @param d2 第二个日期
     * @return 返回较早的日期。如果d1为null，则返回d2；如果d2为null，则返回d1；否则返回较早的日期。
     */
    private Date minDate(Date d1, Date d2) {
        if (d1 == null) {
            return d2;
        }
        if (d2 == null) {
            return d1;
        }
        return d1.before(d2) ? d1 : d2;
    }
}
