package com.lds.oneplanning.wps.helper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.entity.LineCapacity;
import com.lds.oneplanning.basedata.entity.LineUph;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.model.OrderScheduleDTO;
import com.lds.oneplanning.wps.model.OrderWarningDTO;
import com.lds.oneplanning.wps.model.TimeQtyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuang<PERSON><PERSON><PERSON>
 * @Email: zhuang<PERSON><PERSON><PERSON>@leedarson.com
 * @Date: 2025/5/20 11:52
 */
@Slf4j
@Service
public class ScheduleViewHelper {


    /**
     * 一个线体内的 多个订单
     * 排产视图按白夜班map：日期-白夜班-订单-(工时，数量)
     * @param orders
     * @return
     */
    public Map<LocalDate, Map<Integer, Map<String, TimeQtyDTO>>> getScheduleDayShiftMap(String lineUUid,List<OrderScheduleDTO> orders,
                                                                                        LocalDate startDate, LocalDate endDate,Map<String,Number> lineCapacityContext,
                                                                                        Map<String, List<LineUph>> uphMap,Map<String, List<LineCapacity>> capacityMap,Map<String,Integer> frozenMap ) {
        // 数据预热，保证日期和班次的map已有值
        Map<LocalDate, Map<Integer, Map<String, TimeQtyDTO>>> resMap = Maps.newLinkedHashMap();
        LocalDate index = startDate;
        while (index.compareTo(endDate)<=0){
            //白班
            Map<String, TimeQtyDTO> moringMap = Maps.newLinkedHashMap();
            //夜班
            Map<String, TimeQtyDTO> nightMap = Maps.newLinkedHashMap();
            Map<Integer, Map<String, TimeQtyDTO>> dayShiftMap = Maps.newLinkedHashMap();
            dayShiftMap.put(1,moringMap);
            dayShiftMap.put(2,nightMap);
            resMap.put(index,dayShiftMap);
            index = index.plusDays(1);
        }
        if (lineUUid == null) {
            return resMap;
        }
        //时长  分白夜班
        List<LineCapacity> lineCapacities = capacityMap.get(lineUUid);
        if(CollectionUtils.isEmpty(lineCapacities)){
            return resMap;
        }
        Map<LocalDate,List<LineCapacity>> capMap =  lineCapacities.stream().collect(Collectors.groupingBy(LineCapacity::getScheduleDate));
        // uph 分产品id
        List<LineUph> lineUphs = uphMap.get(lineUUid);
        Map<String,Float> productIdUphMap = lineUphs.stream().filter(lineUph -> StringUtils.isNotBlank(lineUph.getProductId())).collect(Collectors.toMap(LineUph::getProductId,LineUph::getUph,(aFloat, aFloat2) -> aFloat2));
        if (MapUtils.isEmpty(capMap) || MapUtils.isEmpty(productIdUphMap)) {
            return resMap;
        }
        float totalActualHours  = 0f;
        float totalFullHours  = 0f;
        // 外部是线体， 需要改为排产时长，和白天的时长来做比对
        Map<LocalDate,Float> morningHoursMap = Maps.newLinkedHashMap();

        for (OrderScheduleDTO  order : orders){
            String orderNo = order.getOrderNo();
            // 线体uph  针对某种产品
            float uph = Optional.ofNullable(productIdUphMap.get(order.getProductId())).orElse(1000f);
            Map<LocalDate, Number> scheduleDataMap = order.getScheduleDataMap();
            Integer frozenStatus =  Optional.ofNullable(frozenMap.get(orderNo)).orElse(0);
            int warningQty = CollectionUtils.isEmpty(order.getWarnings()) ? 0 :order.getWarnings().size();
            Map<Integer,Integer> warningLevelMap =  CollectionUtils.isEmpty(order.getWarnings()) ? Maps.newLinkedHashMap()
                    : order.getWarnings().stream().filter(orderWarningDTO -> orderWarningDTO.getWarningLevel()!=null)
                    .collect(Collectors.groupingBy(OrderWarningDTO::getWarningLevel, Collectors.collectingAndThen(Collectors.counting(),Long::intValue)));
            for (Map.Entry<LocalDate,Number> entry : scheduleDataMap.entrySet()){
                if ( entry.getValue() == null || entry.getValue().intValue() ==0) {
                    continue;
                }
                LocalDate scheduleDate = entry.getKey();
                // 订单一天要排的量
                int totalDayQty = entry.getValue().intValue();
                List<LineCapacity> capacityList = Optional.ofNullable(capMap.get(scheduleDate)).orElse(Lists.newArrayList());
                // 白夜班时间，针对日期
                float fullMorningHours = 1.00f * capacityList.stream().filter(lineCapacity -> lineCapacity.getDayType().equals(1))
                        .map(LineCapacity::getProductHours).findAny().orElse(11);
                float fullNightHours = 1.00f * capacityList.stream().filter(lineCapacity -> lineCapacity.getDayType().equals(2))
                        .map(LineCapacity::getProductHours).findAny().orElse(11);
                // 白天可完成的量
                float morningFullQty = fullMorningHours * uph ;
                // 当天已经花费的工时
                float spendHours = morningHoursMap.getOrDefault(scheduleDate,0f);
                // 同一天白班剩余可以排的小时数  ,初始状态就是白天满编 如11h
                float morningLeftHours = Math.max(fullMorningHours - spendHours, 0f);
                // 使用剩余时间计算，当早上时间用完，这个值是0
                float  morningLeftQty = uph * morningLeftHours;

                // 当天实际排产大于早班满排数量，则早班使用满编，否则早班实际排产等于当天全部排产
                Float morningActualQty = (totalDayQty > morningLeftQty ? morningLeftQty : totalDayQty);

                // 当天实际排产大于早班满排数量，晚班数量等于实际排产-早班排产，否则晚班设置0
                Float nightActualQty = (totalDayQty > morningActualQty ? totalDayQty - morningActualQty : 0);
                // 天，班次，订单，数量
                Map<Integer, Map<String, TimeQtyDTO>> dayTypeQtyMap = resMap.get(scheduleDate);
                Map<String, TimeQtyDTO>  moringQtyMap = dayTypeQtyMap.get(1);
                Map<String, TimeQtyDTO>  nightQtyMap = dayTypeQtyMap.get(2);
                // 计算实际生产时间
                float actualMorningHours = morningActualQty/uph;
                float actualNightHours = nightActualQty/uph;

                totalActualHours = totalActualHours+actualMorningHours+actualNightHours;
                totalFullHours = totalFullHours+fullMorningHours+fullNightHours;
                if (morningActualQty+nightActualQty <= 0f) {
                    // 订单当天没有排产数量 ，不参与
                    continue;
                }
                if (morningActualQty > 0f) {
                    moringQtyMap.put(orderNo, TimeQtyDTO.builder().scheduleHours(actualMorningHours).totalHours(fullMorningHours)
                            .qty(morningActualQty.intValue()).frozenStatus(frozenStatus).warningQty(warningQty)
                            .warningLevelMap(warningLevelMap).orderProcessStatus(order.getOrderProcessStatus()).build());
                }
                if (nightActualQty >0f) {
                    nightQtyMap.put(orderNo, TimeQtyDTO.builder().scheduleHours(actualNightHours).totalHours(fullNightHours)
                            .qty(nightActualQty.intValue()).frozenStatus(frozenStatus).warningQty(warningQty)
                            .warningLevelMap(warningLevelMap).orderProcessStatus(order.getOrderProcessStatus()).build());
                }
                // 改为每天可以操作的小时数
                morningHoursMap.merge(scheduleDate,actualMorningHours, Float::sum);
            }
        }
        log.info("线体排产日期map,uuid ={},排产map={}",lineUUid,morningHoursMap);
        // 计算百分比
        BigDecimal percentage=  new BigDecimal(totalActualHours * 100).divide(new BigDecimal(totalFullHours),2, RoundingMode.UP);
        lineCapacityContext.put(lineUUid,percentage);
        return resMap;
    }
    /**
     * 排产视图按天分map：日期-订单-数量
     * @param orders
     * @return
     */
    public Map<LocalDate, Map<String, TimeQtyDTO>> getScheduleDayMap(String lineUUid,List<OrderScheduleDTO> orders,LocalDate startDate,LocalDate endDate,
                                                                     Map<String, List<LineUph>> uphMap,Map<String, List<LineCapacity>> capacityMap,Map<String,Integer> frozenMap) {
        Map<LocalDate, Map<Integer, Map<String, TimeQtyDTO>>> scheduleDayShiftMap = this.getScheduleDayShiftMap(lineUUid,orders, startDate, endDate,Maps.newHashMap(),uphMap, capacityMap,frozenMap);
        Map<LocalDate, Map<String, TimeQtyDTO>> resMap = Maps.newLinkedHashMap();
        List<LineCapacity> lineCapacities = capacityMap.get(lineUUid);
        Map<LocalDate,List<LineCapacity>> capMap =  lineCapacities.stream().collect(Collectors.groupingBy(LineCapacity::getScheduleDate));
        for(Map.Entry<LocalDate,Map<Integer, Map<String, TimeQtyDTO>>> entry : scheduleDayShiftMap.entrySet()){
            LocalDate scheduleDate = entry.getKey();
            Map<Integer, Map<String, TimeQtyDTO>>  scheduleShiftMap = entry.getValue();
            List<LineCapacity> capacityList = Optional.ofNullable(capMap.get(scheduleDate)).orElse(Lists.newArrayList());
            // 白夜班时间，针对日期 计算当天总时长
            float fullMorningHours = 1.00f * capacityList.stream().filter(lineCapacity -> lineCapacity.getDayType().equals(1))
                    .map(LineCapacity::getProductHours).findAny().orElse(11);
            float fullNightHours = 1.00f * capacityList.stream().filter(lineCapacity -> lineCapacity.getDayType().equals(2))
                    .map(LineCapacity::getProductHours).findAny().orElse(11);

            Map<String, TimeQtyDTO> sumMap = Maps.newLinkedHashMap();
            for (Map.Entry<Integer, Map<String, TimeQtyDTO>>  scheduleShiftEntry : scheduleShiftMap.entrySet()){
                Map<String, TimeQtyDTO> orderQtyMap = scheduleShiftEntry.getValue();
                for (Map.Entry<String,TimeQtyDTO> orderQtyEntry : orderQtyMap.entrySet()){
                    String orderNo = orderQtyEntry.getKey();
                    TimeQtyDTO value = orderQtyEntry.getValue();
                    // 修改时长为全天，否则没有merge的时候不会触发合并
                    value.setTotalHours(fullMorningHours+fullNightHours);
                    sumMap.merge(orderNo, value, (oldVal, newVal) -> TimeQtyDTO.builder().scheduleHours(oldVal.getScheduleHours()+newVal.getScheduleHours())
                            .totalHours(fullMorningHours+fullNightHours).qty(oldVal.getQty()+newVal.getQty())
                            .frozenStatus(oldVal.getFrozenStatus()).warningQty(oldVal.getWarningQty())
                            .warningLevelMap(oldVal.getWarningLevelMap()).orderProcessStatus(oldVal.getOrderProcessStatus()).build());
                }
            }
            resMap.put(scheduleDate,sumMap);
        }
        return resMap;
    }

    /**
     * 排产视图按半周分map：周-上下半周-订单-数量
     * @param orders
     * @return
     */
    public Map<String, Map<List<LocalDate>, Map<String, TimeQtyDTO>>> getScheduleWeekMap(String lineUUid,List<OrderScheduleDTO> orders,LocalDate startDate,LocalDate endDate,
                                                                                         Map<String, List<LineUph>> uphMap,Map<String, List<LineCapacity>> capacityMap,Map<String,Integer> frozenMap) {
        Map<LocalDate, Map<String, TimeQtyDTO>> scheduleDayMap = this.getScheduleDayMap(lineUUid,orders, startDate, endDate,uphMap, capacityMap,frozenMap);

        int startSeq = LocalDateTimeUtil.getWeekSeqOfYear(startDate);
        int endSeq = LocalDateTimeUtil.getWeekSeqOfYear(endDate);
        LocalDate index = startDate;
        List<LocalDate> localDates = Lists.newArrayList();
        while (index.compareTo(endDate) <= 0) {
            localDates.add(index);
            index = index.plusDays(1);
        }
        Map<String, Map<List<LocalDate>, Map<String, TimeQtyDTO>>> resMap = Maps.newLinkedHashMap();
        for (int i = startSeq; i < endSeq+1; i++) {
            List<LocalDate> upperHalfDays = this.getHalfWeek(localDates, i, 1);
            List<LocalDate> lowerHalfDays = this.getHalfWeek(localDates, i, 2);
            Map<List<LocalDate>, Map<String, TimeQtyDTO>> upperMap = Maps.newLinkedHashMap();
            upperMap.put(upperHalfDays,this.getHalfWeekScheduleMap(scheduleDayMap, upperHalfDays,lineUUid, capacityMap));
            Map<List<LocalDate>, Map<String, TimeQtyDTO>> lowereMap = Maps.newLinkedHashMap();
            lowereMap.put(lowerHalfDays,this.getHalfWeekScheduleMap(scheduleDayMap,lowerHalfDays,lineUUid, capacityMap));
            String rangeDescUpper = this.getRangeDesc(upperHalfDays);
            if (rangeDescUpper != null) {
                resMap.put("WK"+i+"("+rangeDescUpper+")",upperMap);
            }
            String rangeDescLower = this.getRangeDesc(lowerHalfDays);
            if (rangeDescLower != null) {
                resMap.put("WK"+i+"("+rangeDescLower+")",lowereMap);
            }
        }
        return resMap;
    }

    private Map<String, TimeQtyDTO> getHalfWeekScheduleMap(Map<LocalDate, Map<String, TimeQtyDTO>> scheduleDayMap, List<LocalDate> targetDays,String lineUUid,Map<String, List<LineCapacity>> capacityMap) {
        Set<LocalDate> targetDaySet = new HashSet<>(targetDays); // 提高 contains 性能
        Map<String, TimeQtyDTO> resMap = new LinkedHashMap<>();
        List<LineCapacity> lineCapacities = Optional.ofNullable(capacityMap.get(lineUUid)).orElse(Lists.newArrayList());
        Map<LocalDate,List<LineCapacity>> capMap =  lineCapacities.stream().collect(Collectors.groupingBy(LineCapacity::getScheduleDate));
        float totalHours = 0f;
        // 计算三天总时长
        for (LocalDate localDate: targetDaySet){
            List<LineCapacity> capacityList = Optional.ofNullable(capMap.get(localDate)).orElse(Lists.newArrayList());
            // 白夜班时间，针对日期 计算当天总时长
            float fullMorningHours = 1.00f * capacityList.stream().filter(lineCapacity -> lineCapacity.getDayType().equals(1))
                    .map(LineCapacity::getProductHours).findAny().orElse(11);
            float fullNightHours = 1.00f * capacityList.stream().filter(lineCapacity -> lineCapacity.getDayType().equals(2))
                    .map(LineCapacity::getProductHours).findAny().orElse(11);
            totalHours = totalHours +fullMorningHours+fullNightHours;
        }

        for (Map.Entry<LocalDate, Map<String, TimeQtyDTO>> dateEntry : scheduleDayMap.entrySet()) {
            LocalDate scheduleDate = dateEntry.getKey();
            if (targetDaySet.contains(scheduleDate)) {
                float finalTotalHours = totalHours;
                dateEntry.getValue().forEach((key, value) -> {
                    value.setTotalHours(finalTotalHours);
                    resMap.merge(key, value, (oldVal, newVal) -> TimeQtyDTO.builder().scheduleHours(oldVal.getScheduleHours()+newVal.getScheduleHours())
                            .totalHours(finalTotalHours).qty(oldVal.getQty()+newVal.getQty())
                            .frozenStatus(oldVal.getFrozenStatus()).warningQty(oldVal.getWarningQty())
                            .warningLevelMap(oldVal.getWarningLevelMap()).orderProcessStatus(oldVal.getOrderProcessStatus()).build());
                });
            }
        }
        return resMap;
    }
    private List<LocalDate> getHalfWeek(List<LocalDate> localDates,Integer weekSeq,Integer half ){
        List<LocalDate> weekDateList = localDates.stream().filter(localDate -> weekSeq.equals(LocalDateTimeUtil.getWeekSeqOfYear(localDate))).collect(Collectors.toList());
        if (half.equals(1)){
            // 上半周
            return weekDateList.stream().filter(localDate -> localDate.getDayOfWeek().getValue() <=3).collect(Collectors.toList());
        }else{
            return weekDateList.stream().filter(localDate -> localDate.getDayOfWeek().getValue() >3 && localDate.getDayOfWeek().getValue() < 7  ).collect(Collectors.toList());
        }
    }

    public  String getRangeDesc(List<LocalDate> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return null;
        }
        // 前4位年份加一个横杠去掉
        return sourceList.get(0).toString().substring(5)+"~"+sourceList.get(sourceList.size()-1).toString().substring(5);
    }
}
