package com.lds.oneplanning.wps.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDate;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lds.oneplanning.wps.enums.LightColor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WarningFrozenUnfrozenAbnormal对象", description="")
public class WarningFrozenUnfrozenAbnormal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "销售订单号")
    private String salesOrderNumber;

    @ApiModelProperty(value = "销售订单行项目")
    private String lineNumber;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "工厂")
    private String factory;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "计划上线时间，YYYY-MM-DD")
    private LocalDate plannedOnlineTime;

    @ApiModelProperty(value = "距离计划剩余天数")
    private Integer remainingDaysToPlan;

    @ApiModelProperty(value = "灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "产品组编码")
    private String productGroupCode;

    @ApiModelProperty(value = "是否触发待办到责任人, 0：是，1：否")
    private String sfcfdbdzrr;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "NPI人员")
    private String npiPerson;

    @ApiModelProperty(value = "NPI人员工号")
    private String npiPersonGh;

    @ApiModelProperty(value = "NPI人员userid")
    private String npiPersonUserid;

    @ApiModelProperty(value = "QPL人员")
    private String qplPerson;

    @ApiModelProperty(value = "QPL人员工号")
    private String qplPersonGh;

    @ApiModelProperty(value = "QPL人员userid")
    private String qplPersonUserid;


}
