package com.lds.oneplanning.wps.enums;

/**
 * 排产方向枚举
 */
public enum ScheduleDirectionEnum {
    Forward(1, "正向排产"),
    Backward(-1, "逆向排产"),
    Backward2(-2, "逆向排产2");

    private ScheduleDirectionEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
