package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lds.oneplanning.wps.entity.WarningQualityHistoryRisk;
import com.lds.oneplanning.wps.req.QualityHistoryRiskReq;
import com.lds.oneplanning.wps.vo.QualityHistoryRiskVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/2
 */
public interface WarningQualityHistoryRiskMapper extends BaseMapper<WarningQualityHistoryRisk>{

    /**
     * 查询列表
     * @param req
     * @return
     */
    List<QualityHistoryRiskVO> findList(QualityHistoryRiskReq req);
}
