package com.lds.oneplanning.wps.strategy.concrete;

import cn.hutool.core.date.StopWatch;
import com.google.common.collect.Lists;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.filter.read.WpsOrderReadFilter;
import com.lds.oneplanning.wps.filter.write.WpsOrderWriteFilter;
import com.lds.oneplanning.wps.filter.write.impl.*;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/4/7 9:57
 */
@Slf4j
@Service
public class AutoScheduleStrategy implements WpsOrderProcessStrategy {

    @Resource
    private  List<WpsOrderReadFilter> orderReadFilterList;
    @Resource
    private PlannerInfoWriteFilter plannerInfoWriteFilter; //  0
    @Resource
    private FormInfoWriteFilter formInfoWriteFilter; //  1
    @Resource
    private ProductGroupCodeWriterFilter productGroupCodeWriterFilter; //  2
    @Resource
    private PrepareScheduleWriteFilter prepareScheduleWriteFilter; //  3
    @Resource
    private WeekPlanQtyWriteFilter weekPlanQtyWriteFilter; //  4
    @Resource
    private AutoScheduleWriteFilter autoScheduleWriteFilter; //  5
    @Resource
    private AtpCheckWriteFilter atpCheckWriteFilter; //  8

    private final List<WpsOrderWriteFilter> wpsOrderWriteFilterList = Lists.newArrayList();

    @PostConstruct
    private void init(){
        // 排序放在 初始化方法中
        orderReadFilterList.sort(Comparator.comparing(WpsOrderReadFilter::filterSeq));

        wpsOrderWriteFilterList.add(plannerInfoWriteFilter);
        wpsOrderWriteFilterList.add(formInfoWriteFilter);
        wpsOrderWriteFilterList.add(productGroupCodeWriterFilter);
        wpsOrderWriteFilterList.add(prepareScheduleWriteFilter);
        wpsOrderWriteFilterList.add(weekPlanQtyWriteFilter);
        wpsOrderWriteFilterList.add(autoScheduleWriteFilter);
        wpsOrderWriteFilterList.add(atpCheckWriteFilter);
        //
        wpsOrderWriteFilterList.sort(Comparator.comparing(WpsOrderWriteFilter::filterSeq));

    }

    @Override
    public List<WpsRowData> process(Long userId, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag, Map<String,Object> params) {
        StopWatch stopWatch4Filter = new StopWatch();
        // 基础过滤
        for (WpsOrderReadFilter filter : orderReadFilterList){
            String filterName = filter.getClass().getName();
            if (filterName.equals("com.lds.oneplanning.wps.filter.read.impl.AtpCheckReadFilter")) {
                // AtpCheckReadFilter  apt严格要在排产后才能计算是否信息齐套，
                continue;
            }
            stopWatch4Filter.start(String.format("业务过滤器%s",filterName));
            dirtyList  = filter.filter(userId, factoryCode, dirtyList,cacheFlag);
            stopWatch4Filter.stop();
            log.info(String.format("业务过滤器%s",filterName)+"耗时{}",stopWatch4Filter.prettyPrint(TimeUnit.MILLISECONDS ));
        }
        // 回写过滤
        StopWatch stopWatchWriteFilter = new StopWatch();
        for (WpsOrderWriteFilter filter : wpsOrderWriteFilterList){
            String filterName = filter.getClass().getName();
            stopWatchWriteFilter.start(String.format("业务过滤器%s",filterName));
            dirtyList  = filter.filter(userId, WpsConstants.DATA_SOURCE_AUTO, factoryCode, dirtyList,cacheFlag,params);
            stopWatchWriteFilter.stop();
            log.info(String.format("业务过滤器%s",filterName)+"耗时{}",stopWatchWriteFilter.prettyPrint(TimeUnit.MILLISECONDS ));
        }

        return  dirtyList;
    }

    @Override
    public List<WpsOrderReadFilter> listReadFilter() {
        return orderReadFilterList;
    }
    @Override
    public List<WpsOrderWriteFilter> listWriteFilter() {
        return wpsOrderWriteFilterList;
    }
}
