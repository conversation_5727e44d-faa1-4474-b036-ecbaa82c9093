package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iot.common.exception.BusinessException;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.service.ILineUphService;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderAndon;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderProcedure;
import com.lds.oneplanning.wps.entity.WarningInProductionAbnormal;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.exception.WpsExceptionEnum;
import com.lds.oneplanning.wps.mapper.WarningInProductionAbnormalMapper;
import com.lds.oneplanning.wps.model.InProductionAbnormalDTO;
import com.lds.oneplanning.wps.req.InProductionAbnormalReq;
import com.lds.oneplanning.wps.req.TodoHandleReq;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderAndonService;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderProcedureService;
import com.lds.oneplanning.wps.service.IWarningInProductionAbnormalService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.vo.InProductionAbnormalVO;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Service
public class WarningInProductionAbnormalServiceImpl extends ServiceImpl<WarningInProductionAbnormalMapper, WarningInProductionAbnormal> implements IWarningInProductionAbnormalService {
    @Resource
    private IMesProcessWorkOrderAndonService mesProcessWorkOrderAndonService;
    @Resource
    private IMesProcessWorkOrderProcedureService mesProcessWorkOrderProcedureService;
    @Resource
    private WarningTodoListService warningTodoListService;
    @Resource
    private ILineUphService lineUphService;

    @Override
    public void saveOrUpdate(List<WarningInProductionAbnormal> saveList) {
        List<String> orderNoList = saveList.stream().map(WarningInProductionAbnormal::getOrderNo)
                .collect(Collectors.toList());
        List<WarningInProductionAbnormal> editList = Lists.newArrayList();
        Map<String, WarningInProductionAbnormal> existMap = listByOrderNo(orderNoList).stream()
                .collect(Collectors.toMap(WarningInProductionAbnormal::getOrderNo, v -> v, (v1, v2) -> v1));
        //移除已存在异常列表
        saveList.removeIf(
                abnormal -> {
                    abnormal.setId(null);
                    WarningInProductionAbnormal warningInProductionAbnormal = existMap.get(abnormal.getOrderNo());
                    if (Objects.nonNull(warningInProductionAbnormal)) {
                        warningInProductionAbnormal.setInboundQuantity(abnormal.getInboundQuantity());
                        warningInProductionAbnormal.setUpdateBy(abnormal.getUpdateBy());
                        warningInProductionAbnormal.setUpdateTime(abnormal.getUpdateTime());
                        warningInProductionAbnormal.setLightColor(abnormal.getLightColor());
                        editList.add(warningInProductionAbnormal);
                        return true;
                    }
                    return false;
                }
        );
        if (CollUtil.isNotEmpty(saveList)) {
            saveBatch(saveList);
        }
        if (CollUtil.isNotEmpty(editList)) {
            updateBatchById(editList);
        }

    }

    @Override
    public List<WarningInProductionAbnormal> listByOrderNo(List<String> orderNoList) {
        return baseMapper.selectList(Wrappers.lambdaQuery(WarningInProductionAbnormal.class)
                .in(WarningInProductionAbnormal::getOrderNo, orderNoList)
        );
    }


    @Override
    public <T> Page<T> queryPage(InProductionAbnormalReq req, Class<T> clazz) {
        Page<T> result = new Page<>();
        PageHelper.startPage(req.getPage(), req.getPageSize());
        PageInfo<InProductionAbnormalDTO> pageInfo = new PageInfo<>(baseMapper.findList(req));
        if (CollUtil.isNotEmpty(pageInfo.getList())) {
            List<InProductionAbnormalDTO> dtoList = buildResultList(pageInfo);
            result.setResult(BeanUtil.copyToList(dtoList, clazz));
        }
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(req.getPage());
        result.setPageSize(req.getPageSize());
        return result;
    }

    private List<InProductionAbnormalDTO> buildResultList(PageInfo<InProductionAbnormalDTO> pageInfo) {
        List<InProductionAbnormalDTO> dtoList = pageInfo.getList();
        List<String> workOrderNumberList = dtoList.stream()
                .map(InProductionAbnormalDTO::getWorkOrderNo).collect(Collectors.toList());
        Map<String, List<MesProcessWorkOrderAndon>> andonMap = mesProcessWorkOrderAndonService.findMapByWorkOrderNumber(workOrderNumberList);
        Map<String, List<MesProcessWorkOrderProcedure>> procedureMap = mesProcessWorkOrderProcedureService.findMapByWorkOrderNumber(workOrderNumberList);
        Map<String, Float> lineUphMap = getLineUphMap(dtoList);
        dtoList.forEach(item -> {
            List<MesProcessWorkOrderAndon> andonList = andonMap.get(item.getWorkOrderNo());
            item.setAndonList(andonList);
            item.setSuggestReplanDate(calculateSuggestReplanDate(andonList));
            if (item.getEstimatedCompletionDate() == null) {
                item.setEstimatedCompletionDate(item.getSuggestReplanDate());
            }
            //计算在制天数
            if (item.getMaterialOffShelfDate() != null) {
                item.setProductionDays((int) ChronoUnit.DAYS.between(item.getMaterialOffShelfDate(), LocalDate.now()));
            }
            //计算actualInputQuantityGap
            if (item.getPlannedQuantity() != null && item.getActualInputQuantity() != null) {
                item.setActualInputQuantityGap(item.getPlannedQuantity() - item.getActualInputQuantity());
            }
            if (item.getPlannedQuantity() != null && item.getActualReportingQuantity() != null) {
                item.setActualReportedQuantityGap(item.getPlannedQuantity() - item.getActualReportingQuantity());
            }
            //预计可完工日期
            if (item.getAdjustPlanDate() == null) {
                item.setAdjustPlanDate(item.getSuggestReplanDate());
            }
            //计划预计可完工日期
            if (item.getAdjustPlanDate() != null && item.getPcEstimatedCompletionDate() == null
                    && StringUtils.isNotBlank(item.getLineUuid()) && StringUtils.isNotBlank(item.getMaterialId())) {
                Float uph = lineUphMap.get(item.getLineUuid() + ":" + item.getMaterialId());
                if (uph != null) {
                    //计划数量/uph,去除余数
                    int i = item.getPlannedQuantity() / uph.intValue();
                    item.setPcEstimatedCompletionDate(item.getAdjustPlanDate().plusDays(i));
                }
            }
            item.setProcedureList(procedureMap.get(item.getWorkOrderNo()));
        });
        return dtoList;
    }

    private Map<String, Float> getLineUphMap(List<InProductionAbnormalDTO> dtoList) {
        List<String> lineUUids = Lists.newArrayList();
        List<String> productIds = Lists.newArrayList();

        dtoList.stream().filter(item -> item.getPcEstimatedCompletionDate() == null
                        && StringUtils.isNotBlank(item.getLineUuid()) && StringUtils.isNotBlank(item.getMaterialId()))
                .forEach(item -> {
                    lineUUids.add(item.getLineUuid());
                    productIds.add(item.getMaterialId());
                });
        if (CollUtil.isEmpty(lineUUids) || CollUtil.isEmpty(productIds)) {
            return Maps.newHashMap();
        }
        return lineUphService.findMap(lineUUids, productIds);
    }

    private LocalDate calculateSuggestReplanDate(List<MesProcessWorkOrderAndon> andonList) {
        if (CollUtil.isNotEmpty(andonList)) {
            Date estimatedResolveTime = andonList.get(andonList.size() - 1).getEstimatedResolveTime();
            //如果存在预计关闭时间，则建议再计划日期=预计关闭时间+1天
            if (estimatedResolveTime != null) {
                Date date = DateUtils.addDays(estimatedResolveTime, 1);
                return LocalDateTimeUtil.of(date).toLocalDate();
            } else {
                return null;
            }
        }
        return LocalDate.now();
    }

    @Override
    public void updateData(InProductionAbnormalVO vo) {
        Long userId = UserContextUtils.getUserId();
        //只能更新责任人回复，
        Long id = vo.getId();
        if (id == null) {
            throw new BusinessException(WpsExceptionEnum.ID_CANNOT_BE_EMPTY);
        }
        WarningInProductionAbnormal editEntity = new WarningInProductionAbnormal();
        editEntity.setId(id);
        editEntity.setAffectsUpperLevelPlan(vo.getAffectsUpperLevelPlan());
        editEntity.setImpactType(vo.getImpactType());
        editEntity.setInsufficientOrderReason(vo.getInsufficientOrderReason());
        editEntity.setReasonCategory(vo.getReasonCategory());
        editEntity.setEstimatedCompletionDate(vo.getEstimatedCompletionDate());
        editEntity.setAdjustPlanDate(vo.getAdjustPlanDate());
        editEntity.setPcEstimatedCompletionDate(vo.getPcEstimatedCompletionDate());
        editEntity.setUpdateTime(new Date());
        editEntity.setUpdateBy(userId);
        super.updateById(editEntity);
        WarningInProductionAbnormal entity = super.getById(id);
        if (entity == null) {
            throw new BusinessException(WpsExceptionEnum.RECORD_NOT_EXIST);
        }
        //满足条件，处理待办
        if (ObjectUtils.isNotNull(entity.getEstimatedCompletionDate()) &&
                ObjectUtils.isNotNull(entity.getInsufficientOrderReason()) &&
                ObjectUtils.isNotNull(entity.getReasonCategory())) {
            TodoHandleReq todoHandleReq = new TodoHandleReq();
            todoHandleReq.setType(WpsOrderWarningTypeEnum.IN_PRODUCTION_EXCEPTION);
            todoHandleReq.setIds(Lists.newArrayList(id));
            warningTodoListService.handle(todoHandleReq);
        }
    }
}
