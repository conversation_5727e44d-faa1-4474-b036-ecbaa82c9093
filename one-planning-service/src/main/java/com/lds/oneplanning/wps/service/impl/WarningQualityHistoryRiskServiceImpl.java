package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.entity.WarningQualityHistoryRisk;
import com.lds.oneplanning.wps.entity.WpsDayPlan;
import com.lds.oneplanning.wps.mapper.WarningQualityHistoryRiskMapper;
import com.lds.oneplanning.wps.req.QualityHistoryRiskReq;
import com.lds.oneplanning.wps.service.IWarningQualityHistoryRiskService;
import com.lds.oneplanning.wps.service.IWpsDayPlanService;
import com.lds.oneplanning.wps.vo.QualityHistoryRiskVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/2
 */
@Service
public class WarningQualityHistoryRiskServiceImpl extends ServiceImpl<WarningQualityHistoryRiskMapper, WarningQualityHistoryRisk> implements IWarningQualityHistoryRiskService {

    @Resource
    private IWpsDayPlanService wpsDayPlanService;

    @Override
    public Page<QualityHistoryRiskVO> queryPage(QualityHistoryRiskReq req) {
        Page<QualityHistoryRiskVO> result = new Page<>();
        PageHelper.startPage(req.getPage(), req.getPageSize());
        PageInfo<QualityHistoryRiskVO> pageInfo = new PageInfo<>(baseMapper.findList(req));
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(req.getPage());
        result.setPageSize(req.getPageSize());
        return result;
    }

    @Override
    public void saveOrUpdate(List<WarningQualityHistoryRisk> riskList) {
        if (CollUtil.isEmpty(riskList)) {
            return;
        }
        List<String> existOrderNoList = baseMapper.selectList(Wrappers.lambdaQuery(WarningQualityHistoryRisk.class)
                .in(WarningQualityHistoryRisk::getOrderNo, riskList.stream().map(WarningQualityHistoryRisk::getOrderNo)
                        .distinct().collect(Collectors.toList()))
        ).stream().map(WarningQualityHistoryRisk::getOrderNo).collect(Collectors.toList());
        riskList.removeIf(risk -> existOrderNoList.contains(risk.getOrderNo()));
        if (CollUtil.isNotEmpty(riskList)) {
            saveOrUpdate(riskList);
        }
    }


    public void execute() {
        //查询当前日期+14天内的相关生产订单数据
        List<WpsDayPlan> wpsDayPlans = wpsDayPlanService.listByDates(LocalDate.now(), LocalDate.now().plusDays(14));
        if (CollUtil.isEmpty(wpsDayPlans)) {
            return;
        }
        List<String> orderNoList = wpsDayPlans.stream().map(WpsDayPlan::getBizId).distinct().collect(Collectors.toList());

    }
}
