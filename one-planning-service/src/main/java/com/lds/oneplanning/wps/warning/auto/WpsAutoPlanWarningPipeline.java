package com.lds.oneplanning.wps.warning.auto;

import cn.hutool.core.date.StopWatch;
import com.google.common.collect.Lists;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.warning.auto.handlers.IWpsAutoPlanWarningHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WpsAutoPlanWarningPipeline {

    private final List<IWpsAutoPlanWarningHandler> handlers = Lists.newArrayList();

    @Autowired
    public WpsAutoPlanWarningPipeline(List<IWpsAutoPlanWarningHandler> pipelineHandlers) {
        handlers.addAll(pipelineHandlers);
        handlers.sort(Comparator.comparingInt(IWpsAutoPlanWarningHandler::getOrder));
    }

    public void execute(WpsAutoScheduleContext context) {
        StopWatch stopWatch = new StopWatch();
        for (IWpsAutoPlanWarningHandler handler : handlers) {
            String simpleName = handler.getClass().getSimpleName();
            stopWatch.start("WpsCheckCapacityWarningPipeline-" + simpleName);
            handler.execute(context);
            stopWatch.stop();
        }
        log.info("WpsCheckCapacityWarningPipeline,订单数量={}, 执行耗时={}.", context.getOrderList().size(),
                stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
    }

    public void execute(List<WpsRowData> rowDataList) {
        WpsAutoScheduleContext context = new WpsAutoScheduleContext(rowDataList);
        execute(context);
    }
}