package com.lds.oneplanning.wps.workbench.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class UnMatchOrderResp implements Serializable {

    private static final long serialVersionUID = 2533591261724714015L;

    /**
     * 客户
     */
    private String customer;

    /**
     * 工厂
     */
    private String factory;

    /**
     * 销售订单号
     */
    private String saleOrderNo;

    /**
     * 行项目号
     */
    private String lineItemNo;

    /**
     * 外向交货单
     */
    private String deliveryOrderNo;

    /**
     * 外向交货单行项目
     */
    private String deliveryOrderLineItemNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单数量
     */
    private Integer orderQuantity;

    /**
     * 入库数量
     */
    private Integer inStockQuantity;

    /**
     * 最迟排产日期
     */
    private LocalDate latestPlanDate;

    /**
     * 最终船期
     */
    private LocalDate finalShippingTime;

    /**
     * 处理状态
     */
    private Integer processStatus;
}