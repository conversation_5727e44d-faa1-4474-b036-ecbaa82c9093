package com.lds.oneplanning.wps.workbench.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OrderStatusResp implements Serializable {

    private static final long serialVersionUID = -341373302914430701L;

    /**
     * 时间段编码
     */
    private PeriodInfo periodInfo;

    /**
     * 订单总数
     */
    private Integer totalOrderNum;

    /**
     * 异常订单数
     */
    private Integer abnormalOrderNum;

    /**
     * 异常订单信息
     */
    private List<AbnormalOrderNumDetail> abnormalOrderNumDetails;
}