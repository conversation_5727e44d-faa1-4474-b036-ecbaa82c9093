package com.lds.oneplanning.wps.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class WpsSchedulePlanLogQueryDTO implements Serializable {


    private Integer pageNum;
    private Integer pageSize;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "错误码")
    private String errorCode;
    @ApiModelProperty(value = "销售订单号")
    private String sellOrderNo;

    private Date startTime;
    private Date endTime;

    private Long userId;
}
