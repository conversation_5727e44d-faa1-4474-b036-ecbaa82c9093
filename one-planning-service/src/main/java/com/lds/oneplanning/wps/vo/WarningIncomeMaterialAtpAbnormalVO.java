package com.lds.oneplanning.wps.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="来料异常检验计划人员表单", description="来料异常检验计划人员表单")
public class WarningIncomeMaterialAtpAbnormalVO extends AffectsPlan {

    private Long id;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "销售订单-行项目")
    private String xsddhxm;

    @ApiModelProperty(value = "订单id（商品id）")
    private String productId;

    @ApiModelProperty(value = "工厂")
    private String factory;

    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "计划日期")
    private LocalDate planDate;

    @ApiModelProperty(value = "计划数量")
    private Integer planQuantity;

    @ApiModelProperty(value = "影响数量")
    private Integer impactQuantity;

    @ApiModelProperty(value = "具体欠料ID")
    private String shortageMaterialId;

    @ApiModelProperty(value = "采购PO")
    private String purchasePo;

    @ApiModelProperty(value = "具体欠料描述")
    private String shortageMaterialDesc;

    @ApiModelProperty(value = "来料检验数量")
    private Integer checkQuantity;

    @ApiModelProperty(value = "最晚需求时间")
    private Date latestDemandTime;

    @ApiModelProperty(value = "处理结果")
    private String dealResult;

    @ApiModelProperty(value = "质检人员")
    private String qualityInspectors;

    @ApiModelProperty(value = "质检人员工号")
    private String qualityInspectorsGh;

    @ApiModelProperty(value = "采购人员")
    private String purchaser;

    @ApiModelProperty(value = "采购人员工号")
    private String purchaserGh;

    @ApiModelProperty(value = "下一批到料日期")
    private String nextArrivalDate;

    @ApiModelProperty(value = "最早可再计划日期")
    private String earliestPlanDate;

    @ApiModelProperty(value = "确定再计划日期")
    private String sureReplanDate;

    @ApiModelProperty(value = "是否影响上下层计划")
    private Integer sfyxsxcjh;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    @ApiModelProperty(value = "po表id")
    private String poid;

    @ApiModelProperty(value = "供应商名称")
    private String supplyName;

    @ApiModelProperty(value = "上线时间")
    private String onlineTime;

    private OrderWarningHandleStatusEnum processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

    @ApiModelProperty(value = "是否影响上下层计划", notes = "edit")
    private Boolean affectsUpperLevelPlan;

    @ApiModelProperty(value = "是否发起船期变更")
    private Boolean initiateShipmentChange;
}
