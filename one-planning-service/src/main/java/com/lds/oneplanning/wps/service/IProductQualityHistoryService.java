package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.esb.datafetch.model.EsbAbnormalVO;
import com.lds.oneplanning.wps.entity.ProductQualityHistory;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
public interface IProductQualityHistoryService extends IService<ProductQualityHistory> {

    /**
     * 保存或更新
     * @param esbAbnormalVOList
     */
    void saveOrUpdate(List<EsbAbnormalVO> esbAbnormalVOList);
}
