package com.lds.oneplanning.wps.service;

import com.lds.oneplanning.mps.model.CellModel;
import com.lds.oneplanning.mps.model.RowSaveData;
import com.lds.oneplanning.wps.model.WpsData;
import com.lds.oneplanning.wps.model.WpsExcelHeader;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.req.ReleaseWpsRequest;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/7 10:43
 */
public interface WpsExcelService {
    WpsData getData(Long userId,Integer datasource, Date startTime, Date endTime,String factoryCode,Boolean cacheFlag,Map<String,Object> params);

    List<CellModel> getHeader(Long userId, Date startTime,Date endTime,String factoryCode);

    List<WpsRowData> getBody(Long userId, Integer datasource, Date startTime, Date endTime, String factoryCode, Boolean cacheFlag,Map<String,Object> params);

    void saveData(String factoryCode, Long userId, List<RowSaveData> mpsRowDatas, Date date);

    void releaseWps(String factoryCode, Long userId,ReleaseWpsRequest releaseWpsRequest);

    /**
     * 获取待配置的表头列表
     *
     * @return
     */
    List<WpsExcelHeader> getPendingHeader();

    WpsData getDataWithLock(Long userId, Integer datasource, Date startTime, Date endTime, String factoryCode,Boolean cacheFlag,Map<String,Object> params);
}