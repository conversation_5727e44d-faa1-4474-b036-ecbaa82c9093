package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WpsRowExt;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-27
 */
public interface IWpsRowExtService extends IService<WpsRowExt> {

    List<WpsRowExt> listByBizIds(Collection<String> bizIds);

    void batchSaveByBizId(Collection<WpsRowExt> totalWpsRowExts);

    Integer batchFrozenStatus(Integer status, Collection<String> orderNos);
}
