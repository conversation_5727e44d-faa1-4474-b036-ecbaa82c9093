package com.lds.oneplanning.wps.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WpsPlanWarningMQReq implements Serializable {

    private static final long serialVersionUID = -7118531316297291633L;

    private String factoryCode;

    private Long userId;

    private String planTpe;
}
