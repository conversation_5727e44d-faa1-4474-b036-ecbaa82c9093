package com.lds.oneplanning.wps.controller.open;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrder;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderAndon;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderProcedure;
import com.lds.oneplanning.wps.model.MesProcessWorkOrderAndoDto;
import com.lds.oneplanning.wps.model.MesProcessWorkOrderDto;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderAndonService;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderProcedureService;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderService;
import com.lds.oneplanning.wps.service.IPushMsgToOAService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
@RestController
@RequestMapping("/esb-api/wps/mesProcessWorkOrder")
public class MesProcessWorkOrderController {


    @Resource
    private IMesProcessWorkOrderAndonService mesProcessWorkOrderAndonService;

    @Resource
    private IMesProcessWorkOrderProcedureService mesProcessWorkOrderProcedureService;

    @Resource
    private IMesProcessWorkOrderService mesProcessWorkOrderService;

    @Resource
    private IPushMsgToOAService pushMsgToOAService;

    @PostMapping("/test")
    public boolean test(@RequestBody List<String> loginNames) {
        return pushMsgToOAService.pushMsgToOAByLoginName("测试00001", loginNames, 5, "https://www.baidu.com");
    }

    @ApiOperation(value = "mes回写在制工单信息", notes = "mes回写在制工单信息")
    @PostMapping("/writeBack")
    public void writeBack(@RequestBody List<MesProcessWorkOrderDto> dtos) {
        if (dtos.isEmpty()) {
            return;
        }
        List<MesProcessWorkOrder> mesProcessWorkOrders = BeanUtil.mapList(dtos, MesProcessWorkOrder.class);
        //删除数据,再保存
        QueryWrapper<MesProcessWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .select(MesProcessWorkOrder::getId, MesProcessWorkOrder::getWorkOrderNo)
                .in(MesProcessWorkOrder::getWorkOrderNo, mesProcessWorkOrders.stream().map(MesProcessWorkOrder::getWorkOrderNo).collect(Collectors.toList()));
        Map<String, Long> workOrderNoIdMap = mesProcessWorkOrderService.list(queryWrapper).stream()
                .collect(Collectors.toMap(MesProcessWorkOrder::getWorkOrderNo, MesProcessWorkOrder::getId, (a, b) -> a));
        List<MesProcessWorkOrder> editEntityList = Lists.newArrayList();
        mesProcessWorkOrders.removeIf(mesProcessWorkOrder -> {
            Long id = workOrderNoIdMap.get(mesProcessWorkOrder.getWorkOrderNo());
            if (id != null) {
                mesProcessWorkOrder.setId(id);
                editEntityList.add(mesProcessWorkOrder);
                return true;
            }
            return false;
        });
        if (CollectionUtils.isNotEmpty(mesProcessWorkOrders)) {
            mesProcessWorkOrderService.saveBatch(mesProcessWorkOrders);
        }
        if (CollectionUtils.isNotEmpty(editEntityList)) {
            mesProcessWorkOrderService.updateBatchById(editEntityList);
        }

        List<MesProcessWorkOrderAndon> mesProcessWorkOrderAndons = new ArrayList<>();
        List<MesProcessWorkOrderProcedure> mesProcessWorkOrderProcedures = new ArrayList<>();
        for (MesProcessWorkOrderDto dto : dtos) {
            List<MesProcessWorkOrderAndon> mesProcessWorkOrderAndonList = new ArrayList<>();
            for (int i = 0; i < dto.getAndoList().size(); i++) {
                MesProcessWorkOrderAndon andon = BeanUtil.map(dto.getAndoList().get(i), MesProcessWorkOrderAndon.class);
                andon.setSortNo(i + 1);
                mesProcessWorkOrderAndonList.add(andon);
            }
            mesProcessWorkOrderAndons.addAll(mesProcessWorkOrderAndonList);
            List<MesProcessWorkOrderProcedure> mesProcessWorkOrderProcedureList = new ArrayList<>();
            for (int i = 0; i < dto.getRocedurList().size(); i++) {
                MesProcessWorkOrderProcedure processWorkOrderProcedure = BeanUtil.map(dto.getRocedurList().get(i), MesProcessWorkOrderProcedure.class);
                processWorkOrderProcedure.setSortNo(i + 1);
                mesProcessWorkOrderProcedureList.add(processWorkOrderProcedure);
            }
            mesProcessWorkOrderProcedures.addAll(mesProcessWorkOrderProcedureList);
        }
        if (!mesProcessWorkOrderAndons.isEmpty()) {
            //保存成功后，再删除原来工号号的数据
            QueryWrapper<MesProcessWorkOrderAndon> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.lambda().in(MesProcessWorkOrderAndon::getWorkOrderNo, mesProcessWorkOrderAndons.stream().map(MesProcessWorkOrderAndon::getWorkOrderNo).collect(Collectors.toList()));
            mesProcessWorkOrderAndonService.remove(queryWrapper1);
            mesProcessWorkOrderAndonService.saveBatch(mesProcessWorkOrderAndons);
        }
        if (!mesProcessWorkOrderProcedures.isEmpty()) {
            QueryWrapper<MesProcessWorkOrderProcedure> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.lambda().in(MesProcessWorkOrderProcedure::getWorkOrderNo, mesProcessWorkOrderProcedures.stream().map(MesProcessWorkOrderProcedure::getWorkOrderNo).collect(Collectors.toList()));
            mesProcessWorkOrderProcedureService.remove(queryWrapper2);
            mesProcessWorkOrderProcedureService.saveBatch(mesProcessWorkOrderProcedures);
        }


    }
}
