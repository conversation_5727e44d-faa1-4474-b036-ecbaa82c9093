package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormalShortage;
import com.lds.oneplanning.wps.service.impl.WarningMaterialAtpAbnormalShortageServiceImpl;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalShortageVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【warning_material_atp_abnormal】的数据库操作Service
* @createDate 2025-05-14 17:29:49
*/
public interface WarningMaterialAtpAbnormalShortageService extends IService<WarningMaterialAtpAbnormalShortage> {

    void updatePoInfo();

    void queryPoInfo(List<WarningMaterialAtpAbnormalShortage> shortageList);

    void saveData(WarningMaterialAtpAbnormalShortageServiceImpl.ProcessResult result);

    List<MaterialAtpAbnormalShortageVO> getShortageList(Long id);

    void createTodoList(List<WarningMaterialAtpAbnormal> warningList);
}
