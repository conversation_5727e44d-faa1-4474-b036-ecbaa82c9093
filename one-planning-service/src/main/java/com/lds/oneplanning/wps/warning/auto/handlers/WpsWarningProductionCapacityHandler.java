package com.lds.oneplanning.wps.warning.auto.handlers;

import com.google.common.collect.Maps;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsProductionCapacityWarningEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 排产数量校验预警
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsWarningProductionCapacityHandler implements IWpsAutoPlanWarningHandler {

    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        Map<String, Integer> orderScheduleCapacityMap = Maps.newLinkedHashMap();
        orderList.forEach(order -> {
            if (MapUtils.isNotEmpty(order.getScheduleDataMap())) {
                order.getScheduleDataMap().values().stream()
                        .filter(Objects::nonNull)
                        .forEach(capacity -> orderScheduleCapacityMap.merge(
                                order.getOrderNo(),
                                capacity.intValue(),
                                Integer::sum
                        ));
            }
            Integer scheduleCapacity = orderScheduleCapacityMap.get(order.getOrderNo());
            Integer schedulePcsQtyInt = Optional.ofNullable(order.getSchedulePcsQty())
                    .map(Number::intValue)
                    .orElse(0);
            if (!Objects.equals(scheduleCapacity, schedulePcsQtyInt)) {
                order.getWarningColorMap().put("row", WpsProductionCapacityWarningEnum.ORDER_SCHEDULE_EXCEPTION.getColorValue());
       /*         log.info("WPS告警,排产数量校验预警,订单号:{},scheduleCapacity:{},schedulePcsQty:{}",
                        order.getOrderNo(), scheduleCapacity, schedulePcsQtyInt);*/
            }
        });
    }

    @Override
    public int getOrder() {
        return 2;
    }
}