package com.lds.oneplanning.wps.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@UtilityClass
public class MockDataGenerator {
    private static final Random random = new Random();

    public static Date randomDate() {
        long now = System.currentTimeMillis();
        long randomDay = ThreadLocalRandom.current()
                .nextLong(now - 5L * 24 * 60 * 60 * 1000, now + 5L * 24 * 60 * 60 * 1000);
        return new Date(randomDay);
    }

    public static LocalDateTime randomLocalDateTime() {
        long now = System.currentTimeMillis();
        long randomDay = ThreadLocalRandom.current()
                .nextLong(now - 30L * 24 * 60 * 60 * 1000, now + 30L * 24 * 60 * 60 * 1000);
        return LocalDateTime.ofInstant(new Date(randomDay).toInstant(), java.time.ZoneId.systemDefault());
    }

    public static LocalDate randomLocalDate() {
        long now = System.currentTimeMillis();
        long randomDay = ThreadLocalRandom.current()
                .nextLong(now - 5L * 24 * 60 * 60 * 1000, now + 5L * 24 * 60 * 60 * 1000);
        return LocalDateTime.ofInstant(new Date(randomDay).toInstant(), java.time.ZoneId.systemDefault()).toLocalDate();
    }

    public static String randomString(int length) {
        return UUID.randomUUID().toString().replace("-", "").substring(0, length);
    }

    public static int randomInt(int bound) {
        return random.nextInt(bound) + 1;
    }

    public static boolean randomBoolean() {
        return random.nextBoolean();
    }


    public static <T> List<T> mockList(Class<T> clazz, int size) {
        return IntStream.range(0, size)
                .mapToObj(i -> mockAny(clazz))
                .collect(Collectors.toList());
    }


    public static <T> T mockAny(Class<T> clazz) {
        try {
            T instance = clazz.getDeclaredConstructor().newInstance();
            List<Field> fields = getFieldsWithInheritance(clazz);

            for (Field field : fields) {
                setFieldValue(field, instance);
            }
            return instance;
        } catch (Exception e) {
            throw new RuntimeException("Failed to mock instance of " + clazz.getName(), e);
        }
    }

    /**
     * 获取类及其父类的所有属性
     *
     * @param clazz 类
     * @return 属性列表
     */
    public static List<Field> getFieldsWithInheritance(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null) {
            // 获取当前类的所有属性（包括私有属性）
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            // 获取父类
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    private static <T> void setFieldValue(Field field, T instance) throws IllegalAccessException {
        field.setAccessible(true);
        Class<?> fieldType = field.getType();

        if (fieldType == String.class) {
            field.set(instance, randomString(8));
        } else if (fieldType == int.class || fieldType == Integer.class) {
            field.set(instance, randomInt(100));
        } else if (fieldType == boolean.class || fieldType == Boolean.class) {
            field.set(instance, randomBoolean());
        } else if (fieldType == Date.class) {
            field.set(instance, randomDate());
        } else if (fieldType == LocalDate.class) {
            field.set(instance, randomLocalDate());
        } else if (fieldType == LocalDateTime.class) {
            field.set(instance, randomLocalDateTime());
        } else if (fieldType.isEnum()) {
            Object[] enumValues = fieldType.getEnumConstants();
            field.set(instance, enumValues[random.nextInt(enumValues.length)]);
        } else if (fieldType == List.class) {
            // 对于集合类型，创建一个空列表
            field.set(instance, new java.util.ArrayList<>());
        } else {
            // 对于自定义对象类型，递归mock
            try {
                field.set(instance, mockAny(fieldType));
            } catch (Exception e) {
                // 如果无法mock，则设置为null
                field.set(instance, null);
            }
        }
    }


    public <T> List<T> fetchFromProd(Collection<String> orderNoList, String uri, Class<T> clazz) {
        //获取accessToken
        String tokenUrl = "https://one-planning.leedarson.com/api/basic/anon/refreshToken";
        Map<String, Object> params = new HashMap<>();
        params.put("refreshToken", "b86ed6df-8a76-4a54-8c59-cd6bdc326219");
        params.put("accessToken", "eyJhbGciOiJIUzI1NiJ9.eyJjb250YWluc0ltcG9ydCI6dHJ1ZSwiLnIiOiI5NDE5ODZhZTFmYWY0NjRlOWQ2OThlMDdhMzY3NGYzNiIsImFjY291bnROYW1lIjoiaHVhbmdsaW5nY29uZyIsImlzQWRtaW4iOmZhbHNlLCJ1c2VySWRlbnRpdHkiOjAsInRlcm1pbmFsIjoiV2ViUEMiLCJ1c2VySWQiOjEzNjc3NjY4NjUzODIzNDk3MzgsImFjY291bnRJZCI6MzI0NiwiY2xpZW50VHlwZSI6MSwibG9jYXRpb25JZCI6NTE2LCJjb250YWluc1F1ZXJ5Ijp0cnVlLCJ0ZWwiOiIxNTgwNTkwMjgzNCIsInVzZXJuYW1lIjoi6buE54G16IGqIn0.NtdtIM60zkm8Me5ZdC47aiZaysAPN6YzifVFcQaZHm8");
        String token = null;
        try (HttpResponse resp = HttpUtil.createPost(tokenUrl).body(JSON.toJSONString(params)).execute()) {
            String body = resp.body();
            log.info("获取accessToken结果：{}", body);
            JSONObject bodyJson = JSON.parseObject(body);
            if (bodyJson.getIntValue("code") == 200) {
//                return fetchFromProdWithAccessToken(orderNoList, bodyJson.getString("data"));
                token = bodyJson.getJSONObject("data").getString("accessToken");
            }
        } catch (Exception e) {
            log.warn("调用生产接口失败", e);
        }

        String url = "https://one-planning.leedarson.com/api/one-planning" + uri;

        HttpRequest post = HttpUtil.createPost(url);
        post.header("token", token);
        try (HttpResponse resp = post.body(JSON.toJSONString(orderNoList)).execute()) {
            String body = resp.body();
            log.info("调用生产接口结果：{}", body);
            JSONObject bodyJson = JSON.parseObject(body);
            if (bodyJson.getIntValue("code") == 200) {
                return bodyJson.getJSONArray("data").toJavaList(clazz);
            }
        } catch (Exception e) {
            log.warn("调用生产接口失败", e);
        }

        return Collections.emptyList();
    }

}
