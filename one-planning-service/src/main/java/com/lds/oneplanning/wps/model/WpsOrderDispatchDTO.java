package com.lds.oneplanning.wps.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WpsOrderDispatch对象", description="")
public class WpsOrderDispatchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "计划员工号")
    private String plannerEmpNo;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "计划员名称")
    private String plannerName;

    @ApiModelProperty(value = "订单编号集合")
    private Collection<String> orderNos;
}
