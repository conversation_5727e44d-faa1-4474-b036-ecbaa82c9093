package com.lds.oneplanning.wps.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.model.UserInfoDTO;
import com.lds.oneplanning.basedata.model.UserInfoQueryDTO;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.wps.entity.WarningIncomeMaterialAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningIncomeMaterialPoAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormalShortage;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsPlanTypeEnum;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.service.IWarningIncomeMaterialAtpAbnormalService;
import com.lds.oneplanning.wps.service.IWarningIncomeMaterialPoAtpAbnormalService;
import com.lds.oneplanning.wps.vo.*;
import com.lds.oneplanning.wps.warning.workbench.TempMockData;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import com.lds.oneplanning.wps.warning.workbench.handlers.IncomeMaterialAtpAbnormalHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.beans.ParameterDescriptor;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Api("来料检验异常")
@RestController
@RequestMapping("/wps/income/warning/atp")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningIncomeMaterialAtpAbnormalController {
    private final IWarningIncomeMaterialAtpAbnormalService service;
    private final IWarningIncomeMaterialPoAtpAbnormalService poAtpAbnormalService;
    private final IncomeMaterialAtpAbnormalHandler incomeMaterialAtpAbnormalHandler;
    private final IUserInfoService userInfoService;


    @GetMapping("/test/generateData")
    public void testGenerateData() {
        WpsAutoScheduleContext ctx = TempMockData.getCtx();

        WpsWorkbenchWarningContext context = new WpsWorkbenchWarningContext();
        context.setFactoryCode(ctx.getCurrentFactoryCode());
        context.setOrders(ctx.getOrderList());
        context.setProductType(WpsPlanTypeEnum.WHOLE_MACHINE);
        List<WpsOrderPlanWarning> list = incomeMaterialAtpAbnormalHandler.execute(context, Maps.newHashMap());
        log.info("测试数据生成完成");
    }
    @GetMapping("/test/job")
    public void testjob() {
        poAtpAbnormalService.updateIncomePoInfo();
    }
    @ApiOperation("来料检验异常列表")
    @GetMapping("/getPoList")
    public List<WarningIncomeMaterialPoAtpAbnormal> getPoList(@RequestParam String orderNo) {
        return poAtpAbnormalService.lambdaQuery()
                .eq(WarningIncomeMaterialPoAtpAbnormal::getOrderNo, orderNo)
                .list();
    }

    @PostMapping("/page")
    @ApiOperation("来料检验异常分页查询")
    public IPage<?> page(@RequestBody WarningIncomeMaterialAtpAbnormalParams params) {
        ViewSource source = userInfoService.getOrDefaultUserType(params.getViewRole());
        log.info("查询参数source={}", source);
       return  service.selectPage(source, params);
    }

    @PostMapping("/update")
    @ApiOperation("采购填写处理结果回填结果")
    public void update(@RequestBody IncomeMateialUpdateVO vo) {
        QueryWrapper<WarningIncomeMaterialPoAtpAbnormal> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WarningIncomeMaterialPoAtpAbnormal::getOrderNo, vo.getOrderNo());
        queryWrapper.lambda().eq(WarningIncomeMaterialPoAtpAbnormal::getShortageMaterialId, vo.getMaterialId());
        // 根据生产订单查询记录
        WarningIncomeMaterialPoAtpAbnormal record = poAtpAbnormalService.getBaseMapper().selectOne(queryWrapper);
        if (StrUtil.isNotEmpty(vo.getDealResult())){
            record.setDealResult(vo.getDealResult());
        }
        if (StrUtil.isNotEmpty(vo.getImpactType())){
            record.setImpactType(vo.getImpactType());
        }
        if (vo.getSfyxsxcjh() != null){
            record.setSfyxsxcjh(vo.getSfyxsxcjh());
        }
        if (StrUtil.isNotEmpty(vo.getNextArrivalDate())){
            record.setNextArrivalDate(vo.getNextArrivalDate());
        }
        if (StrUtil.isNotEmpty(vo.getSureReplanDate())){
            record.setSureReplanDate(vo.getSureReplanDate());
        }
        poAtpAbnormalService.saveOrUpdate(record);
    }

}
