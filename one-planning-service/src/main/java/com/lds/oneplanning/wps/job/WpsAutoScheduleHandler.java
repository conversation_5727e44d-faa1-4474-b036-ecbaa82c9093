package com.lds.oneplanning.wps.job;

import com.google.common.collect.Maps;
import com.lds.coral.job.annotation.JobRegister;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.entity.WpsPlanVersion;
import com.lds.oneplanning.wps.enums.WpsPlanSourceEnum;
import com.lds.oneplanning.wps.helper.WpsPlanVersionHelper;
import com.lds.oneplanning.wps.model.WpsData;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsPlanVersionService;
import com.lds.oneplanning.wps.service.WpsExcelService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;

@JobRegister(value = "WpsAutoScheduleHandler", jobName = "WpsAutoScheduleJob", cron = "0 0 7 * * ? *")
@Component
@Slf4j
public class WpsAutoScheduleHandler extends IJobHandler {

    @Autowired
    private WpsExcelService wpsExcelService;

    @Autowired
    private IPlannerDataPermissionService plannerDataPermissionService;

    @Autowired
    private Executor taskExecutor;
    @Autowired
    WpsRowDataFacadeService wpsRowDataFacadeService;
    @Autowired
    IWpsPlanVersionService wpsPlanVersionService;
    @Autowired
    WpsPlanVersionHelper wpsPlanVersionHelper;
    @Autowired
    IPlannerBaseService plannerBaseService;
    @Autowired
    RedissonClient redissonClient;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        ReturnT<String> returnInfo = SUCCESS;
        Long start = System.currentTimeMillis();
        try {
            String version = wpsPlanVersionHelper.createVersion();
            Map<String, Set<Long>> factoryUserMap = plannerDataPermissionService.getFactoryUserMap();
            if (MapUtils.isEmpty(factoryUserMap)) {
                XxlJobLogger.log("No user factory code data found.");
                return SUCCESS;
            }
            for(Map.Entry<String,Set<Long>> entry:factoryUserMap.entrySet()){
                processUserData(entry, version);
            }
            // 使用 CompletableFuture 并行处理任务
//            CompletableFuture<?>[] futures = factoryUserMap.entrySet().parallelStream()
//                    .map(entry -> CompletableFuture.runAsync(() -> processUserData(entry, version), taskExecutor))
//                    .toArray(CompletableFuture[]::new);
            // 等待所有任务完成
//            CompletableFuture.allOf(futures).join();
        } catch (Exception e) {
            log.error("WpsAutoScheduleHandler 执行失败 -->{}", e.getMessage(), e);
            returnInfo = ReturnT.FAIL;
        }
        Long end = System.currentTimeMillis();
        log.info("=========================  WpsAutoScheduleHandler job execute end ,耗时：{}======================",end-start);
        return returnInfo;
    }

    private void processUserData(Map.Entry<String, Set<Long>> entry,String version) {
        try {
            String factoryCode = entry.getKey();
            Set<Long> userIds = entry.getValue();
            Date startTime = LocalDateTimeUtil.localDateToDate(LocalDate.of(LocalDate.now().getYear(),1,1));
            Date endTime = new Date();
            for(Long userId:userIds){
                WpsData wpsData = wpsExcelService.getDataWithLock(userId, WpsConstants.DATA_SOURCE_AUTO, startTime, endTime, factoryCode,false,Maps.newHashMap());
                //保存自动排产数据
                List<WpsRowData> wpsRowDatas = wpsData.getBody();
                if(CollectionUtils.isEmpty(wpsRowDatas)){
                    continue;
                }
                wpsRowDataFacadeService.saveData(wpsRowDatas,userId);
                //生成一个版本
                WpsPlanVersion wpsPlanVersion = wpsPlanVersionHelper.createWpsPlanVersion(plannerBaseService.getEmpNoByUserId(userId),factoryCode);
                wpsPlanVersion.setVersion(version);
                wpsPlanVersion.setSource(WpsPlanSourceEnum.SAVE.getCode());
                wpsPlanVersionService.savePlanVersion(wpsPlanVersion);
            }
        } catch (Exception e) {
            log.error("处理用户数据失败: userId={}, error={}", entry.getKey(), e.getMessage(), e);
            XxlJobLogger.log("处理用户数据失败: userId=" + entry.getKey() + ", error=" + e.getMessage());
        }
    }
}
