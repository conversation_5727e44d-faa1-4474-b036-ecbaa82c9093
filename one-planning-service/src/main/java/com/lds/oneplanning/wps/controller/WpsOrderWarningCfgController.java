package com.lds.oneplanning.wps.controller;


import com.google.common.collect.Maps;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.model.WpsOrderWarningCfgDTO;
import com.lds.oneplanning.wps.service.IWpsOrderWarningCfgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-31
 */
@Api(value = "WpsOrderWarningCfgController", tags = "wps告警预警配置")
@RestController
@RequestMapping("/wps/OrderWarningCfg")
public class WpsOrderWarningCfgController {

    @Resource
    private IWpsOrderWarningCfgService wpsOrderWarningCfgService;
    @Resource
    private IPlannerDataPermissionService plannerDataPermissionService;


    @ApiOperation(value = "获取告警类型", notes = "获取告警类型")
    @GetMapping("/listWarningTypes")
    public List<Map<String,Object>> listWarningTypes (){
        List<Map<String,Object>> resList = Lists.newArrayList();
        for (WpsOrderWarningTypeEnum typeEnum  : WpsOrderWarningTypeEnum.values()){
            Map<String,Object> map = Maps.newLinkedHashMap();
            // 前端为主
            map.put("key",typeEnum.getCode());
            map.put("value",typeEnum.getName());
            map.put("option",typeEnum.getParamMap());
            resList.add(map);
        }
        return resList;
    }

    @ApiOperation(value = "分页查询", notes = "通过开放的若干参数进行接口查询")
    @GetMapping("/page")
    public Page<WpsOrderWarningCfgDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                            @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                            @RequestParam(value = "warningLevel",required = false)Integer warningLevel,
                                            @RequestParam(value = "warningType",required = false)String warningType,
                                            @RequestParam(value = "status",required = false)Integer status,
                                            @RequestParam(value = "pageNum")Integer pageNum,
                                            @RequestParam(value = "pageSize")Integer pageSize
    ){
        List<PlannerDataPermission> dataPermissionList = plannerDataPermissionService.listByUserId(UserContextUtils.getUserId());
        Set<String> factoryCodes =  dataPermissionList.stream().map(PlannerDataPermission::getFactoryCode).collect(Collectors.toSet());
        if (factoryCodes != null) {
            factoryCodes.add(factoryCode);
        }
        return wpsOrderWarningCfgService.page(keyword,factoryCodes,warningLevel,warningType,status,pageNum,pageSize);
    }

    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public WpsOrderWarningCfgDTO detail(@PathVariable("id")Long id){
        return  wpsOrderWarningCfgService.getDetail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    public Long add(@RequestBody WpsOrderWarningCfgDTO dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return wpsOrderWarningCfgService.add(dto);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    public Integer edit(@RequestBody WpsOrderWarningCfgDTO dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setId(id);
        return  wpsOrderWarningCfgService.edit(dto);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    public Integer delete(@PathVariable("id")Long id ){
        return  wpsOrderWarningCfgService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  wpsOrderWarningCfgService.removeByIds(ids) ? 1 : 0;
    }

}
