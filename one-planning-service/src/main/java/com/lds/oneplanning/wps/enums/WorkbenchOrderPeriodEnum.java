package com.lds.oneplanning.wps.enums;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.time.LocalDate;
import java.util.Map;

@Getter
public enum WorkbenchOrderPeriodEnum {

    LAST_MONTH("last_month", "过去一个月", LocalDate.now().minusMonths(1), LocalDate.now()),
    LAST_THREE_DAYS("last_three_days", "三天内", LocalDate.now(), LocalDate.now().plusDays(2)),
    FOUR_TO_SEVEN_DAYS("four_to_seven_days", "4-7天内", LocalDate.now().plusDays(3), LocalDate.now().plusDays(6)),
    SECOND_WEEK("second_week", "第二周", LocalDate.now(), LocalDate.now().plusWeeks(2)),
    THIRD_WEEK("third_week", "第三周", LocalDate.now(), LocalDate.now().plusWeeks(3)),
    FOURTH_WEEK("fourth_week", "第四周", LocalDate.now(), LocalDate.now().plusWeeks(4)),
    SECOND_MONTH("second_month", "第2月", LocalDate.now(), LocalDate.now().plusMonths(2)),
    THIRD_MONTH("third_month", "第3月", LocalDate.now(), LocalDate.now().plusMonths(3));

    private final String code; // 时间段代码
    private final String description; // 时间段描述
    private final LocalDate startDate; // 开始日期
    private final LocalDate endDate; // 截止日期

    WorkbenchOrderPeriodEnum(String code, String description, LocalDate startDate, LocalDate endDate) {
        this.code = code;
        this.description = description;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public static final Map<String, WorkbenchOrderPeriodEnum> CODE_MAP = Maps.newHashMap();

    static {
        for (WorkbenchOrderPeriodEnum workbenchOrderPeriodEnum : WorkbenchOrderPeriodEnum.values()) {
            CODE_MAP.put(workbenchOrderPeriodEnum.getCode(), workbenchOrderPeriodEnum);
        }
    }

    public static WorkbenchOrderPeriodEnum getByCode(String code) {
        return CODE_MAP.get(code);
    }


    /**
     * 获取最小日期
     */
    public static LocalDate getMinDate() {
        LocalDate minDate = null;
        for (WorkbenchOrderPeriodEnum workbenchOrderPeriodEnum : WorkbenchOrderPeriodEnum.values()) {
            if (null == minDate) {
                minDate = workbenchOrderPeriodEnum.getStartDate();
            } else if (workbenchOrderPeriodEnum.getStartDate().isBefore(minDate)) {
                minDate = workbenchOrderPeriodEnum.getStartDate();
            }
        }
        return minDate;
    }

    /**
     * 获取最大日期
     */
    public static LocalDate getMaxDate() {
        LocalDate maxDate = null;
        for (WorkbenchOrderPeriodEnum workbenchOrderPeriodEnum : WorkbenchOrderPeriodEnum.values()) {
            if (null == maxDate) {
                maxDate = workbenchOrderPeriodEnum.getEndDate();
            } else if (workbenchOrderPeriodEnum.getEndDate().isAfter(maxDate)) {
                maxDate = workbenchOrderPeriodEnum.getEndDate();
            }
        }
        return maxDate;
    }
}