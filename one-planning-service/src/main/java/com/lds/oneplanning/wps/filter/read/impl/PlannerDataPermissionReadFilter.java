package com.lds.oneplanning.wps.filter.read.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.model.ProductGroupDTO;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.basedata.service.IProductGroupService;
import com.lds.oneplanning.wps.filter.read.AbstractWpsOrderReadFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/25 17:43
 */
@Slf4j
@Service
public class PlannerDataPermissionReadFilter extends AbstractWpsOrderReadFilter {

    @Resource
    private IPlannerDataPermissionService plannerDataPermissionService;
    @Resource
    private IProductGroupService productGroupService;
    @Override
    public Integer filterSeq() {
        return 3;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, String inputFactoryCode, List<WpsRowData> dirtyList,boolean cacheFlag) {
        List<PlannerDataPermission> plannerDataPermissions = plannerDataPermissionService.listByUserId(userId);
        Set<String> productGroupCodes = plannerDataPermissions.stream().map(PlannerDataPermission::getProductGroupCode).collect(Collectors.toSet());
        List<ProductGroupDTO> productGroupDTOS = productGroupService.listByCodes(productGroupCodes);
        Map<String, ProductGroupDTO> productGroupDTOMap = productGroupDTOS.stream().collect(Collectors.toMap(ProductGroupDTO::getCode, productGroupDTO -> productGroupDTO, (t, t2) -> t2));
        Set<WpsRowData> resSet = Sets.newHashSet();
        for (PlannerDataPermission dataPermission : plannerDataPermissions) {
            // 行内与 行外或
            List<Predicate<WpsRowData>> predicateList = Lists.newArrayList();
            String factoryCode = dataPermission.getFactoryCode();
            if (!factoryCode.equals(inputFactoryCode)) {
                // 不是外部指定的工厂，直接跳过
                continue;
            }
            //   增加产品组过滤
            String productGroupCode = dataPermission.getProductGroupCode();
            // 商品id或者产品id
            String commodityIdOrProductId = dataPermission.getCommodityId();
            // 默认过滤器
            predicateList.add(wpsRowData -> factoryCode.equals(wpsRowData.getFactory()));
            // 返回的产品id可能有多个，多个是分号隔开，
            if (StringUtils.isNotBlank(productGroupCode) ) {
                // 产品组配置  商品id没有配置  10   注意，产品组关联的这个productIds 现在也包含了商品id，先不改字段，前端统一之后改，
                ProductGroupDTO groupDTO = productGroupDTOMap.getOrDefault(productGroupCode,new ProductGroupDTO());
                predicateList.add(wpsRowData -> (wpsRowData.getProductId() != null && this.containsCommonElement(groupDTO.getProductIds(), Arrays.asList(wpsRowData.getProductId().split(";"))))
                        || ( wpsRowData.getCommodityId() !=null && this.containsCommonElement(groupDTO.getProductIds(), Arrays.asList(wpsRowData.getCommodityId().split(";")))));
            }
            if (StringUtils.isNotBlank(commodityIdOrProductId)) {
                // 产品组没有配置，商品产品id有配置 01  注意是过滤商品id或产品id
                predicateList.add(wpsRowData -> (wpsRowData.getCommodityId() != null && wpsRowData.getCommodityId().contains(commodityIdOrProductId))
                        || (wpsRowData.getProductId() != null && wpsRowData.getProductId().contains(commodityIdOrProductId)));
            }
            resSet.addAll(this.filterRowData(dirtyList,predicateList));
        }
        return Lists.newArrayList(resSet);
    }

    private  boolean containsCommonElement(Set<String> sourceSet, List<String> list2) {
        if (CollectionUtils.isEmpty(sourceSet) || CollectionUtils.isEmpty(list2)) {
            return false;
        }
        for (String item : list2) {
            if (sourceSet.contains(item)) {
                return true;
            }
        }
        return false;
    }

    private Set<WpsRowData> filterRowData( List<WpsRowData> dirtyList,List<Predicate<WpsRowData>> predicateList){
        return  dirtyList.stream()
                .filter(predicateList.stream().reduce(Predicate::and).orElse(x -> true))
                .collect(Collectors.toSet());
    }


}
