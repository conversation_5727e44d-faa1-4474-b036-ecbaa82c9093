package com.lds.oneplanning.wps.enums;

import java.util.Arrays;

/**
 * 自动排产错误枚举
 */
public enum SchedulePlanErrorCodeEnum {
    LINE_UPH_NOT_EXIST("1", "线体UPH不存在"),
    PRODUCT_GORUP_NOT_EXIST("2", "产品组不存在"),
    SCHEDULE_PRIORITY_EMPTY("3", "排产优先级为空"),
    LINE_CAPACITY_NOT_EXIST("4", "排产时长不存在"),
    ORDER_UPH_NOT_EXIST("6", "订单UPH不存在"),
    ;

    private SchedulePlanErrorCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        SchedulePlanErrorCodeEnum scheduldPlanErrorCodeEnum = Arrays.stream(SchedulePlanErrorCodeEnum.values()).filter(item -> item.code.equals(code)).findFirst().orElse(null);
        return scheduldPlanErrorCodeEnum == null ? null : scheduldPlanErrorCodeEnum.getName();
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
