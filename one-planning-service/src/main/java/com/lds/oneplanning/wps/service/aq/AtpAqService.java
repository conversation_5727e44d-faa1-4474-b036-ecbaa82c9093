package com.lds.oneplanning.wps.service.aq;

import com.lds.oneplanning.config.AtpJmsConfig;
import com.lds.oneplanning.esb.cache.WpsOrderCacheUtils;
import lombok.extern.slf4j.Slf4j;
import oracle.jms.AQjmsFactory;
import oracle.jms.AQjmsSession;
import oracle.jms.AQjmsTextMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.jms.*;
import java.util.Properties;

@Slf4j
@Component
public class AtpAqService {

    @Autowired
    private AtpJmsConfig atpJmsConfig;

    private static final String MV_REFRESH_QUEUE_NAME = "ADM_ATP.MV_REFRESH_QUEUE";

    private QueueConnection queueConnection;
    private AQjmsSession queueSession;

    @PostConstruct
    public void consumerMvRefreshQueue() {
        try {
            initializeQueueConnection();
            startMessageConsumer();
            log.info("Consumer started for {}", MV_REFRESH_QUEUE_NAME);
        } catch (JMSException e) {
            log.error("Error starting consumer for queue: {}", MV_REFRESH_QUEUE_NAME, e);
        }
    }

    private void initializeQueueConnection() throws JMSException {
        Properties props = new Properties();
        props.setProperty("user", atpJmsConfig.getUsername());
        props.setProperty("password", atpJmsConfig.getPassword());

        QueueConnectionFactory queueConnectionFactory = AQjmsFactory.getQueueConnectionFactory(
                atpJmsConfig.getJdbcUrl(), props);

        queueConnection = queueConnectionFactory.createQueueConnection();
        queueConnection.start();
        queueSession = (AQjmsSession) queueConnection.createQueueSession(false, Session.AUTO_ACKNOWLEDGE);
    }

    private void startMessageConsumer() throws JMSException {
        Queue queue = queueSession.createQueue(MV_REFRESH_QUEUE_NAME);
        MessageConsumer consumer = queueSession.createConsumer(queue);
        consumer.setMessageListener(this::processMessage);
    }

    private void processMessage(Message message) {
        try {
            AQjmsTextMessage aQjmsTextMessage = (AQjmsTextMessage) message;
            long jmsTimestamp = message.getJMSTimestamp();
            String adtPayload = aQjmsTextMessage.getText();
            WpsOrderCacheUtils.cleanAtpCache();
            log.info("Received message from MV_REFRESH_QUEUE, timestamp: {}, payload: {}.", jmsTimestamp, adtPayload);
        } catch (JMSException e) {
            log.error("Error processing message from MV_REFRESH_QUEUE", e);
        }
    }

    @PreDestroy
    public void cleanUp() {
        try {
            if (queueSession != null) {
                queueSession.close();
            }
            if (queueConnection != null) {
                queueConnection.close();
            }
            log.info("Resources cleaned up for {}", MV_REFRESH_QUEUE_NAME);
        } catch (JMSException e) {
            log.error("Error closing resources for queue: {}", MV_REFRESH_QUEUE_NAME, e);
        }
    }
}