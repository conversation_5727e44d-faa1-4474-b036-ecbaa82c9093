package com.lds.oneplanning.wps.controller;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.wps.entity.WarningFrozenUnfrozenAbnormal;
import com.lds.oneplanning.wps.entity.WarningIncomeMaterialPoAtpAbnormal;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsPlanTypeEnum;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.service.IWarningFrozenUnfrozenAbnormalService;
import com.lds.oneplanning.wps.service.impl.WarningFrozenUnfrozenAbnormalServiceImpl;
import com.lds.oneplanning.wps.vo.FreezeUnFreezeAbnormalVO;
import com.lds.oneplanning.wps.vo.WarningFrozenUnfrozenAbnormalParams;
import com.lds.oneplanning.wps.vo.WarningIncomeMaterialAtpAbnormalParams;
import com.lds.oneplanning.wps.vo.WarningIncomeMaterialAtpAbnormalVO;
import com.lds.oneplanning.wps.warning.workbench.TempMockData;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import com.lds.oneplanning.wps.warning.workbench.handlers.FrozenUnfrozenAbnormalHandler;
import com.sun.xml.bind.v2.TODO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  冻结解冻异常
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
@Api("冻结解冻异常")
@RestController
@RequestMapping("/wps/frozen/unfrozen/warning")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Slf4j
public class WarningFrozenUnfrozenAbnormalController {

     private final IWarningFrozenUnfrozenAbnormalService warningFrozenUnfrozenAbnormalService;
     private final FrozenUnfrozenAbnormalHandler  frozenUnfrozenAbnormalHandler;
     private final IUserInfoService userInfoService;
     private final IFactoryFacadeService factoryFacadeService;

    @GetMapping("/generateData")
    public void testGenerateData() {
        WpsAutoScheduleContext ctx = TempMockData.getCtx();
        WpsWorkbenchWarningContext context = new WpsWorkbenchWarningContext();
        context.setFactoryCode(ctx.getCurrentFactoryCode());
        context.setOrders(ctx.getOrderList());
        context.setProductType(WpsPlanTypeEnum.WHOLE_MACHINE);
        List<WpsOrderPlanWarning> list = frozenUnfrozenAbnormalHandler.execute(context, Maps.newHashMap());
        log.info("测试数据生成完成");
    }
    @PostMapping("/page")
    @ApiOperation("冻结解冻异常分页查询")
    public IPage<FreezeUnFreezeAbnormalVO> page(@RequestBody WarningFrozenUnfrozenAbnormalParams params) {
        ViewSource source = userInfoService.getOrDefaultUserType(params.getSource());
        Long userId = UserContextUtils.getUserId();
//        String  userIdStr = "1367766865382353155";
//        Long userId = Long.valueOf(userIdStr);
        // 使用实体类进行分页查询
        Page<FreezeUnFreezeAbnormalVO> pageEntity = new Page<>(params.getPage(), params.getPageSize());
         if (ViewSource.PC.equals(source) && null != userId){
            List<Factory> factories = factoryFacadeService.listByUser(userId);
            if (factories.isEmpty()){
                log.info("用户没有工厂权限");
                return new Page<>(params.getPage(), params.getPageSize());
            }
             String factoryCodes = factories.stream()
                     .map(Factory::getCode)  // 提取每个 Factory 对象的 code 属性
                     .collect(Collectors.joining(","));
            params.setFactory(factoryCodes);
        } else if (ViewSource.NPI.equals(source)){
             params.setNpiUserid(userId);
        } else if (ViewSource.QPL.equals(source)){
             params.setQplUserid(userId);
        } else {
             return new Page<>(params.getPage(), params.getPageSize());
         }
        // 执行查询
        return  warningFrozenUnfrozenAbnormalService.selectPage(pageEntity, params);
    }
}
