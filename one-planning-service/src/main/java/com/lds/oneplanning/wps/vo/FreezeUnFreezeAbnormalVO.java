package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * 订单冻结解冻预警
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13
 */
@Data
@ApiModel(value = "订单冻结解冻预警")
@TableHeader(type = WpsOrderWarningTypeEnum.FROZEN_UNFROZEN_WARNING, source = {ViewSource.PC,ViewSource.NPI,ViewSource.QPL})
public class FreezeUnFreezeAbnormalVO {

    @ApiModelProperty(value = "灯色")
    private String lightColor;

    @ApiModelProperty(value = "销售订单-行项目")
    private String xsddhxm;

    @ApiModelProperty(value = "销售订单号")
    private String salesOrderNumber;

    @ApiModelProperty(value = "销售订单行项目")
    private String lineNumber;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "工厂")
    private String factory;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "计划上线时间")
    private LocalDate plannedOnlineTime;

    @ApiModelProperty(value = "距离计划剩余天数")
    private Integer remainingDaysToPlan;

    private OrderWarningHandleStatusEnum processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

    @ApiModelProperty(value = "是否触发待办到责任人")
    private String sfcfdbdzrr;
}
