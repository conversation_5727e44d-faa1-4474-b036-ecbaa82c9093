package com.lds.oneplanning.wps.helper;

import com.lds.coral.common.util.date.DateUtil;
import com.lds.oneplanning.wps.entity.WpsPlanVersion;
import org.springframework.stereotype.Component;

@Component
public class WpsPlanVersionHelper {

    /**
     * 创建计划的快照版本
     * @param version
     * @param plannerEmpNo
     * @param source
     * @return
     */
    public WpsPlanVersion createWpsPlanVersion(String plannerEmpNo,String factoryCode){
        WpsPlanVersion wpsPlanVersion = new WpsPlanVersion();
        wpsPlanVersion.setVersion(createVersion());
        wpsPlanVersion.setPlannerEmpNo(plannerEmpNo);
        wpsPlanVersion.setFactoryCode(factoryCode);
        return wpsPlanVersion;
    }

    public String createVersion() {
        return DateUtil.getCurrentDateStr(DateUtil.DB_STORE_DATE_MINUTE);
    }
}
