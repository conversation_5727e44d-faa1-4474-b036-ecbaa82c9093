package com.lds.oneplanning.wps.controller;

import com.google.common.collect.Maps;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.basedata.service.impl.ImportExportService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.model.*;
import com.lds.oneplanning.wps.service.IWpsSchedulePlanLogService;
import com.lds.oneplanning.wps.service.WpsExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Api(value = "WpsSchedulePlanLogController", tags = "WpsSchedulePlanLogController")
@RestController
@RequestMapping("/wpsSchedulePlanLog")
public class WpsSchedulePlanLogController {
    @Autowired
    IWpsSchedulePlanLogService wpsSchedulePlanLogService;
    @Autowired
    ImportExportService importExportService;
    @Autowired
    WpsExcelService wpsExcelService;
    @Autowired
    IPlannerDataPermissionService plannerDataPermissionService;

    @GetMapping("/action/findPage")
    public Page<WpsSchedulePlanLogDTO> findPage(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                @RequestParam(value = "pageSize", defaultValue = "15") Integer pageSize,
                                                @RequestParam(value = "orderNo", required = false) String orderNo,
                                                @RequestParam(value = "sellOrderNo", required = false) String sellOrderNo,
                                                @RequestParam(value = "factoryCode", required = false) String factoryCode,
                                                @RequestParam(value = "errorCode", required = false) String errorCode,
                                                @RequestParam(value = "startTime", required = false) Date startTime,
                                                @RequestParam(value = "endTime", required = false) Date endTime) {
        WpsSchedulePlanLogQueryDTO queryDTO = new WpsSchedulePlanLogQueryDTO();
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(pageSize);
        queryDTO.setFactoryCode(factoryCode);
        queryDTO.setOrderNo(orderNo);
        queryDTO.setErrorCode(errorCode);
        queryDTO.setStartTime(startTime);
        queryDTO.setSellOrderNo(sellOrderNo);
        queryDTO.setEndTime(endTime);
        queryDTO.setUserId(UserContextUtils.getUserId());
        return wpsSchedulePlanLogService.findPage(queryDTO);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping("/action/export")
    public ResponseEntity exportExcel(@RequestParam(value = "orderNo", required = false) String orderNo,
                                      @RequestParam(value = "sellOrderNo", required = false) String sellOrderNo,
                                      @RequestParam(value = "factoryCode", required = false) String factoryCode,
                                      @RequestParam(value = "errorCode", required = false) String errorCode,
                                      @RequestParam(value = "startTime", required = false) Date startTime,
                                      @RequestParam(value = "endTime", required = false) Date endTime) {
        WpsSchedulePlanLogQueryDTO queryDTO = new WpsSchedulePlanLogQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(Integer.MAX_VALUE);
        queryDTO.setFactoryCode(factoryCode);
        queryDTO.setOrderNo(orderNo);
        queryDTO.setErrorCode(errorCode);
        queryDTO.setStartTime(startTime);
        queryDTO.setSellOrderNo(sellOrderNo);
        queryDTO.setEndTime(endTime);
        queryDTO.setUserId(UserContextUtils.getUserId());
        List<WpsSchedulePlanLogDTO> wpsSchedulePlanLogs = wpsSchedulePlanLogService.findList(queryDTO);
        //对象转换
        List<WpsSchedulePlanLogExcelDTO> wpsSchedulePlanLogExcelList = BeanUtil.mapList(wpsSchedulePlanLogs, WpsSchedulePlanLogExcelDTO.class);
        return importExportService.exportData(wpsSchedulePlanLogExcelList, WpsSchedulePlanLogExcelDTO.class, "自动排产异常信息");
    }
    @ApiOperation(value = "排产异常检测", notes = "排产异常检测")
    @PostMapping("/action/scheduleInspect")
    public void scheduleInspect() {
        Set<String> factoryCodes = plannerDataPermissionService.getFactoryCodeByUserId(UserContextUtils.getUserId());
        for(String factoryCode:factoryCodes){
            Date startTime = LocalDateTimeUtil.localDateToDate(LocalDate.of(LocalDate.now().getYear(), 1, 1));
            Date endTime = new Date();
            Map<String, Object> params = Maps.newHashMap();
            params.put(WpsConstants.IS_SAVE_SCHEDULE_PLANLOG_PARAM, true);
            wpsExcelService.getDataWithLock(UserContextUtils.getUserId(), WpsConstants.DATA_SOURCE_AUTO, startTime, endTime, factoryCode, false, params);
        }
    }
}
