package com.lds.oneplanning.wps.controller;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.req.QualityHistoryRiskReq;
import com.lds.oneplanning.wps.service.IWarningQualityHistoryRiskService;
import com.lds.oneplanning.wps.vo.QualityHistoryRiskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/6/2
 */
@Api("品质履历")
@RestController
@RequestMapping("/wps/warning/qualityHistoryRisk")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningQualityHistoryRiskController {

    private final IWarningQualityHistoryRiskService warningQualityHistoryRiskService;

    @PostMapping("/page")
    @ApiOperation("品质履历分页查询")
    public Page<QualityHistoryRiskVO> page(@RequestBody QualityHistoryRiskReq req) {
        return warningQualityHistoryRiskService.queryPage(req);
    }
}
