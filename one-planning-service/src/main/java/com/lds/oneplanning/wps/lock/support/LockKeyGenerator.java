package com.lds.oneplanning.wps.lock.support;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 分布式锁键生成器
 * <p>
 * 用于解析SpEL表达式并生成分布式锁的键
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LockKeyGenerator {

    /**
     * SpEL表达式解析器
     */
    private final ExpressionParser parser = new SpelExpressionParser();

    /**
     * 参数名发现器
     */
    private final ParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    /**
     * SpEL表达式模式
     */
    private static final Pattern SPEL_PATTERN = Pattern.compile("\\#\\{([^}]*)\\}");

    /**
     * 生成锁的键
     *
     * @param prefix 键前缀
     * @param key    键模板，可以包含SpEL表达式
     * @param point  切点
     * @return 生成的锁键
     */
    public String generateKey(String prefix, String key, ProceedingJoinPoint point) {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("分布式锁的key不能为空");
        }

        // 如果不包含SpEL表达式，直接返回
        if (!containsSpEL(key)) {
            return prefix + key;
        }

        // 解析SpEL表达式
        return prefix + parseSpEL(key, point);
    }

    /**
     * 判断字符串是否包含SpEL表达式
     *
     * @param str 要检查的字符串
     * @return 是否包含SpEL表达式
     */
    private boolean containsSpEL(String str) {
        return str.contains("#{");
    }

    /**
     * 解析SpEL表达式
     *
     * @param keyExpr SpEL表达式
     * @param point   切点
     * @return 解析后的字符串
     */
    private String parseSpEL(String keyExpr, ProceedingJoinPoint point) {
        // 创建解析上下文
        EvaluationContext context = createEvaluationContext(point);

        // 使用正则表达式查找并替换所有的SpEL表达式
        Matcher matcher = SPEL_PATTERN.matcher(keyExpr);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String spelExpr = matcher.group(1);
            Object value = parser.parseExpression(spelExpr).getValue(context);
            matcher.appendReplacement(sb, value != null ? value.toString() : "null");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 创建SpEL表达式解析上下文
     *
     * @param point 切点
     * @return 解析上下文
     */
    private EvaluationContext createEvaluationContext(ProceedingJoinPoint point) {
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Method method = methodSignature.getMethod();
        Object[] args = point.getArgs();
        Object target = point.getTarget();

        // 创建方法参数上下文
        MethodBasedEvaluationContext context = new MethodBasedEvaluationContext(
                target, method, args, nameDiscoverer);

        // 将方法参数添加到上下文中
        String[] parameterNames = nameDiscoverer.getParameterNames(method);
        if (Objects.nonNull(parameterNames)) {
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
        }

        return context;
    }
}
