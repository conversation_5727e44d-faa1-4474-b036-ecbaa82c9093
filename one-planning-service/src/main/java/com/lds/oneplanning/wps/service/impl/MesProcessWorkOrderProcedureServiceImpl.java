package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderProcedure;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderProcedure;
import com.lds.oneplanning.wps.mapper.MesProcessWorkOrderProcedureMapper;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderProcedureService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
@Service
public class MesProcessWorkOrderProcedureServiceImpl extends ServiceImpl<MesProcessWorkOrderProcedureMapper, MesProcessWorkOrderProcedure> implements IMesProcessWorkOrderProcedureService {

    @Override
    public void removeByWorkOrderNoList(List<String> workOrderNoList) {
        baseMapper.delete(new QueryWrapper<MesProcessWorkOrderProcedure>().lambda()
                .in(MesProcessWorkOrderProcedure::getWorkOrderNo, workOrderNoList));
    }

    @Override
    public Map<String, List<MesProcessWorkOrderProcedure>> findMapByWorkOrderNumber(List<String> workOrderNumberList) {
        return baseMapper.selectList(Wrappers.lambdaQuery(MesProcessWorkOrderProcedure.class)
                        .in(MesProcessWorkOrderProcedure::getWorkOrderNo, workOrderNumberList)
                        .orderByAsc(MesProcessWorkOrderProcedure::getSortNo,  MesProcessWorkOrderProcedure::getId))
                .stream().collect(Collectors.groupingBy(MesProcessWorkOrderProcedure::getWorkOrderNo));
    }
}
