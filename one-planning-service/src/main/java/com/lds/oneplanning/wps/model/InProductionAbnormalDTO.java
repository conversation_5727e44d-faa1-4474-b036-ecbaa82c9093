package com.lds.oneplanning.wps.model;

import com.lds.oneplanning.wps.entity.MesProcessWorkOrderAndon;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderProcedure;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 在制工单异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13
 */
@Data
@ApiModel(value = "在制工单异常")
public class InProductionAbnormalDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    @ApiModelProperty(value = "排产日期")
    private LocalDate schedulingDate;

    @ApiModelProperty(value = "物料下架日期")
    private LocalDate materialOffShelfDate;

    @ApiModelProperty(value = "上线日期")
    private LocalDate onlineDate;

    @ApiModelProperty(value = "排产设备")
    private String schedulingEquipment;

    @ApiModelProperty(value = "排产模具")
    private String schedulingMold;

    @ApiModelProperty(value = "生产车间")
    private String productionWorkshop;

    @ApiModelProperty(value = "生产课长")
    private String productionForeman;

    @ApiModelProperty(value = "生产线体")
    private String productionLine;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "关联订单号")
    private String associatedOrderNo;

    @ApiModelProperty(value = "计划数量")
    private Integer plannedQuantity;

    @ApiModelProperty(value = "在制天数")
    private Integer productionDays;

    @ApiModelProperty(value = "实际投入数量")
    private Integer actualInputQuantity;

    @ApiModelProperty(value = "gap")
    private Integer actualInputQuantityGap;

    @ApiModelProperty(value = "实际报工数量")
    private Integer actualReportingQuantity;

   @ApiModelProperty(value = "实际报工gap")
    private Integer actualReportedQuantityGap;

    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "是否影响上下层计划")
    private Boolean affectsUpperLevelPlan;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    @ApiModelProperty(value = "建议再计划日期")
    private LocalDate suggestReplanDate;

    @ApiModelProperty(value = "预计可完工日期")
    private LocalDate estimatedCompletionDate;

    @ApiModelProperty(value = "未满单原因")
    private String insufficientOrderReason;

    @ApiModelProperty(value = "原因分类")
    private String reasonCategory;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "已入库数量")
    private Integer inboundQuantity;

    @ApiModelProperty(value = "调整后计划日期")
    private LocalDate adjustPlanDate;

    @ApiModelProperty(value = "计划预计可完工日期")
    private LocalDate pcEstimatedCompletionDate;

    @ApiModelProperty(value = "生产课长工号")
    private String foremanGh;

    @ApiModelProperty(value = "线长工号")
    private String lineLeaderGh;

    @ApiModelProperty(value = "线长名称")
    private String lineLeaderName;

    @ApiModelProperty(value = "计划人员-工号")
    private String plannerEmpNo;

    @ApiModelProperty(value = "计划人员-名称")
    private String planner;

    @ApiModelProperty(value = "SAP状态")
    private String sapStatus;

    @ApiModelProperty(value = "是否产生在制异常")
    private Integer inProductionException;

    @ApiModelProperty(value = "是否完结")
    private Integer isCompleted;

    @ApiModelProperty(value = "线体uuid")
    private String lineUuid;

    @ApiModelProperty(value = "线体编码")
    private String lineCode;

    @ApiModelProperty(value = "流程状态")
    private OrderWarningHandleStatusEnum processStatus;

    /**
     * 工单异常
     */
    List<MesProcessWorkOrderAndon> andonList;
    /**
     * 报工数据
     */
    List<MesProcessWorkOrderProcedure> procedureList;
}
