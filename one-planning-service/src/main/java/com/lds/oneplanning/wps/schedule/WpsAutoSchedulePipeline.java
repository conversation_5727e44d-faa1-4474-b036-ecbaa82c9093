package com.lds.oneplanning.wps.schedule;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.lds.oneplanning.wps.cache.WpsAutoScheduleCache;
import com.lds.oneplanning.wps.entity.WpsSchedulePlanLog;
import com.lds.oneplanning.wps.schedule.enums.WpsScheduleNewHandlerEnum;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.service.IWpsSchedulePlanLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WpsAutoSchedulePipeline {

    private final List<IWpsAutoScheduleHandler> handlers = Lists.newLinkedList();
    @Autowired
    private WpsAutoScheduleCache wpsAutoScheduleCache;
    @Autowired
    IWpsSchedulePlanLogService wpsSchedulePlanLogService;


    @Autowired
    public WpsAutoSchedulePipeline(List<IWpsAutoScheduleHandler> pipelineHandlers) {
        handlers.addAll(pipelineHandlers);
        handlers.sort(Comparator.comparingInt(IWpsAutoScheduleHandler::getOrder));
    }

    public void execute(WpsAutoScheduleContext context) {
        log.debug("自动排产接收到的订单数据：{}",JSON.toJSONString(context.getOrderList()));
        StopWatch stopWatch = new StopWatch();
        List<IWpsAutoScheduleHandler> allHandlers = getAllHandlers(context.getUserId());
        for (IWpsAutoScheduleHandler handler : allHandlers) {
            String simpleName = handler.getClass().getSimpleName();
            stopWatch.start("WpsAutoSchedulePipeline-" + simpleName);
            try {
                handler.execute(context);
            } catch (Exception e) {
                    log.error("WpsAutoSchedulePipeline执行handler={}出错,异常信息={}", simpleName, e.getMessage(), e);
            }
            stopWatch.stop();
        }
        context.setHandleAutoSchedule(true);
        log.info("WpsAutoSchedulePipeline执行完成,订单数量={},执行耗时={}.", context.getOrderList().size(),
                stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        //保存排产的错误日志
        if(context.getIsSaveSchedulePlanLog()){
            List<WpsSchedulePlanLog> wpsSchedulePlanLogs=context.getSchedulePlanLogMap().entrySet().stream().map(entry -> entry.getValue()).collect(Collectors.toList());
            wpsSchedulePlanLogService.batchSave(wpsSchedulePlanLogs,context.getCurrentFactoryCode());
        }
        String dailyProductionLineMapStr = MapUtils.isNotEmpty(context.getDailyProductionLineMap())? JSON.toJSONString(context.getDailyProductionLineMap()):"";
        String orderDailyScheduleDataMapStr = MapUtils.isNotEmpty(context.getOrderDailyScheduleDataMap())?JSON.toJSONString(context.getOrderDailyScheduleDataMap()):"";
        log.info("WpsAutoSchedulePipeline-生产线预排产数据,dailyProductionLineMap:{}",dailyProductionLineMapStr);
        log.info("WpsAutoSchedulePipeline-日排产数据,orderDailyScheduleDataMap:{}",orderDailyScheduleDataMapStr);
    }

    /**
     * 获取新版或旧版的处理器
     * @return
     */
    private List<IWpsAutoScheduleHandler> getAllHandlers(Long userId) {
        List<IWpsAutoScheduleHandler> filterHandlers;
        Boolean isNew = wpsAutoScheduleCache.getAutoScheduleCache(userId);
        //TODO自动排产统一走新版的自动排产
        List<Class> values = Arrays.asList(WpsScheduleNewHandlerEnum.values()).stream().map(WpsScheduleNewHandlerEnum::getClazz).collect(Collectors.toList());
        filterHandlers = handlers.stream().filter(handler -> !values.contains(handler.getClass())).collect(Collectors.toList()) ;
//        if(isNew){
//            List<Class> values = Arrays.asList(WpsScheduleNewHandlerEnum.values()).stream().map(WpsScheduleNewHandlerEnum::getClazz).collect(Collectors.toList());
//            filterHandlers = handlers.stream().filter(handler -> !values.contains(handler.getClass())).collect(Collectors.toList()) ;
//
//        }else{
//            List<Class> values =  Arrays.asList(WpsScheduleNewHandlerEnum.values()).stream().map(WpsScheduleNewHandlerEnum::getNewClazz).collect(Collectors.toList());
//            filterHandlers = handlers.stream().filter(handler -> !values.contains(handler.getClass())).collect(Collectors.toList()) ;
//        }
        log.info("执行的自动排产的handler:{}",filterHandlers);
        return filterHandlers;
    }
}
