package com.lds.oneplanning.wps.controller;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Maps;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsPlanTypeEnum;
import com.lds.oneplanning.wps.req.DeliveryDateAbnormalReq;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.WarningDeliveryDateAbnormalService;
import com.lds.oneplanning.wps.utils.PageHelper;
import com.lds.oneplanning.wps.vo.DeliveryDateAbnormalVO;
import com.lds.oneplanning.wps.warning.workbench.TempMockData;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import com.lds.oneplanning.wps.warning.workbench.handlers.DeliveryDateAbnormalHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api("交期异常")
@RestController
@RequestMapping("/wps/warning/delivery")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningDeliveryDateAbnormalController {
    private final WarningDeliveryDateAbnormalService abnormalService;
    private final IUserInfoService userInfoService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public Page<DeliveryDateAbnormalVO> page(@RequestParam(value = "source", required = false) ViewSource viewSource,
                                             @RequestBody DeliveryDateAbnormalReq vo) {
        ViewSource source = userInfoService.getOrDefaultUserType(viewSource);
        return PageHelper.cover(abnormalService.queryPage(source, vo));
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public void update(@RequestBody DeliveryDateAbnormalVO vo) {
        abnormalService.updateData(vo);
    }

    @GetMapping("/test/generateData")
    public void testGenerateData() {
        log.info("测试数据生成中...");
        WpsAutoScheduleContext ctx = TempMockData.getCtx3();

        WpsWorkbenchWarningContext context = new WpsWorkbenchWarningContext();
        context.setFactoryCode(ctx.getCurrentFactoryCode());
        context.setOrders(ctx.getOrderList());
        context.setProductType(WpsPlanTypeEnum.WHOLE_MACHINE);

        List<WpsOrderPlanWarning> list = SpringUtil.getBean(DeliveryDateAbnormalHandler.class)
                .execute(context, Maps.newHashMap());
        if (CollectionUtils.isNotEmpty(list)) {
            SpringUtil.getBean(IWpsOrderPlanWarningService.class).batchSaveUnHandlerWarning(list);
        }


        log.info("测试数据生成完成");
    }
}
