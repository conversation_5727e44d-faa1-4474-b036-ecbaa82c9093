package com.lds.oneplanning.wps.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class WpsSchedulePlanLogExcelDTO implements Serializable {
    @ExcelProperty("工厂编码")
    private String factoryCode;

    @ExcelProperty("订单类型")
    private String orderType;

    @ExcelProperty("销售订单号")
    private String sellOrderNo;

    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("行项目")
    private String rowItem;

    @ExcelProperty("产品id")
    private String productId;

    @ExcelProperty("商品id")
    private String commodityId;

    @ExcelProperty("错误码名称")
    private String errorName;

    @ExcelProperty("备注")
    private String remark;
}
