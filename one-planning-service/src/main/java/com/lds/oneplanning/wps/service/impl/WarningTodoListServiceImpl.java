package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.lds.basic.account.user.api.UserApi2;
import com.lds.basic.account.user.dto.UserDto;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.entity.WarningTodoList;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.lock.DistributedLock;
import com.lds.oneplanning.wps.mapper.WarningTodoListMapper;
import com.lds.oneplanning.wps.po.WarningTodoPO;
import com.lds.oneplanning.wps.req.TodoHandleReq;
import com.lds.oneplanning.wps.service.IPushMsgToOAService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【warning_todo_list】的数据库操作Service实现
 * @createDate 2025-05-16 09:58:56
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningTodoListServiceImpl extends ServiceImpl<WarningTodoListMapper, WarningTodoList>
        implements WarningTodoListService {
    private final IPushMsgToOAService pushMsgToOAService;
    private final UserApi2 userApi2;
    private final EsbDataFetchService esbDataFetchService;

    private static final String[] MOCK_USER_NAME = {"daishaokun1", "huanglingcong", "liurongfu", "zhangzijian", "hongzhenping"};

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(WpsOrderWarningTypeEnum warningType, List<WarningTodoList> todoList) {
        if (warningType == null || CollectionUtils.isEmpty(todoList)) {
            return;
        }

        // 从 todoList 中提取出 bizId 列表，去除空值和重复项
        List<Long> bizIdList = todoList.stream()
                .map(WarningTodoList::getBizId)
                .filter(Objects::nonNull) // 过滤掉 bizId 为 null 的项
                .distinct() // 去重
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(bizIdList)) {
            // 如果所有 todoList 的 bizId 都为 null，则无需查询现有数据，全部视为新增
            // 直接调用 saveData 方法保存 todoList
            saveData(todoList);
            return;
        }

        // 根据 bizId 和分配人分组查询现有数据
        Map<String, WarningTodoList> existingDataMap = queryExistDataAndGroupByBizIdAndAssignee(warningType, bizIdList);

        // 初始化要插入和要更新的列表
        List<WarningTodoList> toBeInserted = new java.util.ArrayList<>();
        List<WarningTodoList> toBeUpdated = new java.util.ArrayList<>();
        CopyOptions copyOptions = CopyOptions.create().ignoreNullValue(); // 创建忽略空值的复制选项

        // 遍历 todoList，根据条件判断是新增还是更新
        for (WarningTodoList currentTodoItem : todoList) {
            if (currentTodoItem.getBizId() == null) { // BizId 为 null 的直接新增
                toBeInserted.add(currentTodoItem);
                continue;
            }
            String key = currentTodoItem.getBizId() + "#" + currentTodoItem.getAssignee(); // 构造 key
            WarningTodoList existingItem = existingDataMap.get(key); // 根据 key 从 existingDataMap 中获取现有数据

            if (existingItem != null) {
                // 存在，则更新
                BeanUtil.copyProperties(currentTodoItem, existingItem, copyOptions); // 使用 copyOptions 复制属性
                // existingItem 已经包含了ID，可以直接用于批量更新
                toBeUpdated.add(existingItem);
                existingDataMap.remove(key); // 从 map 中移除，剩下的就是需要删除的
            } else {
                // 不存在，则新增
                toBeInserted.add(currentTodoItem);
            }
        }

        // 删除 existingDataMap 中余下的条目，即数据库中有但新的 todoList 中没有的记录
        removeData(existingDataMap);

        // 保存要插入的数据
        saveData(toBeInserted);

        // 更新要更新的数据
        updateData(toBeUpdated);
    }


    /**
     * 根据业务类型和业务ID列表查询并分组待办事项列表
     *
     * @param warningType 警告类型
     * @param bizIdList   业务ID列表
     * @return 分组后的待办事项列表，Map的键为业务ID+分配者字符串，值为对应的待办事项列表对象
     */
    private Map<String, WarningTodoList> queryExistDataAndGroupByBizIdAndAssignee(WpsOrderWarningTypeEnum warningType, List<Long> bizIdList) {
        List<WarningTodoList> existingDbEntries = super.lambdaQuery()
                .eq(WarningTodoList::getWarningType, warningType.getCode())
                .in(WarningTodoList::getBizId, bizIdList)
                .list();

        // 使用 bizId + assignee 作为key，方便查找
        // 保留第一个，处理潜在重复
        return existingDbEntries.stream()
                .collect(Collectors.toMap(e -> e.getBizId() + "#" + e.getAssignee(), e -> e, (e1, e2) -> e1));
    }

    /**
     * 更新数据
     *
     * @param toBeUpdated 要更新的待办事项警告列表
     */
    private void updateData(List<WarningTodoList> toBeUpdated) {
        if (CollectionUtils.isNotEmpty(toBeUpdated)) {
            super.updateBatchById(toBeUpdated, 1000); // 使用MyBatis Plus的批量更新
        }
    }

    /**
     * 保存数据到数据库
     *
     * @param toBeInserted 待插入的待办事项列表
     */
    private void saveData(List<WarningTodoList> toBeInserted) {
        if (CollectionUtils.isNotEmpty(toBeInserted)) {
            super.saveBatch(toBeInserted, 1000);
        }
    }

    /**
     * 从现有数据映射中移除数据。
     *
     * @param existingDataMap 现有的数据映射，键为字符串，值为WarningTodoList对象。
     */
    private void removeData(Map<String, WarningTodoList> existingDataMap) {
        if (CollectionUtils.isNotEmpty(existingDataMap.values())) {
            List<Long> idsToRemove = existingDataMap.values().stream()
                    .map(WarningTodoList::getId)
                    .collect(Collectors.toList());
            super.removeByIds(idsToRemove);
        }
    }


    private String randomAssignee(String assignee) {
        return StringUtils.isEmpty(assignee) ? assignee : getMockUserName();
    }

    private String getMockUserName() {
        int index = (int) (Math.random() * MOCK_USER_NAME.length);
        return MOCK_USER_NAME[index];
    }

    /**
     * 推送LCP消息，不要添加事务，成功一条更新一条，失败不回滚其他数据。
     */
    @Override
    @DistributedLock(key = "pushLcp")
    public void pushLcp() {
        //查询未处理的数据
        Integer pushStatus = 0;
        List<WpsOrderWarningTypeEnum> types = Collections.emptyList();
        pushMessage(pushStatus, types);
    }

    @Override
    @DistributedLock(key = "pushLcp")
    public void forcePushLcp(WpsOrderWarningTypeEnum type, Set<Long> bizIds) {
        List<WarningTodoPO> list = baseMapper.queryUnClosedData(type, bizIds);
        doPush(list, false);
    }

    private void pushMessage(Integer pushStatus, List<WpsOrderWarningTypeEnum> types) {
        List<WarningTodoPO> list = baseMapper.queryUnHandleData(pushStatus, types);
        doPush(list, true);
    }

    private void doPush(List<WarningTodoPO> list, boolean recordResult) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("没有待处理的数据");
        }

        for (WarningTodoPO po : list) {
            WpsOrderWarningTypeEnum type = WpsOrderWarningTypeEnum.getByCode(po.getWarningType());
            if (type == null) {
                log.warn("不存在的警告类型:{}", po.getWarningType());
                continue;
            }

            toPush(po, type, recordResult);
        }
        log.info("推送LCP消息完成");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void eliminateAlarms(Collection<Long> bizIds, WpsOrderWarningTypeEnum warningType) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return;
        }
        super.lambdaUpdate()
                .in(WarningTodoList::getBizId, bizIds)
                .eq(WarningTodoList::getWarningType, warningType)
                .set(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.CLOSED)
                .update();
    }

    @Override
    public void handle(TodoHandleReq req) {
        super.lambdaUpdate()
                .in(WarningTodoList::getBizId, req.getIds())
                .eq(WarningTodoList::getWarningType, req.getType())
                .eq(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.UN_HANDLE)
                .set(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.HANDLED)
                .update();
    }

    private void toPush(WarningTodoPO po, WpsOrderWarningTypeEnum type, boolean recordResult) {
        String template = "您有未处理的排产异常，请及时处理。"
                + System.lineSeparator() + "异常类型：{}"
                + System.lineSeparator() + "工厂编码:{}"
                + System.lineSeparator() + "数量:{}";

        //就一个正式环境，懒得做配置了
        String domain = "https://one-planning.leedarson.com";

        String text = StrUtil.format(template, type.getName(), po.getFactoryCode(), po.getNum());
        String baseUrl = domain + type.getViewPageUri();
        String url = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .queryParam("factoryCode", po.getFactoryCode())
                .build()
                .toUriString();

        if (true) {
            //测试环境，直接打印日志
            log.info("推送消息先不处理: {}, 处理人:{}, 警告类型:{}, 工厂编码:{}, 数量:{}", true, po.getAssignee(), type.getName(), po.getFactoryCode(), po.getNum());
            return;
        }

        try {
            //推送消息到LCP公众号
            boolean isSuccess = pushMsgToOAService.pushMsgToOAByLoginName(text, Collections.singletonList(po.getAssignee()), 5, url);
            log.info("推送消息结果: {}, 处理人:{}, 警告类型:{}, 工厂编码:{}, 数量:{}", isSuccess, po.getAssignee(), type.getName(), po.getFactoryCode(), po.getNum());

            if (isSuccess && recordResult) {
                // 使用新的SQL方法更新push_status
                baseMapper.updatePushStatusByAssigneeWarningTypeFactory(
                        po.getAssignee(),
                        po.getWarningType(),
                        po.getFactoryCode());
            }
        } catch (Exception e) {
            log.error("推送消息失败", e);
        }
    }


    @DistributedLock(key = "pushLcp")
    @Override
    public void firstUpgrade() {
        //8:30第一次
        //10第二次
        //11    第三次
        //12    第四次

        //物料不齐套，交期异常，来料检验异常 * 2
        //在制工单 * 4

        Integer pushStatus = 1;
        List<WpsOrderWarningTypeEnum> upgradeTypes = Arrays.stream(WpsOrderWarningTypeEnum.values())
                .filter(WpsOrderWarningTypeEnum::isTodoUpgradeable)
                .collect(Collectors.toList());
        upgradeInProduction(upgradeTypes, pushStatus);
    }

    @DistributedLock(key = "pushLcp")
    @Override
    public void secondUpgrade() {
        Integer pushStatus = 2;
        List<WpsOrderWarningTypeEnum> types = Collections.singletonList(WpsOrderWarningTypeEnum.IN_PRODUCTION_EXCEPTION);
        upgradeInProduction(types, pushStatus);
    }

    private void upgradeInProduction(List<WpsOrderWarningTypeEnum> types, Integer pushStatus) {
        upgradeLeader(pushStatus, types);
        pushMessage(pushStatus, types);
    }

    @DistributedLock(key = "pushLcp")
    @Override
    public void thirdUpgrade() {
        Integer pushStatus = 3;
        List<WpsOrderWarningTypeEnum> types = Collections.singletonList(WpsOrderWarningTypeEnum.IN_PRODUCTION_EXCEPTION);
        upgradeInProduction(types, pushStatus);
    }

    @DistributedLock(key = "pushLcp")
    @Override
    public void fourthUpgrade() {
        Integer pushStatus = 4;
        List<WpsOrderWarningTypeEnum> types = Collections.singletonList(WpsOrderWarningTypeEnum.IN_PRODUCTION_EXCEPTION);
        upgradeInProduction(types, pushStatus);
    }

    private void upgradeLeader(Integer pushStatus, List<WpsOrderWarningTypeEnum> types) {
        List<WarningTodoList> list = super.lambdaQuery()
                .eq(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.UN_HANDLE)
                .eq(WarningTodoList::getPushStatus, pushStatus)
                .in(CollectionUtils.isNotEmpty(types), WarningTodoList::getWarningType, types)
                .ne(WarningTodoList::getAssignee, "")
                .list();

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        //搜索并设置上级为待办人
        searchLeader(list);

        super.updateBatchById(list);
    }

    private void searchLeader(List<WarningTodoList> list) {
        Set<String> userLoginNames = list.stream().map(WarningTodoList::getAssignee).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(userLoginNames)) {
            return;
        }

        //调用esb接口，根据userLoginNames换成员工编号

        Map<String, String> jobNoMap = Maps.newHashMap();
        for (String userLoginName : userLoginNames) {
            UserDto req = new UserDto();
            req.setLoginName(userLoginName);
            Optional.ofNullable(userApi2.getDto(req)).ifPresent(e -> jobNoMap.put(userLoginName, e.getJobNo()));
        }
        Set<String> keys = jobNoMap.keySet();

        if (CollectionUtils.isEmpty(keys)) {
            return;
        }

        Map<String, String> leaderMap = esbDataFetchService.fetchLeader(keys);
        log.info("查询ESB上级结果: {}", leaderMap);
        for (WarningTodoList todo : list) {
            Optional.ofNullable(leaderMap.get(todo.getAssignee())).ifPresent(todo::setAssignee);
        }
        log.info("升级待办人完成");
    }
}




