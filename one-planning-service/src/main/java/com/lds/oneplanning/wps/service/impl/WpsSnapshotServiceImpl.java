package com.lds.oneplanning.wps.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lds.oneplanning.wps.entity.WpsPlanVersion;
import com.lds.oneplanning.wps.entity.WpsSnapshot;
import com.lds.oneplanning.wps.enums.WpsPlanSourceEnum;
import com.lds.oneplanning.wps.helper.WpsSnapshotHelper;
import com.lds.oneplanning.wps.mapper.WpsSnapshotMapper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsSnapshotService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【wps_snapshot】的数据库操作Service实现
* @createDate 2025-05-08 11:38:52
*/
@Slf4j
@Service
public class WpsSnapshotServiceImpl extends ServiceImpl<WpsSnapshotMapper, WpsSnapshot> implements IWpsSnapshotService {

    @Autowired
    WpsSnapshotHelper wpsSnapshotHelper;
    @Override
    public void deleteByPlanVersionId(Long planVersionId) {
        this.remove(Wrappers.<WpsSnapshot>lambdaQuery().eq(WpsSnapshot::getPlanVersionId,planVersionId));
    }

    @Override
    public void saveSnapshot(List<WpsRowData> wpsRowDatas, WpsPlanVersion wpsPlanVersion) {
        List<WpsSnapshot> wpsSnapshots = wpsRowDatas.stream().map(wpsRowData ->wpsSnapshotHelper.createWpsSnapshot(wpsRowData,wpsPlanVersion)).collect(Collectors.toList());
        log.info("saveSnapshot版本信息：{}",wpsPlanVersion);
        Map<String,WpsSnapshot> wpsSnapshotMap = findList(wpsPlanVersion,wpsRowDatas);
        //存在的版本做更新
        for(WpsSnapshot wpsSnapshot:wpsSnapshots){
            String key  = wpsSnapshot.getVersion()+"-"+wpsSnapshot.getBizId()+"-"+wpsSnapshot.getLineCode();
            if(!wpsSnapshotMap.containsKey(key)){
                continue;
            }
            WpsSnapshot oldWpsSnapshot = wpsSnapshotMap.get(key);
            wpsSnapshot.setId(oldWpsSnapshot.getId());
            wpsSnapshot.setCreateTime(oldWpsSnapshot.getCreateTime());
        }
        //保存快照信息
        List<WpsSnapshot> updateDatas = wpsSnapshots.stream().filter(wpsSnapshot -> Objects.nonNull(wpsSnapshot.getId())).collect(Collectors.toList());
        List<WpsSnapshot> insertDatas = wpsSnapshots.stream().filter(wpsSnapshot -> Objects.isNull(wpsSnapshot.getId())).collect(Collectors.toList());
        this.saveBatch(insertDatas);
        this.updateBatchById(updateDatas);
    }


    @Override
    public List<WpsRowData> findList(String plannerEmpNo, String factoryCode, String version,List<String> bizIds, WpsPlanSourceEnum source) {
        List<WpsRowData> resultList = Lists.newArrayList();
        List<WpsSnapshot> wpsRowDatas = this.list(Wrappers.<WpsSnapshot>lambdaQuery()
                .eq(StringUtils.isNotBlank(plannerEmpNo),WpsSnapshot::getPlannerEmpNo,plannerEmpNo)
                .eq(StringUtils.isNotBlank(factoryCode),WpsSnapshot::getFactoryCode,factoryCode)
                .eq(StringUtils.isNotBlank(version),WpsSnapshot::getVersion,version)
                .in(CollectionUtils.isNotEmpty(bizIds),WpsSnapshot::getBizId,bizIds)
                .eq(Objects.nonNull(source),WpsSnapshot::getSource,source.getCode()));
        if(CollectionUtils.isEmpty(wpsRowDatas)){
          return resultList;
        }
        wpsRowDatas.forEach(data->{
            if(StringUtils.isBlank(data.getWpsData())){
                return;
            }
            resultList.add(JSONObject.parseObject(data.getWpsData(),WpsRowData.class));
        });
        return resultList;
    }

    @Override
    public List<WpsRowData> listByCreateTime(String factoryCode, WpsPlanSourceEnum source, LocalDate startDate, LocalDate endDate) {
        // 根据更新时间倒序,保证最新的在前面
        List<WpsSnapshot> snapshots = baseMapper.selectList(Wrappers.<WpsSnapshot>lambdaQuery()
                .eq(StringUtils.isNotBlank(factoryCode),WpsSnapshot::getFactoryCode,factoryCode)
                .eq(source!=null,WpsSnapshot::getSource,source.getCode())
                .ge(startDate!= null,WpsSnapshot::getCreateTime,startDate)
                .lt(endDate!=null,WpsSnapshot::getCreateTime,endDate)
                .orderByDesc(WpsSnapshot::getUpdateTime)
        );
        Set<WpsRowData> resSet = Sets.newHashSet();
        snapshots.forEach(data->{
            if(StringUtils.isBlank(data.getWpsData())){
               return;
            }
            // 这行代码会根据 lineCode和orderNo进行判重，保留第一次出现的
            resSet.add(JSONObject.parseObject(data.getWpsData(),WpsRowData.class));
        });
        return Lists.newArrayList(resSet);
    }

    /**
     * 获取版本快照信息
     * @param wpsPlanVersion
     * @param wpsRowDatas
     * @return
     */
    private Map<String,WpsSnapshot> findList(WpsPlanVersion wpsPlanVersion, List<WpsRowData> wpsRowDatas){
        List<String> bizIds = wpsRowDatas.stream().filter(data-> StringUtils.isNotBlank(data.getOrderNo())).map(WpsRowData::getOrderNo).collect(Collectors.toList());
        return this.list(Wrappers.<WpsSnapshot>lambdaQuery()
                .eq(WpsSnapshot::getVersion,wpsPlanVersion.getVersion())
                .in(WpsSnapshot::getBizId,bizIds))
                .stream().collect(Collectors.toMap(data->data.getVersion()+"-"+data.getBizId()+"-"+data.getLineCode(), data->data,(k1, k2)->k1));
    }


}




