package com.lds.oneplanning.wps.schedule.model;

import com.google.common.collect.Maps;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 生产线类
 */
@Data
public class WpsProductionLine implements Serializable {

    private static final long serialVersionUID = -3828939045265400241L;

    /**
     * 产线UUID
     */
    private String lineUuid;

    /**
     * 产线编码
     */
    private String lineCode;

    /**
     * 产线类计划时长：线体类对应日期的计划生产时长*排产比例
     */
    private float planScheduleHours;

    /**
     * 已排产时长
     */
    private float scheduledHours;

    /**
     * 产线类待排产时长：SUM(线体类对应日期的生产时长*排产比例)
     */
    private float waitingScheduleHours;

    /**
     * 订单已排产时长Map：key为订单号，value为该订单已排产时长
     */
    private Map<String, Float> orderScheduledHoursMap = Maps.newLinkedHashMap();
}