package com.lds.oneplanning.wps.service.facade.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.model.PlannerLineCfgDTO;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.basedata.service.ILineUphService;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.basedata.service.IPlannerLineCfgService;
import com.lds.oneplanning.common.utils.OrderArithUtil;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsOrderTypeEnum;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import com.lds.oneplanning.wps.service.IWpsDayPlanService;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WpsOrderCommonServiceImpl implements IWpsOrderCommonService {

    @Autowired
    private IWpsDayPlanService wpsDayPlanService;

    @Autowired
    private ILineUphService lineUphService;

    @Autowired
    private IPlannerLineCfgService plannerLineCfgService;

    @Autowired
    private ILineInfoService lineInfoService;

    @Autowired
    private IPlannerDataPermissionService plannerDataPermissionService;

    @Override
    public void doFrozenOrders(WpsAutoScheduleContext context, List<WpsRowData> frozenOrderList, List<LocalDate> localDates) {
        if (CollectionUtils.isEmpty(frozenOrderList) || CollectionUtils.isEmpty(localDates)) {
            return;
        }
        LocalDate startDate = localDates.get(0);
        LocalDate endDate = localDates.get(localDates.size() - 1);
        List<String> frozenOrderNos = frozenOrderList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toList());
        Map<String, Map<String, Map<LocalDate, Integer>>> frozenOrderMap = wpsDayPlanService
                .getMapByBizIdsAndDates(frozenOrderNos, startDate, endDate);
        if (MapUtils.isEmpty(frozenOrderMap)) {
            return;
        }
        frozenOrderList.forEach(order -> processOrder(context, order, WpsOrderTypeEnum.FROZEN, frozenOrderMap, localDates));
    }

    @Override
    public Map<String, Map<String, Float>> getLineProductUphMap(Map<String, List<String>> lineProductIdMap) {
        if (MapUtils.isEmpty(lineProductIdMap)) {
            return Maps.newHashMap();
        }
        return loadLineProductUphMapFromDB(lineProductIdMap);
    }

    @Override
    public Set<String> getSuitableLineUuids(WpsAutoScheduleContext context, WpsRowData order, Integer priority) {
        String productGroupCode = order.get_productGroupCode();
        String customerCode = order.getCustomerCode();
        Set<String> suitableLineUuids = new LinkedHashSet<>();
        Map<Integer, Set<String>> allLinePriorityMap = new HashMap<>();
        // 从上下文中获取优先级映射
        Map<String, Map<String, Map<Integer, Set<String>>>> customerProductGroupPriorityMap = context.getCustomerProductGroupPriorityMap();
        Map<String, Map<Integer, Set<String>>> customerLinePriorityMap = context.getCustomerLinePriorityMap();
        Map<String, Map<Integer, Set<String>>> productGroupPriorityMap = context.getProductGroupPriorityMap();
        // 处理优先级映射
        processPriorityMap(priority, customerProductGroupPriorityMap, customerCode, productGroupCode, allLinePriorityMap);
        processPriorityMap(priority, customerLinePriorityMap, customerCode, allLinePriorityMap);
        processPriorityMap(priority, productGroupPriorityMap, productGroupCode, allLinePriorityMap);

        Map<Integer, Set<String>> productGroupPriorityLog =  productGroupPriorityMap.get(productGroupCode);
        log.info("订单获取到线体数据orderNo:{},userId:{},priority:{},productGroupPriorityLog:{},productGroupCode:{}",  order.getOrderNo(), context.getUserId(), priority, productGroupPriorityLog,productGroupCode);
        // 按优先级排序并添加到合适的产线代码集合中
        allLinePriorityMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> suitableLineUuids.addAll(entry.getValue()));
        return suitableLineUuids;
    }

    @Override
    public List<LineInfo> getLineInfosByUserId(Long userId) {
        if (null == userId) {
            return Lists.newArrayList();
        }
        // 查询计划员是否有配置可排产产线
        List<PlannerLineCfgDTO> mpsPlannerLineCategoryDTOS = plannerLineCfgService.listByUserId(userId, BaseDataConstant.CONFIG_TYPE_LINE);
        if (CollectionUtils.isNotEmpty(mpsPlannerLineCategoryDTOS)) {
            // 计划员有配置可排产产线类
            List<String> lineUuids = mpsPlannerLineCategoryDTOS.stream()
                    .map(PlannerLineCfgDTO::getLineUuid).
                    distinct().collect(Collectors.toList());
            return lineInfoService.listByUuids(lineUuids);
        }
        // 计划员未配置可排产产线，根据车间查询所有产线
        List<PlannerDataPermission> mpsPlannerDataPermissions = plannerDataPermissionService.listByUserId(userId);
        if (CollectionUtils.isNotEmpty(mpsPlannerDataPermissions)) {
            List<String> workshopCodes = mpsPlannerDataPermissions.stream().map(PlannerDataPermission::getWorkshopCode)
                    .distinct().collect(Collectors.toList());
            return lineInfoService.listByWorkshopCodes(workshopCodes);
        }
        return Lists.newArrayList();
    }

    private void processPriorityMap(Integer priority,
                                    Map<String, Map<String, Map<Integer, Set<String>>>> priorityMap,
                                    String customerCode,
                                    String productGroupCode,
                                    Map<Integer, Set<String>> allLinePriorityMap) {
        if (MapUtils.isNotEmpty(priorityMap) && StringUtils.isNotEmpty(customerCode) && StringUtils.isNotEmpty(productGroupCode)) {
            Map<String, Map<Integer, Set<String>>> productGroupPriorityMap = priorityMap.get(customerCode);
            if (MapUtils.isNotEmpty(productGroupPriorityMap)) {
                addLineUuid(priority, productGroupPriorityMap.get(productGroupCode), allLinePriorityMap);
            }
        }
    }

    private void processPriorityMap(Integer priority,
                                    Map<String, Map<Integer, Set<String>>> priorityMap,
                                    String key,
                                    Map<Integer, Set<String>> allLinePriorityMap) {
        if (MapUtils.isNotEmpty(priorityMap) && StringUtils.isNotEmpty(key)) {
            addLineUuid(priority, priorityMap.get(key), allLinePriorityMap);
        }
    }

    /**
     * 添加产线代码的辅助方法
     *
     * @param priority
     * @param linePriorityMap
     * @param allLinePriorityMap
     */
    private void addLineUuid(Integer priority,
                             Map<Integer, Set<String>> linePriorityMap,
                             Map<Integer, Set<String>> allLinePriorityMap) {
        if (MapUtils.isNotEmpty(linePriorityMap)) {
            Set<String> lineUuids = linePriorityMap.get(priority);
            if (CollectionUtils.isNotEmpty(lineUuids)) {
                allLinePriorityMap.computeIfAbsent(priority, k -> new LinkedHashSet<>()).addAll(lineUuids);
            }
        }
    }

    private Map<String, Map<String, Float>> loadLineProductUphMapFromDB(Map<String, List<String>> lineProductIdMap) {
        Map<String, Map<String, Float>> resultMap = Maps.newHashMap();
        lineProductIdMap.forEach((lineUuid, productIds) -> {
            if (CollectionUtils.isEmpty(productIds)) {
                return;
            }
            Map<String, Float> uphByTypeAndCodes = lineUphService.getUphByTypeAndUuid(BaseDataConstant.CONFIG_TYPE_LINE, lineUuid, productIds);
            if (MapUtils.isEmpty(uphByTypeAndCodes)) {
                return;
            }
            resultMap.put(lineUuid, uphByTypeAndCodes);
        });
        return resultMap;
    }

    @Override
    public void processOrder(WpsAutoScheduleContext context, WpsRowData order, WpsOrderTypeEnum wpsOrderTypeEnum,
                              Map<String, Map<String, Map<LocalDate, Integer>>> planedOrderMap,
                              List<LocalDate> localDates) {
        String orderNo = order.getOrderNo();
        int waitingOrderQty = order.getSchedulePcsQty().intValue();
        LocalDate estProductionFinishDate = order.get_estProductionFinishDate();

        Map<String, Map<LocalDate, Integer>> lineUuidMap = planedOrderMap.get(orderNo);
        if (MapUtils.isEmpty(lineUuidMap)) {
            return;
        }
        // 订单->生产线UUID->uph映射
        Map<String, Map<String, Float>> orderLineUphMap = context.getOrderLineUphMap();
        if (MapUtils.isEmpty(orderLineUphMap)) {
            return;
        }
        Map<String, Float> lineUuidUphMap = orderLineUphMap.get(orderNo);
        if (MapUtils.isEmpty(lineUuidUphMap)) {
            return;
        }
        lineUuidMap.forEach((lineUuid, dateMap) -> {
            Map<LocalDate, WpsProductionLine> dailyProductionLineMap = context.getDailyProductionLineMap().get(lineUuid);
            Float uph = lineUuidUphMap.get(lineUuid);
            if (null == uph) {
                return;
            }
            dateMap.forEach((date, scheduledQty) -> {
                if(Objects.isNull(estProductionFinishDate)){
                    log.info("estProductionFinishDate为空，orderNO:{}",orderNo);
                    return;
                }
                // 判断订单是否为未完工且已过期的订单
                if (WpsOrderTypeEnum.PUBLISHED.equals(wpsOrderTypeEnum) && waitingOrderQty > 0
                        && (estProductionFinishDate.isBefore(date) || estProductionFinishDate.isEqual(date))) {
                    scheduledQty += waitingOrderQty;
                    order.setWaitingOrderQty(0);
                }
                updateProductionLine(context, dailyProductionLineMap, lineUuid, orderNo, date, scheduledQty, uph);
            });
        });
    }

    /**
     * 7天内的订单不参与自动排产
     * @param context
     * @param order
     * @param planedOrderMap
     */
    @Override
    public void processSaveOrder(WpsAutoScheduleContext context, WpsRowData order,
                             Map<String, Map<String, Map<LocalDate, Integer>>> planedOrderMap,LocalDate endDate) {
        String orderNo = order.getOrderNo();
        int waitingOrderQty = order.getSchedulePcsQty().intValue();

        Map<String, Map<LocalDate, Integer>> lineUuidMap = planedOrderMap.get(orderNo);
        if (MapUtils.isEmpty(lineUuidMap)) {
            log.info("processSaveOrder->lineUuidMap为空,orderNo:{}",order.getOrderNo());
            return;
        }
        // 订单->生产线UUID->uph映射
        Map<String, Map<String, Float>> orderLineUphMap = context.getOrderLineUphMap();
        if (MapUtils.isEmpty(orderLineUphMap)) {
            return;
        }
        Map<String, Float> lineUuidUphMap = orderLineUphMap.get(orderNo);
        if (MapUtils.isEmpty(lineUuidUphMap)) {
            log.info("processSaveOrder->lineUuidUphMap,orderNo:{}",order.getOrderNo());
            return;
        }
        lineUuidMap.forEach((lineUuid, dateMap) -> {
            Map<LocalDate, WpsProductionLine> dailyProductionLineMap = context.getDailyProductionLineMap().computeIfAbsent(lineUuid, k -> Maps.newLinkedHashMap());
            Float uph = lineUuidUphMap.get(lineUuid);
            if (null == uph) {
                log.info("processSaveOrder->uph为空,orderNo:{},lineUuid:{}",order.getOrderNo(),lineUuid);
                return;
            }
            int totalScheduledQty = 0;
            //最近7天数据不参与自动排产
            for(Map.Entry<LocalDate, Integer> lineDateMap:  dateMap.entrySet()){
                LocalDate date = lineDateMap.getKey();
                int scheduledQty =lineDateMap.getValue();
                if(date.compareTo(endDate)>0){
                    break;
                }
                setWpsProductionLine(dailyProductionLineMap,date,scheduledQty,lineUuid,orderNo,uph);
                updateProductionLine(context, dailyProductionLineMap, lineUuid, orderNo, date, scheduledQty, uph);
                log.info("7天内的订单不参与自动排产->processSaveOrder,orderNo:{},lineUuId:{},scheduledQty:{},date:{}",orderNo,lineUuid,scheduledQty,date);
                totalScheduledQty=totalScheduledQty+scheduledQty;
            }
            //待产数量大于7天内已排数量，则把7天后以排的数量都不参与排产
            if(waitingOrderQty>totalScheduledQty){
                dateMap.forEach((date, scheduledQty) -> {
                    log.info("待产数量大于7天内已排数量，则把7天后以排的数量都不参与排产，orderNo:{},lineUuId:{},scheduledQty:{},date:{}",orderNo,lineUuid,scheduledQty,date);
                    if(date.isAfter(endDate)){
                        //已排产的数据不存在需要手动填充
                        setWpsProductionLine(dailyProductionLineMap,date,scheduledQty,lineUuid,orderNo,uph);
                        updateProductionLine(context, dailyProductionLineMap, lineUuid, orderNo, date, scheduledQty, uph);
                    }
                });
            }
        });
    }
    public void updateProductionLine(WpsAutoScheduleContext context, Map<LocalDate, WpsProductionLine> dailyProductionLineMap,
                                      String lineUuid, String orderNo, LocalDate date,
                                      Integer scheduledQty, Float uph) {
        float waitingOrderHour = OrderArithUtil.floatDivide(scheduledQty, uph);
        WpsProductionLine wpsProductionLine = dailyProductionLineMap.get(date);
        if(Objects.isNull(wpsProductionLine)){
            log.info("wpsProductionLine不存在，orderNo:{},lineUuid:{},dailyProductionLineMap:{}",orderNo,lineUuid, dailyProductionLineMap);
            return;
        }
        wpsProductionLine.setWaitingScheduleHours(OrderArithUtil.floatSubtract(wpsProductionLine.getWaitingScheduleHours(), waitingOrderHour));
        wpsProductionLine.setScheduledHours(OrderArithUtil.floatAdd(wpsProductionLine.getScheduledHours(), waitingOrderHour));
        updateScheduledHours(wpsProductionLine, orderNo, waitingOrderHour);
        updateDailyScheduleData(context, orderNo, date, lineUuid, scheduledQty);
    }

    private void updateScheduledHours(WpsProductionLine productionLine, String orderNo, float hours) {
        Map<String, Float> orderScheduledHoursMap = productionLine.getOrderScheduledHoursMap();
        orderScheduledHoursMap.merge(orderNo, hours, Float::sum);
    }

    public void updateDailyScheduleData(WpsAutoScheduleContext context, String orderNo, LocalDate date, String lineUuid, int scheduledQty) {
        Map<LocalDate, Map<String, Integer>> localDateMapMap = context.getOrderDailyScheduleDataMap()
                .computeIfAbsent(orderNo, k -> Maps.newHashMap());
        localDateMapMap.computeIfAbsent(date, k -> Maps.newHashMap())
                .merge(lineUuid, scheduledQty, Integer::sum);
        log.debug("orderNo:{}, date:{}, lineUuid:{}, scheduledQty:{}", orderNo, date, lineUuid, scheduledQty);
    }
    private void setWpsProductionLine(Map<LocalDate, WpsProductionLine> dailyProductionLineMap,LocalDate scheduledDate,int scheduledQty,String lineUuid,String orderNo,Float uph){
        if(!dailyProductionLineMap.containsKey(scheduledDate)){
            WpsProductionLine wpsProductionLine = new WpsProductionLine();
            float waitingOrderHour = OrderArithUtil.floatDivide(scheduledQty, uph);
            wpsProductionLine.setLineUuid(lineUuid);
            wpsProductionLine.setWaitingScheduleHours(waitingOrderHour);
            wpsProductionLine.setPlanScheduleHours(waitingOrderHour);
            dailyProductionLineMap.put(scheduledDate, wpsProductionLine);
            log.info("最近7天已保存的订单没有在排产计划里面，orderNo:{},lineUuid:{},date:{},scheduleQty:{}",orderNo,lineUuid,scheduledDate,scheduledQty);
        }
    }
}