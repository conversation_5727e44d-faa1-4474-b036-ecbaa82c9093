package com.lds.oneplanning.wps.warning;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.lds.coral.mq.rocketmq.producer.RocketMQTemplateProducer;
import com.lds.coral.mq.rocketmq.producer.TraceSendCallback;
import com.lds.oneplanning.wps.constants.RocketMQConstants;
import com.lds.oneplanning.wps.enums.WpsPlanTypeEnum;
import com.lds.oneplanning.wps.event.WpsPlanWarningEvent;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.req.WpsPlanWarningMQReq;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessContext;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessEnum;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningPipeline;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class WpsPlanWarningManager {

    @Autowired
    private WpsWorkbenchWarningPipeline wpsWorkbenchWarningPipeline;

    @Autowired
    private RocketMQTemplateProducer rocketMQTemplate;

    @Autowired
    private WpsOrderProcessContext wpsOrderProcessContext;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 通知告警
     *
     * @param factoryCode
     * @param userId
     * @param wpsPlanTypeEnum
     */
    public void notify(String factoryCode, Long userId, WpsPlanTypeEnum wpsPlanTypeEnum) {
        if (factoryCode == null || userId == null || wpsPlanTypeEnum == null) {
            log.error("Invalid input parameters: factoryCode={}, userId={}, wpsPlanTypeEnum={}",
                    factoryCode, userId, wpsPlanTypeEnum);
            return;
        }
        WpsPlanWarningMQReq wpsPlanWarningMQReq = WpsPlanWarningMQReq.builder()
                .factoryCode(factoryCode)
                .userId(userId)
                .planTpe(wpsPlanTypeEnum.name())
                .build();
        rocketMQTemplate.asyncSendMessage(RocketMQConstants.LDX_OP_WPS_WARNING_TOPIC, JSON.toJSONString(wpsPlanWarningMQReq),
                new TraceSendCallback() {

                    @Override
                    public void ok(SendResult sendResult) {
                        log.info("Successfully sent WpsPlanWarning message, factoryCode: {}, userId:{}, planTpe:{}, sendResult: {}",
                                factoryCode, userId, wpsPlanTypeEnum.name(), sendResult);
                    }

                    @Override
                    public void error(Throwable throwable) {
                        log.error("发送构建排产异常消息失败, req:{}, error：{}", JSON.toJSONString(wpsPlanWarningMQReq), throwable.getMessage());
                    }
                });
    }

    public void processMessage(WpsPlanWarningMQReq wpsPlanWarningMQReq) {
        String factoryCode = wpsPlanWarningMQReq.getFactoryCode();
        Long userId = wpsPlanWarningMQReq.getUserId();
        String planTpe = wpsPlanWarningMQReq.getPlanTpe();
        List<WpsRowData> orders = wpsOrderProcessContext
                .process(WpsOrderProcessEnum.READ_STORAGE, userId, null, null, factoryCode, true, Maps.newHashMap());
        WpsAutoScheduleContext context = new WpsAutoScheduleContext();
        context.setCurrentFactoryCode(factoryCode);
        context.setOrderList(orders);
        wpsWorkbenchWarningPipeline.execute(factoryCode, userId, context, WpsPlanTypeEnum.getEnumByFullCode(planTpe));

        //发送spring事件，通知告警处理完成
        applicationEventPublisher.publishEvent(new WpsPlanWarningEvent(this, wpsPlanWarningMQReq));
    }
}