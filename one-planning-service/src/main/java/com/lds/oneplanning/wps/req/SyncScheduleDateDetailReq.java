package com.lds.oneplanning.wps.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/5/29
 */
@Data
public class SyncScheduleDateDetailReq {

    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "排产日期不能为空")
    private LocalDate scheduleDate;
}
