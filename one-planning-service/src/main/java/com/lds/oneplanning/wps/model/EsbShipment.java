package com.lds.oneplanning.wps.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EsbShipment {

    /**
     * 供应商科目编号
     */
    @JSONField(name = "LIFNR")
    @JsonProperty("LIFNR")
    private String supplyCode;
    /**
     * 采购组织
     */
    @JSONField(name = "EKORG")
    @JsonProperty("EKORG")
    private String org;

    /**
     * 物料编号
     */
    @J<PERSON>NField(name = "MATNR")
    @JsonProperty("MATNR")
    private String materialId;

    /**
     * 物料送货模式
     */
    @JSONField(name = "ZSHMS")
    @JsonProperty("ZSHMS")
    private String deliveryMode;

    public EsbShipment(String supplyCode, String org, String materialId) {
        this.supplyCode = supplyCode;
        this.org = org;
        this.materialId = materialId;
    }
}
