package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WpsRowExt对象", description="")
public class WpsRowExt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "业务编码")
    private String bizId;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "计划员工号")
    private String plannerEmpNo;

    @ApiModelProperty(value = "线体编码")
    private String lineCode;

    @ApiModelProperty(value = "线体uuid")
    private String lineUuid;

    @ApiModelProperty(value = "开始排产日期")
    private LocalDate scheduleDate;

    @ApiModelProperty(value = "排产年份如2025")
    private Integer scheduleYear;

    @ApiModelProperty(value = "开始排产自然周：如6,7")
    private Integer scheduleWeek;

    @ApiModelProperty(value = "2025021200001 年月日+5位数")
    private Long scheduleSeq;

    @ApiModelProperty(value = "是否冻结产能：0否1是，默认是0")
    private Integer frozenStatus;

    @ApiModelProperty(value = "预计生产完成日期")
    private LocalDate estProductionFinishDate;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WpsRowExt that = (WpsRowExt) o;
        return Objects.equals(bizId, that.bizId) && Objects.equals(lineCode, that.lineCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(bizId, lineCode);
    }
}
