package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.helper.UserApiHelper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 警告待办列表实体类
 */
@TableName(value = "warning_todo_list")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WarningTodoList {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 预警类型
     */
    @TableField(value = "warning_type")
    private WpsOrderWarningTypeEnum warningType;

    /**
     * 业务编号
     */
    @TableField(value = "biz_id")
    private Long bizId;

    @TableField(value = "factory_code")
    private String factoryCode;

    /**
     * 流程状态
     */
    @TableField(value = "process_status")
    private OrderWarningHandleStatusEnum processStatus;

    /**
     * 待办人，存登录名（为了LCP推送），搜索的时候使用{@link UserApiHelper#getUserLoginName()}获取当前登录人
     */
    @TableField(value = "assignee")
    private String assignee;

    /**
     * 流程编号
     */
    @TableField(value = "process_id")
    private String processId;

    /**
     *
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     *
     */
    @TableField(value = "updated_at")
    private Date updatedAt;

    /**
     * 推送计数
     */
    @TableField(value = "push_status")
    private Integer pushStatus;

    /**
     * 构造方法
     *
     * @param bizId    业务编号
     * @param assignee 代办人
     */
    public WarningTodoList(WpsOrderWarningTypeEnum warningType, String factoryCode, Long bizId, String assignee) {
        this.warningType = warningType;
        this.bizId = bizId;
        this.assignee = assignee;
        this.processStatus = OrderWarningHandleStatusEnum.UN_HANDLE;
        this.factoryCode = factoryCode;
    }

    /**
     * 构造方法
     *
     * @param bizId    业务编号
     * @param assignee 代办人
     */
    public WarningTodoList(WpsOrderWarningTypeEnum warningType, Long bizId, String assignee) {
        this.warningType = warningType;
        this.bizId = bizId;
        this.assignee = assignee;
        this.processStatus = OrderWarningHandleStatusEnum.UN_HANDLE;
    }

    public WarningTodoList(WpsOrderWarningTypeEnum warningType, Long bizId) {
        this.warningType = warningType;
        this.bizId = bizId;
        this.processStatus = OrderWarningHandleStatusEnum.UN_HANDLE;
    }
}