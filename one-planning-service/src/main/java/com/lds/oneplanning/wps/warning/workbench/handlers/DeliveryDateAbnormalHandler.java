package com.lds.oneplanning.wps.warning.workbench.handlers;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.lds.basic.account.user.api.UserApi;
import com.lds.oneplanning.wps.entity.WarningDeliveryDateAbnormal;
import com.lds.oneplanning.wps.entity.WarningTodoList;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.entity.WpsOrderWarningCfg;
import com.lds.oneplanning.wps.enums.*;
import com.lds.oneplanning.wps.event.WpsPlanWarningEvent;
import com.lds.oneplanning.wps.helper.UserApiHelper;
import com.lds.oneplanning.wps.helper.WpsRowDataMergeHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.WarningDeliveryDateAbnormalService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.utils.DateUtils;
import com.lds.oneplanning.wps.utils.OrderNoUtils;
import com.lds.oneplanning.wps.utils.WpsDateUtil;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DeliveryDateAbnormalHandler implements IWpsWorkbenchWarningHandler {
    private final WarningDeliveryDateAbnormalService deliveryDateAbnormalService;
    private final IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    private final WarningTodoListService todoListService;
    private final UserApi userApi;

    @Override
    public List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext ctx, Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap) {
        if (ctx == null || CollectionUtils.isEmpty(ctx.getOrders())) {
            return Collections.emptyList();
        }

        //异常条件：预计完工日期晚于SAP完工日期
        List<WpsRowData> abnormalList = filterAbnormalData(ctx);

        log.info("{} 异常数量:{}", getWarningType(), abnormalList.size());

        if (CollectionUtils.isEmpty(abnormalList)) {
            //消警
            eliminateAlarms(Collections.emptyList());
            return Collections.emptyList();
        }

        //去掉没排产的数据
        abnormalList.removeIf(row -> row.getOnlineTime() == null);

        log.info("{} 移除未排产数据后：异常数量:{}", getWarningType(), abnormalList.size());
        if (CollectionUtils.isEmpty(abnormalList)) {
            //消警
            eliminateAlarms(Collections.emptyList());
            return Collections.emptyList();
        }

        //合并相同订单的数据
        abnormalList = WpsRowDataMergeHelper.mergeSameOrder(abnormalList);

        //分析异常数据
        return analyzeAbnormalData(ctx, abnormalList);
    }

    /**
     * 过滤异常数据
     *
     * @param ctx 上下文对象，包含订单信息
     */
    private List<WpsRowData> filterAbnormalData(WpsWorkbenchWarningContext ctx) {
        List<WpsRowData> abnormalList = new ArrayList<>();
        //旧逻辑：预计完工日期晚于SAP完工日期
        //新逻辑：calculateFinishTime（SAP完工日期）-onlineTime（第一天）<=2 影响出货，其他影响上线
        for (WpsRowData row : ctx.getOrders()) {
            LocalDate onlineTime = WpsDateUtil.getStartScheduleDate(row.getScheduleDataMap());
            LocalDate sapDate = row.getCalculateFinishTime();

            if (DateUtils.daysBetween(onlineTime, sapDate) <= 2) {
                abnormalList.add(row);
            }
        }

        return abnormalList;
    }

    /**
     * 分析异常数据并生成警告信息
     *
     * @param ctx          工作台警告上下文
     * @param abnormalList 异常数据列表
     * @return 包含警告信息的列表，如果未分析出异常则返回空列表
     */
    private List<WpsOrderPlanWarning> analyzeAbnormalData(WpsWorkbenchWarningContext ctx,
                                                          List<WpsRowData> abnormalList) {

        log.info("开始分析异常数据");

        // 分析异常数据
        List<WarningDeliveryDateAbnormal> warningList = analyzeData(ctx, abnormalList);
        log.info("完成在库异常数据分析，警告列表大小: {}", warningList.size());

        //消警
        log.info("开始消警");
        eliminateAlarms(warningList);

        //保存或更新
        log.info("开始保存异常数据");
        saveOrUpdate(ctx, warningList);
        log.info("完成保存异常数据，警告列表大小: {}", warningList.size());

        log.info("开始创建待办");
        createTodoList(ctx.getUserId(), ctx.getFactoryCode(), warningList);

        //主表消警
        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());

        return convertData(ctx, abnormalList, warningList);
    }

    private List<WpsOrderPlanWarning> convertData(WpsWorkbenchWarningContext ctx, List<WpsRowData> abnormalList, List<WarningDeliveryDateAbnormal> warningList) {
        Map<String, LightColor> lightColorMap = warningList.stream()
                .collect(Collectors.toMap(WarningDeliveryDateAbnormal::getOrderNumber, WarningDeliveryDateAbnormal::getLightColor, (o, o2) -> o));
        return buildWarning(ctx, abnormalList, lightColorMap);
    }

    /**
     * 创建待办事项列表
     *
     * @param userId      用户ID
     * @param factoryCode 工厂代码
     * @param warningList 包含警告信息的列表
     */
    private void createTodoList(Long userId, String factoryCode, List<WarningDeliveryDateAbnormal> warningList) {
        String loginName = UserApiHelper.getUserLoginName(userId);

        List<WarningTodoList> todoList = warningList.stream()
                .map(e -> new WarningTodoList(getWarningType(), factoryCode, e.getId(), loginName))
                .collect(Collectors.toList());

        log.info("待办列表大小: {}", todoList.size());

        todoListService.saveData(getWarningType(), todoList);
    }

    private void saveOrUpdate(WpsWorkbenchWarningContext ctx, List<WarningDeliveryDateAbnormal> warningList) {
        // 1. 提取订单号列表
        List<String> orderNoList = warningList.stream()
                .map(WarningDeliveryDateAbnormal::getOrderNumber)
                .distinct()
                .collect(Collectors.toList());

        // 2. 查询数据库中已存在的记录
        List<WarningDeliveryDateAbnormal> existList = getExistList(orderNoList);
        log.info("Found {} existing warning records for order numbers: {}", existList.size(), orderNoList);

        // 3. 构建已存在记录的映射表（订单号 -> 物料ID -> 实体）
        Map<String, WarningDeliveryDateAbnormal> existTable = new HashMap<>();
        existList.forEach(warning -> existTable.put(warning.getOrderNumber(), warning));

        // 4. 初始化更新和插入列表
        List<WarningDeliveryDateAbnormal> updateList = new ArrayList<>();
        List<WarningDeliveryDateAbnormal> insertList = new ArrayList<>();

        // 5. 遍历warningList，区分更新和插入
        CopyOptions opt = CopyOptions.create().ignoreNullValue();
        warningList.forEach(warning -> {
            warning.setFactoryCode(ctx.getFactoryCode());
            warning.setUpdatedBy(ctx.getUserId());

            if (existTable.containsKey(warning.getOrderNumber())) {
                // 5.1 如果记录已存在，执行更新操作
                WarningDeliveryDateAbnormal entity = existTable.get(warning.getOrderNumber());

                BeanUtil.copyProperties(warning, entity, opt);
                // 反向更新到warning中，以便外部能获取到id
                BeanUtil.copyProperties(entity, warning);
                updateList.add(warning);
            } else {
                warning.setCreatedBy(ctx.getUserId());
                // 5.2 如果记录不存在，添加到插入列表
                insertList.add(warning);
            }
        });

        // 6. 批量更新和插入
        if (CollectionUtils.isNotEmpty(updateList)) {
            log.info("Updating {} warning records", updateList.size());
            deliveryDateAbnormalService.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("Inserting {} new warning records", insertList.size());
            deliveryDateAbnormalService.saveBatch(insertList);
        }
    }

    private List<WarningDeliveryDateAbnormal> getExistList(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return Collections.emptyList();
        }
        return deliveryDateAbnormalService.lambdaQuery()
                .in(WarningDeliveryDateAbnormal::getOrderNumber, orderNoList)
                .list();
    }

    /**
     * 消除告警，根据订单号来判断
     *
     * @param warningList 告警信息列表
     */
    private void eliminateAlarms(List<WarningDeliveryDateAbnormal> warningList) {
        //获取所有待办的数据
        List<WarningDeliveryDateAbnormal> existData = deliveryDateAbnormalService.queryUnHandleData();
        log.info("获取未处理的警告数据计数: {}", existData.size());

        Set<String> newWarningOrderNumber = warningList.stream()
                .map(WarningDeliveryDateAbnormal::getOrderNumber)
                .collect(Collectors.toSet());

        Set<Long> toBeRemoveIds = existData.stream()
                .filter(e -> !newWarningOrderNumber.contains(e.getOrderNumber()))
                .map(WarningDeliveryDateAbnormal::getId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(toBeRemoveIds)) {
            log.info("消警列表：{}", toBeRemoveIds);
            todoListService.lambdaUpdate()
                    .set(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.CLOSED)
                    .in(WarningTodoList::getBizId, toBeRemoveIds)
                    .eq(WarningTodoList::getWarningType, this.getWarningType())
                    .update();
        }

        //主表消警
        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
    }


    /**
     * 分析异常数据，生成警告交付日期异常列表。
     *
     * @param ctx          上下文对象，包含警告相关的配置和信息
     * @param abnormalList 异常数据列表
     * @return 警告交付日期异常列表
     */
    private List<WarningDeliveryDateAbnormal> analyzeData(WpsWorkbenchWarningContext ctx,
                                                          List<WpsRowData> abnormalList) {
        log.info("开始分析异常数据, 排产场景:{}, 异常数据列表大小: {}", ctx.getProductType(), abnormalList.size());
        return abnormalList.stream()
                //判断是不是整灯，目前只处理整灯订单
                .filter(e -> WpsPlanTypeEnum.WHOLE_MACHINE.equals(ctx.getProductType()))
                .map(row -> covertToAbnormalData(ctx, row))
                .collect(Collectors.toList());
    }

    private WarningDeliveryDateAbnormal covertToAbnormalData(WpsWorkbenchWarningContext ctx, WpsRowData row) {
        WarningDeliveryDateAbnormal abnormal = new WarningDeliveryDateAbnormal();
        abnormal.setCustomer(row.getCustomerCode());
        abnormal.setSalesOrderNumber(OrderNoUtils.trimPreZero(OrderNoUtils.getUnifyOrderNo(row)));
        abnormal.setLineNumber(OrderNoUtils.trimPreZero(row.getRowItem()));
        abnormal.setOrderNumber(row.getOrderNo());
        abnormal.setMaterialId(row.getCommodityId());
        abnormal.setMaterialDescription(row.getCommodityDesc());
        abnormal.setShortageGroup("");

        //排产第一天
        abnormal.setPlannedOnlineTime(WpsDateUtil.getStartScheduleDate(row.getScheduleDataMap()));
        //排产最后一天
        abnormal.setEstFinishTime(WpsDateUtil.lastDate(row.getScheduleDates()));
        abnormal.setCalculateFinishTime(row.getCalculateFinishTime());
        //GAP天数:SAP完工日期 - 排产第一天
        abnormal.setGapDays(DateUtils.daysBetween(abnormal.getPlannedOnlineTime(), abnormal.getCalculateFinishTime()));

        abnormal.setOrderUnitQty(row.getOrderUnitQty());
        abnormal.setOriginalFinishTime(WpsDateUtil.lastDate(row.getScheduleDates()));

        /*
         * 整灯：①影响出货：gap ≤ 2天； ②影响上线：其余影响
         * 组件&部件：(先不做，需求不明确)
         * ①影响整灯计划：gap ≤3 天；
         * ②影响组件&部件上线：上线时间-需求时间）（对应采购订单号需求时间）(WPS)≤5天（组件上线（WPS）实际排产第一天）-UB7需求时间（采购交期）DS_ATP-WLQTDJ-USE_DATE（WPS带出））
         */
        DeliveryDateAbnormalImpactType impactType = getDeliveryDateAbnormalImpactType(abnormal);

        abnormal.setDeliveryDateAbnormalImpactType(impactType.getName());
        abnormal.setLightColor(impactType.getLightColor());
        abnormal.setAdjustedOnlineTime(abnormal.getOriginalFinishTime());
        abnormal.setAffectsUpperLevelPlan(false);
        abnormal.setProductType(ctx.getProductType());
        abnormal.setFactoryCode(ctx.getFactoryCode());

        return abnormal;
    }

    private DeliveryDateAbnormalImpactType getDeliveryDateAbnormalImpactType(WarningDeliveryDateAbnormal abnormal) {
        DeliveryDateAbnormalImpactType impactType = DeliveryDateAbnormalImpactType.AFFECTS_ONLINE;
        if (abnormal.getGapDays() <= 2) {
            impactType = DeliveryDateAbnormalImpactType.AFFECTS_SHIPMENT;
        }
        return impactType;
    }


    @Override
    public WpsOrderWarningCategoryEnum getWarningCategory() {
        return WpsOrderWarningCategoryEnum.DEFAULT;
    }

    @Override
    public WpsOrderWarningTypeEnum getWarningType() {
        return WpsOrderWarningTypeEnum.DELIVERY_DATE_ABNORMAL;
    }

    @EventListener
    public void handleCustomEvent(WpsPlanWarningEvent event) {
        //主表消警
        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
    }

}
