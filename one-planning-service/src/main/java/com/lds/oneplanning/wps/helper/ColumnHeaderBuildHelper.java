package com.lds.oneplanning.wps.helper;

import com.lds.oneplanning.wps.vo.TableColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.UtilityClass;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@UtilityClass
public class ColumnHeaderBuildHelper {
    public static List<TableColumn> build(Class<?> clazz) {
        List<TableColumn> columns = new ArrayList<>();

        // 获取当前类和所有父类的字段
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            processClassFields(currentClass, columns);
            currentClass = currentClass.getSuperclass();
        }

        return columns;
    }

    /**
     * 处理指定类的字段并填充列配置
     *
     * @param targetClass 需要处理的Java类
     * @param columns     列配置集合
     */
    private static void processClassFields(Class<?> targetClass, List<TableColumn> columns) {
        for (Field field : targetClass.getDeclaredFields()) {
            processField(field, columns);
        }
    }

    /**
     * 处理单个字段并生成列配置
     *
     * @param field   需要处理的字段
     * @param columns 列配置集合
     */
    private static void processField(Field field, List<TableColumn> columns) {
        ApiModelProperty property = field.getAnnotation(ApiModelProperty.class);
        if (property != null) {
            if (!isColumnExist(columns, field.getName())) {
                TableColumn column = createTableColumn(field, property);
                columns.add(column);
                handleNestedType(field, column);
            }
        }
    }

    /**
     * 检查列是否已存在
     *
     * @param columns   列配置集合
     * @param fieldName 字段名称
     * @return 是否已存在同名列配置
     */
    private static boolean isColumnExist(List<TableColumn> columns, String fieldName) {
        return columns.stream().anyMatch(c -> c.getProp().equals(fieldName));
    }

    /**
     * 创建表格列配置对象
     *
     * @param field    Java字段对象
     * @param property 字段注解信息
     * @return 初始化后的TableColumn对象
     */
    private static TableColumn createTableColumn(Field field, ApiModelProperty property) {
        TableColumn column = new TableColumn();
        column.setLabel(property.value());
        column.setProp(field.getName());
        if (Objects.equals(property.notes(),"edit")){
            column.setCanEdit(true);
        }
        return column;
    }

    /**
     * 处理嵌套对象类型
     *
     * @param field    字段类型
     * @param parentColumn 父列配置对象
     */
    private static void handleNestedType(Field field, TableColumn parentColumn) {
        // 判断是不是Collection.class的子类
        boolean isCollection = Collection.class.isAssignableFrom(field.getType());

        // 如果是集合，需要获取泛型类型
        if (isCollection) {
            parentColumn.setIsArray(isCollection);
            handleCollectionType(field, parentColumn);
        }

        // 如果字段本身有@ApiModel注解
        if (field.getType().isAnnotationPresent(ApiModel.class)) {
            parentColumn.setChild(build(field.getType()));
        }
    }

    private static void handleCollectionType(Field field, TableColumn parentColumn) {
        Type genericType = field.getGenericType();
        if (genericType instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericType;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                Type firstGenericType = actualTypeArguments[0];
                if (firstGenericType instanceof Class) {
                    Class<?> genericClass = (Class<?>) firstGenericType;
                    if (genericClass.isAnnotationPresent(ApiModel.class)) {
                        parentColumn.setChild(build(genericClass));
                    }
                }
            }
        }
    }

}
