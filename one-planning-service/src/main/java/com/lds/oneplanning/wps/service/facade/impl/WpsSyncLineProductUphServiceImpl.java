package com.lds.oneplanning.wps.service.facade.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.LineUph;
import com.lds.oneplanning.basedata.entity.ProductGroup;
import com.lds.oneplanning.basedata.entity.ProductGroupRel;
import com.lds.oneplanning.basedata.entity.SchedulePriority;
import com.lds.oneplanning.basedata.service.ILineUphService;
import com.lds.oneplanning.basedata.service.IProductGroupRelService;
import com.lds.oneplanning.basedata.service.IProductGroupService;
import com.lds.oneplanning.basedata.service.ISchedulePriorityService;
import com.lds.oneplanning.common.utils.ThreadPoolUtil;
import com.lds.oneplanning.esb.biz.req.GetDpsLineUphRequest;
import com.lds.oneplanning.esb.biz.resp.GetDpsLineUphResponse;
import com.lds.oneplanning.esb.datafetch.service.IEsbMesDataFetchService;
import com.lds.oneplanning.wps.service.facade.IWpsSyncLineProductUphService;
import com.lds.oneplanning.wps.utils.TraceIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WpsSyncLineProductUphServiceImpl implements IWpsSyncLineProductUphService {

    @Autowired
    private IEsbMesDataFetchService esbMesDataFetchService;

    @Autowired
    private ILineUphService lineUphService;

    @Autowired
    private ISchedulePriorityService schedulePriorityService;

    @Autowired
    private IProductGroupRelService productGroupRelService;

    @Autowired
    private IProductGroupService productGroupService;

    @Override
    public void syncLineProductUph() {
        ThreadPoolUtil.instance().execute(() -> execute(null));
    }

    @Override
    public void syncLineProductUphByFactoryCode(String factoryCode) {
        if (StringUtils.isEmpty(factoryCode)) {
            return;
        }
        String traceId = TraceIdUtils.getTraceId();
        ThreadPoolUtil.instance().execute(() -> {
            String localTraceId = StringUtils.defaultIfEmpty(traceId, IdUtil.fastSimpleUUID().substring(16));
            TraceIdUtils.setTraceId("syncLineProductUph-" + localTraceId);
            try {
                execute(factoryCode);
            } catch (Exception e) {
                log.error("同步线体产品UPH失败, factoryCode:{}", factoryCode, e);
            } finally {
                TraceIdUtils.clearTraceId();
            }
        });
    }

    private void execute(String factoryCode) {
        List<SchedulePriority> schedulePriorityList = schedulePriorityService.list();
        log.info("syncLineProductUph schedulePriorityList size:{}.", schedulePriorityList.size());
        if (CollectionUtils.isEmpty(schedulePriorityList)) {
            return;
        }
        Set<String> productGroupCodes = schedulePriorityList.stream().map(SchedulePriority::getProductGroupCode).collect(Collectors.toSet());
        log.info("syncLineProductUph productGroupCodes size:{}.", productGroupCodes.size());
        if (CollectionUtils.isEmpty(productGroupCodes)) {
            return;
        }
        // 按工厂过滤
        if (StringUtils.isNotEmpty(factoryCode)) {
            List<ProductGroup> productGroups = productGroupService.listByFactoryCodeAndCodes(factoryCode, productGroupCodes);
            if (CollectionUtils.isEmpty(productGroups)) {
                return;
            }
            productGroupCodes = productGroups.stream().map(ProductGroup::getCode).collect(Collectors.toSet());
        }
        List<ProductGroupRel> productGroupRels = productGroupRelService.listByGroupCodes(productGroupCodes);
        log.info("syncLineProductUph productGroupRels size:{}.", productGroupRels.size());
        if (CollectionUtils.isEmpty(productGroupRels)) {
            return;
        }
        Map<String, Set<String>> groupProductIdMap = productGroupRels.stream()
                .collect(Collectors.groupingBy(ProductGroupRel::getProductGroupCode,
                        Collectors.mapping(ProductGroupRel::getProductId, Collectors.toSet())));
        Map<String, Set<String>> lineCodeProductIdMap = Maps.newLinkedHashMap();
        Map<String, String> lineCodeUuidMap = Maps.newHashMap();
        // schedulePriorityList按更新时间倒序排序
        schedulePriorityList.sort((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()));
        schedulePriorityList.forEach(schedulePriority -> {
            String lineCode = schedulePriority.getLineCode();
            String lineUuid = schedulePriority.getLineUuid();
            lineCodeUuidMap.put(lineCode, lineUuid);
            log.info("syncLineProductUph lineCode:{}, lineUuid:{}, productGroupCode:{}.",
                    lineCode, lineUuid, schedulePriority.getProductGroupCode());
            String productGroupCode = schedulePriority.getProductGroupCode();
            Set<String> productIds = groupProductIdMap.get(productGroupCode);
            if (CollectionUtils.isNotEmpty(productIds)) {
                lineCodeProductIdMap.computeIfAbsent(lineCode, k -> new HashSet<>())
                        .addAll(productIds);
            }
        });
        if (MapUtils.isEmpty(lineCodeProductIdMap)) {
            return;
        }
        lineCodeProductIdMap.forEach((lineCode, productIds) -> {
            try {
                String lineUuid = lineCodeUuidMap.get(lineCode);
                if (StringUtils.isEmpty(lineUuid)) {
                    log.warn("syncLineProductUph lineCode:{}, lineUuid is null.", lineCode);
                    return;
                }
                batchSaveLineUph(lineUuid, lineCode, productIds);
            } catch (Exception e) {
                log.error("同步线体产品UPH失败, lineCode:{}, productIds:{}.", lineCode, productIds, e);
            }
        });
        clearDeletedLineUph(new HashSet<>(lineCodeUuidMap.values()));
    }

    private void batchSaveLineUph(String lineUuid, String lineCode, Set<String> productIds) {
        log.info("syncLineProductUph lineUuid:{}, lineCode:{},productIds size:{}.",
                lineUuid, lineCode, productIds.size());
        // 4. 创建请求
        List<GetDpsLineUphRequest> getDpsLineUphRequests = createDpsLineUphRequests(lineCode, productIds);
        // 5. 获取DPS线路UPH数据
        List<GetDpsLineUphResponse> getDpsLineUphResponses = fetchDpsLineUphResponses(getDpsLineUphRequests);
        if (CollectionUtils.isEmpty(getDpsLineUphResponses)) {
            return;
        }
        // 6. 处理响应并保存
        List<LineUph> lineUphList = processUphResponses(lineUuid, getDpsLineUphResponses);
        lineUphService.batchSaveByUuid(BaseDataConstant.CONFIG_TYPE_LINE, lineUuid, lineUphList);
        log.info("syncLineProductUph save lineUuid:{}, size:{}.", lineUuid, lineUphList.size());
    }

    private void clearDeletedLineUph(Set<String> lineUuids) {
        if (CollectionUtils.isEmpty(lineUuids)) {
            log.debug("No lineUuids provided for LineUph deletion");
            return;
        }
        try {
            Set<String> lineUuidsToDelete = Optional.ofNullable(lineUphService.list(Wrappers.<LineUph>lambdaQuery()
                            .select(LineUph::getLineUuid)
                            .eq(LineUph::getConfigType, BaseDataConstant.CONFIG_TYPE_LINE)
                            .notIn(LineUph::getLineUuid, lineUuids)))
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(LineUph::getLineUuid)
                    .collect(Collectors.toSet());

            if (!lineUuidsToDelete.isEmpty()) {
                lineUphService.batchDeleteByUuids(BaseDataConstant.CONFIG_TYPE_LINE, lineUuidsToDelete);
                log.info("Deleted {} LineUph records for configType={}",
                        lineUuidsToDelete.size(), BaseDataConstant.CONFIG_TYPE_LINE);
            } else {
                log.debug("No LineUph records found to delete for configType={}", BaseDataConstant.CONFIG_TYPE_LINE);
            }
        } catch (Exception e) {
            log.error("Failed to clear deleted LineUph records for configType={} and {} lineUuids",
                    BaseDataConstant.CONFIG_TYPE_LINE, lineUuids.size(), e);
        }
    }

    private List<GetDpsLineUphRequest> createDpsLineUphRequests(String lineCode, Set<String> productIds) {
        return productIds.stream()
                .map(productId -> GetDpsLineUphRequest.builder()
                        .lineCode(lineCode)
                        .productCode(productId)
                        .build())
                .collect(Collectors.toList());
    }

    private List<GetDpsLineUphResponse> fetchDpsLineUphResponses(List<GetDpsLineUphRequest> requests) {
        List<GetDpsLineUphResponse> responses = Lists.newArrayList();
        List<List<GetDpsLineUphRequest>> partitionList = Lists.partition(requests, 100);
        for (List<GetDpsLineUphRequest> subList : partitionList) {
            Stopwatch stopwatch = Stopwatch.createStarted();
            log.debug("获取DpsLineUph->subList:{}", JSON.toJSONString(subList));
            List<GetDpsLineUphResponse> subResponses = esbMesDataFetchService.getRetryDpsLineUphList(subList);
            log.debug("响应DpsLineUph->subList:{}", JSON.toJSONString(subResponses));
            stopwatch.stop();
            log.info("获取DPS线体UPH数据完成,耗时:{}ms,请求数量:{},返回数量:{}.", stopwatch.elapsed(TimeUnit.MILLISECONDS), subList.size(), subResponses.size());
            if (CollectionUtils.isEmpty(subResponses)) {
                continue;
            }
            responses.addAll(subResponses);
            // 控制请求频率
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                log.error("sleep error.", e);
            }
        }
        return responses;
    }

    private List<LineUph> processUphResponses(String lineUuid, List<GetDpsLineUphResponse> responses) {
        return responses.stream()
                .filter(response -> response.getUph() != null && response.getUph() != 0)
                .map(response -> {
                    LineUph lineUph = new LineUph();
                    lineUph.setLineUuid(lineUuid);
                    lineUph.setConfigType(BaseDataConstant.CONFIG_TYPE_LINE);
                    lineUph.setConfigCode(response.getLineCode());
                    lineUph.setProductId(response.getProductCode());
                    lineUph.setUph(response.getUph());
                    return lineUph;
                })
                .collect(Collectors.toList());
    }
}
