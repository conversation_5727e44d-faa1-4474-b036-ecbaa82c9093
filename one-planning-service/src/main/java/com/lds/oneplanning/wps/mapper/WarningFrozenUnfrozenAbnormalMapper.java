package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningFrozenUnfrozenAbnormal;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.vo.FreezeUnFreezeAbnormalVO;
import com.lds.oneplanning.wps.vo.WarningFrozenUnfrozenAbnormalParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
public interface WarningFrozenUnfrozenAbnormalMapper extends BaseMapper<WarningFrozenUnfrozenAbnormal> {

    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningMaterialAtpAbnormal }>
     */
    List<WarningFrozenUnfrozenAbnormal> queryUnHandleData();
    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningMaterialAtpAbnormal }>
     */
    IPage<FreezeUnFreezeAbnormalVO> selectPage(Page<FreezeUnFreezeAbnormalVO> page, @Param("params") WarningFrozenUnfrozenAbnormalParams params);

}
