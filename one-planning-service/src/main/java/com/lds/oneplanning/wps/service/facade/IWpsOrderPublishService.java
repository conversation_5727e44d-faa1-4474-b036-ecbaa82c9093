package com.lds.oneplanning.wps.service.facade;

import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;

import java.time.LocalDate;
import java.util.List;

public interface IWpsOrderPublishService {

    /**
     * 处理已发布订单
     *
     * @param context
     * @param localDates
     */
    void publish(WpsAutoScheduleContext context, List<LocalDate> localDates);

    /**
     * 重置订单未未发布
      * @param context
     */
    void resetPublishedOrders(WpsAutoScheduleContext context);

    /**
     * 验证发布订单
     * @param context
     * @param startDate
     * @param endDate
     * @return
     */
    List<String> checkPublishedOrders(WpsAutoScheduleContext context, LocalDate startDate, LocalDate endDate);
}