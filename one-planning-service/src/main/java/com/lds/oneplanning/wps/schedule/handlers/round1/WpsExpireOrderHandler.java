package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.lds.oneplanning.wps.helper.WpsAutoScheduleOrderHelper;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 过期订单及没有自动排产日期的订单处理
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsExpireOrderHandler implements IWpsAutoScheduleHandler {

    @Autowired
    WpsAutoScheduleOrderHelper wpsAutoScheduleOrderHelper;
    @Override
    public void execute(WpsAutoScheduleContext context) {

    }


    @Override
    public int getOrder() {
        return 9;
    }

    @Override
    public int getRound() {
        return 1;
    }
}