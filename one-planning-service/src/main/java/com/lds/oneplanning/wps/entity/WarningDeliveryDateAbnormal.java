package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.WpsPlanTypeEnum;
import lombok.Data;

import java.time.LocalDate;

/**
 * 交期异常
 *
 * @TableName warning_delivery_date_abnormal
 */
@TableName(value = "warning_delivery_date_abnormal")
@Data
public class WarningDeliveryDateAbnormal {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 计划上线时间(排产第一天)
     */
    @TableField(value = "planned_online_time")
    private LocalDate plannedOnlineTime;

    /**
     * 客户
     */
    @TableField(value = "customer")
    private String customer;

    /**
     * 生产订单号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 行项目
     */
    @TableField(value = "line_number")
    private String lineNumber;

    /**
     * 销售订单/采购单号
     */
    @TableField(value = "sales_order_number")
    private String salesOrderNumber;

    /**
     * 物料ID
     */
    @TableField(value = "material_id")
    private String materialId;

    /**
     * 物料描述
     */
    @TableField(value = "material_description")
    private String materialDescription;

    /**
     * 物料组
     */
    @TableField(value = "shortage_group")
    private String shortageGroup;

    /**
     * 订单数量
     */
    @TableField(value = "order_unit_qty")
    private Integer orderUnitQty;

    /**
     * 原完工日期
     * update 2025-5-30 改成计划完工日期，取值为排产最后一天
     */
    @TableField(value = "original_finish_time")
    private LocalDate originalFinishTime;

    /**
     * 计算的允许最后完工日期 - SAP完工日期
     */
    @TableField(value = "calculate_finish_time")
    private LocalDate calculateFinishTime;

    /**
     * 预计最新完工时间-WPS排产的最后一天，可编辑
     */
    @TableField(value = "est_finish_time")
    private LocalDate estFinishTime;

    @TableField(value = "est_finish_time_edited")
    private LocalDate estFinishTimeEdited;


    /**
     * GAP天数,est_finish_time - calculate_finish_time
     */
    private Integer gapDays;

    /**
     * 交付日期异常影响类型
     */
    @TableField(value = "delivery_date_abnormal_impact_type")
    private String deliveryDateAbnormalImpactType;

    /**
     * 预警灯色
     */
    @TableField(value = "light_color")
    private LightColor lightColor;

    /**
     * 调整后上线时间，可编辑
     */
    @TableField(value = "adjusted_online_time")
    private LocalDate adjustedOnlineTime;

    /**
     * 调整后上线时间，可编辑, 用于前端展示
     */
    @TableField(value = "adjusted_online_time_edited")
    private LocalDate adjustedOnlineTimeEdited;

    /**
     * 是否影响上下层计划，可编辑
     */
    @TableField(value = "affects_upper_level_plan")
    private Boolean affectsUpperLevelPlan;

    /**
     * 影响类型，可编辑
     */
    @TableField(value = "impact_type")
    private String impactType;

    /**
     * 产品类型 (整机/组件/部件)
     */
    @TableField(value = "product_type")
    private WpsPlanTypeEnum productType;

    /**
     * 工厂编码
     */
    @TableField(value = "factory_code")
    private String factoryCode;

    @TableField(value = "created_by")
    private Long createdBy;
    @TableField(value = "updated_by")
    private Long updatedBy;
}