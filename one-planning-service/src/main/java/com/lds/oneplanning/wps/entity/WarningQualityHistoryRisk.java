package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("warning_quality_history_risk")
@ApiModel(value = "WarningQualityHistoryRisk对象", description = "品质履历记录表")
public class WarningQualityHistoryRisk implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "计划时间")
    @TableField("plan_time")
    private LocalDate planTime;

    @ApiModelProperty(value = "生产车间")
    @TableField("production_workshop")
    private String productionWorkshop;

    @ApiModelProperty(value = "生产线长(工号)")
    @TableField("line_leader_no")
    private String lineLeaderNo;

    @ApiModelProperty(value = "生产线长(名字)")
    @TableField("line_leader_name")
    private String lineLeaderName;

    @ApiModelProperty(value = "工厂编号")
    @TableField("factory_code")
    private String factoryCode;

    @ApiModelProperty(value = "订单号")
    @TableField("order_no")
    private String orderNo;

    @ApiModelProperty(value = "产品ID")
    @TableField("product_id")
    private String productId;

    @ApiModelProperty(value = "产品品类")
    @TableField("product_category")
    private String productCategory;

    @ApiModelProperty(value = "计划数量")
    @TableField("plan_qty")
    private Integer planQty;

    @ApiModelProperty(value = "异常原因")
    @TableField("exception_reason")
    private String exceptionReason;

    @ApiModelProperty(value = "异常发生日期")
    @TableField("exception_date")
    private LocalDate exceptionDate; // 可以改为 LocalDate 类型

    @ApiModelProperty(value = "对应对策")
    @TableField("measure")
    private String measure;

    @ApiModelProperty(value = "异常责任人工号")
    @TableField("exception_person_no")
    private String exceptionPersonNo;

    @ApiModelProperty(value = "异常责任人")
    @TableField("exception_person")
    private String exceptionPerson;

    @ApiModelProperty(value = "品质负责人工号")
    @TableField("quality_person_no")
    private String qualityPersonNo;

    @ApiModelProperty(value = "品质负责人")
    @TableField("quality_person")
    private String qualityPerson;

    @ApiModelProperty(value = "是否闭环管理 (0:否, 1:是)")
    @TableField("closed_loop")
    private Boolean closedLoop;

    @ApiModelProperty(value = "系统推送 (0:否, 1:是)")
    @TableField("system_push")
    private Boolean systemPush;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人id")
    @TableField("update_by")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建者id")
    @TableField("create_by")
    private Long createBy;
}
