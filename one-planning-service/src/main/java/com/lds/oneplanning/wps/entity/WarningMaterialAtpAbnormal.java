package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.MaterialAtpAbnormalType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 物料不齐套异常，跟订单{@link WpsOrderPlanWarning}是一对一的关系，跟物料{@link WarningMaterialAtpAbnormalShortage}是一对多的关系
 * </p>
 * <p>
 * 待办信息是跟物料{@link WarningMaterialAtpAbnormalShortage}一对一关联的
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-14
 */
@TableName(value = "warning_material_atp_abnormal")
@Data
public class WarningMaterialAtpAbnormal {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "计划上线时间")
    private LocalDate plannedOnlineTime;

    @ApiModelProperty(value = "上线数量")
    private Integer onlineQuantity;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "销售订单/采购单号")
    private String salesOrderNumber;

    @ApiModelProperty(value = "行项目")
    private String lineNumber;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    @ApiModelProperty(value = "可得日期")
    private LocalDate availableDate;

    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "预计可计划日期")
    private LocalDate estimatedPlanDate;

    @ApiModelProperty(value = "调整后上线时间")
    private LocalDate adjustedOnlineTime;

    /**
     * 调整后上线时间-前端编辑
     */
    private LocalDate adjustedOnlineTimeEdited;

    @ApiModelProperty(value = "GAP天数")
    private Integer adjustedGapDays;

    @ApiModelProperty(value = "相关责任人回复")
    private String responsiblePersonReply;

    @ApiModelProperty(value = "是否影响上下层计划")
    private Boolean affectsUpperLevelPlan;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    //扩展字段
    @ApiModelProperty(value = "异常类型")
    private MaterialAtpAbnormalType abnormalType;

    private Long createdBy;

    private Long updatedBy;

    /**
     *
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     *
     */
    @TableField(value = "updated_at")
    private Date updatedAt;


    //扩展字段，不写入数据库
    @TableField(exist = false)
    private Set<String> shortageIds;
    @TableField(exist = false)
    private List<WarningMaterialAtpAbnormalShortage> shortageList;



    /**
     * 订单数据
     */
    @TableField(value = "order_data")
    private String orderData;

    /**
     * ATP数据
     */
    @TableField(value = "apt_data")
    private String aptData;
}