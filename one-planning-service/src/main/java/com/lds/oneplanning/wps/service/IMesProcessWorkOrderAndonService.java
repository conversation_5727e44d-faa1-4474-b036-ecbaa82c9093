package com.lds.oneplanning.wps.service;

import com.lds.oneplanning.wps.entity.MesProcessWorkOrderAndon;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
public interface IMesProcessWorkOrderAndonService extends IService<MesProcessWorkOrderAndon> {

    /**
     * 根据工单号查询工单andon信息
     * @param workOrderNumberList
     */
    Map<String,List<MesProcessWorkOrderAndon>> findMapByWorkOrderNumber(List<String> workOrderNumberList);

    /**
     * 根据工单号删除andon信息
     * @param workOrderNoList
     */
    void removeByWorkOrderNoList(List<String> workOrderNoList);
}
