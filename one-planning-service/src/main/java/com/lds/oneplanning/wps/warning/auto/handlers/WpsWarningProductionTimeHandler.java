package com.lds.oneplanning.wps.warning.auto.handlers;

import com.lds.oneplanning.common.utils.OrderArithUtil;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsProductionCapacityWarningEnum;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 排产时长预警处理器
 */
@Slf4j
@Component
public class WpsWarningProductionTimeHandler implements IWpsAutoPlanWarningHandler {

    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        // 订单->生产线code->uph映射
        Map<String, Map<String, Float>> orderLineUphMap = context.getOrderLineUphMap();
        if (MapUtils.isEmpty(orderLineUphMap)) {
            return;
        }
        orderList.forEach(order -> processOrder(context, order, orderLineUphMap));
    }

    private void processOrder(WpsAutoScheduleContext context, WpsRowData order, Map<String, Map<String, Float>> orderLineUphMap) {
        String orderNo = order.getOrderNo();
        String lineUuid = order.getLineUuid();
        Map<LocalDate, Number> scheduleDataMap = order.getScheduleDataMap();
        if (isOrderValid(orderNo, lineUuid, scheduleDataMap)) {
            Map<String, Float> lineUphMap = orderLineUphMap.get(orderNo);
            Float uph = lineUphMap != null ? lineUphMap.get(lineUuid) : null;
            if (null != uph) {
                processScheduleData(context, order, lineUuid, scheduleDataMap, uph);
            }
        }
    }

    private boolean isOrderValid(String orderNo, String lineUuid, Map<LocalDate, Number> scheduleDataMap) {
        return StringUtils.isNotEmpty(orderNo) && StringUtils.isNotEmpty(lineUuid) && MapUtils.isNotEmpty(scheduleDataMap);
    }

    private void processScheduleData(WpsAutoScheduleContext context, WpsRowData order, String lineUuid, Map<LocalDate, Number> scheduleDataMap, Float uph) {
        Map<LocalDate, WpsProductionLine> dailyProductionLineMap = context.getDailyProductionLineMap().get(lineUuid);
        scheduleDataMap.forEach((localDate, scheduledOrderQty) -> {
            if (MapUtils.isEmpty(dailyProductionLineMap) || null == scheduledOrderQty) {
                return;
            }
            WpsProductionLine wpsProductionLine = dailyProductionLineMap.get(localDate);
            if (null == wpsProductionLine) {
                return;
            }
            if (!context.isHandleAutoSchedule()) {
                updateScheduledHours(wpsProductionLine, scheduledOrderQty.floatValue(), uph);
                log.debug("updateScheduledHours lineUuid:{},localDate:{},scheduledHours:{},planScheduleHours:{}.",
                        wpsProductionLine.getLineUuid(), localDate,
                        wpsProductionLine.getScheduledHours(), wpsProductionLine.getPlanScheduleHours()
                        );
            }
            doCheckCapacityWarning(order, lineUuid, localDate, wpsProductionLine);
        });
    }

    private void updateScheduledHours(WpsProductionLine wpsProductionLine, float scheduledOrderQty, Float uph) {
        float scheduledOrderHour = OrderArithUtil.floatDivide(scheduledOrderQty, uph);
        wpsProductionLine.setScheduledHours(OrderArithUtil.floatAdd(wpsProductionLine.getScheduledHours(), scheduledOrderHour));
    }


    @Override
    public int getOrder() {
        return 1;
    }

    private void doCheckCapacityWarning(WpsRowData order, String lineUuid, LocalDate localDate, WpsProductionLine wpsProductionLine) {
        if (null == order || StringUtils.isEmpty(lineUuid) || null == localDate || null == wpsProductionLine) {
            return;
        }
        float scheduledHours = wpsProductionLine.getScheduledHours();
        float planScheduleHours = wpsProductionLine.getPlanScheduleHours();
        if (scheduledHours >= planScheduleHours) {
            float capacity = OrderArithUtil.floatDivide(scheduledHours, planScheduleHours);
            int colorValue = WpsProductionCapacityWarningEnum.getEnumByCapacity(capacity).getColorValue();
            String warningDateStr = localDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
            order.getWarningColorMap().put(warningDateStr, colorValue);
            log.debug("WPS告警,排产时长预警,订单号:{},lineUuid:{},warningDateStr:{},scheduledHours:{},planScheduleHours:{}.",
                    order.getOrderNo(), lineUuid, warningDateStr,scheduledHours, planScheduleHours);
        }
    }
}