package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lds.oneplanning.common.utils.OrderArithUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 订单截止时间自动排产处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsEndProductPeriodOrderHandler implements IWpsAutoScheduleHandler {

    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        orderList.forEach(order -> {
            if (Optional.ofNullable(order.get_frozenStatus()).orElse(0) == 0) {
                processOrder(context, order);
            }
        });
    }

    private void processOrder(WpsAutoScheduleContext context, WpsRowData order) {
        String orderNo = order.getOrderNo();
        int waitingOrderQty = order.getWaitingOrderQty();
        Map<String, Map<LocalDate, Map<String, Integer>>> orderDailyScheduleDataMap = context.getOrderDailyScheduleDataMap();
        if (waitingOrderQty > 0) {
            String productGroupCode = order.get_productGroupCode();
            Set<String> lineUuids = getLineUuids(context.getProductGroupPriorityMap(), productGroupCode);
            if (CollectionUtils.isNotEmpty(lineUuids)) {
                String lineUuid = lineUuids.iterator().next();
                LocalDate endProductPeriod = getLastByTypeAndCodeAndDate(context, lineUuid, order);
                if (null == endProductPeriod) {
                    return;
                }
                Map<String, Map<String, Float>> orderLineUphMap = context.getOrderLineUphMap();
                Float uph = Optional.ofNullable(orderLineUphMap.get(orderNo))
                        .map(lineUphMap -> lineUphMap.get(lineUuid))
                        .orElse(null);
                if (null == uph) {
                    return;
                }
                Map<LocalDate, Map<String, Integer>> dailyScheduleDataMap = orderDailyScheduleDataMap.computeIfAbsent(orderNo, k -> Maps.newHashMap());
                Map<String, Integer> lineUuidQtyMap = dailyScheduleDataMap.computeIfAbsent(endProductPeriod, k -> Maps.newHashMap());
                updateLineUuidQuantity(lineUuidQtyMap, lineUuid, waitingOrderQty);
                updateProductionLine(context, lineUuid, endProductPeriod, order.getOrderNo(), waitingOrderQty, uph);
                order.setWaitingOrderQty(0);
                log.info("WPS排产,endProductPeriodOrder,orderNo:{},date:{},lineUuid:{},waitingOrderQty:{}.",
                        orderNo, endProductPeriod, lineUuid, waitingOrderQty);
            }
        }
    }

    private LocalDate getLastByTypeAndCodeAndDate(WpsAutoScheduleContext context, String lineUuid, WpsRowData order) {
        LocalDate startProductPeriod = order.get_startProductPeriod();
        LocalDate endProductPeriod = order.get_endProductPeriod();
        Map<LocalDate, WpsProductionLine> localDateWpsProductionLineMap = context.getDailyProductionLineMap().get(lineUuid);
        if (MapUtils.isEmpty(localDateWpsProductionLineMap)) {
            return null;
        }
        Set<LocalDate> localDates = localDateWpsProductionLineMap.keySet();
        // 求startProductPeriod和endProductPeriod日期区间的，且在localDates列表中最大的日期
        return localDates.stream()
                .filter(date -> !date.isBefore(startProductPeriod) && !date.isAfter(endProductPeriod))
                .max(LocalDate::compareTo)
                .orElse(null);
    }

    private Set<String> getLineUuids(Map<String, Map<Integer, Set<String>>> productGroupPriorityMap, String productGroupCode) {
        Map<Integer, Set<String>> priorityMap = productGroupPriorityMap.get(productGroupCode);
        if (MapUtils.isEmpty(priorityMap)) {
            return Sets.newLinkedHashSet();
        }
        for (int i = WpsConstants.MIN_PRODUCT_GROUP_PRIORITY_SEQ; i <= WpsConstants.MAX_PRODUCT_GROUP_PRIORITY_SEQ; i++) {
            if (priorityMap.containsKey(i)) {
                return priorityMap.get(i);
            }
        }
        return Sets.newLinkedHashSet();
    }

    private void updateLineUuidQuantity(Map<String, Integer> lineUuidQtyMap, String lineUuid, int waitingOrderQty) {
        lineUuidQtyMap.merge(lineUuid, waitingOrderQty, Integer::sum);
    }

    private void updateProductionLine(WpsAutoScheduleContext context, String lineUuid, LocalDate endProductPeriod,
                                      String orderNo, float waitingOrderQty, Float uph) {
        Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap = context.getDailyProductionLineMap();
        Map<LocalDate, WpsProductionLine> localDateWpsProductionLineMap = dailyProductionLineMap.get(lineUuid);
        if (MapUtils.isEmpty(localDateWpsProductionLineMap)) {
            return;
        }
        WpsProductionLine wpsProductionLine = localDateWpsProductionLineMap.get(endProductPeriod);
        if (null != wpsProductionLine && null != uph) {
            wpsProductionLine.setWaitingScheduleHours(0F);
            float waitingOrderHours = OrderArithUtil.floatDivide(waitingOrderQty, uph);
            float scheduledHours = OrderArithUtil.floatAdd(wpsProductionLine.getScheduledHours(), waitingOrderHours);
            wpsProductionLine.setScheduledHours(scheduledHours);
            wpsProductionLine.getOrderScheduledHoursMap().merge(orderNo, waitingOrderHours, Float::sum);
            log.info("doEndProductPeriodOrderHandler,orderNo:{},date:{},lineUuid:{},uph:{},waitingOrderHour:{},waitingOrderQty:{}.",
                    orderNo, endProductPeriod, lineUuid, uph, waitingOrderHours, waitingOrderQty);
        }
    }

    @Override
    public int getOrder() {
        return 8;
    }

    @Override
    public int getRound() {
        return 1;
    }
}