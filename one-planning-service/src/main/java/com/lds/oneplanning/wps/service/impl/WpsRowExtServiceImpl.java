package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.wps.entity.WpsRowExt;
import com.lds.oneplanning.wps.mapper.WpsRowExtMapper;
import com.lds.oneplanning.wps.service.IWpsRowExtService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-27
 */
@Service
public class WpsRowExtServiceImpl extends ServiceImpl<WpsRowExtMapper, WpsRowExt> implements IWpsRowExtService {
    @Override
    public List<WpsRowExt> listByBizIds(Collection<String> bizIds) {
        if (bizIds == null || bizIds.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<WpsRowExt>lambdaQuery().in(WpsRowExt::getBizId,bizIds));
    }

    @Override
    public void batchSaveByBizId(Collection<WpsRowExt> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> bizIds =sourceList.stream().map(WpsRowExt::getBizId).collect(Collectors.toSet());
        LambdaQueryWrapper<WpsRowExt> deleteQuery = Wrappers.<WpsRowExt>lambdaQuery().in(WpsRowExt::getBizId,bizIds);
        baseMapper.delete(deleteQuery);
        // 重新新增
        this.saveBatch(sourceList);
    }

    @Override
    public Integer batchFrozenStatus(Integer status, Collection<String> orderNos) {
        if (status == null || CollectionUtils.isEmpty(orderNos)) {
            return 0;
        }
        LambdaUpdateWrapper<WpsRowExt> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(WpsRowExt::getFrozenStatus,status);
        lambdaUpdateWrapper.in(WpsRowExt::getBizId,orderNos);
        return baseMapper.update(new WpsRowExt(), lambdaUpdateWrapper);
    }
}
