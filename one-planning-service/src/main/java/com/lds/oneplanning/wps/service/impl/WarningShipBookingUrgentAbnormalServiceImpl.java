package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.entity.WarningShipBookingUrgentAbnormal;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.mapper.WarningShipBookingUrgentAbnormalMapper;
import com.lds.oneplanning.wps.req.ShipBookingUrgentReq;
import com.lds.oneplanning.wps.service.IWarningShipBookingUrgentAbnormalService;
import com.lds.oneplanning.wps.vo.ShipBookingUrgentVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningShipBookingUrgentAbnormalServiceImpl extends ServiceImpl<WarningShipBookingUrgentAbnormalMapper, WarningShipBookingUrgentAbnormal> implements IWarningShipBookingUrgentAbnormalService {

    private final IFactoryFacadeService factoryFacadeService;

    @Override
    public List<WarningShipBookingUrgentAbnormal> queryUnHandleData() {
        return baseMapper.queryUnHandleData();
    }

    @Override
    public Page<ShipBookingUrgentVO> queryPage(ViewSource source, ShipBookingUrgentReq req) {
        if (StringUtils.isNotEmpty(req.getFactoryCodes())) {
            req.setFactoryCodeList(Arrays.asList(StringUtils.split(req.getFactoryCodes(), ",")));
        }
        if (ViewSource.PC.equals(source)) {
            Long userId = UserContextUtils.getUserId();
            List<Factory> factories = factoryFacadeService.listByUser(userId);
            if (CollectionUtils.isEmpty(factories)) {
                log.info("PC user {} has no associated factories, returning empty page", userId);
                return new Page<>(req.getPage(), req.getPageSize());
            }
            List<String> factoryCodes = factories.stream()
                    .map(Factory::getCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(req.getFactoryCodeList())) {
                factoryCodes = factoryCodes.stream().filter(req.getFactoryCodeList()::contains)
                        .collect(Collectors.toList());
            }
            req.setFactoryCodeList(factoryCodes);
            log.info("PC user {} associated factories: {}", userId, factoryCodes);
        }
        if (CollUtil.isEmpty(req.getFactoryCodeList())) {
            return new Page<>(req.getPage(), req.getPageSize());
        }
        IPage<ShipBookingUrgentVO> pageParam = new Page<>(req.getPage(), req.getPageSize());
        Page<ShipBookingUrgentVO> resultPage = baseMapper.queryPage(pageParam, null, req);
        resultPage.getRecords().forEach(item -> {
            // Calculate days remaining
            if (item.getShipScheduleDate() != null) {
                item.setDaysRemainingBeforeShipment(LocalDateTimeUtil.calculateDaysBetween(LocalDate.now(), item.getShipScheduleDate()));
            }
            // Set process status name
            if (item.getProcessStatus() != null) {
                item.setProcessStatusName(item.getProcessStatus().getName());
            }
            // Process OM job numbers and names
            String omJobNos = item.getOmJobNos();
            String omNames = item.getOmNames();
            if (StrUtil.isBlank(omJobNos) || StrUtil.isBlank(omNames)) {
                return;
            }
            String[] omJobNosArr = omJobNos.split(",");
            String[] omNamesArr = omNames.split(",");
            if (omJobNosArr.length != omNamesArr.length) {
                log.error("Mismatched OM job numbers and names: jobNos={}, names={}", omJobNos, omNames);
                return;
            }
            String omCombined = IntStream.range(0, omJobNosArr.length)
                    .mapToObj(i -> omJobNosArr[i].trim() + ":" + omNamesArr[i].trim())
                    .collect(Collectors.joining(";"));
            item.setOm(omCombined);
        });
        return resultPage;
    }
}