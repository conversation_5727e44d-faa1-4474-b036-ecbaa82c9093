package com.lds.oneplanning.wps.warning.workbench.handlers;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.lds.oneplanning.esb.model.AtpLackOffMaterialRespVO;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormalShortage;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.entity.WpsOrderWarningCfg;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.MaterialAtpAbnormalType;
import com.lds.oneplanning.wps.enums.WpsOrderWarningCategoryEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.event.WpsPlanWarningEvent;
import com.lds.oneplanning.wps.helper.EsbApiWrapper;
import com.lds.oneplanning.wps.helper.WpsRowDataMergeHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.WarningMaterialAtpAbnormalService;
import com.lds.oneplanning.wps.service.WarningMaterialAtpAbnormalShortageService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.utils.DateUtils;
import com.lds.oneplanning.wps.utils.OrderNoUtils;
import com.lds.oneplanning.wps.utils.TraceIdUtils;
import com.lds.oneplanning.wps.utils.WpsDateUtil;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class MaterialAtpAbnormalHandler implements IWpsWorkbenchWarningHandler {
    private final WarningMaterialAtpAbnormalService atpAbnormalService;
    private final IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    private final WarningTodoListService todoListService;
    private final WarningMaterialAtpAbnormalShortageService materialAtpAbnormalShortageService;
    private final EsbApiWrapper esbApiWrapper;

    @Override
    public List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext ctx, Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap) {
        log.info("开始执行库存齐套异常信息处理");
        try {
            return doExecute(ctx);
        } finally {
            log.info("库存齐套异常信息处理完成");
        }
    }

    private List<WpsOrderPlanWarning> doExecute(WpsWorkbenchWarningContext ctx) {
        if (ctx == null || CollectionUtils.isEmpty(ctx.getOrders())) {
            return Collections.emptyList();
        }

        List<WpsRowData> atpStoreAbnormalList = new ArrayList<>();
        List<WpsRowData> atpInfoAbnormalList = new ArrayList<>();
        filterAbnormalData(ctx, atpStoreAbnormalList, atpInfoAbnormalList);

        log.info("库存齐套异常数量:{}, 信息齐套异常数量{}", atpStoreAbnormalList.size(), atpInfoAbnormalList.size());

        if (CollectionUtils.isEmpty(atpStoreAbnormalList) && CollectionUtils.isEmpty(atpInfoAbnormalList)) {
            //消警
            wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
            return Collections.emptyList();
        }

        //去掉没排产的数据
        atpStoreAbnormalList.removeIf(row -> row.getOnlineTime() == null);
        atpInfoAbnormalList.removeIf(row -> row.getOnlineTime() == null);

        log.info("移除未排产数据后：库存齐套异常数量:{}, 信息齐套异常数量{}", atpStoreAbnormalList.size(), atpInfoAbnormalList.size());
        if (CollectionUtils.isEmpty(atpStoreAbnormalList) && CollectionUtils.isEmpty(atpInfoAbnormalList)) {
            //消警
            wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
            return Collections.emptyList();
        }

        //合并相同订单的数据
        atpStoreAbnormalList = WpsRowDataMergeHelper.mergeSameOrder(atpStoreAbnormalList);
        atpInfoAbnormalList = WpsRowDataMergeHelper.mergeSameOrder(atpInfoAbnormalList);

        //分析异常数据
        return analyzeAbnormalData(ctx, atpStoreAbnormalList, atpInfoAbnormalList);
    }

    /**
     * 过滤异常数据
     *
     * @param ctx                  上下文对象，包含订单信息
     * @param atpStoreAbnormalList ATP存储结果异常的订单列表
     * @param atpInfoAbnormalList  ATP信息结果异常的订单列表
     */
    private static void filterAbnormalData(WpsWorkbenchWarningContext ctx, List<WpsRowData> atpStoreAbnormalList, List<WpsRowData> atpInfoAbnormalList) {
        for (WpsRowData row : ctx.getOrders()) {
            if (row.getAtpStatus() == 3) {
                // 信息不齐套
                atpInfoAbnormalList.add(row);
            } else if (row.getAtpStatus() == 2) {
                //库存不齐套
                atpStoreAbnormalList.add(row);
            }
        }
    }

    /**
     * 分析异常数据并生成警告信息
     *
     * @param ctx                  工作台警告上下文
     * @param atpStoreAbnormalList ATP存储异常数据列表
     * @param atpInfoAbnormalList  ATP信息异常数据列表
     * @return 包含警告信息的列表，如果未分析出异常则返回空列表
     */
    private List<WpsOrderPlanWarning> analyzeAbnormalData(WpsWorkbenchWarningContext ctx,
                                                          List<WpsRowData> atpStoreAbnormalList,
                                                          List<WpsRowData> atpInfoAbnormalList) {

        log.info("开始分析异常数据");
        List<WarningMaterialAtpAbnormal> warningList = new ArrayList<>();
        List<WpsRowData> abnormalRowList = new ArrayList<>();

        // 分析在库异常数据
        analyzeInStoreExceptions(atpStoreAbnormalList, abnormalRowList, warningList);
        log.info("完成在库异常数据分析，警告列表大小: {}", warningList.size());

        // 分析信息异常数据
        abnormalInfoAnalysis(atpInfoAbnormalList, abnormalRowList, warningList);
        log.info("完成信息异常数据分析，警告列表大小: {}", warningList.size());

        List<String> debugInfo = warningList.stream()
                .map(e -> "订单号:" + e.getOrderNumber() + ", 物料ID: " + JSON.toJSONString(e.getShortageIds()))
                .collect(Collectors.toList());
        log.info("生成的异常订单数据: {}", JSON.toJSONString(debugInfo));

        // 调用ATP接口，填充数据
        fillAtpData(ctx.getFactoryCode(), warningList);

        SpringUtil.getBean(MaterialAtpAbnormalHandler.class).saveData(ctx, warningList);

        return convertData(ctx, abnormalRowList, warningList);
    }

    private List<WpsOrderPlanWarning> convertData(WpsWorkbenchWarningContext ctx, List<WpsRowData> abnormalList, List<WarningMaterialAtpAbnormal> warningList) {
        Map<String, LightColor> lightColorMap = warningList.stream()
                .collect(Collectors.toMap(WarningMaterialAtpAbnormal::getOrderNumber, WarningMaterialAtpAbnormal::getLightColor, (o, o2) -> o));
        return buildWarning(ctx, abnormalList, lightColorMap);
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveData(WpsWorkbenchWarningContext ctx, List<WarningMaterialAtpAbnormal> warningList) {
        log.info("开始保存异常数据");
        if (CollectionUtils.isNotEmpty(warningList)) {
            //保存或更新
            saveOrUpdate(ctx, warningList);
            log.info("完成保存异常数据，警告列表大小: {}", warningList.size());

            log.info("开始创建待办");
            materialAtpAbnormalShortageService.createTodoList(warningList);

            String tid = TraceIdUtils.getTraceId();
            ThreadUtil.execute(() -> queryPoInfo(tid, warningList));
        }

        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
    }

    /**
     * 查询采购单信息
     *
     * @param tid
     * @param warningList 包含警告材料的列表
     */
    private void queryPoInfo(String tid, List<WarningMaterialAtpAbnormal> warningList) {
        TraceIdUtils.setTraceId(tid);
        List<WarningMaterialAtpAbnormalShortage> shortageList = warningList.stream()
                .flatMap(e -> e.getShortageList().stream())
                .collect(Collectors.toList());
        try {
            materialAtpAbnormalShortageService.queryPoInfo(shortageList);
        } catch (Exception e) {
            log.error("查询采购单数据失败", e);
        }
    }


    private void saveOrUpdate(WpsWorkbenchWarningContext ctx, List<WarningMaterialAtpAbnormal> warningList) {
        // 1. 提取订单号列表
        List<String> orderNoList = warningList.stream()
                .map(WarningMaterialAtpAbnormal::getOrderNumber)
                .distinct()
                .collect(Collectors.toList());
        log.info("Extracted order numbers count: {}", orderNoList.size());

        // 2. 查询数据库中已存在的记录
        List<WarningMaterialAtpAbnormal> existList = queryExistAtpAbnormal(orderNoList);
        log.info("Existing records count in database: {}", existList.size());

        // 3. 构建已存在记录的映射表（订单号 -> 物料ID -> 实体）
        Map<String, WarningMaterialAtpAbnormal> existTable = new HashMap<>();
        existList.forEach(warning -> existTable.put(warning.getOrderNumber(), warning));

        // 4. 初始化更新和插入列表
        List<WarningMaterialAtpAbnormal> updateList = new ArrayList<>();
        List<WarningMaterialAtpAbnormal> insertList = new ArrayList<>();

        // 5. 遍历warningList，区分更新和插入
        CopyOptions opt = CopyOptions.create().ignoreNullValue();
        warningList.forEach(warning -> {
            warning.setFactoryCode(ctx.getFactoryCode());
            warning.setUpdatedBy(ctx.getUserId());

            if (existTable.containsKey(warning.getOrderNumber())) {
                // 5.1 如果记录已存在，执行更新操作
                WarningMaterialAtpAbnormal entity = existTable.get(warning.getOrderNumber());
                BeanUtil.copyProperties(warning, entity, opt);
                // 反向更新到warning中，以便外部能获取到id
                BeanUtil.copyProperties(entity, warning);
                updateList.add(warning);
            } else {
                warning.setCreatedBy(ctx.getUserId());
                // 5.2 如果记录不存在，添加到插入列表
                insertList.add(warning);
            }
        });

        log.info("更新AtpAbnormal列表大小: {}, 插入列表大小: {}", updateList.size(), insertList.size());

        // 6. 批量更新和插入
        if (CollectionUtils.isNotEmpty(updateList)) {
            atpAbnormalService.updateBatchById(updateList);
            log.info("Updated abnormal records count: {}", updateList.size());

            updateAbnormalShortage(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            atpAbnormalService.saveBatch(insertList);
            log.info("Inserted abnormal records count: {}", insertList.size());
            //新增物料信息
            insertAbnormalShortage(insertList);
        }

        // 消警
        eliminateOrderAlarms(existList, updateList);
    }

    private List<WarningMaterialAtpAbnormal> queryExistAtpAbnormal(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return Collections.emptyList();
        }
        return atpAbnormalService.lambdaQuery()
                .in(WarningMaterialAtpAbnormal::getOrderNumber, orderNoList)
                .list();
    }

    /**
     * 消除订单报警
     *
     * @param existList  当前存在的报警信息列表
     * @param updateList 更新的报警信息列表
     */
    private void eliminateOrderAlarms(List<WarningMaterialAtpAbnormal> existList, List<WarningMaterialAtpAbnormal> updateList) {
        Set<String> existOrderNumbers = existList.stream()
                .map(WarningMaterialAtpAbnormal::getOrderNumber)
                .collect(Collectors.toSet());
        Set<String> updateOrderNumbers = updateList.stream()
                .map(WarningMaterialAtpAbnormal::getOrderNumber)
                .collect(Collectors.toSet());
        log.info("开始消警，存在订单数: {}, 更新订单数: {}", existOrderNumbers.size(), updateOrderNumbers.size());


        List<Long> toBeRemoveAbnormalIds = existList.stream()
                .filter(e -> !updateOrderNumbers.contains(e.getOrderNumber()))
                .peek(e -> log.info("订单号: {} 已消警", e.getOrderNumber()))
                .map(WarningMaterialAtpAbnormal::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(toBeRemoveAbnormalIds)) {
            log.info("No abnormal IDs to remove");
            return;
        }

        List<WarningMaterialAtpAbnormalShortage> list = materialAtpAbnormalShortageService.lambdaQuery()
                .in(WarningMaterialAtpAbnormalShortage::getAbnormalId, toBeRemoveAbnormalIds)
                .list();
        log.info("Found {} shortage records to remove", list.size());

        Set<Long> toBeRemoveShortageIds = list.stream()
                .map(WarningMaterialAtpAbnormalShortage::getId)
                .collect(Collectors.toSet());
        todoListService.eliminateAlarms(toBeRemoveShortageIds, getWarningType());
        log.info("Eliminated {} shortage alarms", toBeRemoveShortageIds.size());
    }

    /**
     * 更新物料异常短缺信息
     *
     * @param abnormalList 异常物料列表
     */
    private void updateAbnormalShortage(List<WarningMaterialAtpAbnormal> abnormalList) {
        setAbnormalId(abnormalList);

        // 旧数据消警
        // 获取异常物料ID集合
        List<WarningMaterialAtpAbnormalShortage> existList = queryExistAbnormalShortage(abnormalList);

        // 将已存在的物料短缺信息按异常ID分组
        Map<Long, List<WarningMaterialAtpAbnormalShortage>> existShortageMap = groupByAbnormalId(existList);

        // 初始化插入和更新列表
        List<WarningMaterialAtpAbnormalShortage> insertList = new ArrayList<>();
        List<WarningMaterialAtpAbnormalShortage> updateList = new ArrayList<>();

        // 设置复制选项，忽略空值
        CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();

        // 遍历异常物料列表
        Set<String> emptyShortageList = new HashSet<>();
        for (WarningMaterialAtpAbnormal abnormal : abnormalList) {
            if (CollectionUtils.isEmpty(abnormal.getShortageList())) {
                emptyShortageList.add(abnormal.getOrderNumber());
                continue;
            }


            // 获取当前异常ID对应的已存在物料短缺信息列表
            List<WarningMaterialAtpAbnormalShortage> exitStorageList = existShortageMap.get(abnormal.getId());

            // 如果不存在已存在的物料短缺信息，则将所有短缺信息添加到插入列表
            if (CollectionUtils.isEmpty(exitStorageList)) {
                insertList.addAll(abnormal.getShortageList());
                continue;
            }

            // 将已存在的物料短缺信息按短缺ID映射成Map
            Map<String, WarningMaterialAtpAbnormalShortage> shortageMap = exitStorageList.stream()
                    .collect(Collectors.toMap(WarningMaterialAtpAbnormalShortage::getShortageId, e -> e, (e1, e2) -> e1));

            // 遍历当前异常物料的短缺信息
            for (WarningMaterialAtpAbnormalShortage shortage : abnormal.getShortageList()) {
                // 如果短缺信息已存在，则更新信息并添加到更新列表
                if (shortageMap.containsKey(shortage.getShortageId())) {
                    WarningMaterialAtpAbnormalShortage exist = shortageMap.get(shortage.getShortageId());
                    BeanUtil.copyProperties(shortage, exist, copyOptions);
                    // 反向更新到shortage中，以便外部能获取到id
                    BeanUtil.copyProperties(exist, shortage);
                    updateList.add(shortage);
                }
                // 如果短缺信息不存在，则添加到插入列表
                else {
                    insertList.add(shortage);
                }
            }
        }

        log.warn("订单号: {}，没有缺料信息", emptyShortageList);

        log.info("更新AtpAbnormalShortage列表大小: {}, 插入列表大小: {}", updateList.size(), insertList.size());

        // 如果有更新列表，则批量更新物料短缺信息
        if (CollectionUtils.isNotEmpty(updateList)) {
            materialAtpAbnormalShortageService.updateBatchById(updateList);
        }

        // 如果有插入列表，则批量插入物料短缺信息
        if (CollectionUtils.isNotEmpty(insertList)) {
            materialAtpAbnormalShortageService.saveBatch(insertList);
        }

        // 调用消警方法
        eliminateAlarms(updateList, existList);
    }

    @NotNull
    private static Map<Long, List<WarningMaterialAtpAbnormalShortage>> groupByAbnormalId(List<WarningMaterialAtpAbnormalShortage> existList) {
        return existList
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        WarningMaterialAtpAbnormalShortage::getAbnormalId,
                        HashMap::new,
                        Collectors.toList()
                ));
    }

    private List<WarningMaterialAtpAbnormalShortage> queryExistAbnormalShortage(List<WarningMaterialAtpAbnormal> abnormalList) {
        Set<Long> abnormalIds = abnormalList.stream()
                .map(WarningMaterialAtpAbnormal::getId)
                .collect(Collectors.toSet());

        // 查询已存在的物料短缺信息
        List<WarningMaterialAtpAbnormalShortage> list = materialAtpAbnormalShortageService.lambdaQuery()
                .in(WarningMaterialAtpAbnormalShortage::getAbnormalId, abnormalIds)
                .list();

        log.info("现有物料信息：{}", list.stream()
                .map(WarningMaterialAtpAbnormalShortage::getShortageId)
                .collect(Collectors.toList()));

        return list;
    }


    /**
     * 消除告警
     *
     * @param updateList 更新后的告警列表
     * @param existList  现有的告警列表
     */
    private void eliminateAlarms(List<WarningMaterialAtpAbnormalShortage> updateList, List<WarningMaterialAtpAbnormalShortage> existList) {
        Set<String> updateShortage = updateList.stream()
                .map(WarningMaterialAtpAbnormalShortage::getShortageId)
                .collect(Collectors.toSet());
        log.info("更新Shortage后的告警列表：{}", updateShortage);
        Set<Long> toBeRemoveIds = existList.stream()
                .filter(e -> !updateShortage.contains(e.getShortageId()))
                .map(WarningMaterialAtpAbnormalShortage::getId)
                .collect(Collectors.toSet());
        log.info("更新Shortage后消警列表：{}", toBeRemoveIds);
        todoListService.eliminateAlarms(toBeRemoveIds, getWarningType());
    }

    private void insertAbnormalShortage(List<WarningMaterialAtpAbnormal> insertList) {
        setAbnormalId(insertList);
        List<WarningMaterialAtpAbnormalShortage> shortageList = insertList.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getShortageList()))
                .flatMap(e -> e.getShortageList().stream())
                .collect(Collectors.toList());
        log.info("Start to save abnormal shortage list, size: {}", shortageList.size());
        materialAtpAbnormalShortageService.saveBatch(shortageList);
        log.info("Finish saving abnormal shortage list");
    }

    private static void setAbnormalId(List<WarningMaterialAtpAbnormal> insertList) {
        insertList.forEach(e -> e.getShortageList().forEach(s -> s.setAbnormalId(e.getId())));
    }


    /**
     * 填充ATP数据
     *
     * @param factoryCode
     * @param warningList 警告材料ATP异常列表
     */
    private void fillAtpData(String factoryCode, List<WarningMaterialAtpAbnormal> warningList) {
        List<AtpLackOffMaterialRespVO> respList = fetchAtpCheckList(warningList);
        log.info("查询到的Atp数据：{}", respList.size());
        List<String> debugInfo = respList.stream()
                .map(vo -> "Atp数据, 订单: " + vo.getWORK_NO() + ", 物料: " + vo.getMATERIAL_ITEM_NO())
                .collect(Collectors.toList());
        log.info("Atp数据详情：{}", JSON.toJSONString(debugInfo));

        if (CollectionUtils.isEmpty(respList)) {
            log.warn("没有查询到Atp数据，不进行后续处理");
            return;
        }
        // 订单和物料查询到的atp可能有多条数据
        Table<String, String, List<AtpLackOffMaterialRespVO>> table = buildTable(respList);

        Set<String> emptyAtpOrder = new HashSet<>();
        warningList.forEach(abnormal -> {
            List<WarningMaterialAtpAbnormalShortage> shortageList = buildShortageList(factoryCode, abnormal, table);
            abnormal.setAptData(JSON.toJSONString(shortageList));
            abnormal.setShortageList(shortageList);
            if (CollectionUtils.isEmpty(shortageList)) {
                //如果没有物料数据，则跳过
                emptyAtpOrder.add(abnormal.getOrderNumber());
                return;
            }

            //找出最大齐套日期
            LocalDate maxQitao = shortageList.stream().map(WarningMaterialAtpAbnormalShortage::getQitaoDate)
                    .max(Comparator.naturalOrder()).orElse(null);

            //可得日期+1=预计可计划日期
            abnormal.setEstimatedPlanDate(DateUtils.offsetDays(maxQitao, 3));
            //调整后上线时间
            abnormal.setAdjustedOnlineTime(abnormal.getEstimatedPlanDate());
            //调整后上线时间-原上线时间 - 查询时最好重新计算
            abnormal.setAdjustedGapDays(DateUtils.daysBetween(abnormal.getPlannedOnlineTime(), abnormal.getAdjustedOnlineTime()));
        });

        log.info("订单号：{}，没有查询到物料数据", emptyAtpOrder);
    }

    /**
     * 构建短缺列表
     *
     * @param factoryCode
     * @param abnormal    警告物料ATP异常对象
     * @param table       表格数据，键为订单号和短缺ID，值为短缺物料的响应对象列表
     * @return 短缺列表
     */
    private List<WarningMaterialAtpAbnormalShortage> buildShortageList(String factoryCode, WarningMaterialAtpAbnormal abnormal, Table<String, String, List<AtpLackOffMaterialRespVO>> table) {
        List<WarningMaterialAtpAbnormalShortage> shortageList = new ArrayList<>();
        for (String shortageId : abnormal.getShortageIds()) {
            List<AtpLackOffMaterialRespVO> list = table.get(abnormal.getOrderNumber(), shortageId);
            if (CollectionUtils.isEmpty(list)) {
                //找不到ATP数据，跳过
                continue;
            }

            list.forEach(vo -> {
                WarningMaterialAtpAbnormalShortage shortage = buildShortage(vo);
                shortage.setOrderNumber(abnormal.getOrderNumber());
                shortage.setShortageId(shortageId);
                shortage.setFactoryCode(factoryCode);
                shortageList.add(shortage);

                //可得日期取最大值
                abnormal.setAvailableDate(DateUtils.max(shortage.getQitaoDate(), abnormal.getAvailableDate()));
            });
        }
        return shortageList;
    }

    /**
     * 根据传入的AtpLackOffMaterialRespVO对象构建短缺对象
     *
     * @param vo AtpLackOffMaterialRespVO对象，包含构建短缺对象所需的数据
     * @return 构建好的短缺对象WarningMaterialAtpAbnormalShortage
     */
    private WarningMaterialAtpAbnormalShortage buildShortage(AtpLackOffMaterialRespVO vo) {
        WarningMaterialAtpAbnormalShortage shortage = new WarningMaterialAtpAbnormalShortage();
        shortage.setShortageDescription(vo.getMATERIAL_ITEM_NAME());
        shortage.setRequiredTime(DateUtils.parseData(vo.getMATERIAL_NEED_DATE()));
        shortage.setAvailableDate(DateUtils.parseData(vo.getUSE_DATE()));
        shortage.setQitaoDate(DateUtils.parseData(vo.getPARENT_QITAO_DATE()));
        shortage.setShortageQuantity(toInt(vo.getUSE_QTY()));
        //gap:需求日期 - 查询时最好重新计算
        shortage.setGapDays(DateUtils.daysBetween(shortage.getAvailableDate(), shortage.getRequiredTime()));
        //"供应商 - 采购交期(可得日期)"
        shortage.setSupplier(vo.getSUPPLY());
        shortage.setPurchaseDeliveryTime(shortage.getAvailableDate());
        shortage.setPurchaseInCharge(vo.getPO_GROUP_R());
        shortage.setGroupInCharge(vo.getPO_GROUP());
        return shortage;
    }


    /**
     * 根据给定的响应列表构建表格
     *
     * @param respList 响应列表
     * @return 构建好的表格
     */
    private Table<String, String, List<AtpLackOffMaterialRespVO>> buildTable(List<AtpLackOffMaterialRespVO> respList) {
        Table<String, String, List<AtpLackOffMaterialRespVO>> table = HashBasedTable.create();
        respList.stream()
                .filter(vo -> StringUtils.isNotEmpty(vo.getMATERIAL_ITEM_NO()) && StringUtils.isNotEmpty(vo.getWORK_NO()))
                .forEach(vo -> {
                    List<AtpLackOffMaterialRespVO> list = table.get(vo.getWORK_NO(), vo.getMATERIAL_ITEM_NO());
                    if (list == null) {
                        list = Lists.newArrayList();
                        table.put(vo.getWORK_NO(), vo.getMATERIAL_ITEM_NO(), list);
                    }
                    list.add(vo);
                });
        return table;
    }


    private List<AtpLackOffMaterialRespVO> fetchAtpCheckList(List<WarningMaterialAtpAbnormal> warningList) {
        List<String> orderNoList = warningList.stream()
                .map(WarningMaterialAtpAbnormal::getOrderNumber)
                .collect(Collectors.toList());

        return esbApiWrapper.fetchAtpCheckList(orderNoList);
    }

    /**
     * 对异常信息进行分析处理。
     *
     * @param atpInfoAbnormalList 待分析的异常信息列表
     * @param abnormalRowList     存储异常信息的列表
     * @param warningList         存储警告信息的列表
     */
    private void abnormalInfoAnalysis(List<WpsRowData> atpInfoAbnormalList, List<WpsRowData> abnormalRowList, List<WarningMaterialAtpAbnormal> warningList) {
        // 获取当前时间
        LocalDate now = LocalDate.now();
        //获取第4-14天
        LocalDate start = now.plusDays(3);
        LocalDate end = now.plusDays(13);

        for (WpsRowData wpsRowData : atpInfoAbnormalList) {
            //只记录次日，4-14天内的信息异常
            MaterialAtpAbnormalType type = getInfoAbnormalType(wpsRowData.getOnlineTime(), start, end);
            if (type == null) continue;

            abnormalRowList.add(wpsRowData);

            LightColor color = LightColor.YELLOW;

            addAbnormalData(wpsRowData, color, type, warningList);
        }
    }

    /**
     * 分析并处理在库异常数据
     *
     * @param atpStoreAbnormalList atp在库异常数据列表
     * @param abnormalRowList      异常行列表
     * @param warningList          警告列表
     */
    private void analyzeInStoreExceptions(List<WpsRowData> atpStoreAbnormalList, List<WpsRowData> abnormalRowList, List<WarningMaterialAtpAbnormal> warningList) {
        // 获取当前时间
        LocalDate now = LocalDate.now();
        // 获取明天的日期
        LocalDate tomorrow = now.plusDays(1);
        // 获取3天后日期
        LocalDate threeDaysLater = now.plusDays(3);
        for (WpsRowData wpsRowData : atpStoreAbnormalList) {
            //只记录次日，三天内的在库异常
            MaterialAtpAbnormalType type = getStoreAbnormalType(wpsRowData.getOnlineTime(), tomorrow, threeDaysLater);
            if (type == null) continue;

            abnormalRowList.add(wpsRowData);

            LightColor color = LightColor.RED;

            addAbnormalData(wpsRowData, color, type, warningList);
        }
    }

    /**
     * 将异常数据添加到警告列表中
     *
     * @param wpsRowData  包含材料代码的 WPS 行数据
     * @param color       异常数据对应的颜色
     * @param type        异常数据的类型
     * @param warningList 用于存储异常数据的警告列表
     */
    private void addAbnormalData(WpsRowData wpsRowData, LightColor color, MaterialAtpAbnormalType type, List<WarningMaterialAtpAbnormal> warningList) {

        Set<String> materialIds = Collections.emptySet();
        switch (type) {
            case IN_STOCK_NOT_COMPLETE_1:
            case IN_STOCK_NOT_COMPLETE_3:
                materialIds = wpsRowData.getLackOfMaterialIds();
                break;
            case INFORMATION_NOT_COMPLETE_14:
                materialIds = wpsRowData.getLackOfInfoMaterialIds();
                break;
        }
        WarningMaterialAtpAbnormal abnormal = toAbnormalData(wpsRowData, materialIds, color, type);
        warningList.add(abnormal);
    }

    /**
     * 根据WpsRowData对象、开始日期和结束日期，获取信息异常类型
     *
     * @param onLineDate WpsRowData对象
     * @param start      开始日期
     * @param end        结束日期
     * @return 如果WpsRowData的上线时间在开始日期和结束日期之间，则返回MaterialAtpAbnormalType.INFORMATION_NOT_COMPLETE_14，否则返回null
     */
    @Nullable
    private MaterialAtpAbnormalType getInfoAbnormalType(Date onLineDate, LocalDate start, LocalDate end) {
        MaterialAtpAbnormalType type;
        if (DateUtils.isBetween(start, end, onLineDate)) {
            type = MaterialAtpAbnormalType.INFORMATION_NOT_COMPLETE_14;
        } else {
            return null;
        }
        return type;
    }

    /**
     * 根据给定数据判断并返回库存异常类型
     *
     * @param onlineTime     店铺数据对象
     * @param tomorrow       明日日期
     * @param threeDaysLater 三日后的日期
     * @return 根据判断结果返回相应的库存异常类型，若无匹配结果则返回null
     */
    @Nullable
    private MaterialAtpAbnormalType getStoreAbnormalType(Date onlineTime, LocalDate tomorrow, LocalDate threeDaysLater) {
        MaterialAtpAbnormalType type;
        if (DateUtils.isSameDay(tomorrow, onlineTime)) {
            type = MaterialAtpAbnormalType.IN_STOCK_NOT_COMPLETE_1;
        } else if (DateUtils.isBetween(tomorrow, threeDaysLater, onlineTime)) {
            type = MaterialAtpAbnormalType.IN_STOCK_NOT_COMPLETE_3;
        } else {
            return null;
        }
        return type;
    }

    /**
     * 将WPS数据行转换为异常数据
     *
     * @param materialIds 物料代码
     * @param wpsRowData  WPS数据行对象
     * @param color       指示灯颜色
     * @param type        物料ATP异常类型
     * @return 异常数据对象
     */
    private WarningMaterialAtpAbnormal toAbnormalData(WpsRowData wpsRowData, Set<String> materialIds, LightColor color, MaterialAtpAbnormalType type) {
        WarningMaterialAtpAbnormal abnormal = new WarningMaterialAtpAbnormal();
        abnormal.setPlannedOnlineTime(WpsDateUtil.getStartScheduleDate(wpsRowData.getScheduleDataMap()));
        abnormal.setOnlineQuantity(toInt(wpsRowData.getOrderUnitQty()));
        abnormal.setCustomer(wpsRowData.getCustomerCode());
        abnormal.setSalesOrderNumber(OrderNoUtils.trimPreZero(OrderNoUtils.getUnifyOrderNo(wpsRowData)));
        abnormal.setLineNumber(OrderNoUtils.trimPreZero(wpsRowData.getRowItem()));
        abnormal.setOrderNumber(wpsRowData.getOrderNo());
        abnormal.setMaterialId(wpsRowData.getCommodityId());
        abnormal.setMaterialDescription(wpsRowData.getCommodityDesc());
        abnormal.setLightColor(color);
        abnormal.setAbnormalType(type);
        abnormal.setShortageIds(materialIds);
        abnormal.setOrderData(JSON.toJSONString(wpsRowData));
        return abnormal;
    }


    @Override
    public WpsOrderWarningCategoryEnum getWarningCategory() {
        return WpsOrderWarningCategoryEnum.DEFAULT;
    }

    @Override
    public WpsOrderWarningTypeEnum getWarningType() {
        return WpsOrderWarningTypeEnum.ATP_EXCEPTION;
    }

    @EventListener
    public void handleCustomEvent(WpsPlanWarningEvent event) {
        log.info("收到排产异常分析结束事件，开始处理...");
        //主表消警
        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
    }
}
