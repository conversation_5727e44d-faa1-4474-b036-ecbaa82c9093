package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.util.StrUtil;
import com.lds.oneplanning.common.service.IBasicUserService;
import com.lds.oneplanning.esb.datafetch.model.EsbIncomeMaterialCheckAbnormalData;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.entity.WarningIncomeMaterialPoAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormalShortage;
import com.lds.oneplanning.wps.entity.WarningTodoList;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.mapper.WarningIncomeMaterialPoAtpAbnormalMapper;
import com.lds.oneplanning.wps.service.IWarningIncomeMaterialAtpAbnormalService;
import com.lds.oneplanning.wps.service.IWarningIncomeMaterialPoAtpAbnormalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningIncomeMaterialPoAtpAbnormalServiceImpl extends ServiceImpl<WarningIncomeMaterialPoAtpAbnormalMapper, WarningIncomeMaterialPoAtpAbnormal> implements IWarningIncomeMaterialPoAtpAbnormalService {

    private final WarningTodoListService warningTodoListService;
    private final EsbDataFetchService esbDataFetchService;
    private final IBasicUserService basicUserService;
    private final WarningTodoListService todoListService;
    private final IWarningIncomeMaterialAtpAbnormalService warningIncomeMaterialAtpAbnormalService;
    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningIncomeMaterialPoAtpAbnormal }>
     */
    @Override
    public List<WarningIncomeMaterialPoAtpAbnormal> queryUnHandleData() {
        return baseMapper.queryUnHandleData();
    }

    @Override
    public void updateIncomePoInfo() {
        List<Long> undoList = warningTodoListService.lambdaQuery()
                .in(WarningTodoList::getProcessStatus, Arrays.asList(OrderWarningHandleStatusEnum.UN_HANDLE, OrderWarningHandleStatusEnum.HANDLED))
                .select(WarningTodoList::getId, WarningTodoList::getBizId)
                .eq(WarningTodoList::getWarningType, WpsOrderWarningTypeEnum.MATERIAL_INSPECTION_ABNORMAL)
                .list().stream().map(WarningTodoList::getBizId).collect(Collectors.toList());
        //查询所有数据进行处理
        List<WarningIncomeMaterialPoAtpAbnormal> list = this.lambdaQuery()
                .in(WarningIncomeMaterialPoAtpAbnormal::getId, undoList)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        queryIncomePoInfo(list,undoList);
    }

    @Override
    @Transactional
    public void queryIncomePoInfo(List<WarningIncomeMaterialPoAtpAbnormal> poList, List<Long> undoList) {
        // 去查询sap接口SAP-ZPPFU1065，返回处理结果更新数据
        List<EsbIncomeMaterialCheckAbnormalData> checkAbnormalDataList = new ArrayList<>();
        for (WarningIncomeMaterialPoAtpAbnormal poAtpAbnormal : poList){
            EsbIncomeMaterialCheckAbnormalData checkAbnormalData = new EsbIncomeMaterialCheckAbnormalData();
            checkAbnormalData.setMATNR(poAtpAbnormal.getShortageMaterialId());
            checkAbnormalData.setWERKS(poAtpAbnormal.getFactory());
            checkAbnormalDataList.add(checkAbnormalData);
        }
        List<EsbIncomeMaterialCheckAbnormalData> esbIncomeMaterialCheckAbnormalData = esbDataFetchService.incomeMaterialCheckAbnormal(checkAbnormalDataList);
        esbIncomeMaterialCheckAbnormalData.forEach(data -> {
            if (StrUtil.isEmpty(data.getKURZTEXT())){
                data.setKURZTEXT("待检");
            }
        });
        List<WarningIncomeMaterialPoAtpAbnormal> poListNew = new ArrayList<>();
        List<String> materialIds = esbIncomeMaterialCheckAbnormalData.stream().map(EsbIncomeMaterialCheckAbnormalData::getMATNR).distinct().collect(Collectors.toList());
        //poList去掉不包含materialIds的记录
        poList = poList.stream().filter(po -> materialIds.contains(po.getShortageMaterialId())).collect(Collectors.toList());
        for (WarningIncomeMaterialPoAtpAbnormal poAtpAbnormal : poList){
           for (EsbIncomeMaterialCheckAbnormalData data : esbIncomeMaterialCheckAbnormalData){
               if (poAtpAbnormal.getFactory().equals(data.getWERKS()) && poAtpAbnormal.getShortageMaterialId().equals(data.getMATNR()) && !"合格".equals(data.getKURZTEXT())){
                   poAtpAbnormal.setDealResult(data.getKURZTEXT());
                   poListNew.add(poAtpAbnormal);
               }
           }
        }
        // 获取poList的id
        List<Long> poIds = poListNew.stream().map(WarningIncomeMaterialPoAtpAbnormal::getId).collect(Collectors.toList());
        boolean b = this.removeByIds(poIds);
        if (b){
            // 去掉主表不包含订单号的记录
            List<String> orderNos = poListNew.stream().map(WarningIncomeMaterialPoAtpAbnormal::getOrderNo).distinct().collect(Collectors.toList());
            List<Long> ids = this.lambdaQuery()
                    .notIn(WarningIncomeMaterialPoAtpAbnormal::getOrderNo, orderNos)
                    .list().stream().map(WarningIncomeMaterialPoAtpAbnormal::getId).collect(Collectors.toList());
            warningIncomeMaterialAtpAbnormalService.removeByIds(ids);
            //poListNew去掉处理结果为合格的记录
            boolean b1 = this.saveBatch(poListNew);
            if (b1){
                // 先把待办删掉
                todoListService.removeByIds(undoList);
                createTodoList(poListNew);
            }
        }
    }

    @Override
    public void createTodoList(List<WarningIncomeMaterialPoAtpAbnormal> warningList) {
        List<WarningTodoList> todoList = new ArrayList<>();
        Map<String, Map<String, String>> existWerk = new HashMap<>();
        for (WarningIncomeMaterialPoAtpAbnormal e : warningList){
            if("待检".equals(e.getDealResult()) || "不合格".equals(e.getDealResult())){
                String[] ghs = e.getQualityInspectorsGh().split(",");
                if (ghs.length > 0) {
                    Map<String, String> stringStringMap = new HashMap<>();
                    if (existWerk.containsKey(e.getFactory())){
                        stringStringMap = existWerk.get(e.getFactory());
                    } else {
                        stringStringMap = basicUserService.batchGetLoginNamesByJobNos(Arrays.asList(ghs));
                        existWerk.put(e.getFactory(), stringStringMap);
                    }
                    //循环stringStringMap
                    for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
                        String loginName = entry.getValue();
                        WarningTodoList todo = new WarningTodoList( WpsOrderWarningTypeEnum.MATERIAL_INSPECTION_ABNORMAL, e.getFactory(),  e.getId(), loginName);
                        todoList.add(todo);
                    }
                }
            }
            if ("退货".equals(e.getDealResult()) || "报废".equals(e.getDealResult())){
                if (StrUtil.isNotEmpty(e.getPoGroup())){
                    // 根据物料组查询登录名
                    Map<String, String> stringStringMap = basicUserService.batchGetLoginNamesByPoGroups(Collections.singletonList(e.getPoGroup()));
                    WarningTodoList todo = new WarningTodoList( WpsOrderWarningTypeEnum.MATERIAL_INSPECTION_ABNORMAL,e.getFactory(),  e.getId(), stringStringMap.get(e.getPoGroup()));
                    todoList.add(todo);
                }
            }
        }
        log.info("待办列表大小: {}", todoList.size());
        todoListService.saveData( WpsOrderWarningTypeEnum.MATERIAL_INSPECTION_ABNORMAL, todoList);
    }
}
