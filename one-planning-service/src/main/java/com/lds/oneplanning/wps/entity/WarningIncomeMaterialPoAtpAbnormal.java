package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.lds.oneplanning.wps.enums.LightColor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WarningIncomeMaterialPoAtpAbnormal对象", description="")
public class WarningIncomeMaterialPoAtpAbnormal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "具体欠料ID")
    private String shortageMaterialId;

    @ApiModelProperty(value = "采购PO")
    private String purchasePo;

    @ApiModelProperty(value = "具体欠料描述")
    private String shortageMaterialDesc;

    @ApiModelProperty(value = "来料检验数量")
    private Integer checkQuantity;

    @ApiModelProperty(value = "处理结果")
    private String dealResult;

    @ApiModelProperty(value = "质检人员")
    private String qualityInspectors;

    @ApiModelProperty(value = "质检人员工号")
    private String qualityInspectorsGh;

    @ApiModelProperty(value = "采购人员")
    private String purchaser;

    @ApiModelProperty(value = "采购组")
    @TableField(exist = false)
    private String poGroup;

    @ApiModelProperty(value = "采购人员工号")
    private String purchaserGh;

    @ApiModelProperty(value = "下一批到料日期")
    private String nextArrivalDate;

    @ApiModelProperty(value = "最早可再计划日期")
    private String earliestPlanDate;

    @ApiModelProperty(value = "确定再计划日期")
    private String sureReplanDate;

    @ApiModelProperty(value = "是否影响上下层计划")
    private Integer sfyxsxcjh;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    @ApiModelProperty(value = "供应商名称")
    private String supplyName;

    @ApiModelProperty(value = "影响数量")
    @TableField("Impact_quantity")
    private String impactQuantity;

    @ApiModelProperty(value = "工厂")
    private String factory;

    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

}
