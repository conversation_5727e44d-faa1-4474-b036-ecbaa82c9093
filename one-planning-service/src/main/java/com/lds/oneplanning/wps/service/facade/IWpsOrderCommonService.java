package com.lds.oneplanning.wps.service.facade;

import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsOrderTypeEnum;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IWpsOrderCommonService {

    /**
     * 处理已冻结订单
     *
     * @param context
     * @param frozenOrderList
     * @param localDates
     */
    void doFrozenOrders(WpsAutoScheduleContext context, List<WpsRowData> frozenOrderList, List<LocalDate> localDates);

    /**
     * 按<产线UUID,<产品编号,Uph>>分组
     *
     * @param lineProductIdMap
     * @return
     */
    Map<String, Map<String, Float>> getLineProductUphMap(Map<String, List<String>> lineProductIdMap);

    Set<String> getSuitableLineUuids(WpsAutoScheduleContext context, WpsRowData order, Integer priority);

    List<LineInfo> getLineInfosByUserId(Long userId);

    void updateDailyScheduleData(WpsAutoScheduleContext context, String orderNo, LocalDate date, String lineUuid, int scheduledQty);

    void processOrder(WpsAutoScheduleContext context, WpsRowData order, WpsOrderTypeEnum wpsOrderTypeEnum,
                      Map<String, Map<String, Map<LocalDate, Integer>>> planedOrderMap,
                      List<LocalDate> localDates);

    /**
     * 处理已保存过的订单
     * @param context
     * @param order
     * @param planedOrderMap
     */
    void processSaveOrder(WpsAutoScheduleContext context, WpsRowData order,
                                 Map<String, Map<String, Map<LocalDate, Integer>>> planedOrderMap,LocalDate endDate);

    /**
     * 更新排产信息
     * @param context
     * @param dailyProductionLineMap
     * @param lineUuid
     * @param orderNo
     * @param date
     * @param scheduledQty
     * @param uph
     */
    void updateProductionLine(WpsAutoScheduleContext context, Map<LocalDate, WpsProductionLine> dailyProductionLineMap,
                                     String lineUuid, String orderNo, LocalDate date,
                                     Integer scheduledQty, Float uph);
}