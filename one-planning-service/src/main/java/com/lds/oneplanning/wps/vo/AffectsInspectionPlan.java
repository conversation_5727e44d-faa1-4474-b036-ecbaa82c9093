package com.lds.oneplanning.wps.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(value = "影响验货计划")
@Data
public class AffectsInspectionPlan {
    @ApiModelProperty(value = "销售订单号")
    private String sellOrderNo;
    @ApiModelProperty(value = "行项目")
    private String rowItem;
    @ApiModelProperty(value = "ID")
    private String id;
    @ApiModelProperty(value = "验货日期")
    private Date planDate;
    @ApiModelProperty(value = "影响数量")
    private Integer quantity;
}