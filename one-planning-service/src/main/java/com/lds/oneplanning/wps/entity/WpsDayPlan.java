package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WpsWeekPlan对象", description="")
public class WpsDayPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "业务id")
    private String bizId;

    @ApiModelProperty(value = "完整业务id")
    private String fullBizId;

    @ApiModelProperty(value = "产线列表编码")
    private String lineCode;

    @ApiModelProperty(value = "线体uuid")
    private String lineUuid;

    @ApiModelProperty(value = "排产日期")
    private LocalDate scheduleDate;

    @ApiModelProperty(value = "预产数量")
    private Integer prePlanQuantity;

    @ApiModelProperty(value = "冻结数")
    private Integer frozenQty;

    @ApiModelProperty(value = "预排产时长")
    private Float prePlanDuration;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
