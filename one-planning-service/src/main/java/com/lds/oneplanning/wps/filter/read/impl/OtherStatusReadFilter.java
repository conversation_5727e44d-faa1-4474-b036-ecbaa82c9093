package com.lds.oneplanning.wps.filter.read.impl;

import com.lds.oneplanning.wps.filter.read.AbstractWpsOrderReadFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 上线日期在100天后的不需要
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/25 17:23
 */
@Slf4j
@Service
public class OtherStatusReadFilter extends AbstractWpsOrderReadFilter {

    @Resource
    private IMesProcessWorkOrderService mesProcessWorkOrderService;

    @Override
    public Integer filterSeq() {
        return 9;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag) {
        //特殊处理 日期过滤 上线日期在100天以后的不要  取消日期上线日期过滤
//        dirtyList = dirtyList.stream().filter(wpsRowData -> wpsRowData.getCalculateOnlineTime() != null &&
//                (wpsRowData.getCalculateOnlineTime().isBefore(LocalDate.now().plusDays(100)))).collect(Collectors.toList());
        Set<String> orderNos = dirtyList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toSet());
        Map<String, Integer> reportQtyMap = mesProcessWorkOrderService.getReportQtyMap(orderNos);
        dirtyList.stream().forEach(wpsRowData -> wpsRowData.setMesProcessStatus(reportQtyMap.getOrDefault(wpsRowData.getOrderNo(), 0)));
        return dirtyList;
    }


}
