package com.lds.oneplanning.wps.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.constants.ScheduleConstant;
import com.lds.oneplanning.basedata.enums.OrderSubTypeEnum;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.*;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/7 17:00
 */
@ApiModel(value="wps excel行对象", description="wps excel行对象")
@Data
public class WpsRowData {
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WpsRowData that = (WpsRowData) o;
        return Objects.equals(lineCode, that.lineCode) && Objects.equals(orderNo, that.orderNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(lineCode, orderNo);
    }


    @ApiModelProperty(value = "包材版面")
    private String packagePrint;

    @ApiModelProperty(value = "主计划")
    private String mainPlan;

    @ApiModelProperty(value = "主计划工号")
    private String plannerEmpNo;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;
    @ApiModelProperty(value = "车间名称")
    private String workshopName;
    @ApiModelProperty(value = "线体UUID")
    private String lineUuid;
    @ApiModelProperty(value = "线体编码")
    private String lineCode;
    @ApiModelProperty(value = "线体名称")
    private String lineName;
    @ApiModelProperty(value = "客户组-订单接口-LOCCO")
    private String customerGroup;
    @ApiModelProperty(value = "客户代码-订单接口SORTL")
    private String customerCode;
    @ApiModelProperty(value = "销售订单号-订单接口-VBELN")
    private String sellOrderNo;
    @ApiModelProperty(value = "行项目-订单接口-订单接口-POSNR")
    private String rowItem;
    @ApiModelProperty(value = "om负责人-评审接口")
    private String omRespons;
    @ApiModelProperty(value = "生产工厂（编码）-订单接口-WERKS")
    private String factory;
    @ApiModelProperty(value = "订单类型-订单接口-ZTYPE")
    private String orderType;
    @ApiModelProperty(value = "具体生产订单类型编码-AUART")
    private String productOrderTypeCode;
    @ApiModelProperty(value = "具体生产订单类型转化名称-AUART")
    private String productOrderType;
    @ApiModelProperty(value = "订单号-订单接口-ZDDH")
    private String orderNo;
    @ApiModelProperty(value = "外向交货单-订单接口-未对应")
    private String outDeliveryNo;
    @ApiModelProperty(value = "外向行项目-订单接口-未对应")
    private String outRowItem;
    @ApiModelProperty(value = "商品id-订单接口-ZSPID")
    private String commodityId;
    @ApiModelProperty(value = "商品描述-订单接口-未对应")
    private String commodityDesc;
    @ApiModelProperty(value = "跨工厂物料状态")
    private String crossPlantMaterialStatus;
    @ApiModelProperty(value = "三级产品线")
    private String productLineLevel3;
    @ApiModelProperty(value = "五级产品线")
    private String productLineLevel5;
    @ApiModelProperty(value = "品类（大）")
    private String category;
    @ApiModelProperty(value = "机型（中）")
    private String machineModel;
    @ApiModelProperty(value = "规格（小）")
    private String specification;
    @ApiModelProperty(value = "产品id")
    private String productId;
    @ApiModelProperty(value = "产品描述")
    private String productDesc;
    @ApiModelProperty(value = "产品组编码")
    private String productGroupCode;
    @ApiModelProperty(value = "产品组名称")
    private String productGroupName;
    @ApiModelProperty(value = "订单数量（套数）")
    private Integer orderUnitQty;
    @ApiModelProperty(value = "订单数量（只数）")
    private Integer orderPcsQty;
    @ApiModelProperty(value = "转化数量")
    private Integer transQty;
    @ApiModelProperty(value = "已报工数量（只数）")
    private Integer reportedPcsQty;
    @ApiModelProperty(value = "已入库数量（只数）")
    private Integer stockedPcsQty;
    @ApiModelProperty(value = "需排产数量（只数）")
    private Integer schedulePcsQty;
    @ApiModelProperty(value = "业务留样数量")
    private Integer bizSampleQty;
    @ApiModelProperty(value = "品保留样数量")
    private Integer qcSampleQty;
    @ApiModelProperty(value = "库存齐套物料map: key是物料组编码，value是物料描述")
    private Map<String,String> materialMap = Maps.newLinkedHashMap();
    @ApiModelProperty(value = "库存齐套物料map，key是物料组编码，value是对应物料id集合")
    private Map<String, Set<String>> materialCodeMap = Maps.newLinkedHashMap();

    @ApiModelProperty(value = "信息齐套物料map: key是物料组编码，value是物料描述")
    private Map<String,String> infoMaterialMap = Maps.newLinkedHashMap();
    @ApiModelProperty(value = "信息齐套物料map，key是物料组编码，value是对应物料id集合")
    private Map<String, Set<String>> infoMaterialCodeMap = Maps.newLinkedHashMap();

    @ApiModelProperty(value = "库存不齐套物料id集合-原生id")
    private Set<String> lackOfMaterialIds = Sets.newLinkedHashSet();
    @ApiModelProperty(value = "信息不齐套物料id集合-原生id")
    private Set<String> lackOfInfoMaterialIds = Sets.newLinkedHashSet();

    @ApiModelProperty(value = "1库存齐套 2信息齐套 3信息不齐套 4未知")
    private Integer atpStatus = 4;

    public String getAtpStatusDesc(){
        switch (atpStatus){
            case 1 : return "库存齐套";
            case 2 : return "信息齐套";
            case 3 : return "信息不齐套";
            case 4 : return "未知";
            default: return "未知";
        }
    }

    @ApiModelProperty(value = "0未开始 1在制  2完工")
    private Integer mesProcessStatus = 0;

    public Integer getOrderPcsQty() {
        Integer res = Optional.ofNullable(orderPcsQty).orElse(0);
        if (res >0) {
            return res;
        }else if( res == 0 && this.getTransQty()!=null && this.getOrderUnitQty()!=null){
            return this.getTransQty() * this.getOrderUnitQty();
        }else{
            return 0;
        }
    }

    /**
     * 销售订单置空，其他返回orderNo
     * @return
     */
    public String getProductPlanOrderNo(){
        if ("销售订单".equals(orderType)) {
            return null;
        }
        return this.getOrderNo();
    }

    @ApiModelProperty(value = "标准工时（总装+老化+包装）")
    private String stdWorkHours;
    @ApiModelProperty(value = "包装方式")
    private String packageType;
    @ApiModelProperty(value = "产能结构")
    private String capacityStruct;
    @ApiModelProperty(value = "上线日期-真实排产的第一天")
    private Date onlineTime;
    @ApiModelProperty(value = "预排产上线日期-允许排产的最早时间")
    private LocalDate preOnlineTime;

    @ApiModelProperty(value = "原始完工日期")
    private Date originalFinishTime;
    @ApiModelProperty(value = "是否验货")
    private String isInspect;
    @ApiModelProperty(value = "原始计划验货日期")
    private Date originalInspectTime;
    @ApiModelProperty(value = "最新计划验货日期")
    private Date latestInspectTime;
    @ApiModelProperty(value = "原始计划装柜日期")
    private Date originalLoadTime;
    @ApiModelProperty(value = "最新计划装柜日期")
    private Date latestLoadTime;
    @ApiModelProperty(value = "原始船期")
    private Date originalShipTime;
    @ApiModelProperty(value = "最终船期")
    private Date finalShipTime;
    @ApiModelProperty(value = "推估需求完工日期")
    private Date estFinishTime;
    @ApiModelProperty(value = "生产开始日期（组装/上线）")
    private Date productStartTime;
    @ApiModelProperty(value = "生产结束日期")
    private Date productEndTime;
    @ApiModelProperty(value = "原回复完工日期")
    private Date originalReplyTime;
    @ApiModelProperty(value = "新回复完工日期")
    private Date latestReplyTime;
    @ApiModelProperty(value = "备注1")
    private String remark1;
    @ApiModelProperty(value = "备注2")
    private String remark2;
    @ApiModelProperty(value = "排产方向：1正 -1 -2")
    private Integer scheduleDirection = ScheduleConstant.DEFAULT_DIRECTION;


    /**
     * value - key是日期，value是当日排产数量
     * 拿第一个value非空的日期作为排产的第一天
     */
    private Map<LocalDate,Number> scheduleDataMap = Maps.newLinkedHashMap();

    /**
     * 排产容量告警
     * "row"表示一整行
     */
    private Map<String, Integer> warningColorMap = Maps.newLinkedHashMap();

    @ApiModelProperty(value = "产品单价")
    private Number productUnitPrice;
    @ApiModelProperty(value = "订单签发完成日期")
    private Date orderSignFinishTime;
    @ApiModelProperty(value = "包装/总装")
    private String finalAssembly;
    @ApiModelProperty(value = "风险物料信息备注")
    private String riskMaterialRemark;
    @ApiModelProperty(value = "虚拟订单号")
    private String virtualOrderNo;
    @ApiModelProperty(value = "虚拟订单项次")
    private String virtualRowItem;
    @ApiModelProperty(value = "首次评审船期")
    private Date firstReviewShipTime;
    @ApiModelProperty(value = "pono")
    private String pono;
    @ApiModelProperty(value = "客户物料编号")
    private String customerMaterialNo;
    @ApiModelProperty(value = "客户顺序")
    private String customerSeq;
    @ApiModelProperty(value = "瓦数")
    private String powerNum;
    @ApiModelProperty(value = "尺寸（方形/原型/规格）")
    private String size;


    @ApiModelProperty(value = "订单创建日期")
    private LocalDate createDate;

    // 功能字段 不显示
    // 冻结状态0否 1是 下划线开头，前端不渲染，表示动作属性
    @ApiModelProperty(value = "冻结状态")
    @JSONField(name = "_frozenStatus")
    private Integer _frozenStatus;
    @ApiModelProperty(value = "可以开始排产日期")
    @JSONField(name = "_startProductPeriod")
    private LocalDate _startProductPeriod;

    @ApiModelProperty(value = "截止排产日期")
    @JSONField(name = "_endProductPeriod")
    private LocalDate _endProductPeriod;

    @ApiModelProperty(value = "可以开始排产日期(自动排产设置)")
    @JSONField(name = "_startProductPeriodTemp")
    private LocalDate _startProductPeriodTemp;


    @ApiModelProperty(value = "可以截止排产日期(自动排产设置)")
    @JSONField(name = "_endProductPeriodTemp")
    private LocalDate _endProductPeriodTemp;

    /**
     * 待排产数量(内存变量)
     */
    @JSONField(serialize = false)
    private int waitingOrderQty = 0;

    /**
     * 是否客户专属产线类(内存变量)
     */
    @JSONField(serialize = false)
    private boolean isCustomerExclusive;

    @JSONField(serialize = false)
    private String _productGroupCode;

    @JSONField(serialize = false)
    private String _productGroupName;

    @JSONField(serialize = false)
    private Integer _publishStatus;

    /**
     * 预计生产完成日期(内存变量)
     */
    @JSONField(serialize = false)
    private LocalDate _estProductionFinishDate;

    /**
     * 是否虚拟线体订单(内存变量)
     */
    @JSONField(serialize = false)
    private boolean _virtualLineOrder;
    /**
     * 计算的允许最后完工日期
     */
    private LocalDate calculateFinishTime;

    private boolean outDeliverSetFlag = false;

    /**
     * 排产日期
     */
    private List<LocalDate> scheduleDates;
    /**
     * 生产该订单是否出现换产品组，默认是没有
     */
    private Boolean lineChangeProductGroup=false;
    public String getUnifyOrderNo(){
        return Optional.ofNullable(this.getSellOrderNo()).orElse(this.getOrderNo());
    }

    public String getSellOrderAndRowItemNo(){
        return this.getSellOrderNo()+"-"+this.getRowItem();
    }

    public LocalDate getCalculateFinishTime() {
        if (calculateFinishTime != null) {
            return calculateFinishTime;
        }
        Date res =  this.getEstFinishTime() !=null ? this.getEstFinishTime() : this.getOriginalFinishTime();
        return res ==null ? null : LocalDateTimeUtil.dateToLocalDate(res);
    }

    /**
     * 判断是否虚拟线体订单
     *
     * @return
     */
    public boolean get_virtualLineOrder() {
        String orderType = this.getOrderType();
        String productOrderTypeCode = this.getProductOrderTypeCode();
        if(StringUtils.isEmpty(orderType)){
            return false;
        }
        if("采购订单".equals(orderType) || "采购申请".equals(orderType)){
            return true;
        }
        return "生产订单".equals(orderType) &&
                (OrderSubTypeEnum.REWORK.getCode().equals(productOrderTypeCode)
                        || OrderSubTypeEnum.PP_SAMPLE.getCode().equals(productOrderTypeCode));
    }

    public LocalDate getRecordDate(){
        if (this.getOrderSignFinishTime()!=null) {
            return LocalDateTimeUtil.dateToLocalDate(this.getOrderSignFinishTime());
        }else {
            return null;
        }
    }

    /**
     * 获取告警的最终船期
     *
     * @return
     */
    public LocalDate getWarningFinalShipDate() {
        return Optional.ofNullable(getFinalShipTime())
                .map(LocalDateTimeUtil::dateToLocalDate)
                .orElseGet(() -> LocalDateTimeUtil.dateToLocalDate(getOriginalShipTime()));
    }

    /**
     * 获取最终验货日期
     *
     * @return
     */
    public LocalDate getFinalInspectDate() {
        return Optional.ofNullable(getLatestInspectTime())
                .map(LocalDateTimeUtil::dateToLocalDate)
                .orElseGet(() -> LocalDateTimeUtil.dateToLocalDate(getOriginalInspectTime()));
    }

    /**
     * 可能为null 需要判断
     * @return
     */
    public LocalDate getPlanLoadDate(){
      Date date = Optional.ofNullable(this.getLatestLoadTime()).orElse(this.getOriginalLoadTime());
      return LocalDateTimeUtil.dateToLocalDate(date);
    }

    public LocalDate getOnLineDate() {
        return Optional.ofNullable(this.getOnlineTime())
                .map(LocalDateTimeUtil::dateToLocalDate)
                .orElse(null);
    }
}