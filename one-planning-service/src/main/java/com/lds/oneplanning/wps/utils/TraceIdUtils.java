package com.lds.oneplanning.wps.utils;

import cn.hutool.core.util.IdUtil;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

/**
 * <AUTHOR> rel="nofollow" href="mailto:<EMAIL>">daishaokun</a>
 * @apiNote 链路id工具类
 * @since 2023/1/5
 */
@UtilityClass
public class TraceIdUtils {
    public static final String TRACE_ID = "X-B3-TraceId";
    public static final String SPAN_ID = "X-B3-SpanId";
    public static final String PARENT_SPAN_ID = "X-B3-ParentSpanId";
    public static final String SAMPLED = "X-B3-Sampled";
    public static final String FLAGS = "X-B3-Flags";

    /**
     * 设置链路id
     *
     * @return {@link String}
     */
    public static String setTraceId() {
        String traceId = MDC.get(TRACE_ID);
        if (StringUtils.isEmpty(traceId)) {
            traceId = IdUtil.fastSimpleUUID().substring(16);
            MDC.put(TRACE_ID, traceId);
        }
        return traceId;
    }

    public static String setTraceId(String traceId) {
        if (StringUtils.isEmpty(traceId)) {
            traceId = IdUtil.fastSimpleUUID().substring(16);
        }
        MDC.put(TRACE_ID, traceId);
        return traceId;
    }

    /**
     * 设置链路id
     *
     * @return {@link String}
     */
    public static String newTraceId() {
        String traceId = IdUtil.fastSimpleUUID().substring(16);
        MDC.put(TRACE_ID, traceId);
        return traceId;
    }


    public static String getTraceId() {
        return MDC.get(TRACE_ID);
    }

    public static String setSpanId() {
        String spanId = IdUtil.fastSimpleUUID().substring(16);
        MDC.put(SPAN_ID, spanId);
        return spanId;
    }

    public static String setSpanId(String spanId) {
        if (StringUtils.isEmpty(spanId)) {
            spanId = IdUtil.fastSimpleUUID().substring(16);
        }
        MDC.put(SPAN_ID, spanId);
        return spanId;
    }

    public static void clearTraceId() {
        MDC.remove(TRACE_ID);
        MDC.remove(SPAN_ID);
    }

}
