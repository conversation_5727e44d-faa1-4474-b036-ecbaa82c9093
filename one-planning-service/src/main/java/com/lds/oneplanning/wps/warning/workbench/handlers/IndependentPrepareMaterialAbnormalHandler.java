package com.lds.oneplanning.wps.warning.workbench.handlers;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.lds.oneplanning.esb.datafetch.model.EsbFcData;
import com.lds.oneplanning.esb.datafetch.model.EsbPlanOrderComponent;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.entity.*;
import com.lds.oneplanning.wps.enums.*;
import com.lds.oneplanning.wps.event.WpsPlanWarningEvent;
import com.lds.oneplanning.wps.helper.UserApiHelper;
import com.lds.oneplanning.wps.helper.WpsRowDataMergeHelper;
import com.lds.oneplanning.wps.model.EsbPlanOrder;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.WarningIndependentPrepareMaterialAbnormalDetailService;
import com.lds.oneplanning.wps.service.WarningIndependentPrepareMaterialAbnormalService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.utils.DateUtils;
import com.lds.oneplanning.wps.utils.MockDataGenerator;
import com.lds.oneplanning.wps.utils.OrderNoUtils;
import com.lds.oneplanning.wps.utils.WpsDateUtil;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 独立备料SO
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class IndependentPrepareMaterialAbnormalHandler implements IWpsWorkbenchWarningHandler {
    @Value("${wps.mock.switch:0}")
    private Integer isDebug;
    private final WarningIndependentPrepareMaterialAbnormalService abnormalService;
    private final WarningIndependentPrepareMaterialAbnormalDetailService abnormalDetailService;
    private final IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    private final WarningTodoListService todoListService;
    private final EsbDataFetchService esbDataFetchService;

    private static final String FILTER_ORDER_TYPE = "计划订单";

    @Override
    public List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext ctx, Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap) {
        if (ctx == null || CollectionUtils.isEmpty(ctx.getOrders())) {
            return Collections.emptyList();
        }
        // 1.取值逻辑：WPS排产，订单类型为：计划订单，当前日期（以上线日期）+30天的所有计划订单
        List<WpsRowData> abnormalList = filterAbnormalData(ctx);
        log.info("{} 异常数量:{}", getWarningType(), abnormalList.size());
        if (CollectionUtils.isEmpty(abnormalList)) {
            //消警
            eliminateAlarms(Collections.emptyList());
            return Collections.emptyList();
        }
        log.info("{} 移除未排产数据后：异常数量:{}", getWarningType(), abnormalList.size());
        if (CollectionUtils.isEmpty(abnormalList)) {
            //消警
            eliminateAlarms(Collections.emptyList());
            return Collections.emptyList();
        }
        //合并相同订单的数据
        abnormalList = WpsRowDataMergeHelper.mergeSameOrder(abnormalList);
        //分析异常数据
        return analyzeAbnormalData(ctx, abnormalList);
    }

    /**
     * 过滤异常数据
     *
     * @param ctx 上下文对象，包含订单信息
     */
    private List<WpsRowData> filterAbnormalData(WpsWorkbenchWarningContext ctx) {
        List<WpsRowData> abnormalList = new ArrayList<>();
        //中国：gap = onlineTime - now <= 30d
        //泰国：gap = onlineTime - now <= 50d
        Date now = new Date();
        for (WpsRowData row : ctx.getOrders()) {
            if (!FILTER_ORDER_TYPE.equals(row.getOrderType())) {
                // 只处理计划订单
                continue;
            }
            //去掉没排产的数据
            Date onlineTime = row.getOnlineTime();
            if (null == onlineTime) {
                continue;
            }
            if (OrderNoUtils.isThailand(row.getOrderNo())) {
                if (DateUtil.betweenDay(onlineTime, now, true) <= 50) {
                    //泰国且gap <= 50天
                    abnormalList.add(row);
                }
            } else if (DateUtil.betweenDay(onlineTime, now, true) <= 30) {
                //中国且gap <= 30天
                abnormalList.add(row);
            }
        }
        return abnormalList;
    }

    /**
     * 分析异常数据并生成警告信息
     *
     * @param ctx          工作台警告上下文
     * @param abnormalList 异常数据列表
     * @return 包含警告信息的列表，如果未分析出异常则返回空列表
     */
    private List<WpsOrderPlanWarning> analyzeAbnormalData(WpsWorkbenchWarningContext ctx,
                                                          List<WpsRowData> abnormalList) {
        log.info("开始分析异常数据");
        // 分析异常数据
        List<WarningIndependentPrepareMaterialAbnormal> warningList = analyzeData(ctx, abnormalList);
        log.info("完成在库异常数据分析，警告列表大小: {}", warningList.size());
        if (CollectionUtils.isEmpty(warningList)) {
            eliminateAlarms(warningList);
            return Collections.emptyList();
        }
        fillAtpData(warningList);
        //消警
        log.info("开始消警");
        eliminateAlarms(warningList);

        //保存或更新
        log.info("开始保存异常数据");
        saveOrUpdate(ctx, warningList);
        log.info("完成保存异常数据，警告列表大小: {}", warningList.size());
        //创建MC数据
        createMcData(ctx.getFactoryCode(), ctx.getUserId(), warningList);
        log.info("开始创建待办");
        createTodoList(ctx.getUserId(), ctx.getFactoryCode(), warningList);
        return convertData(ctx, abnormalList, warningList);
    }

    private List<WpsOrderPlanWarning> convertData(WpsWorkbenchWarningContext ctx, List<WpsRowData> abnormalList, List<WarningIndependentPrepareMaterialAbnormal> warningList) {
        Map<String, LightColor> lightColorMap = warningList.stream()
                .collect(Collectors.toMap(WarningIndependentPrepareMaterialAbnormal::getOrderNumber, WarningIndependentPrepareMaterialAbnormal::getLightColor, (o, o2) -> o));
        return buildWarning(ctx, abnormalList, lightColorMap);
    }

    private void createMcData(String factoryCode, Long userId, List<WarningIndependentPrepareMaterialAbnormal> warningList) {
        if (CollUtil.isEmpty(warningList)) {
            return;
        }
        Set<String> planOrderNoList = warningList.stream()
                .map(WarningIndependentPrepareMaterialAbnormal::getOrderNumber)
                .collect(Collectors.toSet());
        List<EsbPlanOrderComponent> esbPlanOrderComponents = fetchPlanOrderComponent(factoryCode, planOrderNoList);
        if (CollUtil.isEmpty(esbPlanOrderComponents)) {
            return;
        }
        // <计划订单号, 物料信息>
        Map<String, List<EsbPlanOrderComponent>> planOrderComponentMap = esbPlanOrderComponents.stream()
                .collect(Collectors.groupingBy(EsbPlanOrderComponent::getPlanOrderNo));
        Set<String> materialNoSet = esbPlanOrderComponents.stream()
                .map(EsbPlanOrderComponent::getMaterialNo)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toSet());
        // <物料ID, 消耗信息>
        Map<String, EsbFcData> esbFcDataMap = fetchFcData(factoryCode, materialNoSet);
        // 组装数据
        List<WarningIndependentPrepareMaterialAbnormalDetail> details = Lists.newArrayList();
        // 迭代planOrderComponentMap
        planOrderComponentMap.forEach((planOrderNo, subPlanOrderComponentList) -> {
            subPlanOrderComponentList.forEach(subPlanOrderComponent -> {
                if (null == subPlanOrderComponent) {
                    return;
                }
                String materialNo = subPlanOrderComponent.getMaterialNo();
                if (StrUtil.isEmpty(materialNo)) {
                    return;
                }
                Integer componentRequirementQuantity = subPlanOrderComponent.getComponentRequirementQuantity();
                WarningIndependentPrepareMaterialAbnormalDetail detail = new WarningIndependentPrepareMaterialAbnormalDetail();
                detail.setOrderNumber(planOrderNo);
                detail.setMaterialId(materialNo);
                // TODO 外部接口需要补充物料描述字段
                detail.setMaterialDescription("");
                detail.setMaterialDemandQuantity(componentRequirementQuantity);
                // TODO 后面6个月数量可全部消耗完成才为是，否则为否
                detail.setMaterialIsConsumable(0);

                EsbFcData esbFcData = esbFcDataMap.get(materialNo);
                if (null != esbFcData) {
                    // 预计消耗月份（取值：1-6或者>6）
                    detail.setEstimatedConsumptionMonths(esbFcData.getEstimatedConsumptionMonths(componentRequirementQuantity));
                    detail.setConsumptionN1(esbFcData.getFutureQuantity01());
                    detail.setConsumptionN2(esbFcData.getFutureQuantity02());
                    detail.setConsumptionN3(esbFcData.getFutureQuantity03());
                    detail.setConsumptionN4(esbFcData.getFutureQuantity04());
                    detail.setConsumptionN5(esbFcData.getFutureQuantity05());
                    detail.setConsumptionN6(esbFcData.getFutureQuantity06());
                }
                detail.setCreatedBy(userId);
                detail.setCreatedAt(new Date());
                detail.setUpdatedBy(userId);
                detail.setUpdatedAt(new Date());
                details.add(detail);
            });
        });
        if (CollUtil.isNotEmpty(details)) {
            abnormalDetailService.saveBatch(details);
        }
    }

    private void fillAtpData(List<WarningIndependentPrepareMaterialAbnormal> warningList) {
        List<String> orderNumbers = warningList.stream()
                .map(WarningIndependentPrepareMaterialAbnormal::getOrderNumber)
                .distinct()
                .collect(Collectors.toList());
        List<EsbPlanOrder> planOrders = fetchPlanOrderData(orderNumbers);
        if (CollectionUtils.isEmpty(planOrders)) {
            return;
        }
        Map<String, EsbPlanOrder> planOrderMap = planOrders.stream()
                .collect(Collectors.toMap(EsbPlanOrder::getPlannedOrder, e -> e, (e1, e2) -> e1));
        Set<String> planOrderNoList = planOrderMap.keySet();
        log.info("接口查询到计划订单列表: {}", planOrderNoList);
        //找出没有查到的订单号
        Set<String> notFoundOrderNumbers = orderNumbers.stream()
                .filter(e -> !planOrderNoList.contains(e))
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(notFoundOrderNumbers)) {
            log.warn("接口查询不到以下订单号: {}", notFoundOrderNumbers);
        }
        //接口查询 LCP-GETPLANNEDORDER
        warningList.forEach(abnormal -> Optional.ofNullable(planOrderMap.get(abnormal.getOrderNumber()))
                .ifPresent(planOrder -> {
                    abnormal.setResponsiblePerson(planOrder.getApplicantName() + "(" + planOrder.getApplicantEmpNo() + ")");
                    // YJSAPZZSJ - expectedSapOfficialDate - initialEstimatedNormalizationDate
                    DateTime initialEstimatedNormalizationDate = DateUtil.parseDateTime(planOrder.getExpectedSapOfficialDate());
                    abnormal.setInitialEstimatedNormalizationDate(DateUtils.parseData(initialEstimatedNormalizationDate));

                    // YJDDJQ - expectedDeliveryDate - initialEstimatedShipmentDate
                    DateTime initialEstimatedShipmentDate = DateUtil.parseDateTime(planOrder.getExpectedDeliveryDate());
                    abnormal.setInitialEstimatedShipmentDate(DateUtils.parseData(initialEstimatedShipmentDate));
                    abnormal.setPlannedOrderNumber(planOrder.getRiskMaterialOrderNumber());
                    abnormal.setCategory(planOrder.getCategory());
                }));

    }

    private List<EsbPlanOrder> fetchPlanOrderData(List<String> orderNumbers) {
        if (Objects.equals(isDebug, 1)) {
            //调试，调用生产的接口查询
            return MockDataGenerator.fetchFromProd(orderNumbers, "/wps/warning/ipm/queryPlanOrder", EsbPlanOrder.class);
        }
        if (CollUtil.isEmpty(orderNumbers)) {
            return Collections.emptyList();
        }
        List<EsbPlanOrder> resultList = Lists.newArrayList();
        // 按100个分组
        List<List<String>> groupList = CollUtil.split(orderNumbers, 100);
        groupList.forEach(subOrderNoList -> {
            List<EsbPlanOrder> esbPlanOrderList = esbDataFetchService.fetchPlanOrder(subOrderNoList);
            if (CollUtil.isNotEmpty(esbPlanOrderList)) {
                resultList.addAll(esbPlanOrderList);
            }
        });
        return resultList;
    }

    private List<EsbPlanOrderComponent> fetchPlanOrderComponent(String factoryCode, Collection<String> planOrderNoList) {
        if (StrUtil.isEmpty(factoryCode) || CollectionUtils.isEmpty(planOrderNoList)) {
            return Collections.emptyList();
        }
        List<EsbPlanOrderComponent> resutlList = Lists.newArrayList();
        // 按100个分组
        List<List<String>> groupList = CollUtil.split(planOrderNoList, 100);
        groupList.forEach(subPlanOrderNoList -> {
            List<EsbPlanOrderComponent> params = Lists.newArrayList();
            subPlanOrderNoList.forEach(planOrderNo -> {
                EsbPlanOrderComponent param = new EsbPlanOrderComponent();
                param.setPlanOrderNo(planOrderNo);
                param.setFactory(factoryCode);
                params.add(param);
            });
            List<EsbPlanOrderComponent> esbPlanOrderComponents = esbDataFetchService.fetchPlanOrderComponent(params);
            if (CollUtil.isEmpty(esbPlanOrderComponents)) {
                resutlList.addAll(esbPlanOrderComponents);
            }
        });
        return resutlList;
    }

    private Map<String, EsbFcData> fetchFcData(String factoryCode, Collection<String> materialNoList) {
        if (StrUtil.isEmpty(factoryCode) || CollUtil.isEmpty(materialNoList)) {
            return Collections.emptyMap();
        }
        List<EsbFcData> resultList = Lists.newArrayList();
        String yearMonth = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM");
        // 按100个分组
        List<List<String>> groupList = CollUtil.split(materialNoList, 100);
        groupList.forEach(subMaterialNoList -> {
            List<EsbFcData> params = Lists.newArrayList();
            subMaterialNoList.forEach(materialNo -> {
                EsbFcData param = new EsbFcData();
                param.setFactoryCode(factoryCode);
                param.setMaterialNumber(materialNo);
                params.add(param);
            });
            List<EsbFcData> esbFcDataList = esbDataFetchService.fetchFcData(yearMonth, params);
            if (CollUtil.isEmpty(esbFcDataList)) {
                resultList.addAll(esbFcDataList);
            }
        });
        return resultList.stream()
                .collect(Collectors.toMap(EsbFcData::getMaterialNumber, e -> e, (e1, e2) -> e1));
    }

    /**
     * 创建待办事项列表
     *
     * @param userId      用户ID
     * @param factoryCode 工厂代码
     * @param warningList 包含警告信息的列表
     */
    private void createTodoList(Long userId, String factoryCode, List<WarningIndependentPrepareMaterialAbnormal> warningList) {
        /*
         * 待办需要分场景推送：
         *  - 红色：风险备库人员（责任人）主管
         *  - 黄色：风险备库人员（责任人）
         *  - MC待办：计划单是否取消=“是”，按工厂维度推送给MC物控（查询配置表）
         * **/

        String loginName = UserApiHelper.getUserLoginName(userId);

        List<WarningTodoList> todoList = warningList.stream()
                .map(e -> new WarningTodoList(getWarningType(), factoryCode, e.getId(), loginName))
                .collect(Collectors.toList());

        log.info("待办列表大小: {}", todoList.size());

        todoListService.saveData(getWarningType(), todoList);
    }

    private void saveOrUpdate(WpsWorkbenchWarningContext ctx, List<WarningIndependentPrepareMaterialAbnormal> warningList) {
        // 1. 提取订单号列表
        List<String> orderNoList = warningList.stream()
                .map(WarningIndependentPrepareMaterialAbnormal::getOrderNumber)
                .distinct()
                .collect(Collectors.toList());

        // 2. 查询数据库中已存在的记录
        List<WarningIndependentPrepareMaterialAbnormal> existList = getExistList(orderNoList);
        log.info("Found {} existing warning records for order numbers: {}", existList.size(), orderNoList);

        // 3. 构建已存在记录的映射表（订单号 -> 物料ID -> 实体）
        Map<String, WarningIndependentPrepareMaterialAbnormal> existTable = new HashMap<>();
        existList.forEach(warning -> existTable.put(warning.getOrderNumber(), warning));

        // 4. 初始化更新和插入列表
        List<WarningIndependentPrepareMaterialAbnormal> updateList = new ArrayList<>();
        List<WarningIndependentPrepareMaterialAbnormal> insertList = new ArrayList<>();

        // 5. 遍历warningList，区分更新和插入
        CopyOptions opt = CopyOptions.create().ignoreNullValue();
        warningList.forEach(warning -> {
            warning.setFactoryCode(ctx.getFactoryCode());
            warning.setUpdatedBy(ctx.getUserId());

            if (existTable.containsKey(warning.getOrderNumber())) {
                // 5.1 如果记录已存在，执行更新操作
                WarningIndependentPrepareMaterialAbnormal entity = existTable.get(warning.getOrderNumber());

                BeanUtil.copyProperties(warning, entity, opt);
                // 反向更新到warning中，以便外部能获取到id
                BeanUtil.copyProperties(entity, warning);
                updateList.add(warning);
            } else {
                warning.setCreatedBy(ctx.getUserId());
                // 5.2 如果记录不存在，添加到插入列表
                insertList.add(warning);
            }
        });

        // 6. 批量更新和插入
        if (CollectionUtils.isNotEmpty(updateList)) {
            log.info("Updating {} warning records", updateList.size());
            abnormalService.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("Inserting {} new warning records", insertList.size());
            abnormalService.saveBatch(insertList);
        }
    }

    private List<WarningIndependentPrepareMaterialAbnormal> getExistList(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return Collections.emptyList();
        }
        return abnormalService.lambdaQuery()
                .in(WarningIndependentPrepareMaterialAbnormal::getOrderNumber, orderNoList)
                .list();
    }

    /**
     * 消除告警，根据订单号来判断
     *
     * @param warningList 告警信息列表
     */
    private void eliminateAlarms(List<WarningIndependentPrepareMaterialAbnormal> warningList) {
        //获取所有待办的数据
        List<WarningIndependentPrepareMaterialAbnormal> existData = abnormalService.queryUnHandleData();
        log.info("获取未处理的警告数据计数: {}", existData.size());

        Set<String> newWarningOrderNumber = warningList.stream()
                .map(WarningIndependentPrepareMaterialAbnormal::getOrderNumber)
                .collect(Collectors.toSet());

        Set<Long> toBeRemoveIds = existData.stream()
                .filter(e -> !newWarningOrderNumber.contains(e.getOrderNumber()))
                .map(WarningIndependentPrepareMaterialAbnormal::getId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(toBeRemoveIds)) {
            log.info("消警列表：{}", toBeRemoveIds);
            todoListService.lambdaUpdate()
                    .set(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.CLOSED)
                    .in(WarningTodoList::getBizId, toBeRemoveIds)
                    .eq(WarningTodoList::getWarningType, this.getWarningType())
                    .update();
        }

        //主表消警
        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
    }

    private WpsOrderPlanWarning toWarning(String factoryCode, Long userId, WpsRowData order) {
        return buildWpsOrderPlanWarning(factoryCode, userId, order.getOrderNo(), order.getLineUuid(), order.getLineCode(),
                OrderWarningLevelEnum.WARNING, order.get_startProductPeriod());
    }


    /**
     * 分析异常数据，生成警告交付日期异常列表。
     *
     * @param ctx          上下文对象，包含警告相关的配置和信息
     * @param abnormalList 异常数据列表
     * @return 警告交付日期异常列表
     */
    private List<WarningIndependentPrepareMaterialAbnormal> analyzeData(WpsWorkbenchWarningContext ctx,
                                                                        List<WpsRowData> abnormalList) {

        //员工编号转成员工id
        //获取员工上级领导
        //获取工厂的MC人员列表
        //更新时需要累加次数（判断当前状态是不是关闭）

        log.info("开始分析异常数据, 排产场景:{}, 异常数据列表大小: {}", ctx.getProductType(), abnormalList.size());
        return abnormalList.stream()
                .map(row -> covertToAbnormalData(ctx, row))
                .collect(Collectors.toList());
    }

    private WarningIndependentPrepareMaterialAbnormal covertToAbnormalData(WpsWorkbenchWarningContext ctx, WpsRowData row) {
        WarningIndependentPrepareMaterialAbnormal abnormal = new WarningIndependentPrepareMaterialAbnormal();

        abnormal.setSalesOrderNumber(OrderNoUtils.trimPreZero(OrderNoUtils.getUnifyOrderNo(row)));
        abnormal.setLineNumber(OrderNoUtils.trimPreZero(row.getRowItem()));
        abnormal.setOrderNumber(row.getOrderNo());
        abnormal.setProductId(row.getCommodityId());
        abnormal.setMaterialDescription(row.getCommodityDesc());

        //计算gap天数
        Date now = new Date();
        Date onlineTime = row.getOnlineTime();
        long gap = DateUtil.betweenDay(onlineTime, now, true);
        Country country = OrderNoUtils.getCountry(row.getOrderNo());

        //排产第一天
        abnormal.setPlannedOnlineTime(WpsDateUtil.getStartScheduleDate(row.getScheduleDataMap()));

        abnormal.setLightColor(getLightColor(country, gap));
        abnormal.setFactoryCode(ctx.getFactoryCode());
        abnormal.setChangeCount(1);
        abnormal.setCountry(country);

        abnormal.setOrderQuantity(row.getOrderUnitQty());
        abnormal.setOriginalCompletionDate(DateUtils.parseData(row.getOriginalFinishTime()));

        //TODO 待确认
//        abnormal.setDelayedNormalizationDate();

        //TODO 根据子表计算
//        abnormal.setMaterialChangeOrCancellationCompleted();


        abnormal.setCreatedBy(ctx.getUserId());
        abnormal.setUpdatedBy(ctx.getUserId());


        return abnormal;
    }

    /**
     * 红色
     * 中国：gap<=14
     * 泰国：gap<=42
     * <p>
     * 黄色
     * 中国：14< gap <=21
     * 泰国：42< gap <=49
     * <p>
     * 其他
     * 中国：gap>21
     * 泰国：gap>49
     *
     * @param country 国家代码
     * @param gap     天数差
     * @return 灯色枚举值
     */
    private LightColor getLightColor(Country country, long gap) {
        switch (country) {
            case CN:
                if (gap <= 14) {
                    return LightColor.RED;
                } else if (gap <= 21) {
                    return LightColor.YELLOW;
                } else {
                    return LightColor.BLANK;
                }
            case TH:
                if (gap <= 42) {
                    return LightColor.RED;
                } else if (gap <= 49) {
                    return LightColor.YELLOW;
                } else {
                    return LightColor.BLANK;
                }
            default:
                throw new IllegalArgumentException("不支持的国家代码：" + country);
        }

    }


    @Override
    public WpsOrderWarningCategoryEnum getWarningCategory() {
        return WpsOrderWarningCategoryEnum.DEFAULT;
    }

    @Override
    public WpsOrderWarningTypeEnum getWarningType() {
        return WpsOrderWarningTypeEnum.INDEPENDENT_PREPARE_MATERIAL_ABNORMAL;
    }

    @EventListener
    public void handleCustomEvent(WpsPlanWarningEvent event) {
        //主表消警
        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
    }

}
