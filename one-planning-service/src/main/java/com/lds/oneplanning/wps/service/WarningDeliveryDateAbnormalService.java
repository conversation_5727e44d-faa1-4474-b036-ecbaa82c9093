package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WarningDeliveryDateAbnormal;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.req.DeliveryDateAbnormalReq;
import com.lds.oneplanning.wps.vo.DeliveryDateAbnormalVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【warning_delivery_date_abnormal(交期异常)】的数据库操作Service
* @createDate 2025-05-20 09:05:31
*/
public interface WarningDeliveryDateAbnormalService extends IService<WarningDeliveryDateAbnormal> {

    /**
     * 查询联合国处理数据
     *
     * @return {@link List }<{@link WarningDeliveryDateAbnormal }>
     */
    List<WarningDeliveryDateAbnormal> queryUnHandleData();

    /**
     * 查询页面
     *
     * @param source 来源
     * @param vo     vo
     * @return {@link Page }<{@link DeliveryDateAbnormalVO }>
     */
    Page<DeliveryDateAbnormalVO> queryPage(ViewSource source, DeliveryDateAbnormalReq vo);

    /**
     * 更新数据
     *
     * @param vo vo
     */
    void updateData(DeliveryDateAbnormalVO vo);
}
