package com.lds.oneplanning.wps.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransportDays {
    private List<Result> result;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Result {
        /**
         * 供应商编码
         */
        private String supplierCode;
        /**
         * 供应商名称
         */
        private String supplierName;
        /**
         * 采购组织
         */
        private String purchaseOrg;
        /**
         * 交货提前期
         */
        private String deliveryDays;
        /**
         * 运输天数
         */
        private String transportDays;
    }
}
