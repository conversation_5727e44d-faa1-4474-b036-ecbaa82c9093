package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.MesDailyPlanDetail;
import com.lds.oneplanning.wps.req.MesDailyPlanDetailReq;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-24
 */
public interface IMesDailyPlanDetailService extends IService<MesDailyPlanDetail> {

    void saveByPlanDateAndProductionLine(List<MesDailyPlanDetailReq> mesDailyPlanDetails);
}