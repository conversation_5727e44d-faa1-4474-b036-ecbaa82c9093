package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName wps_snapshot
 */
@Data
@TableName(value ="wps_snapshot")
@ApiModel(value="WpsSnapshot对象", description="")
public class WpsSnapshot implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 计划员工号
     */
    private String plannerEmpNo;

    /**
     * 线体编码
     */
    private String lineCode;

    private String lineUuid;

    /**
     * 计划数据
     */
    private String wpsData;

    /**
     * 版本
     */
    private String version;

    /**
     * 工厂编码
     */
    private String factoryCode;
    /**
     * 版本ID
     */
    private Long planVersionId;
    /**
     * 来源 1：保存  2：定时发布
     */
    private Integer source;


    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人id
     */
    private Long updateBy;

       /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}