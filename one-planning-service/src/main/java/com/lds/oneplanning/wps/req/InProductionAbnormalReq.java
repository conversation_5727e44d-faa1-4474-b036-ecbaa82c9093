package com.lds.oneplanning.wps.req;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2025/5/21
 */
@Data
public class InProductionAbnormalReq extends BasePageReq {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "排产开始日期")
    private String schedulingStartDate;

    @ApiModelProperty(value = "排产结束日期")
    private String schedulingEndDate;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "工厂编码，用，隔开")
    private String factoryCodes;

    @ApiModelProperty(value = "工厂编码列表")
    private Collection<String> factoryCodeList;

    @ApiModelProperty(value = "最小在制天数")
    private Integer minProductionDays;

    @ApiModelProperty(value = "最大在制天数")
    private Integer maxProductionDays;

    /**
     * 待办人，存登录名（为了LCP推送），搜索的时候使用UserContextUtils.getAccountName()获取当前登录人
     */
    @TableField(value = "assignee")
    private String assignee;

    @ApiModelProperty(value = "计划员工号")
    private String plannerEmpNo;

    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "处理状态")
    private OrderWarningHandleStatusEnum processStatus;

    @ApiModelProperty(value = "是否产生在制异常")
    private Integer inProductionException;

    @ApiModelProperty(value = "是否完结")
    private Integer isCompleted;

    @ApiModelProperty(value = "报工状态 0未完成 1已完成")
    private Integer reportStatus;

    @ApiModelProperty(value = "排产场景（0:整机，1:组件，2：部件）")
    private Integer planType;


}
