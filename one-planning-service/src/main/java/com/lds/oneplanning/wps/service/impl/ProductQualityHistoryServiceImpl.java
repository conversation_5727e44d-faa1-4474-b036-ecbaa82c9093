package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.esb.datafetch.model.EsbAbnormalVO;
import com.lds.oneplanning.wps.entity.ProductQualityHistory;
import com.lds.oneplanning.wps.mapper.ProductQualityHistoryMapper;
import com.lds.oneplanning.wps.service.IProductQualityHistoryService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Service
public class ProductQualityHistoryServiceImpl  extends ServiceImpl<ProductQualityHistoryMapper, ProductQualityHistory> implements IProductQualityHistoryService {
    @Override
    public void saveOrUpdate(List<EsbAbnormalVO> esbAbnormalVOList) {
        List<String> bhList = esbAbnormalVOList.stream().map(EsbAbnormalVO::getBh).collect(Collectors.toList());
        List<String> existBhList = baseMapper.selectList(Wrappers.lambdaQuery(ProductQualityHistory.class)
                        .in(ProductQualityHistory::getBh, bhList))
                .stream().map(ProductQualityHistory::getBh).collect(Collectors.toList());
        List<EsbAbnormalVO> insertList = esbAbnormalVOList.stream()
                .filter(esbAbnormalVO -> !existBhList.contains(esbAbnormalVO.getBh()))
                .collect(Collectors.toList());
        List<ProductQualityHistory> productQualityHistories = BeanUtil.copyToList(insertList, ProductQualityHistory.class);
        saveBatch(productQualityHistories);
    }
}
