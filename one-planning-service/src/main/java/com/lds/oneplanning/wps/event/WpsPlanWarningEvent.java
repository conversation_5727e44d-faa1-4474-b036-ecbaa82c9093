package com.lds.oneplanning.wps.event;

import com.lds.oneplanning.wps.req.WpsPlanWarningMQReq;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class WpsPlanWarningEvent extends ApplicationEvent {
    private WpsPlanWarningMQReq message;

    public WpsPlanWarningEvent(Object source) {
        super(source);
    }

    public WpsPlanWarningEvent(Object source, WpsPlanWarningMQReq message) {
        super(source);
        this.message = message;
    }
}
