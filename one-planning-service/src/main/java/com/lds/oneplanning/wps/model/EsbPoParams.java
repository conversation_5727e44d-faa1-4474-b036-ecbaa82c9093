package com.lds.oneplanning.wps.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EsbPoParams {

    @JSONField(name = "IT_DATA")
    @JsonProperty("IT_DATA")
    private List<PoInfo> poInfoList;
    @JSONField(name = "IT_DATA2")
    @JsonProperty("IT_DATA2")
    private List<MaterialInfo> materialInfoList;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PoInfo {
        /**
         * 采购订单号
         */
        @JSONField(name = "EBELN")
        @JsonProperty("EBELN")
        private String poNo;
        /**
         * 采购订单行项目
         */
        @JSONField(name = "EBELP")
        @JsonProperty("EBELP")
        private String poRowItem;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MaterialInfo {
        /**
         * 物料id
         */
        @JSONField(name = "MATNR")
        @JsonProperty("MATNR")
        private String materialId;
        /**
         * 工厂编码
         */
        @JSONField(name = "WERKS")
        @JsonProperty("WERKS")
        private String factoryCode;
    }

    public void addMaterialInfo(String materialId, String factoryCode) {
        if (materialInfoList == null) {
            materialInfoList = Lists.newArrayList();
        }
        materialInfoList.add(new MaterialInfo(materialId, factoryCode));
    }
}
