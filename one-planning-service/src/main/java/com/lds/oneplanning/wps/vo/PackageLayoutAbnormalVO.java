package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 包材版面异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13
 */
@Data
@ApiModel(value = "包材版面异常")
@TableHeader(type = WpsOrderWarningTypeEnum.PACKAGE_LAYOUT_ABNORMAL, source = ViewSource.DEFAULT)
public class PackageLayoutAbnormalVO {
    @ApiModelProperty(value = "销售订单号")
    private String salesOrderNumber;

    @ApiModelProperty(value = "行项目")
    private Integer lineItem;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "生产订单号")
    private String orderNumber;

    @ApiModelProperty(value = "商品ID")
    private String productId;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    @ApiModelProperty(value = "计划上线时间")
    private Date plannedOnlineTime;

    @ApiModelProperty(value = "距离计划上线时间剩余天数")
    private Integer daysRemainingBeforeOnline;

    @ApiModelProperty(value = "灯色")
    private String lightColor;

    @ApiModelProperty(value = "订单数量")
    private Integer orderQuantity;

    @ApiModelProperty(value = "是否维护版面")
    private Boolean isMaintainLayout;

    @ApiModelProperty(value = "是否触发代办到责任人")
    private Boolean isTriggerTodoToResponsiblePerson;
}
