package com.lds.oneplanning.wps.workbench.resp;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class AptExceptionOrderResp implements Serializable {

    private static final long serialVersionUID = -568155157606668111L;

    /**
     * 客户
     */
    private String customer;

    /**
     * 销售订单号
     */
    private String saleOrderNo;

    /**
     * 行项目号
     */
    private String lineItemNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 物料简称
     */
    private String materialName;

    /**
     * 物料需求时间
     */
    private LocalDate materialDemandTime;

    /**
     * 物料需求数量
     */
    private Integer materialDemandQuantity;

    /**
     * 供应商简称
     */
    private String supplierName;

    /**
     * 采购组
     */
    private String purchaseGroup;

    /**
     * PO交期
     */
    private LocalDate poDueDate;

    /**
     * 处理状态
     */
    private Integer processStatus;
}