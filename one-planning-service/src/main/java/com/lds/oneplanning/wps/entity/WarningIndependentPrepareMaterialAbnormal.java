package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lds.oneplanning.wps.enums.Country;
import com.lds.oneplanning.wps.enums.LightColor;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * 独立备料SO未转正
 * @TableName warning_independent_prepare_material_abnormal
 */
@TableName(value ="warning_independent_prepare_material_abnormal")
@Data
public class WarningIndependentPrepareMaterialAbnormal {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 计划单号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 国家
     */
    @TableField(value = "country")
    private Country country;

    /**
     * 责任人
     * 接口：LCP-GETPLANNEDORDER-CODE+SQR
     */
    @TableField(value = "responsible_person")
    private String responsiblePerson;

    @TableField(value = "responsible_person_id")
    private String responsiblePersonId;

    /**
     * 初次预计转正日期
     * 接口：LCP-GETPLANNEDORDER-YJSAPZZSJ
     */
    @TableField(value = "initial_estimated_normalization_date")
    private LocalDate initialEstimatedNormalizationDate;

    /**
     * 初次预计出货日期
     * 接口：LCP-GETPLANNEDORDER-YJDDJQ
     */
    @TableField(value = "initial_estimated_shipment_date")
    private LocalDate initialEstimatedShipmentDate;

    /**
     * 整灯风险备料单号
     * 接口：LCP-GETPLANNEDORDER-FXBKSQ
     */
    @TableField(value = "planned_order_number")
    private String plannedOrderNumber;

    /**
     * 工厂
     */
    @TableField(value = "factory_code")
    private String factoryCode;

    /**
     * 商品ID
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 物料描述
     */
    @TableField(value = "material_description")
    private String materialDescription;

    /**
     * 订单数量
     */
    @TableField(value = "order_quantity")
    private Integer orderQuantity;

    /**
     * 上线日期
     */
    @TableField(value = "planned_online_time")
    private LocalDate plannedOnlineTime;

    /**
     * 原始完工
     */
    @TableField(value = "original_completion_date")
    private LocalDate originalCompletionDate;

    /**
     * 延迟后转正
     */
    @TableField(value = "delayed_normalization_date")
    private LocalDate delayedNormalizationDate;

    /**
     * 物料是否可消耗
     */
    @TableField(value = "material_change_or_cancellation_completed")
    private Integer materialChangeOrCancellationCompleted;

    /**
     * 最新预计转正日期
     */
    @TableField(value = "latest_estimated_normalization_date")
    private LocalDate latestEstimatedNormalizationDate;

    /**
     * 最新预计出货日期
     */
    @TableField(value = "latest_estimated_shipment_date")
    private LocalDate latestEstimatedShipmentDate;

    /**
     * 已变更次数
     */
    @TableField(value = "change_count")
    private Integer changeCount;

    /**
     * 灯色
     */
    @TableField(value = "light_color")
    private LightColor lightColor;

    /**
     * 类别
     * 接口：LCP-GETPLANNEDORDER-BLLB
     */
    @TableField(value = "category")
    private String category;

    /**
     * 销售订单-行项目
     */
    @TableField(value = "sales_order_number")
    private String salesOrderNumber;

    /**
     * 采购订单-行项目
     */
    @TableField(value = "purchase_order_number")
    private String purchaseOrderNumber;

    /**
     * 行号
     */
    @TableField(value = "line_number")
    private String lineNumber;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private Long updatedBy;

    /**
     * 
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     * 
     */
    @TableField(value = "updated_at")
    private Date updatedAt;

    /**
     * 最新告警日期
     */
    @TableField(value = "latest_warning_date")
    private Date latestWarningDate;
}