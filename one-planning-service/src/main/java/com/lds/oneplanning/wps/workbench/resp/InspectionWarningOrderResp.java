package com.lds.oneplanning.wps.workbench.resp;

import lombok.Data;

import java.time.LocalDate;

@Data
public class InspectionWarningOrderResp {

    private static final long serialVersionUID = -2898188268347669952L;

    /**
     * 客户
     */
    private String customer;

    /**
     * 工厂
     */
    private String factory;

    /**
     * 销售订单号
     */
    private String saleOrderNo;

    /**
     * 行项目号
     */
    private String lineItemNo;

    /**
     * 生产车间
     */
    private String workshop;

    /**
     * 线体
     */
    private String line;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 入库数量
     */
    private Integer inStockQuantity;

    /**
     * 最终验货日期
     */
    private LocalDate finalInspectionDate;

    /**
     * 处理状态
     */
    private Integer processStatus;
}
