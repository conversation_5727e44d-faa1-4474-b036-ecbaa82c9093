package com.lds.oneplanning.wps.job;

import com.lds.coral.job.annotation.JobRegister;
import com.lds.oneplanning.common.utils.ThreadPoolUtil;
import com.lds.oneplanning.mps.service.IMpsFormInfoService;
import com.lds.oneplanning.wps.service.IWpsFormInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * 每天8点执行一次
 *
 * <AUTHOR>
 * @Date 2021/6/10 11:21
 */
@JobRegister(value = "SynReportedQtyDayHandler", jobName = "SynReportedQtyDayJob", cron = "0 0 8 * * ? *")
@Component
@Slf4j
public class SynReportedQtyDayHandler extends IJobHandler {
    @Resource
    private IWpsFormInfoService wpsFormInfoService;

    @Value("${ehr.locationId:516}")
    private Long ehrLocationId;
    @Override
    public ReturnT<String> execute(String s){
        ReturnT<String> returnInfo = SUCCESS;
        try {
            XxlJobLogger.log("SynReportedQtyDayHandler xxl-job start");
            log.info("SynReportedQtyDayHandler execute start time={}", LocalDateTime.now());
            ScheduledThreadPoolExecutor executor = ThreadPoolUtil.instance();
            executor.execute(() -> wpsFormInfoService.synReportedQtyData());
            log.info("SynReportedQtyDayHandler execute finish time={}", LocalDateTime.now());

        } catch (Exception e) {
            log.error("SynReportedQtyDayHandler 执行失败 -->{}",e.getMessage(),e);
            returnInfo = ReturnT.FAIL;
        }
        XxlJobLogger.log("=========================  SynReportedQtyDayHandler job execute end ======================");
        return returnInfo;
    }

}
