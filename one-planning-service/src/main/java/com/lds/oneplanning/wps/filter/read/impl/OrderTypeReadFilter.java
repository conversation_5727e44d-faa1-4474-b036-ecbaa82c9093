package com.lds.oneplanning.wps.filter.read.impl;

import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.enums.OrderTypeEnum;
import com.lds.oneplanning.basedata.service.IFactoryOrderCfgService;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.basedata.service.IPlannerOrderCfgService;
import com.lds.oneplanning.wps.filter.read.AbstractWpsOrderReadFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * @Description:
 * @Author: zhuang<PERSON>ayin
 * @Email: zhuang<PERSON><PERSON><EMAIL>
 * @Date: 2025/3/25 17:16
 */
@Slf4j
@Service
public class OrderTypeReadFilter extends AbstractWpsOrderReadFilter {
    @Resource
    private IPlannerOrderCfgService plannerOrderCfgService;
    @Resource
    private IFactoryOrderCfgService factoryOrderCfgService;
    @Resource
    private IPlannerBaseService plannerBaseService;
    @Override
    public Integer filterSeq() {
        return 1;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag) {
        String empNo = plannerBaseService.getEmpNoByUserId(userId);
        // 1、计划员订单配置优先
        Set<String> orderTypeCodes = plannerOrderCfgService.listOrderSubTypeByEmpNo(empNo, BaseDataConstant.BUSINESS_TYPE_ORDER);
        if (CollectionUtils.isEmpty(orderTypeCodes)) {
            //  2、若无计划员特殊订单配置，使用工厂特殊订单配置
            orderTypeCodes = factoryOrderCfgService.listOrderSubTypeByFactoryCode(factoryCode, BaseDataConstant.BUSINESS_TYPE_ORDER);
        }
        if (orderTypeCodes.isEmpty()) {
            //3、 若都没有配置，则销售 计划和订单 默认添加
            orderTypeCodes.addAll(Sets.newHashSet(OrderTypeEnum.SELL.getCode(),OrderTypeEnum.PLAN.getCode(),OrderTypeEnum.PROD.getCode()));
        }
        Set<String> orderTypeNames = Sets.newLinkedHashSet();
        orderTypeCodes.stream().forEach(code -> orderTypeNames.add(OrderTypeEnum.getNameByCode(code)));
        // 订单类型过滤 （默认支持销售订单 计划订单 生产订单，采购订单和采购申请需要特殊配置才支持）
        dirtyList.removeIf(wpsRowData -> !orderTypeNames.contains(wpsRowData.getOrderType()));
   /*     List<WpsRowData> threadSafeList = new CopyOnWriteArrayList<>(dirtyList);
        threadSafeList.removeIf(wpsRowData -> !orderTypeNames.contains(wpsRowData.getOrderType()));*/
        return dirtyList;
    }

}
