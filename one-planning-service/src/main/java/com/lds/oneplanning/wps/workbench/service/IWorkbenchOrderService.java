package com.lds.oneplanning.wps.workbench.service;

import com.lds.oneplanning.wps.workbench.req.HandleWarningOrderReq;
import com.lds.oneplanning.wps.workbench.req.WorkbenchOrderWarningReq;
import com.lds.oneplanning.wps.workbench.resp.*;

import java.util.List;

public interface IWorkbenchOrderService {

    List<OrderStatusResp> listOrderStatus(Long userId);

    List<ProductionRatioResp> productionRatio(Long userId);

    List<UnMatchOrderResp> listUnMatchOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq);

    List<UnStoreOrderResp> listUnStoreWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq);

    List<InspectionWarningOrderResp> listInspectionWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq);

    List<PackingNoPrintResp> listUnPackingPrintWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq);

    List<AptExceptionOrderResp> listAtpExceptionOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq);
    List<BookingUrgentResp> listBookingUrgentWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq);

    void handleWarningOrder(Long userId, HandleWarningOrderReq handleWarningOrderReq);

    List<SectionPayloadWarningOrderResp> listSectionPayloadWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq);
}