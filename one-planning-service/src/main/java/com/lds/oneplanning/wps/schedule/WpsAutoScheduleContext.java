package com.lds.oneplanning.wps.schedule;

import com.google.common.collect.Maps;
import com.lds.oneplanning.mps.date.YearWeekMap;
import com.lds.oneplanning.wps.entity.WpsSchedulePlanLog;
import com.lds.oneplanning.wps.model.WpsDateRange;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.*;

@Data
@NoArgsConstructor
public class WpsAutoScheduleContext implements Serializable {

    private static final long serialVersionUID = 4209152096432085847L;

    /**
     * 当前工厂编码
     */
    private String currentFactoryCode;

    private Long userId;

    private LocalDate currentDate;

    private YearWeekMap yearWeekMap;

    /**
     * 开始排产日期
     */
    private Date startScheduleDate;

    /**
     * 截止排产日期
     */
    private Date endScheduleDate;

    private int weeksToPush;

    /**
     * 订单数据
     */
    private List<WpsRowData> orderList;

    /**
     * 客户拥有的产线UUID列表
     */
    private List<String> lineUuids;

    /**
     * 虚拟线体UUID
     */
    private String virtualLineUuid;

    /**
     * 是否自动排产
     */
    private boolean handleAutoSchedule;

    /**
     * 需要排产的产品ID列表
     */
    private List<String> productIds;

    /**
     * 需要排产的商品ID列表
     */
    private List<String> commodityIds;

    /**
     * 生产线预排产数据
     * key: 产线UUID
     * value: <日期，排产数据>
     */
    private Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap;

    /**
     * 订单->生产线UUID->uph映射
     */
    private Map<String, Map<String, Float>> orderLineUphMap;

    /**
     * Map<客戶编码,Map<优先级，Set<产线UUID>>
     */
    private Map<String, Map<Integer, Set<String>>> customerLinePriorityMap;

    /**
     * Map<客戶编码,Map<产品组编码,Map<优先级，Set<产线UUID>>>
     */
    private Map<String, Map<String, Map<Integer, Set<String>>>> customerProductGroupPriorityMap;

    /**
     * Map<产品组编码,Map<优先级，Set<产线UUID>>
     */
    private Map<String, Map<Integer, Set<String>>> productGroupPriorityMap;

    /**
     * Map<订单编码,Map<优先级，Set<产线UUID>>>
     */
    private Map<String,Map<Integer,Set<String>>> orderLinePriorityMap;

    /**
     * 排产日期列表
     */
    private List<LocalDate> scheduleDates;

    /**
     * 产品容量预警列表(单元格)
     * key: 订单编码+线体UUID
     * value: 日期->容量预警颜色
     */
    private Map<String, Map<LocalDate, Integer>> warningCellColorMap = Maps.newHashMap();

    /**
     * 产品容量预警列表(行)
     * key: 订单编码+线体UUID
     * value: 容量预警颜色
     */
    private Map<String, Integer> warningRowColorMap = Maps.newHashMap();

    /**
     * 日排产数据(内存变量)
     * key: orderNo
     * value: 日期->产线UUID->日排产数据
     */
    private Map<String, Map<LocalDate, Map<String, Integer>>> orderDailyScheduleDataMap = Maps.newHashMap();

    /**
     * 日预排产数据(内存变量)
     * key: orderNo
     * value: 产线UUID->日期->日预排产数据
     */
    private Map<String, Map<String, Map<LocalDate, Integer>>> orderDailyPrePlanQuantityMap = Maps.newHashMap();

    /**
     * 已发布订单日期范围Map
     */
    private Map<String, List<WpsDateRange>> publishOrderDateRangeMap = Maps.newHashMap();
    /**
     * 最近7天的订单编号
     */
    private List<String> sevenOrderList = Lists.newArrayList();
    /**
     * 是否启动新排产计划
     */
    private Boolean isNewSchedule = false;
    /**
     * 线体换产品时长
     */
    private float changeLineHour=0f;

    /**
     * 线体生产的产品组编码 KEY：线体ID，VALUE：日期->产品组编码
     */
    private Map<String,Map<LocalDate,String>> lineProductGroupMap = Maps.newHashMap();
    /**
     *自动排产的错误日志
     */
    private Map<String,WpsSchedulePlanLog> schedulePlanLogMap = Maps.newHashMap();
    /**
     * 是否保存排产计划日志
     */
    private Boolean isSaveSchedulePlanLog=false;
    /**
     * 线体编码 KEY：线体ID，VALUE：线体编码
     */
    private Map<String,String> lineCodeMap = Maps.newHashMap();

    public WpsAutoScheduleContext(List<WpsRowData> rowDataList) {
        this.orderList = rowDataList;
        this.dailyProductionLineMap = Maps.newHashMap();
        this.scheduleDates = Lists.newArrayList();
        this.productGroupPriorityMap = Maps.newHashMap();
        this.customerLinePriorityMap = Maps.newHashMap();
        this.customerProductGroupPriorityMap = Maps.newHashMap();
        this.orderLinePriorityMap = Maps.newHashMap();
        this.orderLineUphMap = Maps.newHashMap();
    }
}