package com.lds.oneplanning.wps.utils;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalVO;
import lombok.experimental.UtilityClass;

@UtilityClass
public class PageHelper {

    public static <T> Page<T> cover(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        if (page == null) {
            return new Page<>();
        }
        Page<T> result = new Page<>();
        result.setResult(page.getRecords());
        result.setPages((int) page.getPages());
        result.setPageNum((int) page.getPages());
        result.setPageSize((int) page.getSize());
        result.setTotal(page.getTotal());

        return result;
    }
}
