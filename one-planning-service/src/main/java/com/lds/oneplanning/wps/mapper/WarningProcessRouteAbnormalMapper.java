package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningProcessRouteAbnormal;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lds.oneplanning.wps.vo.WarningIncomeMaterialAtpAbnormalParams;
import com.lds.oneplanning.wps.vo.WarningIncomeMaterialAtpAbnormalVO2;
import com.lds.oneplanning.wps.vo.WarningProcessRouteAbnormalVO;
import com.lds.oneplanning.wps.vo.WarningProcessRouteParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-06-04
 */
public interface WarningProcessRouteAbnormalMapper extends BaseMapper<WarningProcessRouteAbnormal> {
    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningMaterialAtpAbnormal }>
     */
    List<WarningProcessRouteAbnormal> queryUnHandleData();

    IPage<WarningProcessRouteAbnormalVO> selectPage(Page<WarningProcessRouteAbnormalVO> page, @Param("params") WarningProcessRouteParams params);

}
