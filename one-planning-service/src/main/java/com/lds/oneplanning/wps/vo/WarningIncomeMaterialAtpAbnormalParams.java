package com.lds.oneplanning.wps.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.req.BasePageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
@Data
public class WarningIncomeMaterialAtpAbnormalParams extends BasePageReq {

    // PC查询条件
    @ApiModelProperty(value = "计划角色：PC,IQC,PO")
    private ViewSource  viewRole;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "工厂")
    private String factory;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "订单id（商品id）")
    private String productId;

    @ApiModelProperty(value = "计划日期-开始")
    private String startPlanDate;

    @ApiModelProperty(value = "计划日期-结束")
    private String endPlanDate;

    // IQC PO查询条件
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "采购合同")
    private String purchaseContract;

    @ApiModelProperty(value = "物料id")
    private String materialId;

    @ApiModelProperty(value = "处理结果")
    private String dealResult;

    @ApiModelProperty(value = "工号")
    private String gh;
    private String processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

}
