package com.lds.oneplanning.wps.filter.write.impl;

import com.lds.oneplanning.wps.entity.WpsFormInfo;
import com.lds.oneplanning.wps.filter.write.AbstractWpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsFormInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: formInfo 回填
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/26 15:27
 */
@Slf4j
@Service
public class FormInfoWriteFilter extends AbstractWpsOrderWriteFilter {

    @Resource
    private IWpsFormInfoService wpsFormInfoService;
    @Override
    public Integer filterSeq() {
        return 1;
    }

    @Override
    protected List<WpsRowData> doFilter(Long userId, Integer datasource, String factoryCode, List<WpsRowData> wpsRowDatas,boolean cacheFlag, Map<String,Object> params) {
        List<String> bizIds = wpsRowDatas.stream().map(WpsRowData::getOrderNo).collect(Collectors.toList());
        Map<String, WpsFormInfo> fromMaps = wpsFormInfoService.listByBizIds(bizIds).stream().collect(Collectors.toMap(WpsFormInfo::getBizId, fromInfo -> fromInfo,(t, t2) -> t2));
        for (WpsRowData rowData :wpsRowDatas){
            String bizId = rowData.getOrderNo();
            WpsFormInfo formInfo = fromMaps.get(bizId);
            if (formInfo !=null) {
                rowData.setOriginalReplyTime(formInfo.getOldReplyCompletionDate());
                rowData.setLatestReplyTime(formInfo.getNewReplyCompletionDate());
                rowData.setRemark1(formInfo.getRemarkOne());
                rowData.setRemark2(formInfo.getRemarkTwo());
                rowData.setFinalAssembly(formInfo.getPackagingOrFinalAssembly());
                rowData.setRiskMaterialRemark(formInfo.getRemarkHazardousInfo());
                rowData.setCustomerSeq(formInfo.getGuestSeq());
            }

            Integer reportQty = formInfo !=null ? formInfo.getReportQty() : 0;
            Integer scheduleQty;
            if ("销售订单".equals(rowData.getOrderType())) {
                Integer storedPcsQty = Optional.ofNullable(rowData.getTransQty()).orElse(0)
                        *  Optional.ofNullable(rowData.getStockedPcsQty()).orElse(0);
                scheduleQty = rowData.getOrderPcsQty() - storedPcsQty;
            } else {
                scheduleQty = rowData.getOrderPcsQty() - Optional.ofNullable(rowData.getReportedPcsQty()).orElse(0);
            }
            rowData.setReportedPcsQty(reportQty);
            rowData.setSchedulePcsQty(scheduleQty);
        }
        return wpsRowDatas;
    }

}
