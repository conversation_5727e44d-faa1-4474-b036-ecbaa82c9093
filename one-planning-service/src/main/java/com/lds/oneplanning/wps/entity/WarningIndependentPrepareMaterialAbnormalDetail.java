package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 独立备料SO未转正（MC）
 * @TableName warning_independent_prepare_material_abnormal_detail
 */
@TableName(value ="warning_independent_prepare_material_abnormal_detail")
@Data
public class WarningIndependentPrepareMaterialAbnormalDetail {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 计划单号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * BOM物料ID
     */
    @TableField(value = "material_id")
    private String materialId;

    /**
     * 物料描述
     */
    @TableField(value = "material_description")
    private String materialDescription;

    /**
     * 数量
     */
    @TableField(value = "material_demand_quantity")
    private Integer materialDemandQuantity;

    /**
     * 物料是否可消耗
     */
    @TableField(value = "material_is_consumable")
    private Integer materialIsConsumable;

    /**
     * 工厂
     */
    @TableField(value = "factory_code")
    private String factoryCode;

    /**
     * 预计消耗月份（取值：1-6或者>6）
     */
    @TableField(value = "estimated_consumption_months")
    private String estimatedConsumptionMonths;

    /**
     * 呆滞风险等级
     */
    @TableField(value = "stagnant_risk_level")
    private String stagnantRiskLevel;

    /**
     * N-1
     */
    @TableField(value = "consumption_n1")
    private Integer consumptionN1;

    /**
     * N-2
     */
    @TableField(value = "consumption_n2")
    private Integer consumptionN2;

    /**
     * N-3
     */
    @TableField(value = "consumption_n3")
    private Integer consumptionN3;

    /**
     * N-4
     */
    @TableField(value = "consumption_n4")
    private Integer consumptionN4;

    /**
     * N-5
     */
    @TableField(value = "consumption_n5")
    private Integer consumptionN5;

    /**
     * N-6
     */
    @TableField(value = "consumption_n6")
    private Integer consumptionN6;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private Long updatedBy;

    /**
     * 
     */
    @TableField(value = "created_at")
    private Date createdAt;

    /**
     * 
     */
    @TableField(value = "updated_at")
    private Date updatedAt;
}