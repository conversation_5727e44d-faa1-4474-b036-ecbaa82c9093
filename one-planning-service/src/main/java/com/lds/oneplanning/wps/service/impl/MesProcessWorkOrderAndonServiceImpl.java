package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lds.oneplanning.wps.entity.MesProcessWorkOrderAndon;
import com.lds.oneplanning.wps.mapper.MesProcessWorkOrderAndonMapper;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderAndonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
@Service
public class MesProcessWorkOrderAndonServiceImpl extends ServiceImpl<MesProcessWorkOrderAndonMapper, MesProcessWorkOrderAndon> implements IMesProcessWorkOrderAndonService {

    @Override
    public Map<String, List<MesProcessWorkOrderAndon>> findMapByWorkOrderNumber(List<String> workOrderNumberList) {
        return baseMapper.selectList(Wrappers.lambdaQuery(MesProcessWorkOrderAndon.class)
        .in(MesProcessWorkOrderAndon::getWorkOrderNo, workOrderNumberList)
                .orderByAsc(MesProcessWorkOrderAndon::getSortNo,  MesProcessWorkOrderAndon::getId))
                .stream().collect(Collectors.groupingBy(MesProcessWorkOrderAndon::getWorkOrderNo));
    }

    @Override
    public void removeByWorkOrderNoList(List<String> workOrderNoList) {
        baseMapper.delete(new QueryWrapper<MesProcessWorkOrderAndon>().lambda()
                .in(MesProcessWorkOrderAndon::getWorkOrderNo, workOrderNoList));
    }
}
