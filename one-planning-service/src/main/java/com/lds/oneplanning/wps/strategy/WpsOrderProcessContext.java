package com.lds.oneplanning.wps.strategy;

import cn.hutool.core.date.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.iot.common.util.SpringUtil;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.filter.read.WpsOrderReadFilter;
import com.lds.oneplanning.wps.filter.write.WpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuang<PERSON>ayin
 * @Email: <EMAIL>
 * @Date: 2025/4/7 9:58
 */
@Component
public class WpsOrderProcessContext {

    @Resource
    private IEsbDataFetchService dataFetchService;

    private WpsOrderProcessStrategy getHandler(WpsOrderProcessEnum processEnum){
        return SpringUtil.getBean(processEnum.getCode(),WpsOrderProcessStrategy.class);
    }
    public List<WpsRowData> process(WpsOrderProcessEnum processEnum, Long userId, String factoryCode,boolean cacheFlag, Map<String,Object> params){
      return this.process(processEnum, userId, null,null,factoryCode,cacheFlag,params);
    }
    public List<WpsRowData> process(WpsOrderProcessEnum processEnum, Long userId, LocalDate startDate, LocalDate endDate, String factoryCode,boolean cacheFlag, Map<String,Object> params){
        startDate = startDate ==null ? LocalDate.of(LocalDate.now().getYear(),1,1) : startDate;
        endDate = endDate == null ? LocalDate.now() : endDate;

        List<WpsRowData> dirtyList = dataFetchService.fetchWpsOrderList(startDate, endDate, Lists.newArrayList(factoryCode)); ;
      /*  if (cacheFlag) {
            dirtyList= dataFetchService.fetchWpsOrdersWitchCache(startDate, endDate, factoryCode);
        }else{
            // 指定查询接口
            dirtyList = dataFetchService.fetchWpsOrderList(startDate, endDate, Lists.newArrayList(factoryCode));
            // 清理一级缓存
            WpsOrderCacheUtils.cleanOrdersCacheLevel1(factoryCode, startDate, endDate);
            //清理二级缓存
            WpsOrderCacheUtils.cleanOrderKeysByFactory(factoryCode);
        }*/

        return  this.getHandler(processEnum).process(userId, factoryCode, dirtyList,cacheFlag,params);
    }

    /**
     * 查询工厂未来futureDays天计划订单
     * @param factoryCode
     * @param orderTypeList
     * @param futureDays
     * @return
     */

    public List<WpsRowData> queryFuturePlanOrders(String factoryCode, List<String> orderTypeList, int futureDays){
        LocalDate startDate = LocalDate.parse("2025-01-01");
        LocalDate endDate = LocalDate.now();
        List<WpsRowData> dirtyList = dataFetchService.fetchWpsOrderList(startDate, endDate, Lists.newArrayList(factoryCode));
        // wpsRowData.getProductStartTime()
        return  dirtyList.stream().filter(wpsRowData -> orderTypeList.contains(wpsRowData.getOrderType())).collect(Collectors.toList());
    }

    public List<WpsRowData> processList(List<WpsOrderProcessEnum> processEnumList, Long userId,
                                        LocalDate startDate, LocalDate endDate, String factoryCode,boolean cacheFlag,Map<String,Object> params){
        startDate = startDate ==null ? LocalDate.of(LocalDate.now().getYear(),1,1) : startDate;
        endDate = endDate == null ? LocalDate.now() : endDate;
        List<WpsRowData> dirtyList = dataFetchService.fetchWpsOrderList(startDate, endDate, Lists.newArrayList(factoryCode));
/*        if (cacheFlag) {
            dirtyList = dataFetchService.fetchWpsOrdersWitchCache(startDate, endDate, factoryCode);
        }else{
            // 指定查询接口
            dirtyList = dataFetchService.fetchWpsOrderList(startDate, endDate, Lists.newArrayList(factoryCode));
        }*/

        List<WpsOrderReadFilter> totalReadFilter =  Lists.newArrayList();
        List<WpsOrderWriteFilter> totalWriteFilter =  Lists.newArrayList();
        Set<Integer> readFilterSet = Sets.newLinkedHashSet();
        Set<Integer> writeFilterSet = Sets.newLinkedHashSet();
        for (WpsOrderProcessEnum processEnum : processEnumList){
            // 读操作
            List<WpsOrderReadFilter> wpsOrderReadFilters = this.getHandler(processEnum).listReadFilter();
            for (WpsOrderReadFilter readFilter : wpsOrderReadFilters){
                if (!readFilterSet.contains(readFilter.filterSeq())) {
                    totalReadFilter.add(readFilter);
                    readFilterSet.add(readFilter.filterSeq());
                }
            }
            // 写操作
            List<WpsOrderWriteFilter> wpsOrderWriteFilters = this.getHandler(processEnum).listWriteFilter();
            for (WpsOrderWriteFilter writeFilter : wpsOrderWriteFilters){
                if (!writeFilterSet.contains(writeFilter.filterSeq())) {
                    totalWriteFilter.add(writeFilter);
                    writeFilterSet.add(writeFilter.filterSeq());
                }
            }
        }
        // 读过滤
        totalReadFilter.sort(Comparator.comparing(WpsOrderReadFilter::filterSeq));
        StopWatch stopWatch4Filter = new StopWatch();
        for (WpsOrderReadFilter filter : totalReadFilter){
            String filterName = filter.getClass().getName();
            stopWatch4Filter.start(String.format("业务过滤器%s",filterName));
            dirtyList  = filter.filter(userId, factoryCode, dirtyList,cacheFlag);
            stopWatch4Filter.stop();
        }
        // 写过滤
        totalWriteFilter.sort(Comparator.comparing(WpsOrderWriteFilter::filterSeq));
        StopWatch stopWatchWriteFilter = new StopWatch();
        for (WpsOrderWriteFilter filter : totalWriteFilter){
            String filterName = filter.getClass().getName();
            stopWatchWriteFilter.start(String.format("业务过滤器%s",filterName));
            dirtyList  = filter.filter(userId, WpsConstants.DATA_SOURCE_AUTO, factoryCode, dirtyList,cacheFlag,params);
            stopWatchWriteFilter.stop();
        }


        return dirtyList;
    }

}
