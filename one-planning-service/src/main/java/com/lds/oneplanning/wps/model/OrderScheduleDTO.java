package com.lds.oneplanning.wps.model;

import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.*;

/**
 * @Description:
 * @Author: zhuang<PERSON>ayin
 * @Email: <EMAIL>
 * @Date: 2025/5/8 14:24
 */
@ApiModel(value="订单排产对象", description="订单排产对象")
@Data
public class OrderScheduleDTO {
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderScheduleDTO that = (OrderScheduleDTO) o;
        return Objects.equals(lineCode, that.lineCode) && Objects.equals(orderNo, that.orderNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(lineCode, orderNo);
    }


    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "线体UUID")
    private String lineUuid;
    @ApiModelProperty(value = "线体编码")
    private String lineCode;
    @ApiModelProperty(value = "线体名称")
    private String lineName;
    @ApiModelProperty(value = "订单类型-订单接口-ZTYPE")
    private String orderType;
    @ApiModelProperty(value = "订单号-订单接口-ZDDH")
    private String orderNo;
    @ApiModelProperty(value = "销售订单号-订单接口-VBELN")
    private String sellOrderNo;
    @ApiModelProperty(value = "销售行项目-订单接口-订单接口-POSNR")
    private String rowItem;
    @ApiModelProperty(value = "产品组编码")
    private String productGroupCode;
    @ApiModelProperty(value = "产品组名称")
    private String productGroupName;
    @ApiModelProperty(value = "订单数量（套数）")
    private Integer orderUnitQty;
    @ApiModelProperty(value = "订单数量（只数）")
    private Integer orderPcsQty;
    @ApiModelProperty(value = "需排产数量（只数）")
    private Integer schedulePcsQty;
    @ApiModelProperty(value = "完工日期")
    private LocalDate calculateFinishTime;

    @ApiModelProperty(value = "上线日期")
    private Date onlineTime;

    @ApiModelProperty(value = "产品id")
    private String productId;
    @ApiModelProperty(value = "商品id-订单接口-ZSPID")
    private String commodityId;

    @ApiModelProperty(value = "冻结状态")
    private Integer  frozenStatus = 0;

    @ApiModelProperty(value = "订单制程状态：1进行中 2 已完结")
    private Integer  orderProcessStatus = 1;

    @ApiModelProperty(value = "开始排产日期")
    private LocalDate startProductPeriod;

    @ApiModelProperty(value = "截止排产日期")
    private LocalDate endProductPeriod;

    @ApiModelProperty(value = "订单排期数据")
    private Map<LocalDate,Number> scheduleDataMap = Maps.newLinkedHashMap() ;


    public String getLineDesc(){
        return getLineName()+" "+getLineCode();
    }


    private List<String> orderNos ;

    private List<OrderWarningDTO> warnings;
    /**
     * 版本差异
     */
    private Set<String> versionDifferences;
}
