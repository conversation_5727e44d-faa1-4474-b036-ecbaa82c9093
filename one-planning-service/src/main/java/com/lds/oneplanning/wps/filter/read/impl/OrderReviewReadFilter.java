package com.lds.oneplanning.wps.filter.read.impl;

import com.lds.oneplanning.esb.cache.WpsOrderCacheUtils;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.wps.filter.read.AbstractWpsOrderReadFilter;
import com.lds.oneplanning.wps.model.OrderReviewData;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.utils.WpsTransUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuang<PERSON><PERSON>in
 * @Email: zhuang<PERSON><PERSON><EMAIL>
 * @Date: 2025/3/25 18:58
 */
@Slf4j
@Service
public class OrderReviewReadFilter extends AbstractWpsOrderReadFilter {
    @Resource
    private IEsbDataFetchService dataFetchService;
    @Override
    public Integer filterSeq() {
        return 5;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag) {
        return dirtyList;
   /*     // 有缓存的时候从缓存中获取，缓存没有命中，则继续往下调用接口
        if (cacheFlag) {
            List<OrderReviewData>   cacheReviewList = WpsOrderCacheUtils.getReviewFromCache(dirtyList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toSet()));
            if (CollectionUtils.isNotEmpty(cacheReviewList)) {
                Map<String,OrderReviewData> reviewMap = cacheReviewList.stream().collect(Collectors.toMap(OrderReviewData::getOrderNo, wpsRowData -> wpsRowData,(t, t2) -> t2));
                dirtyList.stream().filter(wpsRowData -> reviewMap.containsKey(wpsRowData.getOrderNo())).forEach(wpsRowData -> {
                    OrderReviewData reviewTarget = reviewMap.get(wpsRowData.getOrderNo());
                    // 字段替换
                    WpsTransUtils.coverOrderReviewData(reviewTarget,wpsRowData);
                });
                return  dirtyList;
            }else{
                // 2025-05-21  当缓存中找不到时无法区分是不存在还是本来就空，实际操作中订单评审接口请求非常超时，这里如果制定缓存查询，则没有命中直接返回，不再发起接口请求，如需刷缓存，指定flag为false
                return dirtyList;
            }
        }

        // 订单评审接口数据回填
        Set<String> sellOrderNos = dirtyList.stream().map(WpsRowData::getSellOrderNo).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(sellOrderNos)) {
            return dirtyList;
        }
        List<OrderReviewData> orderReviewList = dataFetchService.fetchOrderReviewListV2(sellOrderNos);
        // 订单评审接口单独处理(要求销售订单号不为空)
        dirtyList.stream().filter(wpsRowData -> StringUtils.isNotBlank(wpsRowData.getRowItem()) &&  StringUtils.isNotBlank(wpsRowData.getSellOrderNo()) ).forEach(wpsRowData -> {
            //   评审接口回填 这些规则负责和数据质量之差，难崩  行项目号为空 不参与比对
            OrderReviewData orderReview = orderReviewList.stream().filter(reviewData ->
                            StringUtils.isNotBlank(reviewData.getRowItem()) && StringUtils.isNotBlank(reviewData.getSellOrderNo()) &&
                                    Integer.valueOf(reviewData.getSellOrderNo()).equals(Integer.valueOf(wpsRowData.getSellOrderNo())) &&
                                    Integer.valueOf(reviewData.getRowItem()).equals(Integer.valueOf(wpsRowData.getRowItem())))
                    .findAny().orElse(new OrderReviewData());
            //命中之后 设置订单号
            orderReview.setOrderNo(wpsRowData.getOrderNo());
            WpsTransUtils.coverOrderReviewData(orderReview,wpsRowData);
        });
        WpsOrderCacheUtils.setReviewToCache(orderReviewList);
        return dirtyList;*/
    }


}
