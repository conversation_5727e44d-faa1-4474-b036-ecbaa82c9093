package com.lds.oneplanning.wps.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="在制工单多道工序表信息", description="")
public class MesProcessWorkOrderProcedurDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "工序编码")
    private String procedureCode;

    @ApiModelProperty(value = "工序名称")
    private String procedureName;

    @ApiModelProperty(value = "报工数量")
    private Integer qty;

    @ApiModelProperty(value = "排产设备")
    private String schedulingEquipment;

    @ApiModelProperty(value = "排产模具")
    private String schedulingMold;

}
