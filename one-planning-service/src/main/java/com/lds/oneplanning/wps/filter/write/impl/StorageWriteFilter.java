package com.lds.oneplanning.wps.filter.write.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.entity.Workshop;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.basedata.service.IWorkshopService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.common.utils.ThreadPoolUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.entity.WpsDayPlan;
import com.lds.oneplanning.wps.filter.write.AbstractWpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.service.IWpsDayPlanService;
import com.lds.oneplanning.wps.service.IWpsRowExtService;
import com.lds.oneplanning.wps.utils.WpsTransUtils;
import com.lds.oneplanning.wps.warning.auto.WpsAutoPlanWarningPipeline;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 从数据库读取数据回填
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/26 15:49
 */
@Slf4j
@Service
public class StorageWriteFilter extends AbstractWpsOrderWriteFilter {

    @Resource
    private IWpsDayPlanService weekPlanService;
    @Resource
    private IWpsRowExtService wpsRowExtService;
    @Resource
    private ILineInfoService lineInfoService;
    @Resource
    private WpsAutoPlanWarningPipeline wpsAutoPlanWarningPipeline;
    @Resource
    private IWorkshopService workshopService;

    @Override
    public Integer filterSeq() {
        return 6;
    }

    @Override
    protected List<WpsRowData> doFilter(Long userId, Integer datasource, String factoryCode, List<WpsRowData> wpsRowDatas,boolean cacheFlag, Map<String,Object> params) {
        if (!WpsConstants.DATA_SOURCE_STORAGE.equals(datasource)) {
            //非读取数据 直接返回
            return wpsRowDatas;
        }
        List<WpsRowData> resList = Lists.newArrayList();
        List<Workshop> workshops = workshopService.list(Wrappers.<Workshop>lambdaQuery().eq(Workshop::getFactoryCode,factoryCode));
        Map<String,String> workshoeNameMap = workshops.stream().collect(Collectors.toMap(Workshop::getCode,Workshop::getName,(s, s2) -> s2));
        // 添加数据库查询信息
        resList.addAll(this.setDataFromStorage(wpsRowDatas,workshoeNameMap));
        // 改为异步操作
        ThreadPoolUtil.instance().execute(() -> {
            WpsAutoScheduleContext context;
            context = new WpsAutoScheduleContext(resList);
            //  设置告警颜色
            wpsAutoPlanWarningPipeline.execute(context);
        });
        return resList;
    }

    private List<WpsRowData> setDataFromStorage(List<WpsRowData> wpsRowDatas,Map<String,String> workshoeNameMap){
        List<WpsRowData> resList = Lists.newArrayList();
        List<String> bizIds = wpsRowDatas.stream().map(WpsRowData::getOrderNo).collect(Collectors.toList());

        // 订单 根据编码分组
        //车间信息回填
        Map<String, List<WpsDayPlan>> scheduleMap = weekPlanService.groupByBizId(bizIds,null);
        // 这样才能支持两边的保存效果
        List<LineInfo> lineInfos = lineInfoService.list();
        Map<String,String> lineUUidMap = lineInfos.stream().filter(lineInfo -> lineInfo.getLineUuid()!=null).collect(Collectors.toMap(LineInfo::getCode, LineInfo::getLineUuid,(s, s2) -> s2));
        Map<String,String> lineNameMap = lineInfos.stream().filter(lineInfo -> lineInfo.getName()!=null).collect(Collectors.toMap(LineInfo::getCode, LineInfo::getName,(s, s2) -> s2));
        Map<String,String> workshopMap = lineInfos.stream().filter(lineInfo -> lineInfo.getWorkshopCode()!=null).collect(Collectors.toMap(LineInfo::getCode, LineInfo::getWorkshopCode,(s, s2) -> s2));
        for (WpsRowData rowData :wpsRowDatas){
            String bizId = rowData.getOrderNo();
            // 数据库值覆盖
            if (CollectionUtils.isEmpty(scheduleMap.get(bizId))) {
                // 库表没值 不用构建，直接添加当前数据
                resList.add(rowData);
            }else{
                // 库表有值
                List<WpsDayPlan> weekPlans =  scheduleMap.get(bizId);
                // fuck off dirty data
                weekPlans.stream().forEach(wpsWeekPlan -> wpsWeekPlan.setLineCode(wpsWeekPlan.getLineCode() == null ? "" :wpsWeekPlan.getLineCode()));
                Map<String,List<WpsDayPlan>> groupByLineCode = weekPlans.stream().collect(Collectors.groupingBy(WpsDayPlan::getLineCode));
                for(Map.Entry<String,List<WpsDayPlan>> entry : groupByLineCode.entrySet()){
                    // 一个线体拷贝一份，线体有记录，则裂变成多行数据
                    String lineCode = entry.getKey();
                    String lineUuid =  lineUUidMap.get(lineCode);
                    String lineName =  lineNameMap.get(lineCode);
                    WpsRowData copy = BeanUtil.map(rowData, WpsRowData.class);
/*                    if (!approveLineCodes.contains(lineCode)) {
//                         注意！！！若无权限，则不让其看到库表排产数据 todo 先注释这个跳过逻辑
                        continue;
                    }*/
                    List<WpsDayPlan> wpsDayPlans =  entry.getValue();
                    wpsDayPlans.stream().forEach(weekPlan -> {
                        LocalDate dateKey = weekPlan.getScheduleDate();
                        if (copy.getScheduleDataMap().containsKey(dateKey) ) {
                            copy.getScheduleDataMap().put(dateKey,weekPlan.getPrePlanQuantity());
                            copy.setLineCode(lineCode);
                            copy.setLineUuid(lineUuid);
                            copy.setLineName(lineName);
                            copy.setWorkshopCode(workshopMap.get(lineCode));
                            copy.setWorkshopName(workshoeNameMap.get(copy.getWorkshopCode()));
                        }
                    });
                    resList.add(copy);
                }
            }
        }
        // 读取数据也设置上线日期
        resList.forEach(wpsRowData -> wpsRowData.setOnlineTime(LocalDateTimeUtil.localDateToDate(WpsTransUtils.getStartScheduleDate(wpsRowData.getScheduleDataMap()))));
        return resList;
    }

}
