package com.lds.oneplanning.wps.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 船期临近未定舱异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13 16:20
 */
@ApiModel(description = "船期临近未定舱异常")
@TableHeader(type = WpsOrderWarningTypeEnum.SHIP_BOOKING_URGENT, source = ViewSource.DEFAULT)
@Data
public class ShipBookingUrgentVO implements Serializable {

    private static final long serialVersionUID = 2134854575399543783L;

    @ApiModelProperty(value = "预警灯色")
    private String lightColor;

    @ApiModelProperty(value = "销售订单号")
    private String sellOrderNo;

    @ApiModelProperty(value = "行项目")
    private Integer rowItem;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "商品ID")
    private String commodityId;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    @ApiModelProperty(value = "船期时间")
    private LocalDate shipScheduleDate;

    @ApiModelProperty(value = "距离船期剩余天数")
    private Integer daysRemainingBeforeShipment;

    @ApiModelProperty(value = "订舱状态")
    private String bookingStatus;

    @ApiModelProperty(value = "订单数量")
    private Integer orderQuantity;

    @ApiModelProperty(value = "OM")
    private String om;

    @ApiModelProperty(value = "外向交货单")
    private String outboundDeliveryOrder;

    @ApiModelProperty(value = "外向交货单行号")
    private String outboundDeliveryOrderItem;

    @JsonIgnore
    private String omJobNos;

    @JsonIgnore
    private String omNames;

    @JsonIgnore
    private OrderWarningHandleStatusEnum processStatus;

    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

    @ApiModelProperty(value = "是否发起船期变更")
    private Boolean initiateShipmentChange;
}