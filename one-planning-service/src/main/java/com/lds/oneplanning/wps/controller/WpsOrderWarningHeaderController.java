package com.lds.oneplanning.wps.controller;


import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.helper.ColumnHeaderBuildHelper;
import com.lds.oneplanning.wps.utils.MockDataGenerator;
import com.lds.oneplanning.wps.vo.TableColumn;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-31
 */
@Api(value = "wps告警预警表头")
@RestController
@RequestMapping("/wps/OrderWarningHeader")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WpsOrderWarningHeaderController {
    private final IUserInfoService userInfoService;

    private static final Table<WpsOrderWarningTypeEnum, ViewSource, List<TableColumn>> headerTable = HashBasedTable.create();
    private static final Table<WpsOrderWarningTypeEnum, ViewSource, Class<?>> clazzTable = HashBasedTable.create();

    static {
        loadTableHeader();
    }

    private static void loadTableHeader() {
        Set<Class<?>> classes = ClassUtil.scanPackageByAnnotation("com.lds.oneplanning.wps.vo", TableHeader.class);
        for (Class<?> clazz : classes) {
            if (clazz.isAnnotationPresent(TableHeader.class)) {
                List<TableColumn> columns = ColumnHeaderBuildHelper.build(clazz);
                TableHeader tableHeader = clazz.getAnnotation(TableHeader.class);
                if (tableHeader != null) {
                    for (ViewSource source : tableHeader.source()) {
                        headerTable.put(tableHeader.type(), source, columns);
                        clazzTable.put(tableHeader.type(), source, clazz);
                    }
                }
            }
        }
    }

    @ApiOperation(value = "获取表头", notes = "获取表头")
    @GetMapping("/header")
    public List<TableColumn> header(@RequestParam(value = "source", required = false) ViewSource viewSource, @ApiParam @RequestParam(value = "type") WpsOrderWarningTypeEnum type) {
        ViewSource source = userInfoService.getOrDefaultUserType(viewSource);

        List<TableColumn> tableColumns = getHeader(type, source);
        if (CollectionUtils.isNotEmpty(tableColumns)) {
            return tableColumns;
        }
        //重新加载 - 本地开发需要用到，热加载
        loadTableHeader();
        //再试一下
        return getHeader(type, source);
    }


    @ApiOperation(value = "mock数据", notes = "mock数据")
    @GetMapping("/mock")
    public Page<?> mock(@RequestParam(value = "source", required = false) ViewSource viewSource, @ApiParam @RequestParam(value = "type") WpsOrderWarningTypeEnum type) {
        ViewSource source = userInfoService.getOrDefaultUserType(viewSource);

        Class<?> clazz = getClazz(type, source);
        Page page = new Page<>(1, 10);

        if (clazz != null) {
            List list = MockDataGenerator.mockList(clazz, RandomUtil.randomInt(3, 15));
            page.setResult(list);
            page.setTotal(list.size());
        }

        // 热加载逻辑 - 仅在本地开发时启用
        loadTableHeader();

        return page;
    }


    private List<TableColumn> getHeader(WpsOrderWarningTypeEnum type, ViewSource source) {
        List<TableColumn> tableColumns = headerTable.get(type, source);
        if (CollectionUtils.isNotEmpty(tableColumns)) {
            return tableColumns;
        }

        //获取不到，尝试获取默认表头
        if (!ViewSource.DEFAULT.equals(source)) {
            tableColumns = headerTable.get(type, ViewSource.DEFAULT);
        }

        if (CollectionUtils.isNotEmpty(tableColumns)) {
            return tableColumns;
        }
        return Collections.emptyList();
    }

    private Class<?> getClazz(WpsOrderWarningTypeEnum type, ViewSource source) {
        Class<?> clazz = clazzTable.get(type, source);
        if (clazz != null) {
            return clazz;
        }

        //获取不到，尝试获取默认表头
        if (!ViewSource.DEFAULT.equals(source)) {
            clazz = clazzTable.get(type, ViewSource.DEFAULT);
        }

        return clazz;
    }
}
