package com.lds.oneplanning.wps.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 2.28获取LCP计划订单维度
 * 2.28.1接口编码：LCP-GETPLANNEDORDER
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">da<PERSON><PERSON><PERSON>n</a>
 * @since 2025/5/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EsbPlanOrder {
    /**
     * 申请人工号 CODE
     */
    private String applicantEmpNo;
    /**
     * 申请人姓名 SQR
     */
    private String applicantName;
    /**
     * 预计订单交期 YJDDJQ
     */
    private String expectedDeliveryDate;
    /**
     * 整灯风险备料单号 LSH_FXBKSQ
     */
    private String riskMaterialOrderNumber;
    /**
     * 预计SAP转正日期 YJSAPZZSJ
     */
    private String expectedSapOfficialDate;
    /**
     * 计划订单 JHDD
     */
    private String plannedOrder;
    /**
     * 类别 BLLB
     */
    private String category;
}
