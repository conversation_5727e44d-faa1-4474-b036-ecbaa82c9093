package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lds.oneplanning.wps.enums.LightColor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
@Data
@TableName(value ="warning_ship_booking_urgent_abnormal")
@ApiModel(value="WarningShipBookingUrgentAbnormal对象", description="")
public class WarningShipBookingUrgentAbnormal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;

    @ApiModelProperty(value = "生产订单号")
    private String orderNo;

    @ApiModelProperty(value = "销售订单号")
    private String sellOrderNo;

    @ApiModelProperty(value = "行项目")
    private String rowItem;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "商品ID")
    private String commodityId;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    @ApiModelProperty(value = "船期日期")
    private LocalDate shipScheduleDate;

    @ApiModelProperty(value = "订舱状态")
    private String bookingStatus;

    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "订单数量")
    private Integer orderQuantity;

    @ApiModelProperty(value = "OM工号")
    private String omJobNos;

    @ApiModelProperty(value = "OM姓名")
    private String omNames;

    @ApiModelProperty(value = "外向交货单")
    private String outboundDeliveryOrder;

    @ApiModelProperty(value = "外向交货单行号")
    private String outboundDeliveryOrderItem;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "更新人")
    private Long updateBy;
}