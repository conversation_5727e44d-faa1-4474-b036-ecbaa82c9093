package com.lds.oneplanning.wps.warning.workbench;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.wts.thtask.vo.req.ThTaskLogisticsInfoQueryReq;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.Collections;
import java.util.List;

@Slf4j
public class TempMockData {
    public static final String factoryCode = "3311";
    public static final Long userId = 1367766865382554655L;


    public static WpsAutoScheduleContext getCtx() {
        //获取项目路径下的：src/main/resources/temp/wpsAutoScheduleContext.json
        ClassLoader classLoader = TempMockData.class.getClassLoader();
        URL resource = classLoader.getResource("temp/wpsAutoScheduleContext.json");

        if (resource == null) {
            log.error("文件不存在");
            return new WpsAutoScheduleContext();
        }

        String json = FileUtil.readUtf8String(resource.getFile());
        return JSON.parseObject(json, WpsAutoScheduleContext.class);
    }

    public static WpsWorkbenchWarningContext getCtx2() {
        //获取项目路径下的：src/main/resources/temp/wpsAutoScheduleContext.json
        ClassLoader classLoader = TempMockData.class.getClassLoader();
        URL resource = classLoader.getResource("temp/wpsAutoScheduleContext.json");

        if (resource == null) {
            log.error("文件不存在");
            return new WpsWorkbenchWarningContext();
        }

        String json = FileUtil.readUtf8String(resource.getFile());
        return JSON.parseObject(json, WpsWorkbenchWarningContext.class);
    }

    public static WpsAutoScheduleContext getCtx3() {
        //获取项目路径下的：src/main/resources/temp/wpsAutoScheduleContext.json
        ClassLoader classLoader = TempMockData.class.getClassLoader();
        URL resource = classLoader.getResource("temp/wpsAutoScheduleContext.json");

        if (resource == null) {
            log.error("文件不存在");
            return new WpsAutoScheduleContext();
        }

        String json = FileUtil.readUtf8String(resource.getFile());
        return JSONObject.parseObject(json).getObject("data", WpsAutoScheduleContext.class);
    }

    public static List<ThTaskLogisticsInfoQueryReq> getCtx4() {
        //获取项目路径下的：src/main/resources/temp/wpsAutoScheduleContext.json
        ClassLoader classLoader = TempMockData.class.getClassLoader();
        URL resource = classLoader.getResource("temp/temp2.json");

        if (resource == null) {
            log.error("文件不存在");
            return Collections.emptyList();
        }

        String json = FileUtil.readUtf8String(resource.getFile());
        return JSONObject.parseArray(json, ThTaskLogisticsInfoQueryReq.class);
    }
}
