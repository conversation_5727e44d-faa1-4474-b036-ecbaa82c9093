package com.lds.oneplanning.wps.warning.workbench.handlers;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.lds.oneplanning.basedata.model.UserInfoDTO;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.common.service.IBasicUserService;
import com.lds.oneplanning.esb.datafetch.model.EsbGetPoGroupData;
import com.lds.oneplanning.esb.datafetch.model.EsbIncomeMaterialCheckAbnormalData;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.esb.model.AtpLackOffMaterialRespVO;
import com.lds.oneplanning.wps.entity.*;
import com.lds.oneplanning.wps.enums.*;
import com.lds.oneplanning.wps.helper.EsbApiWrapper;
import com.lds.oneplanning.wps.helper.WpsRowDataMergeHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWarningIncomeMaterialAtpAbnormalService;
import com.lds.oneplanning.wps.service.IWarningIncomeMaterialPoAtpAbnormalService;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.utils.MockDataGenerator;
import com.lds.oneplanning.wps.utils.OrderNoUtils;
import com.lds.oneplanning.wps.utils.WpsDateUtil;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 来料检验异常处理器
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class IncomeMaterialAtpAbnormalHandler implements IWpsWorkbenchWarningHandler {
    @Value("${wps.mock.switch:0}")
    private Integer isDebug = 0;
    private final EsbDataFetchService esbDataFetchService;
    private final IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    private final WarningTodoListService todoListService;
    private final IWarningIncomeMaterialAtpAbnormalService warningIncomeMaterialAtpAbnormalService;
    private final IWarningIncomeMaterialPoAtpAbnormalService warningIncomeMaterialPoAtpAbnormalService;
    private final IBasicUserService basicUserService;
    private final IUserInfoService userInfoService;
    private final EsbApiWrapper esbApiWrapper;


    @Override
    public List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext ctx, Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap) {
        if (ctx == null || CollectionUtils.isEmpty(ctx.getOrders())) {
            return Collections.emptyList();
        }

        List<WpsRowData> atpStoreAbnormalList = new ArrayList<>();
        List<WpsRowData> atpInfoAbnormalList = new ArrayList<>();
        filterAbnormalData(ctx, atpStoreAbnormalList, atpInfoAbnormalList);


        if (Objects.equals(isDebug, 1)) {
            //TODO 现在不齐套的数据没有排产，上线时间都为空，这里为了调试，先随机生成时间，方便调试
            atpStoreAbnormalList.forEach(row -> row.setOnlineTime(MockDataGenerator.randomDate()));
        }

        log.info("库存齐套异常数量:{}, 信息齐套异常数量{}", atpStoreAbnormalList.size(), atpInfoAbnormalList.size());

        if (CollectionUtils.isEmpty(atpStoreAbnormalList) && CollectionUtils.isEmpty(atpInfoAbnormalList)) {
            return Collections.emptyList();
        }

        //去掉没排产的数据
        atpStoreAbnormalList.removeIf(row -> row.getOnlineTime() == null);
        atpInfoAbnormalList.removeIf(row -> row.getOnlineTime() == null);

        log.info("移除未排产数据后：库存齐套异常数量:{}, 信息齐套异常数量{}", atpStoreAbnormalList.size(), atpInfoAbnormalList.size());
        if (CollectionUtils.isEmpty(atpStoreAbnormalList) && CollectionUtils.isEmpty(atpInfoAbnormalList)) {
            return Collections.emptyList();
        }
        //合并相同订单的数据
        atpStoreAbnormalList = WpsRowDataMergeHelper.mergeSameOrder(atpStoreAbnormalList);
        atpInfoAbnormalList = WpsRowDataMergeHelper.mergeSameOrder(atpInfoAbnormalList);
        //分析异常数据
        return analyzeAbnormalData(ctx, atpStoreAbnormalList, atpInfoAbnormalList);
    }

    /**
     * 过滤异常数据
     *
     * @param ctx                  上下文对象，包含订单信息
     * @param atpStoreAbnormalList ATP存储结果异常的订单列表
     * @param atpInfoAbnormalList  ATP信息结果异常的订单列表
     */
    private static void filterAbnormalData(WpsWorkbenchWarningContext ctx, List<WpsRowData> atpStoreAbnormalList, List<WpsRowData> atpInfoAbnormalList) {
        for (WpsRowData row : ctx.getOrders()) {
            if (row.getAtpStatus()==3) {
                // 信息不齐套
                atpInfoAbnormalList.add(row);
            } else if (row.getAtpStatus()==2) {
                //库存不齐套
                atpStoreAbnormalList.add(row);
            }
        }
    }

    /**
     * 分析异常数据并生成警告信息
     *
     * @param ctx                  工作台警告上下文
     * @param atpStoreAbnormalList ATP存储异常数据列表
     * @param atpInfoAbnormalList  ATP信息异常数据列表
     * @return 包含警告信息的列表，如果未分析出异常则返回空列表
     */
    private List<WpsOrderPlanWarning> analyzeAbnormalData(WpsWorkbenchWarningContext ctx,
                                                          List<WpsRowData> atpStoreAbnormalList,
                                                          List<WpsRowData> atpInfoAbnormalList) {

        log.info("开始分析异常数据");
        List<WarningIncomeMaterialAtpAbnormal> warningList = new ArrayList<>();
        List<WarningIncomeMaterialPoAtpAbnormal> poWarningList = new ArrayList<>();
        List<WpsRowData> abnormalRowList = new ArrayList<>();
        atpStoreAbnormalList.addAll(atpInfoAbnormalList);
        abnormalRowList.addAll(atpStoreAbnormalList);
        // 分析在库异常数据
        poWarningList = analyzeInStoreExceptions(atpStoreAbnormalList, warningList, poWarningList);
        log.info("完成在库异常数据分析，warningList异常数据数量: {},poWarningList异常数据数量:{}", warningList.size(),poWarningList.size());
        //消警
        log.info("开始消警");
        eliminateAlarms(poWarningList);
        //保存或更新
        log.info("开始保存异常数据");
        saveOrUpdate(ctx, warningList,poWarningList);
        log.info("完成保存异常数据，警告列表大小: {}", warningList.size());

        log.info("开始创建待办");
        warningIncomeMaterialPoAtpAbnormalService.createTodoList(poWarningList);
        // 拿出poWarningList的订单号
        List<String> orderNos = poWarningList.stream().map(WarningIncomeMaterialPoAtpAbnormal::getOrderNo).collect(Collectors.toList());
        abnormalRowList.removeIf(row -> !orderNos.contains(row.getOrderNo()));
        return convertData(ctx,  abnormalRowList, poWarningList);
    }
    private List<WpsOrderPlanWarning> convertData(WpsWorkbenchWarningContext ctx, List<WpsRowData> abnormalList, List<WarningIncomeMaterialPoAtpAbnormal> warningList) {
        Map<String, LightColor> lightColorMap = warningList.stream()
                .filter(e -> null != e.getLightColor())
                .collect(Collectors.toMap(WarningIncomeMaterialPoAtpAbnormal::getOrderNo, WarningIncomeMaterialPoAtpAbnormal::getLightColor, (o, o2) -> o));
        return buildWarning(ctx, abnormalList, lightColorMap);
    }

    public static LightColor calculateLightColor(LocalDate planDate) {
        if (planDate == null) {
            return LightColor.BLANK;
        }
        LocalDate now = LocalDate.now();
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(now ,planDate);
        if (daysBetween <= 3 && daysBetween > 0) {
            // 小于3天，红灯
            return LightColor.RED;
        } else if (daysBetween > 3 && daysBetween <= 7) {
            // 3~7天内，黄灯
            return LightColor.YELLOW;
        }
        return LightColor.BLANK;
    }

    /**
     * 获取待办处理人
     *
     * @param warningList 告警列表
     * @return 一个以 Long 为键、String 为值的 HashMap，键为待办事项的ID，值为待办处理人的员工编号
     */
    private Map<Long,String> getAssignee(List<WarningIncomeMaterialPoAtpAbnormal> warningList) {
        //TODO 获取待办处理人，逻辑待补充
        return Maps.newHashMap();
    }

    private void saveOrUpdate(WpsWorkbenchWarningContext ctx, List<WarningIncomeMaterialAtpAbnormal> warningList, List<WarningIncomeMaterialPoAtpAbnormal> poWarningList) {
        // 1. 提取订单号列表
        List<String> orderNoList = warningList.stream()
                .map(WarningIncomeMaterialAtpAbnormal::getOrderNo)
                .distinct()
                .collect(Collectors.toList());
        if (orderNoList.isEmpty()){
            return;
        }
        // 2. 查询数据库中已存在的记录
        List<WarningIncomeMaterialAtpAbnormal> existList = warningIncomeMaterialAtpAbnormalService.lambdaQuery()
                .in(WarningIncomeMaterialAtpAbnormal::getOrderNo, orderNoList)
                .list();
        // 判断warningList的订单号实在再existList中，如果有就做更新数据操作，如果没有就做插入数据操作
        List<WarningIncomeMaterialAtpAbnormal> updateList = new ArrayList<>();
        List<WarningIncomeMaterialAtpAbnormal> insertList = new ArrayList<>();
        warningList.forEach(warning -> {
            // 判断warning的订单号是否再existList中
            if (existList.stream().anyMatch(exist -> exist.getOrderNo().equals(warning.getOrderNo()))) {
                // 2.1 如果已存在，更新记录
                WarningIncomeMaterialAtpAbnormal existIncome = existList.stream()
                        .filter(exist -> exist.getOrderNo().equals(warning.getOrderNo()))
                        .findFirst()
                        .orElse(null);
                if (existIncome != null) {
                    BeanUtils.copyProperties(warning, existIncome);
                    updateList.add(existIncome);
                } else {
                    insertList.add(warning);
                }
            } else {
                insertList.add(warning);
            }
        });
        if (!updateList.isEmpty()){
            warningIncomeMaterialAtpAbnormalService.updateBatchById(updateList);
        }
        if (!insertList.isEmpty()) {
            warningIncomeMaterialAtpAbnormalService.saveBatch(insertList);
        }

        // 1. 提取订单号列表
        List<String> poOrderNoList = poWarningList.stream()
                .map(WarningIncomeMaterialPoAtpAbnormal::getOrderNo)
                .distinct()
                .collect(Collectors.toList());
        List<String> poMaterialIdList = poWarningList.stream()
                .map(WarningIncomeMaterialPoAtpAbnormal::getShortageMaterialId)
                .distinct()
                .collect(Collectors.toList());
        List<WarningIncomeMaterialPoAtpAbnormal> poExistList = warningIncomeMaterialPoAtpAbnormalService.lambdaQuery()
                .in(WarningIncomeMaterialPoAtpAbnormal::getOrderNo, poOrderNoList)
                .in(WarningIncomeMaterialPoAtpAbnormal::getShortageMaterialId, poMaterialIdList)
                .list();
        // 3. 构建已存在记录的映射表（订单号 -> 物料ID -> 实体）
        Table<String, String, WarningIncomeMaterialPoAtpAbnormal> existTable = HashBasedTable.create();
        poExistList.forEach(warning -> existTable.put(warning.getOrderNo(), warning.getShortageMaterialId(), warning));

        // 4. 初始化更新和插入列表
        List<WarningIncomeMaterialPoAtpAbnormal> poUpdateList = new ArrayList<>();
        List<WarningIncomeMaterialPoAtpAbnormal> poInsertList = new ArrayList<>();

        // 5. 遍历warningList，区分更新和插入
        CopyOptions opt = CopyOptions.create().ignoreNullValue();
        poWarningList.forEach(warning -> {
//            warning.setFactoryCode(ctx.getFactoryCode());
//            warning.setUpdatedBy(ctx.getUserId());

            if (existTable.contains(warning.getOrderNo(), warning.getShortageMaterialId())) {
                // 5.1 如果记录已存在，执行更新操作
                WarningIncomeMaterialPoAtpAbnormal entity = existTable.get(warning.getOrderNo(), warning.getShortageMaterialId());
                BeanUtil.copyProperties(warning, entity, opt);
                // 反向更新到warning中，以便外部能获取到id
                BeanUtil.copyProperties(entity, warning);
                poUpdateList.add(warning);
            } else {
                // 5.2 如果记录不存在，添加到插入列表
                poInsertList.add(warning);
            }
        });

        // 6. 批量更新和插入
        if (CollectionUtils.isNotEmpty(poUpdateList)) {
            warningIncomeMaterialPoAtpAbnormalService.updateBatchById(poUpdateList);
        }
        if (CollectionUtils.isNotEmpty(poInsertList)) {
            warningIncomeMaterialPoAtpAbnormalService.saveBatch(poInsertList);
        }
    }

    /**
     * 消除告警
     *
     * @param warningList 告警信息列表
     */
    private void eliminateAlarms(List<WarningIncomeMaterialPoAtpAbnormal> warningList) {
        //获取所有待办的数据
        List<WarningIncomeMaterialPoAtpAbnormal> existData = warningIncomeMaterialPoAtpAbnormalService.queryUnHandleData();
        log.info("获取未处理的警告数据计数: {}", existData.size());

        //将warningList组装成Table
        Table<String, String, WarningIncomeMaterialPoAtpAbnormal> warningTable = HashBasedTable.create();
        warningList.forEach(warning -> warningTable.put(warning.getOrderNo(), warning.getShortageMaterialId(), warning));

        //如果existData不存在与warningList中的数据，则删除，根据订单号和物料id来判断
        List<Long> toBeRemoveIdsList = existData.stream()
                .filter(e -> !warningTable.contains(e.getOrderNo(), e.getShortageMaterialId()))
                .map(WarningIncomeMaterialPoAtpAbnormal::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(toBeRemoveIdsList)) {
            log.info("消警列表：{}", toBeRemoveIdsList);
            todoListService.lambdaUpdate()
                    .set(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.CLOSED)
                    .eq(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.UN_HANDLE)
                    .in(WarningTodoList::getBizId, toBeRemoveIdsList)
                    .eq(WarningTodoList::getWarningType, this.getWarningType())
                    .update();

            //主表消警
            wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
        }
    }

    private WpsOrderPlanWarning toWarning(String factoryCode, Long userId, WpsRowData order) {
        return buildWpsOrderPlanWarning(factoryCode, userId, order.getOrderNo(), order.getLineUuid(), order.getLineCode(),
                OrderWarningLevelEnum.WARNING, order.get_startProductPeriod());
    }




    /**
     * 分析并处理在库异常数据
     *
     * @param atpStoreAbnormalList atp在库异常数据列表
     * @param warningList          警告列表
     */
    private List<WarningIncomeMaterialPoAtpAbnormal> analyzeInStoreExceptions(List<WpsRowData> atpStoreAbnormalList,  List<WarningIncomeMaterialAtpAbnormal> warningList,
                                          List<WarningIncomeMaterialPoAtpAbnormal> warningPoList) {
        // 先拿到所有生产订单 查atp数据，过滤use_type_name等于采购单的数据
        List<String> orderNoList = atpStoreAbnormalList.stream()
                .map(WpsRowData::getOrderNo)
                .collect(Collectors.toList());
        log.info("去atp查数据入参：{}",  JSON.toJSONString(orderNoList));
        List<AtpLackOffMaterialRespVO> respList = esbApiWrapper.fetchAtpCheckList(orderNoList);
        //只需要采购单
        List<AtpLackOffMaterialRespVO> filterList = respList.stream().filter(vo ->
                "采购单".equals(vo.getUSE_TYPE_NAME())
        ).collect(Collectors.toList());
        log.info("从atp查出来的数据只要use_work_type的数据条数：{}",  filterList.size());
        // 查出是采购单对应的生产订单
        List<String> workNoList = filterList.stream().map(AtpLackOffMaterialRespVO::getWORK_NO).distinct().collect(Collectors.toList());
        //只要对应有采购单的数据
        List<WpsRowData> existData = atpStoreAbnormalList.stream().filter(warning -> workNoList.contains(warning.getOrderNo())).collect(Collectors.toList());
        for (WpsRowData wpsRowData : existData) {
            WarningIncomeMaterialAtpAbnormal abnormal = new WarningIncomeMaterialAtpAbnormal();
            abnormal.setCustomer(wpsRowData.getCustomerCode());
            abnormal.setOrderNo(wpsRowData.getOrderNo());
            abnormal.setProductId(wpsRowData.getCommodityId());
            abnormal.setMaterialDesc(wpsRowData.getCommodityDesc());
            abnormal.setPlanDate(WpsDateUtil.getStartScheduleDate(wpsRowData.getScheduleDataMap()));
            abnormal.setPlanQuantity(wpsRowData.getOrderPcsQty());
            abnormal.setLineNumber(OrderNoUtils.trimPreZero(wpsRowData.getRowItem()));
            abnormal.setSalesOrderNumber(OrderNoUtils.trimPreZero(OrderNoUtils.getUnifyOrderNo(wpsRowData)));
            abnormal.setOnlineTime(wpsRowData.getOnlineTime());
            abnormal.setFactory(wpsRowData.getFactory());
            abnormal.setLatestDemandTime(wpsRowData.getOnlineTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate().minusDays(1)); //上线时间减一天
            warningList.add(abnormal);
        }
        for (AtpLackOffMaterialRespVO vo : filterList){
            WarningIncomeMaterialPoAtpAbnormal poAtpAbnormal = new WarningIncomeMaterialPoAtpAbnormal();
            poAtpAbnormal.setOrderNo(vo.getWORK_NO());
            poAtpAbnormal.setShortageMaterialId(vo.getMATERIAL_ITEM_NO());
            poAtpAbnormal.setPurchasePo(vo.getUSE_NO());
            poAtpAbnormal.setShortageMaterialDesc(vo.getMATERIAL_ITEM_NAME());
            poAtpAbnormal.setImpactQuantity(String.valueOf(vo.getUSE_QTY()));
            poAtpAbnormal.setPurchaser(vo.getPO_GROUP_R());
            poAtpAbnormal.setPoGroup(vo.getPO_GROUP());
            poAtpAbnormal.setSupplyName(vo.getSUPPLY());
            poAtpAbnormal.setEarliestPlanDate(vo.getUSE_DATE());
            poAtpAbnormal.setSureReplanDate(vo.getUSE_DATE());
            poAtpAbnormal.setFactory(vo.getPLANT());
            warningPoList.add(poAtpAbnormal);
        }
        // 获取来料异常处理结果
        List<EsbIncomeMaterialCheckAbnormalData> checkAbnormalDataList = new ArrayList<>();
        List<EsbGetPoGroupData> esbGetPoGroupDataList = new ArrayList<>();
        for (WarningIncomeMaterialPoAtpAbnormal poAtpAbnormal : warningPoList){
            if (!StrUtil.isEmpty(poAtpAbnormal.getPurchasePo())){
                EsbIncomeMaterialCheckAbnormalData checkAbnormalData = new EsbIncomeMaterialCheckAbnormalData();
                checkAbnormalData.setMATNR(poAtpAbnormal.getShortageMaterialId());
                checkAbnormalData.setWERKS(poAtpAbnormal.getFactory());
                checkAbnormalDataList.add(checkAbnormalData);
            }
            if (!StrUtil.isEmpty(poAtpAbnormal.getPoGroup())){
                EsbGetPoGroupData esbGetPoGroupData = new EsbGetPoGroupData();
                esbGetPoGroupData.setEKGRP(poAtpAbnormal.getPoGroup());
                esbGetPoGroupDataList.add(esbGetPoGroupData);
            }
        }
        //根据采购组查询工号
        List<EsbGetPoGroupData> uniqueList = esbGetPoGroupDataList.stream()
                .filter(egd -> !StrUtil.isEmpty(egd.getEKGRP())) // 确保 EKGRP 非空
                .distinct()
                .collect(Collectors.toList());
        List<EsbGetPoGroupData> poGroupData = esbDataFetchService.getPoGroupData(uniqueList);
        if (!poGroupData.isEmpty()){
            for (WarningIncomeMaterialPoAtpAbnormal poAtpAbnormal : warningPoList){
                for (EsbGetPoGroupData groupData : poGroupData){
                    if (poAtpAbnormal.getPoGroup().equals(groupData.getEKGRP())){
                        poAtpAbnormal.setPurchaserGh(groupData.getEKTEL());
                    }
                }
            }
        }
        List<EsbIncomeMaterialCheckAbnormalData> esbIncomeMaterialCheckAbnormalData = esbDataFetchService.incomeMaterialCheckAbnormal(checkAbnormalDataList);
        //esbIncomeMaterialCheckAbnormalData去掉合格的
        esbIncomeMaterialCheckAbnormalData = esbIncomeMaterialCheckAbnormalData.stream()
                .filter(data -> !"合格".equals(data.getKURZTEXT()))
                .collect(Collectors.toList());
        //将esbIncomeMaterialCheckAbnormalData 代码的短文本为空的字段值赋值为"待检"
        esbIncomeMaterialCheckAbnormalData.forEach(data -> {
            if (StrUtil.isEmpty(data.getKURZTEXT())){
                data.setKURZTEXT("待检");
            }
        });
        List<EsbIncomeMaterialCheckAbnormalData> collect = esbIncomeMaterialCheckAbnormalData.stream()
                .collect(Collectors.groupingBy(
                        data -> data.getWERKS() + "-" + data.getMATNR() + "-" + data.getKURZTEXT(),
                        Collectors.reducing(0, EsbIncomeMaterialCheckAbnormalData::getLOSMENGE, Integer::sum)
                ))
                .entrySet().stream()
                .map(entry -> {
                    String[] keys = entry.getKey().split("-");
                    String werks = keys[0];
                    String matnr = keys[1];
                    String kurztext = keys[2];
                    Integer totalLosmenge = entry.getValue();
                    EsbIncomeMaterialCheckAbnormalData mergedData = new EsbIncomeMaterialCheckAbnormalData();
                    mergedData.setWERKS(werks);
                    mergedData.setMATNR(matnr);
                    mergedData.setKURZTEXT(kurztext);
                    mergedData.setLOSMENGE(totalLosmenge);
                    return mergedData;
                })
                .collect(Collectors.toList());
        log.info("去sap查询来料异常返回条数：{}",  esbIncomeMaterialCheckAbnormalData.size());
        // 获取esbIncomeMaterialCheckAbnormalData的所有物料编码并去重
        List<String> materialIds = esbIncomeMaterialCheckAbnormalData.stream()
                .map(EsbIncomeMaterialCheckAbnormalData::getMATNR)
                .distinct()
                .collect(Collectors.toList());
        // 去掉warningPoList中具体欠料id不包含materialIds
        warningPoList = warningPoList.stream()
                .filter(poAtpAbnormal -> materialIds.contains(poAtpAbnormal.getShortageMaterialId()))
                .collect(Collectors.toList());
        // 通过采购凭证编号和采购凭证的项目编号匹配warningPoList 将检验批数量和代码的短文本回填
        List<WarningIncomeMaterialPoAtpAbnormal> warningAddList = new ArrayList<>();
        for (WarningIncomeMaterialPoAtpAbnormal poAtpAbnormal : warningPoList){
            for (EsbIncomeMaterialCheckAbnormalData data : collect){
                if (poAtpAbnormal.getShortageMaterialId().equals(data.getMATNR())){
                    poAtpAbnormal.setCheckQuantity(data.getLOSMENGE());
                    poAtpAbnormal.setDealResult(data.getKURZTEXT());
                    warningAddList.add(poAtpAbnormal);
                }
            }
        }
        Set<String> validDealResults = new HashSet<>(Arrays.asList("待检", "不合格", "退货", "报废"));
        Set<String> validOrderNos = warningAddList.stream()
                .filter(po -> validDealResults.contains(po.getDealResult()))
                .map(WarningIncomeMaterialPoAtpAbnormal::getOrderNo)
                .collect(Collectors.toSet());
        List<WarningIncomeMaterialPoAtpAbnormal> finalWarningPoList = warningAddList.stream()
                .filter(po -> validDealResults.contains(po.getDealResult()))
                .collect(Collectors.toList());
        log.info("通过采购凭证编号和采购凭证的项目编号匹配的采购单：{}", JSON.toJSONString(validOrderNos));
        warningList.removeIf(abnormal -> !validOrderNos.contains(abnormal.getOrderNo()));
        log.info("移除后剩的个数：{}", warningAddList.size());
        // 聚合工厂查询
        List<String> factoryList = finalWarningPoList.stream().map(po -> po.getFactory()).distinct().collect(Collectors.toList());
        Map<String, List<UserInfoDTO>> userinfoMap = userInfoService.listByFactoryCodes(factoryList);

        Map<String, LocalDate> orderNoToPlanDateMap = warningList.stream().filter(abnormal -> null != abnormal.getPlanDate())
                .collect(Collectors.toMap(
                        WarningIncomeMaterialAtpAbnormal::getOrderNo,
                        WarningIncomeMaterialAtpAbnormal::getPlanDate
                ));
        // 给finalWarningPoList的QualityInspectors和QualityInspectorsGh字段赋值，用stream()
        finalWarningPoList.forEach(po -> {
            po.setLightColor(calculateLightColor(orderNoToPlanDateMap.get(po.getOrderNo())));
            if (userinfoMap.containsKey(po.getFactory())) {
                List<UserInfoDTO> userInfos = userinfoMap.get(po.getFactory());
                po.setQualityInspectors(userInfos.stream().map(UserInfoDTO::getUserName).collect(Collectors.joining(",")));
                po.setQualityInspectorsGh(userInfos.stream().map(UserInfoDTO::getEmpNo).collect(Collectors.joining(",")));
            }
        });
        return finalWarningPoList;
    }
    @Override
    public WpsOrderWarningCategoryEnum getWarningCategory() {
        return WpsOrderWarningCategoryEnum.DEFAULT;
    }

    @Override
    public WpsOrderWarningTypeEnum getWarningType() {
        return WpsOrderWarningTypeEnum.MATERIAL_INSPECTION_ABNORMAL;
    }

}
