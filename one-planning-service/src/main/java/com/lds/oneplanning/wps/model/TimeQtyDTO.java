package com.lds.oneplanning.wps.model;

import com.google.common.collect.Maps;
import com.lds.oneplanning.common.utils.SapBizUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/5/20 17:38
 */

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class TimeQtyDTO {

    private float scheduleHours;

    private float totalHours;

    private Integer frozenStatus;

    private int qty;

    private int warningQty;
    
    @ApiModelProperty(value = "订单制程状态：1进行中 2 已完结")
    private Integer  orderProcessStatus = 1;

    /**
     * key 等级 1红 2黄 ，value 对应的数量
     */
    private Map<Integer,Integer> warningLevelMap = Maps.newConcurrentMap();


    public Number getLineCapacity(){
        if (totalHours==0) {
            return BigDecimal.ZERO;
        }
        return  new BigDecimal(scheduleHours * 100).divide(new BigDecimal(totalHours),2, RoundingMode.UP);
    }

    public Float getScheduleHours() {
        return SapBizUtils.roundToTwoDecimalPlaces(scheduleHours);
    }

    public Float getTotalHours() {
        return SapBizUtils.roundToTwoDecimalPlaces(totalHours);
    }
}
