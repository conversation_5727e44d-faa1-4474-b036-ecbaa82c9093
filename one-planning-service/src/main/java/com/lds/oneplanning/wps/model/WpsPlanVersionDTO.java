package com.lds.oneplanning.wps.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName wps_plan_version
 */
@Data
public class WpsPlanVersionDTO implements Serializable {
    /**
     * 
     */

    private Long id;

    /**
     * 计划员工号
     */
    private String plannerEmpNo;

    /**
     * 来源 1：保存  2：定时发布
     */
    private Integer source;

    /**
     * 版本
     */
    private String version;
    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 更新人id
     */
    private Long updateBy;
}