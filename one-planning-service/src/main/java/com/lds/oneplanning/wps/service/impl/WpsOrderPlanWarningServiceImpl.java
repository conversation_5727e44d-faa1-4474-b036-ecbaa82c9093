package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.OrderWarningLevelEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.mapper.WpsOrderPlanWarningMapper;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.vo.WpsOrderPlanWarningCountVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-28
 */
@Service
public class WpsOrderPlanWarningServiceImpl extends ServiceImpl<WpsOrderPlanWarningMapper, WpsOrderPlanWarning>
        implements IWpsOrderPlanWarningService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveUnHandlerWarning(List<WpsOrderPlanWarning> wpsOrderPlanWarningList) {
        if (CollectionUtils.isEmpty(wpsOrderPlanWarningList)) {
            return;
        }
        // 按 warningType 分组，收集 orderNo
        Map<String, Set<String>> waittingDeleteMap = wpsOrderPlanWarningList.stream()
                .collect(Collectors.groupingBy(
                        WpsOrderPlanWarning::getWarningType,
                        Collectors.mapping(WpsOrderPlanWarning::getOrderNo, Collectors.toSet())
                ));
        deleteOldWarning(waittingDeleteMap);
        this.saveBatch(wpsOrderPlanWarningList);
    }

    @Override
    public List<WpsOrderPlanWarning> listByOrderNosAndDates(List<String> orderNos, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(orderNos) || startDate == null || endDate == null) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectList(Wrappers.<WpsOrderPlanWarning>lambdaQuery()
                .in(WpsOrderPlanWarning::getOrderNo, orderNos)
                .between(WpsOrderPlanWarning::getScheduleDate, startDate, endDate)
                .eq(WpsOrderPlanWarning::getHandleStatus, OrderWarningHandleStatusEnum.UN_HANDLE.getCode()));
    }

    @Override
    public List<WpsOrderPlanWarning> listByWarningTypeAndOrderNoAndDate(List<String> orderNos, String warningType, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(orderNos) || StringUtils.isEmpty(warningType) || startDate == null || endDate == null) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectList(Wrappers.<WpsOrderPlanWarning>lambdaQuery()
                .in(WpsOrderPlanWarning::getOrderNo, orderNos)
                .eq(WpsOrderPlanWarning::getWarningType, warningType)
                .between(WpsOrderPlanWarning::getScheduleDate, startDate, endDate)
                .eq(WpsOrderPlanWarning::getHandleStatus, OrderWarningHandleStatusEnum.UN_HANDLE.getCode()));
    }

    @Override
    public void eliminateAlarms(WpsOrderWarningTypeEnum warningType) {
        switch (warningType) {
            case ATP_EXCEPTION:
                this.baseMapper.eliminateAtpAlarms();
                break;
            case MATERIAL_INSPECTION_ABNORMAL:
                this.baseMapper.eliminateAtpAlarmsLlyc();
                break;
            case FROZEN_UNFROZEN_WARNING:
                this.baseMapper.eliminateAtpAlarmsDjjd();
                break;
            case PROCESS_ROUTE_ABNORMAL:
                this.baseMapper.eliminateAtpAlarmsGylx();
                break;
            case DELIVERY_DATE_ABNORMAL:
                this.baseMapper.eliminateDeliveryDateAbnormalAlarms();
                break;
            case SHIP_BOOKING_URGENT:
                this.baseMapper.eliminateShipBookingUrgentAlarms();
                break;
            default:
                break;
        }
    }

    @Override
    public void eliminateAlarms(WpsOrderWarningTypeEnum warningType, List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return;
        }
        baseMapper.update(null, Wrappers.<WpsOrderPlanWarning>lambdaUpdate()
                .set(WpsOrderPlanWarning::getHandleStatus, OrderWarningHandleStatusEnum.CLOSED.getCode())
                .set(WpsOrderPlanWarning::getHandleTime, new Date())
                .eq(WpsOrderPlanWarning::getWarningType, warningType.getCode())
                .in(WpsOrderPlanWarning::getOrderNo, orderNoList)
        );
    }

    @Override
    public List<WpsOrderPlanWarningCountVO> countByOrderNos(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }
        List<WpsOrderPlanWarningCountVO> list = baseMapper.countByOrderNos(orderNos);
        list.forEach(e -> {
            e.setStatusEnum(OrderWarningHandleStatusEnum.getByCode(e.getHandleStatus()));
            e.setLevelEnum(OrderWarningLevelEnum.getByCode(e.getWarningLevel()));
        });
        return list;
    }

    private void deleteOldWarning(Map<String, Set<String>> waittingDeleteMap) {
        if (MapUtils.isEmpty(waittingDeleteMap)) {
            return;
        }
        waittingDeleteMap.forEach((warningType, orderNoSet) -> {
            if (CollectionUtils.isEmpty(orderNoSet)) {
                return;
            }
            this.baseMapper.delete(Wrappers.<WpsOrderPlanWarning>lambdaQuery()
                    .eq(WpsOrderPlanWarning::getWarningType, warningType)
                    .in(WpsOrderPlanWarning::getOrderNo, orderNoSet));
        });
    }
}