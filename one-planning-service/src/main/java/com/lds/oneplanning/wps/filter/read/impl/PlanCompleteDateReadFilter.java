
package com.lds.oneplanning.wps.filter.read.impl;

import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.esb.model.EsbOrderReq;
import com.lds.oneplanning.wps.filter.read.AbstractWpsOrderReadFilter;
import com.lds.oneplanning.wps.model.OrderPlanCompleteDTO;
import com.lds.oneplanning.wps.model.WpsRowData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @Description: 这是一个很奇葩的业务
 * @Author: zhuang<PERSON>ayin
 * @Email: zhuang<PERSON><EMAIL>
 * @Date: 2025/3/25 19:51
 */
@Slf4j
@Service
public class PlanCompleteDateReadFilter extends AbstractWpsOrderReadFilter {
    @Resource
    private IEsbDataFetchService dataFetchService;
    @Override
    public Integer filterSeq() {
        return 8;
    }
/*    protected List<WpsRowData> doFilter(Long userId, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag) {
        List<EsbOrderReq> esbOrderReqs = Lists.newArrayList();
        dirtyList.stream().filter(wpsRowData ->
                        StringUtils.isNotBlank(wpsRowData.getSellOrderNo()) && StringUtils.isNotBlank(wpsRowData.getRowItem()))
                .forEach(wpsRowData -> {
            EsbOrderReq orderReq = new EsbOrderReq();
            orderReq.setSellOrderNo(wpsRowData.getSellOrderNo());
            orderReq.setRowItemNo(wpsRowData.getRowItem());
            esbOrderReqs.add(orderReq);
        });
        List<OrderPlanCompleteDTO> orderPlanCompleteList = dataFetchService.fetchPlanCompleteDate(esbOrderReqs);
        orderPlanCompleteList = orderPlanCompleteList.stream()
                .filter(dto -> StringUtils.isBlank(dto.getDeliverNo()) && StringUtils.isNotBlank(dto.getDeliverRowItemNo()))
                .collect(Collectors.toList());
        if (orderPlanCompleteList.isEmpty()) {
            return dirtyList;
        }
        orderPlanCompleteList.sort(Comparator.comparing(OrderPlanCompleteDTO::getCompleteDate));
        for(OrderPlanCompleteDTO completeDTO  : orderPlanCompleteList){
            Integer deliverQty = completeDTO.getDeliverQty().intValue();
            LocalDate completeDate = completeDTO.getCompleteDate();
            String deliverNO = completeDTO.getDeliverNo();
            String deliverRowItemNo = completeDTO.getDeliverRowItemNo();
            Integer targetTotalPcs = dirtyList.stream().filter(wpsRowData ->!wpsRowData.isOutDeliverSetFlag()
                            && StringUtils.isNotBlank(wpsRowData.getSellOrderNo()) && StringUtils.isNotBlank(wpsRowData.getRowItem()))
                    .filter(wpsRowData -> wpsRowData.getSellOrderAndRowItemNo().equals(completeDTO.getSellOrderAndRowItemNo())).mapToInt(WpsRowData::getWaitingOrderQty).sum();
            if (targetTotalPcs < deliverQty ) {
                // 全部加起来都不够，跳过
                continue;
            }
            // 所有满足销售单号和行列好一样的数据，为待处理数据todoList，从中挑取若干凑数到总数大于单号要求的数量，列表放在targetList中
            List<WpsRowData> todoList = dirtyList.stream().filter(wpsRowData -> !wpsRowData.isOutDeliverSetFlag() && StringUtils.isNotBlank(wpsRowData.getSellOrderNo()) && StringUtils.isNotBlank(wpsRowData.getRowItem()))
                    .filter(wpsRowData -> wpsRowData.getSellOrderAndRowItemNo().equals(completeDTO.getSellOrderAndRowItemNo())).collect(Collectors.toList());

            // 存放加起来满足数量的行
            List<WpsRowData> targetList =  Lists.newArrayList();
            Integer tempCount = 0;
            for (WpsRowData rowData : todoList){
                tempCount= tempCount + rowData.getOrderPcsQty().intValue();
                if (tempCount >= deliverQty) {
                    // 数量已攒够
                    break;
                }
                // 命中，更新外向交货单 交货单行号 及完工日期
                rowData.setOutDeliveryNo(deliverNO);
                rowData.setOutRowItem(deliverRowItemNo);
                rowData.setCalculateFinishTime(completeDate);
                targetList.add(rowData);
            }
            // 对选中的数据 targetList 行，对对应的dirtyList中的数进行处理，通过outDeliverSetFlag标记行时否已经处理过
            Integer rowCount = targetList.size();
            for (WpsRowData wpsRowData : dirtyList){
                if (rowCount ==0) {
                    break;
                }
                boolean matchFlag =  !wpsRowData.isOutDeliverSetFlag() && StringUtils.isNotBlank(wpsRowData.getSellOrderNo()) && StringUtils.isNotBlank(wpsRowData.getRowItem());
                if (!matchFlag) {
                    continue;
                }
                // 取出的是 targetList中的元素
                WpsRowData changeRow = targetList.stream().filter(change -> change.getSellOrderAndRowItemNo().equals(wpsRowData.getSellOrderAndRowItemNo())).findFirst().orElse(null);
                if (changeRow != null) {
                    changeRow.setOutDeliverSetFlag(true);
                    // 更新外向交货单号行列好，设置标注已完成设置
                    wpsRowData.setOutDeliveryNo(changeRow.getOutDeliveryNo());
                    wpsRowData.setOutRowItem(changeRow.getOutRowItem());
                    wpsRowData.setCalculateFinishTime(changeRow.getCalculateFinishTime());
                    wpsRowData.setOutDeliverSetFlag(true);
                    rowCount-- ;
                }
            }
        }
        return dirtyList;
    }*/

    @Override
    protected List<WpsRowData> doFilter(Long userId, String factoryCode, List<WpsRowData> dirtyList, boolean cacheFlag) {
        if (dirtyList == null || dirtyList.isEmpty()) {
            return dirtyList;
        }

        // Step 1: 提取有效的订单请求
        List<EsbOrderReq> esbOrderReqs = dirtyList.stream()
                .filter(wpsRowData -> StringUtils.isNotBlank(wpsRowData.getSellOrderNo())
                        && StringUtils.isNotBlank(wpsRowData.getRowItem()))
                .map(this::convertToEsbOrderReq)
                .collect(Collectors.toList());

        if (esbOrderReqs.isEmpty()) {
            return dirtyList;
        }

        // Step 2: 获取计划完成数据
        List<OrderPlanCompleteDTO> orderPlanCompleteList = dataFetchService.fetchPlanCompleteDate(esbOrderReqs);
        if (orderPlanCompleteList == null || orderPlanCompleteList.isEmpty()) {
            return dirtyList;
        }

        // Step 3: 过滤掉无效项，并排序
        List<OrderPlanCompleteDTO> filteredList = orderPlanCompleteList.stream()
                .filter(dto -> StringUtils.isBlank(dto.getDeliverNo())
                        && StringUtils.isNotBlank(dto.getDeliverRowItemNo()))
                .sorted(Comparator.comparing(OrderPlanCompleteDTO::getCompleteDate))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) {
            return dirtyList;
        }
        // 计算数量分组
        Map<String,Integer> totalQtyMap = dirtyList.stream()
                .filter(wpsRowData -> !wpsRowData.isOutDeliverSetFlag()
                        && StringUtils.isNotBlank(wpsRowData.getSellOrderNo())
                        && StringUtils.isNotBlank(wpsRowData.getRowItem()))
                .collect(Collectors.groupingBy(WpsRowData::getSellOrderAndRowItemNo,Collectors.summingInt(WpsRowData::getWaitingOrderQty)));
        // 计算todoListMap
        Map<String,List<WpsRowData>> todoListMap =dirtyList.stream()
                .filter(wpsRowData -> !wpsRowData.isOutDeliverSetFlag()
                        && StringUtils.isNotBlank(wpsRowData.getSellOrderNo())
                        && StringUtils.isNotBlank(wpsRowData.getRowItem()))
                .collect(Collectors.groupingBy(WpsRowData::getSellOrderAndRowItemNo));


        // Step 4: 处理每个 OrderPlanCompleteDTO
        List<WpsRowData> toMatchList = Lists.newArrayList();
        for (OrderPlanCompleteDTO dto : filteredList) {
            List<WpsRowData> matchData = getFixTodoList(dto,totalQtyMap,todoListMap);
            if (matchData.isEmpty()) {
                continue;
            }
            toMatchList.addAll(matchData);
        }
        if (toMatchList.isEmpty()) {
            return dirtyList;
        }
        // STEP 5 同步更新原始 dirtyList 中对应对象的状态（如果有必要）
        for (WpsRowData dirty : dirtyList) {
            this.modifyField(dirty,toMatchList);
        }
        return dirtyList;
    }

    private void  modifyField(WpsRowData dirty,List<WpsRowData> toMatchList){
        for (WpsRowData match : toMatchList){
            if (!match.getSellOrderAndRowItemNo().equals(dirty.getSellOrderAndRowItemNo())) {
                continue;
            }
            dirty.setOutDeliveryNo(match.getOutDeliveryNo());
            dirty.setOutRowItem(match.getOutRowItem());
            dirty.setCalculateFinishTime(match.getCalculateFinishTime());
            dirty.setOutDeliverSetFlag(true);
        }
    }

    // 将 WpsRowData 转换为 EsbOrderReq
    private EsbOrderReq convertToEsbOrderReq(WpsRowData rowData) {
        EsbOrderReq req = new EsbOrderReq();
        req.setSellOrderNo(rowData.getSellOrderNo());
        req.setRowItemNo(rowData.getRowItem());
        return req;
    }

    // 对每一个 plan DTO 执行匹配和赋值逻辑
    private List<WpsRowData> getFixTodoList(OrderPlanCompleteDTO dto, Map<String,Integer> totalQtyMap,Map<String,List<WpsRowData>> todoListMap) {
        Integer deliverQty = dto.getDeliverQty().intValue();
        LocalDate completeDate = dto.getCompleteDate();
        String deliverNo = dto.getDeliverNo();
        String deliverRowItemNo = dto.getDeliverRowItemNo();
        String sellOrderAndRowItemNo = dto.getSellOrderAndRowItemNo();
        // 计算当前总数量
        int totalPcs = totalQtyMap.getOrDefault(sellOrderAndRowItemNo,0);
        if (totalPcs < deliverQty) {
            // 不够用，跳过
            return Lists.newArrayList();
        }
        // 收集待处理列表
        List<WpsRowData> todoList =  todoListMap.getOrDefault(sellOrderAndRowItemNo,Lists.newArrayList());
        // 累加满足数量的数据
        List<WpsRowData> targetList = new ArrayList<>();
        int tempCount = 0;

        for (WpsRowData rowData : todoList) {
            tempCount += rowData.getOrderPcsQty();
            targetList.add(rowData);
            if (tempCount >= deliverQty) break;
        }

        // 更新标记和字段
        targetList.forEach(row -> {
            row.setOutDeliveryNo(deliverNo);
            row.setOutRowItem(deliverRowItemNo);
            row.setCalculateFinishTime(completeDate);
            row.setOutDeliverSetFlag(true);
        });
        return targetList;
    }

}
