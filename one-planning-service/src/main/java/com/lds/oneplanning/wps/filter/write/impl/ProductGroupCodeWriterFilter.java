package com.lds.oneplanning.wps.filter.write.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.entity.ProductGroup;
import com.lds.oneplanning.basedata.entity.ProductGroupRel;
import com.lds.oneplanning.basedata.service.IProductGroupRelService;
import com.lds.oneplanning.basedata.service.IProductGroupService;
import com.lds.oneplanning.wps.filter.write.AbstractWpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/31 10:53
 */

@Slf4j
@Service
public class ProductGroupCodeWriterFilter extends AbstractWpsOrderWriteFilter {

    @Resource
    private IProductGroupService productGroupService;
    @Resource
    private IProductGroupRelService productGroupRelService;
    @Override
    public Integer filterSeq() {
        return 2;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, Integer datasource, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag, Map<String,Object> params) {
        // 产品组的排序，排序的时候优先级高的放后面
        List<ProductGroup> productGroupEntities = productGroupService.list(Wrappers.<ProductGroup>lambdaQuery().eq(ProductGroup::getFactoryCode,factoryCode));
        Set<String> productGroupCodes = productGroupEntities.stream().map(ProductGroup::getCode).collect(Collectors.toSet());
        if (productGroupCodes.isEmpty()) {
            return dirtyList;
        }
        //for high performance
        List<ProductGroupRel> relList = productGroupRelService.listByGroupCodes(productGroupCodes);
        Map<String,Set<String>> productIdsMap = relList.stream().collect(Collectors.groupingBy(ProductGroupRel::getProductGroupCode,Collectors.mapping(ProductGroupRel::getProductId,Collectors.toSet())));
        Map<String,String> productGroupNameMap = productGroupEntities.stream().collect(Collectors.toMap(ProductGroup::getCode,ProductGroup::getName,(s, s2) -> s2));
        dirtyList.forEach(wpsRowData -> {
            wpsRowData.setProductGroupCode(this.getProductGroupCode(productGroupCodes,wpsRowData,productIdsMap));
            wpsRowData.setProductGroupName(wpsRowData.getProductGroupCode()==null ? null :productGroupNameMap.get(wpsRowData.getProductGroupCode()));
        });
        return dirtyList;
    }

    private String getProductGroupCode(Set<String> productGroupCodes,WpsRowData wpsRowData,Map<String,Set<String>> productIdsMap){
        for (String productGroupCode  : productGroupCodes){
            Set<String> productIds = Optional.ofNullable(productIdsMap.get(productGroupCode)).orElse(Sets.newHashSet());
            if ((wpsRowData.getProductId() != null && this.containsCommonElement(productIds, Arrays.asList(wpsRowData.getProductId().split(";"))))
                    || ( wpsRowData.getCommodityId() !=null && this.containsCommonElement(productIds, Arrays.asList(wpsRowData.getCommodityId().split(";")))) ) {
                return productGroupCode;
            }
        }
        //        都没有匹配成功
        return null;
    }


    private  boolean containsCommonElement(Set<String> sourceSet, List<String> list2) {
        for (String item : list2) {
            if (sourceSet.contains(item)) {
                return true;
            }
        }
        return false;
    }


}
