package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.google.common.collect.Maps;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.enums.SchedulePlanErrorCodeEnum;
import com.lds.oneplanning.wps.helper.WpsSchedulePlanLogHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.IntStream;

@Slf4j
@Component
public class WpsOrderSuitableLineCodesHandler implements IWpsAutoScheduleHandler {

    @Autowired
    private IWpsOrderCommonService wpsOrderCommonService;
    @Autowired
    private WpsSchedulePlanLogHelper wpsSchedulePlanLogHelper;
    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        fillSuitableLineCodes(context);
    }

    private void fillSuitableLineCodes(WpsAutoScheduleContext context) {
        int minPrioritySeq = WpsConstants.MIN_PRODUCT_GROUP_PRIORITY_SEQ;
        int maxPrioritySeq = WpsConstants.MAX_PRODUCT_GROUP_PRIORITY_SEQ;
        context.getOrderList().forEach(order -> {
            IntStream.rangeClosed(minPrioritySeq, maxPrioritySeq)
                    .forEach(prioritySeq -> {
                        Set<String> suitableLineUuids = wpsOrderCommonService.getSuitableLineUuids(context, order, prioritySeq);
                        if (CollectionUtils.isNotEmpty(suitableLineUuids)) {
                            addSuitableLineUuidsToContext(context, order, prioritySeq, suitableLineUuids);
                            log.info("WPS排产,fillSuitableLineCodes orderNo:{}, prioritySeq:{}, suitableLineUuids:{}",
                                    order.getOrderNo(), prioritySeq, suitableLineUuids);
                        }
                    });
            if(MapUtils.isEmpty(context.getOrderLinePriorityMap().get(order.getOrderNo()))){
                context.getSchedulePlanLogMap().putAll(wpsSchedulePlanLogHelper.createWpsSchedulePlanLog(order, null, SchedulePlanErrorCodeEnum.SCHEDULE_PRIORITY_EMPTY,context));
            }
        });
    }

    private void addSuitableLineUuidsToContext(WpsAutoScheduleContext context, WpsRowData order, int prioritySeq, Set<String> suitableLineUuids) {
        context.getOrderLinePriorityMap()
                .computeIfAbsent(order.getOrderNo(), k -> Maps.newHashMap())
                .put(prioritySeq, suitableLineUuids);
    }

    @Override
    public int getOrder() {
        return 4;
    }

    @Override
    public int getRound() {
        return 1;
    }
}
