package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.wps.entity.WpsPublishedOrder;
import com.lds.oneplanning.wps.mapper.WpsPublishedOrderMapper;
import com.lds.oneplanning.wps.model.WpsDateRange;
import com.lds.oneplanning.wps.service.IWpsPublishedOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class WpsPublishedOrderServiceImpl extends ServiceImpl<WpsPublishedOrderMapper, WpsPublishedOrder> implements IWpsPublishedOrderService {

    @Override
    public List<WpsPublishedOrder> listByBizIdsAndPublishTargetAndDates(List<String> bizIds, String publishTarget,
                                                                        LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(bizIds) || StringUtils.isEmpty(publishTarget) || null == startDate || null == endDate) {
            return Lists.newArrayList();
        }
        return this.baseMapper.selectList(new QueryWrapper<WpsPublishedOrder>()
                .lambda()
                .in(WpsPublishedOrder::getBizId, bizIds)
                .eq(WpsPublishedOrder::getPublishTarget, publishTarget)
                .le(WpsPublishedOrder::getStartDate, startDate)
                .ge(WpsPublishedOrder::getEndDate, endDate)
        );
    }

    @Override
    public Map<String, List<WpsDateRange>> getMapByBizIdsAndPublishTargetAndDates(List<String> bizIds, String publishTarget,
                                                                                  LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(bizIds) || StringUtils.isEmpty(publishTarget)) {
            return Maps.newHashMap();
        }
        Objects.requireNonNull(startDate, "startDate cannot be null");
        Objects.requireNonNull(endDate, "endDate cannot be null");
        List<WpsPublishedOrder> wpsPublishedOrders = this.listByBizIdsAndPublishTargetAndDates(bizIds, publishTarget, startDate, endDate);
        if (CollectionUtils.isEmpty(wpsPublishedOrders)) {
            return Maps.newHashMap();
        }
        return wpsPublishedOrders.stream()
                .collect(Collectors.groupingBy(
                        WpsPublishedOrder::getBizId,
                        Collectors.mapping(
                                order -> WpsDateRange.builder().startDate(startDate).endDate(endDate).build(),
                                Collectors.toList()
                        )
                ));
    }
}
