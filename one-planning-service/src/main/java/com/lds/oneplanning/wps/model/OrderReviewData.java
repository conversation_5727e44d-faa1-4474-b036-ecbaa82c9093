package com.lds.oneplanning.wps.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/5/21 11:13
 */
@Data
public class OrderReviewData {

    @ApiModelProperty(value = "订单号-订单接口-ZDDH")
    private String orderNo;

    @ApiModelProperty(value = "销售订单号-订单接口-VBELN")
    private String sellOrderNo;
    @ApiModelProperty(value = "行项目-订单接口-订单接口-POSNR")
    private String rowItem;
    @ApiModelProperty(value = "业务留样数量")
    private Integer bizSampleQty;
    @ApiModelProperty(value = "品保留样数量")
    private Integer qcSampleQty;
    @ApiModelProperty(value = "是否验货")
    private String isInspect;
    @ApiModelProperty(value = "包装方式")
    private String packageType;
    @ApiModelProperty(value = "om负责人-评审接口")
    private String omRespons;
    @ApiModelProperty(value = "原始计划验货日期")
    private Date originalInspectTime;
    @ApiModelProperty(value = "原始计划装柜日期")
    private Date originalLoadTime;
    @ApiModelProperty(value = "原始船期")
    private Date originalShipTime;
    @ApiModelProperty(value = "原始完工日期")
    private Date originalFinishTime;


}
