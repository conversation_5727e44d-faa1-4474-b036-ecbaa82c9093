package com.lds.oneplanning.wps.helper;

import com.google.common.collect.Lists;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.esb.model.AtpLackOffMaterialRespVO;
import com.lds.oneplanning.po.config.PoCallOffOrderProperties;
import com.lds.oneplanning.wps.utils.MockDataGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 封装exb接口调用
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class EsbApiWrapper {
    /**
     * 注入PoCallOffOrderProperties配置属性
     */
    private final PoCallOffOrderProperties poCallOffOrderProperties;

    /**
     * 注入EsbDataFetchService服务
     */
    private final EsbDataFetchService esbDataFetchService;

    /**
     * 获取ATP检查列表
     *
     * @param orderNoList 订单号列表
     * @return ATP缺少物料响应列表
     */
    public List<AtpLackOffMaterialRespVO> fetchAtpCheckList(Collection<String> orderNoList) {
        // 分批处理，以免数据量太多
        return Lists.partition(new ArrayList<>(orderNoList), 1000)
                .stream()
                .map(this::queryAtpList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private List<AtpLackOffMaterialRespVO> queryAtpList(List<String> list) {
        if (poCallOffOrderProperties.isDebug()) {
            // 调试，调用生产的接口查询
            return MockDataGenerator.fetchFromProd(list, "/esb/mock/fetchAtpCheckListBatch", AtpLackOffMaterialRespVO.class);
        } else {
            return esbDataFetchService.fetchAtpCheckListWithCache(list);
        }
    }
}
