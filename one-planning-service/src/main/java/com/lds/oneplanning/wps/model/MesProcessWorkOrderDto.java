package com.lds.oneplanning.wps.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="在制工单主表信息", description="")
public class MesProcessWorkOrderDto implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "排产日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date schedulingDate;

    @ApiModelProperty(value = "物料下架日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date materialOffShelfDate;

    @ApiModelProperty(value = "上线日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date onlineDate;

    @ApiModelProperty(value = "生产车间")
    private String productionWorkshop;

    @ApiModelProperty(value = "生产课长")
    private String productionForeman;

    @ApiModelProperty(value = "生产线体")
    private String productionLine;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;


    @ApiModelProperty(value = "关联订单号")
    private String associatedOrderNo;

    @ApiModelProperty(value = "计划数量")
    private Integer plannedQuantity;

    @ApiModelProperty(value = "在制天数")
    private Integer productionDays;

    @ApiModelProperty(value = "实际投入数量")
    private Integer actualInputQuantity;

    @ApiModelProperty(value = "实际报工数量")
    private Integer actualReportingQuantity;

    @ApiModelProperty(value = "排产场景（0:整机，1:组件，2：部件）")
    private Integer planType;

    @ApiModelProperty(value = "线体uuid")
    private String lineUuid;

    @ApiModelProperty(value = "线体编码")
    private String lineCode;


    @ApiModelProperty(value = "生产课长工号")
    private String foremanGh;

    @ApiModelProperty(value = "线长工号")
    private String lineLeaderGh;

    @ApiModelProperty(value = "线长名称")
    private String lineLeaderName;

    @ApiModelProperty(value = "计划人员-工号")
    private String plannerEmpNo;

    @ApiModelProperty(value = "计划人员-名称")
    private String planner;

    @ApiModelProperty(value = "在制工单andon信息")
    List<MesProcessWorkOrderAndoDto> andoList;

    @ApiModelProperty(value = " 在制工单多道工序表")
    List<MesProcessWorkOrderProcedurDto> rocedurList;


}
