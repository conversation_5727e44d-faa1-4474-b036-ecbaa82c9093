package com.lds.oneplanning.wps.job;

import com.lds.coral.job.annotation.JobRegister;
import com.lds.oneplanning.wps.service.IWarningIncomeMaterialPoAtpAbnormalService;
import com.lds.oneplanning.wps.service.WarningMaterialAtpAbnormalShortageService;
import com.lds.oneplanning.wps.utils.TraceIdUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 一个小时更新来料异常信息
 *
 * <AUTHOR>
 * @since 2025/6/3
 */
@JobRegister(value = "IncomeMaterialAtpAbnormalJobHandler", jobName = "IncomeMaterialAtpAbnormalJobHandler", cron = "0 0/1 * * * ?")
@Component
@Slf4j
public class IncomeMaterialAtpAbnormalJobHandler extends IJobHandler {
    @Resource
    private IWarningIncomeMaterialPoAtpAbnormalService poAtpAbnormalService;

    @Override
    public ReturnT<String> execute(String s) {
        ReturnT<String> returnInfo = SUCCESS;
        String id = TraceIdUtils.setTraceId();
        XxlJobLogger.log("开始执行: " + id);
        poAtpAbnormalService.updateIncomePoInfo();
        log.info("---------------------------10秒执行一次----------------");
        return returnInfo;
    }

}
