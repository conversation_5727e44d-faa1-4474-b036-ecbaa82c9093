package com.lds.oneplanning.wps.filter.read;

import com.lds.oneplanning.wps.model.WpsRowData;

import java.util.List;

/**
 * @Description: 订单过滤
 * @Author: zhuang<PERSON><PERSON>in
 * @Email: zhuang<PERSON><PERSON><EMAIL>
 * @Date: 2025/3/25 17:09
 */
public interface WpsOrderReadFilter {

    Integer filterSeq();

    /**
     * 直接返回下个对象，方便链式操作
     * @param wpsOrderReadFilter
     * @return
     */
    WpsOrderReadFilter setNext(WpsOrderReadFilter wpsOrderReadFilter);

    List<WpsRowData> filter(Long userId,String factoryCode,List<WpsRowData> dirtyList,boolean cacheFlag);

}
