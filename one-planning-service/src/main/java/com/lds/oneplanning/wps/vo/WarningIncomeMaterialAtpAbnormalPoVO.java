package com.lds.oneplanning.wps.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
@Data
@ApiModel(value="采购PO待办表单", description="采购PO待办表单")
@TableHeader(type = WpsOrderWarningTypeEnum.MATERIAL_INSPECTION_ABNORMAL, source = ViewSource.MC)
public class WarningIncomeMaterialAtpAbnormalPoVO  {

//    @ApiModelProperty(value = "生产订单")
//    private String orderNo;

    @ApiModelProperty(value = "灯色")
    private LightColor color;

    @ApiModelProperty(value = "物料id")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "采购合同")
    private String purchaseContract;

    @ApiModelProperty(value = "行项目")
    private String purchaseHxm;

    @ApiModelProperty(value = "计划数量")
    private Integer planQuantity;

    @ApiModelProperty(value = "检验结果")
    private String checkResult;

    @ApiModelProperty(value = "交付采购")
    private String deliveryProcurement;

    @ApiModelProperty(value = "处理结果")
    private String dealResult;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "下一批最快到货时间")
    private String nextEarliestDhsj;

    private OrderWarningHandleStatusEnum processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

}