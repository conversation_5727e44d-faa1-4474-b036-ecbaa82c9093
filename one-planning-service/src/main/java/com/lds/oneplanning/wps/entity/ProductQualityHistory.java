package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/6/5
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ProductQualityHistory对象", description="")
public class ProductQualityHistory implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 五级产品线
     */
    private String wjcpx;

    /**
     * 初步原因分析
     */
    private String cbyyfx;

    /**
     * 申请日期
     */
    private String sqrq;

    /**
     * 产生对策
     */
    private String csdc;

    /**
     * 申请人工号
     */
    private String creatorcode;

    /**
     * 申请人姓名
     */
    private String sqr;

    /**
     * 负责人工号
     */
    private String zrrcode;

    /**
     * 品质负责人
     */
    private String zrr;

    /**
     * 效果验证
     */
    private String xgyz;

    /**
     * T812环节最后提交人员工号
     */
    private String lastcode;

    /**
     * 产品id
     */
    private String cpid;

    /**
     * 流程编号
     */
    private String bh;

    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者id
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

}
