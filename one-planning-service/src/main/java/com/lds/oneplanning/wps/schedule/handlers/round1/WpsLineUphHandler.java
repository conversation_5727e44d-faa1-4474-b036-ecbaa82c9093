package com.lds.oneplanning.wps.schedule.handlers.round1;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 填充订单可匹配的产线类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsLineUphHandler implements IWpsAutoScheduleHandler {

    @Autowired
    private IWpsOrderCommonService wpsOrderCommonService;

    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<String> lineUuids = context.getLineUuids();
        if (CollectionUtils.isEmpty(lineUuids)) {
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("fillOrderWaitingOrderQtyAndProductIds");
        fillOrderWaitingOrderQtyAndProductIds(context);
        stopWatch.stop();
        stopWatch.start("getOrderLineUphMap");
        Map<String, Map<String, Float>> orderLineUphMap = getOrderLineUphMap(context, lineUuids);
        stopWatch.stop();
        log.info("WpsLineUphHandler 执行耗时：{} ms.",stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        if (MapUtils.isEmpty(orderLineUphMap)) {
            return;
        }
        context.setOrderLineUphMap(orderLineUphMap);
        log.info("WPS排产,订单UPH列表，userId：{},orderLineUphMap:{}.", context.getUserId(),
                JSON.toJSONString(orderLineUphMap));
    }

    private void fillOrderWaitingOrderQtyAndProductIds(WpsAutoScheduleContext context) {
        Set<String> productIds = new HashSet<>();
        Set<String> commodityIds = new HashSet<>();
        context.getOrderList().stream()
                .filter(order -> order.getWaitingOrderQty() > 0)
                .forEach(order -> {
                    Optional.ofNullable(order.getProductId()).ifPresent(productIds::add);
                    Optional.ofNullable(order.getCommodityId()).ifPresent(commodityIds::add);
                });
        context.setProductIds(new ArrayList<>(productIds));
        context.setCommodityIds(new ArrayList<>(commodityIds));
    }

    /**
     * 获取订单可匹配的产线类
     *
     * @param context
     * @param lineUuids
     * @return Map<orderNo, Map <lineUuid, uph>>
     */
    private Map<String, Map<String, Float>> getOrderLineUphMap(WpsAutoScheduleContext context, List<String> lineUuids) {
        Map<String, Map<String, Float>> orderLineUphMap = Maps.newHashMap();
        List<String> productIds = context.getProductIds();
        List<String> commodityIds = context.getCommodityIds();
        if (CollectionUtils.isEmpty(productIds) && CollectionUtils.isEmpty(commodityIds)) {
            return orderLineUphMap;
        }
        Set<String> uphIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(productIds)) {
            uphIdSet.addAll(productIds);
        }
        if (CollectionUtils.isNotEmpty(commodityIds)) {
            uphIdSet.addAll(commodityIds);
        }
        Map<String, List<String>> lineProductIdMap = buildLineProductIdMap(new ArrayList<>(uphIdSet), lineUuids);
        Map<String, Map<String, Float>> lineProductUphMap = wpsOrderCommonService.getLineProductUphMap(lineProductIdMap);
        if (MapUtils.isEmpty(lineProductUphMap)) {
            return orderLineUphMap;
        }
        context.getOrderList().forEach(order -> {
            String orderNo = order.getOrderNo();
            int waitingOrderQty = order.getWaitingOrderQty();
            if (waitingOrderQty > 0) {
                lineUuids.forEach(lineUuid -> {
                    Map<String, Float> lineUphMap = lineProductUphMap.get(lineUuid);
                    if (MapUtils.isEmpty(lineUphMap)) {
                        return;
                    }
                    // 先使用productId填充
                    Optional.ofNullable(order.getProductId())
                            .filter(StringUtils::isNotEmpty)
                            .map(lineUphMap::get)
                            .filter(uph -> uph > 0)
                            .ifPresent(uph -> orderLineUphMap
                                    .computeIfAbsent(orderNo, k -> new HashMap<>())
                                    .put(lineUuid, uph));
                    // commodityId优先级比较高，如果存在就覆盖productId的值
                    Optional.ofNullable(order.getCommodityId())
                            .filter(StringUtils::isNotEmpty)
                            .map(lineUphMap::get)
                            .filter(uph -> uph > 0)
                            .ifPresent(uph -> orderLineUphMap
                                    .computeIfAbsent(orderNo, k -> new HashMap<>())
                                    .put(lineUuid, uph));
                });
            }
        });
        return orderLineUphMap;
    }

    private Map<String, List<String>> buildLineProductIdMap(List<String> productIds, List<String> lineUuids) {
        return lineUuids.stream()
                .collect(Collectors.toMap(
                        lineUuid -> lineUuid,
                        lineUuid -> new ArrayList<>(new HashSet<>(productIds))
                ));
    }

    @Override
    public int getOrder() {
        return 1;
    }

    @Override
    public int getRound() {
        return 1;
    }
}