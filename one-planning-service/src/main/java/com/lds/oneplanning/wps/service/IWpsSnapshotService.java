package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WpsPlanVersion;
import com.lds.oneplanning.wps.entity.WpsSnapshot;
import com.lds.oneplanning.wps.enums.WpsPlanSourceEnum;
import com.lds.oneplanning.wps.model.WpsRowData;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【wps_snapshot】的数据库操作Service
* @createDate 2025-05-08 11:38:52
*/
public interface IWpsSnapshotService extends IService<WpsSnapshot> {
    /**
     * 根据版本删除快照
     * @param planVersionId
     */
    void deleteByPlanVersionId(Long planVersionId);

    /**
     * 保存快照
     * @param wpsRowDatas
     * @param wpsPlanVersion
     */
    void saveSnapshot(List<WpsRowData> wpsRowDatas, WpsPlanVersion wpsPlanVersion);

    /**
     * 查询快照
     * @param plannerNo
     * @param factoryCode
     * @param version
     * @param source
     * @return
     */
    List<WpsRowData> findList(String plannerEmpNo, String factoryCode, String version,List<String> bizIds, WpsPlanSourceEnum source);

    /**
     * 时间区间前闭后开，开始结束日期不带时分秒，指定日期时需要自己考虑边界，如一般情况下endDate需要往后延一天
     * 并需要按照订单去重，取最新的数据
     * @param factoryCode
     * @param source
     * @param startDate
     * @param endDate
     * @return
     */
    List<WpsRowData> listByCreateTime(@NotNull String factoryCode, WpsPlanSourceEnum source, @NotNull LocalDate startDate, @NotNull LocalDate endDate);
}
