package com.lds.oneplanning.wps.filter.read.impl;

import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.enums.OrderSubTypeEnum;
import com.lds.oneplanning.basedata.service.IFactoryOrderCfgService;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.basedata.service.IPlannerOrderCfgService;
import com.lds.oneplanning.wps.filter.read.AbstractWpsOrderReadFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * @Description:
 * @Author: zhuang<PERSON><PERSON>in
 * @Email: zhuang<PERSON><PERSON><EMAIL>
 * @Date: 2025/3/25 17:23
 */
@Slf4j
@Service
public class ProductOrderSubTypeReadFilter extends AbstractWpsOrderReadFilter {
    @Resource
    private IPlannerOrderCfgService plannerOrderCfgService;
    @Resource
    private IFactoryOrderCfgService factoryOrderCfgService;
    @Resource
    private IPlannerBaseService plannerBaseService;
    @Override
    public Integer filterSeq() {
        return 2;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag) {
        String empNo = plannerBaseService.getEmpNoByUserId(userId);
        // 1、计划员特殊订单配置优先
        Set<String> productOrderTypeCodes = plannerOrderCfgService.listOrderSubTypeByEmpNo(empNo, BaseDataConstant.BUSINESS_TYPE_SUB_ORDER);
        if (CollectionUtils.isEmpty(productOrderTypeCodes)) {
            //  2、若无，使用工厂特殊订单的配置
            productOrderTypeCodes = factoryOrderCfgService.listOrderSubTypeByFactoryCode(factoryCode, BaseDataConstant.BUSINESS_TYPE_SUB_ORDER);
        }
        // 3、若都没有配置，默认添加标准生产订单
        productOrderTypeCodes =  CollectionUtils.isEmpty(productOrderTypeCodes)? Sets.newHashSet(OrderSubTypeEnum.STD.getCode()) : productOrderTypeCodes;
        Set<String> finalProductOrderTypeCodes = productOrderTypeCodes;

        dirtyList.removeIf(wpsRowData -> "生产订单".equals(wpsRowData.getOrderType()) && StringUtils.isNotBlank(wpsRowData.getProductOrderTypeCode())
                &&  !finalProductOrderTypeCodes.contains(wpsRowData.getProductOrderTypeCode()));
/*        List<WpsRowData> threadSafeList = new CopyOnWriteArrayList<>(dirtyList);
        threadSafeList.removeIf(wpsRowData -> "生产订单".equals(wpsRowData.getOrderType()) && StringUtils.isNotBlank(wpsRowData.getProductOrderTypeCode())
                &&  !finalProductOrderTypeCodes.contains(wpsRowData.getProductOrderTypeCode()));*/
        return dirtyList;
    }


}
