package com.lds.oneplanning.wps.filter.write.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.entity.Workshop;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.basedata.service.IWorkshopService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.filter.write.AbstractWpsOrderWriteFilter;
import com.lds.oneplanning.wps.helper.WpsOrderSortHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.WpsAutoSchedulePipeline;
import com.lds.oneplanning.wps.utils.WpsTransUtils;
import com.lds.oneplanning.wps.warning.auto.WpsAutoPlanWarningPipeline;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 自动排产回填
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/26 15:38
 */
@Slf4j
@Service
public class AutoScheduleWriteFilter extends AbstractWpsOrderWriteFilter {

    @Resource
    private WpsOrderSortHelper wpsOrderSortHelper;
    @Resource
    private WpsAutoSchedulePipeline wpsAutoSchedulePipeline;
    @Resource
    private IPlannerBaseService plannerBaseService;
    @Resource
    private WpsAutoPlanWarningPipeline wpsAutoPlanWarningPipeline;
    @Resource
    private ILineInfoService lineInfoService;
    @Resource
    private IWorkshopService workshopService;


    @Override
    public Integer filterSeq() {
        return 5;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, Integer datasource, String factoryCode, List<WpsRowData> wpsRowDatas,boolean cacheFlag, Map<String,Object> params) {
        if (!WpsConstants.DATA_SOURCE_AUTO.equals(datasource)) {
            //非自动排产 直接返回
            return wpsRowDatas;
        }
        List<WpsRowData> resList = Lists.newArrayList();
        WpsAutoScheduleContext context;
        // 自动排产前进行排序
        wpsOrderSortHelper.sortBeforeAutoSchedule(wpsRowDatas);
        context = this.runWpsAutoSchedulePipeline(userId, wpsRowDatas, factoryCode,params);

        resList.addAll(this.setWpsAutoSchedulePipeline(userId, wpsRowDatas, context,factoryCode));
        context.setOrderList(resList);
        // 自动排产预警处理 先改异步测试 之后返回
        try {
            wpsAutoPlanWarningPipeline.execute(context);
        }catch (Exception e){
            log.error("AutoScheduleWriteFilter执行wpsAutoPlanWarningPipeline.execute 异常 msg={}",e.getMessage(),e);
        }
        return resList;
    }

    private WpsAutoScheduleContext runWpsAutoSchedulePipeline(Long userId, List<WpsRowData> wpsRowDatas, String factoryCode,Map<String,Object> params) {
        WpsAutoScheduleContext context = new WpsAutoScheduleContext(wpsRowDatas);
        context.setCurrentFactoryCode(factoryCode);
        context.setUserId(userId);
        context.setCurrentDate(LocalDate.now());
        context.setWeeksToPush(12);
        if(MapUtils.isNotEmpty(params) && params.containsKey(WpsConstants.IS_SAVE_SCHEDULE_PLANLOG_PARAM)){
            Boolean flag = (Boolean) params.get(WpsConstants.IS_SAVE_SCHEDULE_PLANLOG_PARAM);
            context.setIsSaveSchedulePlanLog(flag);
        }
        wpsAutoSchedulePipeline.execute(context);
        return context;
    }

    private List<WpsRowData> setWpsAutoSchedulePipeline(Long userId,List<WpsRowData> wpsRowDatas,WpsAutoScheduleContext context,String factoryCode){
        List<WpsRowData> resList = Lists.newArrayList();
        List<LineInfo> lineInfos = lineInfoService.list();
        List<Workshop> workshops = workshopService.list(Wrappers.<Workshop>lambdaQuery().eq(Workshop::getFactoryCode,factoryCode));
        Map<String,String> workshoeNameMap = workshops.stream().collect(Collectors.toMap(Workshop::getCode,Workshop::getName,(s, s2) -> s2));
        for (WpsRowData wpsRowData : wpsRowDatas){
            resList.addAll(wpsOrderSortHelper.listAutoScheduleData(context.getOrderDailyScheduleDataMap(), wpsRowData,lineInfos,workshoeNameMap));
        }
        // 设置上线日期 也设置当前用户为主计划
        String plannerName = plannerBaseService.getNameNoByUserId(userId);
        String empNo = plannerBaseService.getEmpNoByUserId(userId);
        resList.stream().forEach(wpsRowData -> {
            wpsRowData.setMainPlan(plannerName);
            wpsRowData.setPlannerEmpNo(empNo);
            wpsRowData.setOnlineTime(LocalDateTimeUtil.localDateToDate(WpsTransUtils.getStartScheduleDate(wpsRowData.getScheduleDataMap())));
        });
        return resList;
    }

}
