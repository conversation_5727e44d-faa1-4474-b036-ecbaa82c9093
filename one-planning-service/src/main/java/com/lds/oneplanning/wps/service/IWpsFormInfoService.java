package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WpsFormInfo;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-27
 */
public interface IWpsFormInfoService extends IService<WpsFormInfo> {

    WpsFormInfo getByBizId(String bizId);

    List<WpsFormInfo> listByBizIds(Collection<String> bizIds);

    void saveOrUpdateBatchByBizId(List<WpsFormInfo> wpsFormInfos);

    void synReportedQtyData();

    void updateReportedByUserId(Long userId,String factoryCode);
}
