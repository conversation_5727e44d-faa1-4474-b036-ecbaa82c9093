package com.lds.oneplanning.wps.service.facade.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.entity.WpsPlanVersion;
import com.lds.oneplanning.wps.enums.PlanVersionDifferenceEnum;
import com.lds.oneplanning.wps.enums.WpsPlanSourceEnum;
import com.lds.oneplanning.wps.helper.WpsPlanVersionHelper;
import com.lds.oneplanning.wps.helper.WpsSnapshotHelper;
import com.lds.oneplanning.wps.model.OrderScheduleDTO;
import com.lds.oneplanning.wps.model.WpsData;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsPlanVersionService;
import com.lds.oneplanning.wps.service.IWpsSnapshotService;
import com.lds.oneplanning.wps.service.WpsExcelService;
import com.lds.oneplanning.wps.service.facade.IWpsPublishVersionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WpsPublishVersionServiceImpl implements IWpsPublishVersionService {
    @Autowired
    IWpsSnapshotService wpsSnapshotService;
    @Autowired
    WpsPlanVersionHelper wpsPlanVersionHelper;
    @Autowired
    WpsSnapshotHelper wpsSnapshotHelper;
    @Autowired
    WpsExcelService wpsExcelService;
    @Resource
    private IPlannerBaseService plannerBaseService;
    @Autowired
    IWpsPlanVersionService wpsPlanVersionService;

    /**
     * 保存排产计划版本
     * @param userId  用户ID
     * @param factoryCode 工厂代码
     */
    @Override
    public void savePlanSnapshot(Long userId,String factoryCode,String version) {
        Long start = System.currentTimeMillis();
        if(Objects.isNull(userId)){
            log.info("savePlanVersion-》userId不能为空userId:{},factory");
            return;
        }
        String empNo = plannerBaseService.getEmpNoByUserId(userId);
        if(StringUtils.isBlank(empNo)){
            log.info("savePlanVersion-》empNo不能为空,userId:{}",userId,factoryCode);
            return;
        }
        //获取1月1号到当前日期的订单
        Date startTime = LocalDateTimeUtil.localDateToDate(LocalDate.of(LocalDate.now().getYear(),1,1));
        Date endTime =  new Date();
        WpsData wpsData =  wpsExcelService.getData(userId, WpsConstants.DATA_SOURCE_STORAGE,startTime,endTime,factoryCode,true, Maps.newHashMap());
        log.info("获取排产计划表耗时：{},userId:{},factoryCode:{},version:{}",System.currentTimeMillis()-start,userId,factoryCode,version);
        if(CollectionUtils.isEmpty(wpsData.getBody())){
            log.info("获取排产计划的版本为空userId:{},factoryCode:{}",userId,factoryCode);
            return;
        }
        WpsPlanVersion wpsPlanVersion = wpsPlanVersionHelper.createWpsPlanVersion(empNo,factoryCode);
        wpsPlanVersion.setSource(WpsPlanSourceEnum.PUBLISH.getCode());
        wpsPlanVersion.setVersion(version);
        //保存计划版本
        wpsPlanVersionService.savePlanVersion(wpsPlanVersion);
        //保存计划快照信息
        wpsSnapshotService.saveSnapshot(wpsData.getBody(),wpsPlanVersion);
    }

    @Override
    public void compareVersion(String factoryCode, String version, List<OrderScheduleDTO> orderScheduleList) {
        Long start = System.currentTimeMillis();
        if(StringUtils.isBlank(version) || CollectionUtils.isEmpty(orderScheduleList)){
            return;
        }
        List<String> bizIds = orderScheduleList.stream().map(OrderScheduleDTO::getOrderNo).distinct().collect(Collectors.toList());
        List<WpsRowData> oldWpsRowDataList =wpsSnapshotService.findList(null,factoryCode,version,bizIds,WpsPlanSourceEnum.PUBLISH);
        if(CollectionUtils.isEmpty(oldWpsRowDataList)){
            return;
        }
        for(OrderScheduleDTO wpsRowData:orderScheduleList){
            wpsRowData.setVersionDifferences(Sets.newHashSet());
            WpsRowData oldWpsRowData = oldWpsRowDataList.stream().filter(data->data.getOrderNo().equals(wpsRowData.getOrderNo())).findFirst().orElse(null);
            //订单是否是新增
            if(Objects.isNull(oldWpsRowData)){
                wpsRowData.getVersionDifferences().add(PlanVersionDifferenceEnum.ADD.getName());
                continue;
            }
            //订单数量比较
            if(Objects.nonNull(wpsRowData.getOrderPcsQty()) && Objects.nonNull(oldWpsRowData.getOrderPcsQty())){
                if(!Objects.equals(wpsRowData.getOrderPcsQty(),oldWpsRowData.getOrderPcsQty())){
                    wpsRowData.getVersionDifferences().add(PlanVersionDifferenceEnum.ORDER_QTY.getName());
                }
            }else{
                log.info("订单数量为空 orderNO:{},qty:{},oldQtyL:{}",wpsRowData.getOrderNo(),wpsRowData.getOrderPcsQty(),oldWpsRowData.getOrderPcsQty());
            }
            //完工时间比较
            if(Objects.nonNull(wpsRowData.getOnlineTime()) && Objects.nonNull(oldWpsRowData.getOnlineTime())){
                if(wpsRowData.getOnlineTime().before(oldWpsRowData.getOnlineTime())){
                    wpsRowData.getVersionDifferences().add(PlanVersionDifferenceEnum.ORDER_IN_ADVANCE.getName());
                }
                if(wpsRowData.getOnlineTime().after(oldWpsRowData.getOnlineTime())){
                    wpsRowData.getVersionDifferences().add(PlanVersionDifferenceEnum.ORDER_EXTENSION.getName());
                }
            }

        }
        Long end = System.currentTimeMillis();
        log.info("排产版本比较耗时：{}",end-start);
    }
}
