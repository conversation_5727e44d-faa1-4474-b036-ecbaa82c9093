package com.lds.oneplanning.wps.req;

import com.lds.oneplanning.wps.vo.DeliveryDateAbnormalVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

/**
 * 工艺路线异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13 16:20
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "交期异常")
@Data
public class DeliveryDateAbnormalReq extends DeliveryDateAbnormalVO {
    private String factoryCodes;
    private List<String> factoryCodeList;

    private LocalDate startDate;
    private LocalDate endDate;
}
