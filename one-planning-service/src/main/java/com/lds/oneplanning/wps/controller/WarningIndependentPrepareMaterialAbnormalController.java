package com.lds.oneplanning.wps.controller;

import com.lds.oneplanning.esb.datafetch.model.EsbFcData;
import com.lds.oneplanning.esb.datafetch.model.EsbPlanOrderComponent;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.warning.workbench.handlers.IndependentPrepareMaterialAbnormalHandler;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@Api("独立备料SO未转正")
@RestController
@RequestMapping("/wps/warning/ipm")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningIndependentPrepareMaterialAbnormalController {

    private final IndependentPrepareMaterialAbnormalHandler independentPrepareMaterialAbnormalHandler;
    private final EsbDataFetchService esbDataFetchService;

   /* @PostMapping("/page")
    @ApiOperation("独立备料SO异常分页查询")
    public Page<?> page(@RequestParam(value = "source", required = false) ViewSource viewSource,
                        @RequestBody MaterialAtpAbnormalReq vo) {
        ViewSource source = userInfoService.getOrDefaultUserType(viewSource);
        return PageHelper.cover(warningMaterialAtpAbnormalService.queryPage(source, vo));
    }*/

    @PostMapping("/fetchPlanOrderComponent")
    public List<EsbPlanOrderComponent> fetchPlanOrderComponent(@RequestBody List<EsbPlanOrderComponent> req) {
        if (CollectionUtils.isEmpty(req)) {
            return Collections.emptyList();
        }
        return esbDataFetchService.fetchPlanOrderComponent(req);
    }

    @PostMapping("/fetchFcData/{yearMonth}")
    public List<EsbFcData> fetchFcData(@PathVariable(name = "yearMonth") String yearMonth,
                                       @RequestBody List<EsbFcData> req) {
        if (CollectionUtils.isEmpty(req)) {
            return Collections.emptyList();
        }
        return esbDataFetchService.fetchFcData(yearMonth,req);
    }
}
