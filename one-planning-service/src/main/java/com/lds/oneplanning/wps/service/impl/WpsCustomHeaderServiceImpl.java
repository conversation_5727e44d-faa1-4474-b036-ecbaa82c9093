package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.oneplanning.wps.entity.WpsCustomHeaders;
import com.lds.oneplanning.wps.mapper.WpsCustomHeaderMapper;
import com.lds.oneplanning.wps.req.WpsCustomHeaderReq;
import com.lds.oneplanning.wps.service.IWpsCustomHeaderService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class WpsCustomHeaderServiceImpl extends ServiceImpl<WpsCustomHeaderMapper, WpsCustomHeaders> implements IWpsCustomHeaderService {

    @Override
    public List<String> listByFactoryCode(Long userId) {
        if (null == userId) {
            return Lists.newArrayList();
        }
        List<WpsCustomHeaders> wpsCustomHeaders = this.baseMapper.selectList(
                Wrappers.<WpsCustomHeaders>lambdaQuery()
                        .eq(WpsCustomHeaders::getUserId, userId)
                        .orderByAsc(WpsCustomHeaders::getSort));
        if (CollectionUtils.isEmpty(wpsCustomHeaders)) {
            return Lists.newArrayList();
        }
        return wpsCustomHeaders.stream().map(WpsCustomHeaders::getCode).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void save(Long userId, WpsCustomHeaderReq wpsCustomHeaderReq) {
        if (null == userId) {
            return;
        }
        List<String> codes = wpsCustomHeaderReq.getCodes();
        this.baseMapper.delete(Wrappers.<WpsCustomHeaders>lambdaQuery()
                .eq(WpsCustomHeaders::getUserId, userId));
        if (CollectionUtils.isEmpty(codes)) {
            return;
        }
        AtomicReference<Integer> sort = new AtomicReference<>(0);
        List<WpsCustomHeaders> wpsCustomHeaders = codes.stream()
                .map(code -> {
                    WpsCustomHeaders wpsCustomHeader = new WpsCustomHeaders();
                    wpsCustomHeader.setUserId(userId);
                    wpsCustomHeader.setSort(sort.getAndSet(sort.get() + 1));
                    wpsCustomHeader.setCode(code);
                    return wpsCustomHeader;
                }).collect(Collectors.toList());
        this.saveBatch(wpsCustomHeaders);
    }
}