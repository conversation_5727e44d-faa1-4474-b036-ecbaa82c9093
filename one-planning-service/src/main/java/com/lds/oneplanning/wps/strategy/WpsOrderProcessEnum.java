package com.lds.oneplanning.wps.strategy;

import com.lds.oneplanning.esb.cache.WpsOrderCacheUtils;

/**
 * 订单告警类型枚举
 */
public enum WpsOrderProcessEnum {
    PURE("pureStrategy", "仅查询订单"),
    ATP_CHECK("atpCheckStrategy", "ATP欠料"),
    ORDER_REVIEW("orderReviewStrategy", "订单评审"),
    COMPLETE_DATE("completeDateStrategy", "完成日期回填"),
    AUTO_SCHEDULE("autoScheduleStrategy", "自动排产"),
    READ_STORAGE("readStorageStrategy", "读取数据库");

    private String code;
    private String name;
    WpsOrderProcessEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }



    public static String getNameByCode(String code){
        for (WpsOrderProcessEnum typeEnum : WpsOrderProcessEnum.values()){
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getName();
            }
        }
        return null;
    }

}