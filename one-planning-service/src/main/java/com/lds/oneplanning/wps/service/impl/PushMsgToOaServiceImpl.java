package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.wps.service.IPushMsgToOAService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.List;
@Slf4j
@Service
public class PushMsgToOaServiceImpl implements IPushMsgToOAService {

    @Value("${ldx.oa.pushmsg.url:https://app.leedarson.com}")
    private String oaurl;

    @Value("${ldx.oa.pushmsg.pub:XT-063a47fa-09e6-493c-9dc0-c1fa90f0d6cb}")
    private String oaPub;

    @Value("${ldx.oa.pushmsg.eid:2019111102}")
    private String eid;

    @Value("${ldx.oa.pushmsg.oakey:56c212a9bc411ce2e93a679dc4c56e87}")
    private String oakey;

    @Resource
    private IEsbDataFetchService esbDataFetchService;

    /**
     * 推送消息到LCP公众号-传登录名
     *
     * @param text       内容
     * @param loginNames
     * @param type       5推送含链接，2，推送文本
     * @param url        回调链接
     */
    @Override
    public boolean pushMsgToOAByLoginName(String text, List<String> loginNames, int type, String url) {
        List<String> openids = esbDataFetchService.getYzjOpenIDByLoginName(loginNames);
        log.info("推送LCP公众号-获取云之家openid为：{}", openids);
        if (openids.isEmpty()){
            return false;
        }
        return pushMsgToOAByOpenId(text ,openids ,type, url);
    }

    /**
     * 推送消息到LCP公众号-传openid
     *
     * @param text       内容
     * @param toUserList 接收用户的openid
     * @param type       5推送含链接，2，推送文本
     * @param url        回调链接
     */
    @Override
    public boolean pushMsgToOAByOpenId(String text, List<String> toUserList, int type, String url) {
        try {
            if (StrUtil.isEmpty(text)) {
                return false;
            }
            String random = String.valueOf(Math.random() * 100);
            long time_1 = System.currentTimeMillis();
            // form
            JSONObject from = new JSONObject();
            from.put("no", eid);
            from.put("pub", oaPub);
            from.put("time", time_1);
            from.put("nonce", random);
            String[] data = {eid, oaPub, oakey, random, String.valueOf(time_1)};
            from.put("pubtoken", sha(data));
            // to
            JSONArray tos = new JSONArray();
            JSONObject to = new JSONObject();
            to.put("no", eid);
            to.put("user", toUserList);
            tos.add(to);

            JSONObject msg = new JSONObject();
            if (type == 5) {
                msg.put("url", url);
                msg.put("todo", 0);
                msg.put("todoPriStatus", "undo");
                msg.put("text", text);
            } else if (type == 2) {
                msg.put("text", text);
            }
            JSONObject content = new JSONObject();
            content.put("from", from);
            content.put("to", tos);
            content.put("type", type);
            content.put("msg", msg);
            String contentStr = content.toString();
            String rs = sendPostO(oaurl + "/pubacc/pubsendV2", contentStr);
            log.info("发送云之家OA消息返回结果：{}，请求参数：{}" ,rs,contentStr);
            JSONObject rsJson = JSONObject.parseObject(rs);
            if (rsJson.containsKey("success")) {
                return rsJson.getBoolean("success");
            }
        } catch (Exception e) {
            log.error("发送云之家OA消息异常：" + e.getMessage());
        }
        return false;
    }

    /**
     * sha 加密
     * @param data 加密数据字符串数组
     * @return
     */
    public static String sha(String[] data) {
        Arrays.sort(data);
        return DigestUtils.shaHex(StringUtils.join(data));
    }

    public static String sendPostO(String urlstr, String params) {
        StringBuffer result = new StringBuffer();
        try {
            HttpURLConnection connection = null;
            URL url = new URL(urlstr);
            connection = (HttpURLConnection)url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(6000);
            connection.connect();
            OutputStreamWriter out = new OutputStreamWriter(
                    connection.getOutputStream(), "UTF-8");
            out.append(params);
            out.flush();
            out.close();
            int HttpResult = connection.getResponseCode();
            if(HttpResult == 200) {
                BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(),"utf-8"));
                String line = null;
                while((line = br.readLine()) != null) {
                    result.append(line + "\n");
                }
                br.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.toString();
    }
}
