package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningIncomeMaterialAtpAbnormal;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.vo.*;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
public interface IWarningIncomeMaterialAtpAbnormalService extends IService<WarningIncomeMaterialAtpAbnormal> {
    IPage<?> selectPage(ViewSource source, WarningIncomeMaterialAtpAbnormalParams params);
    IPage<WarningIncomeMaterialAtpAbnormalVO2> selectPagePc(Page<WarningIncomeMaterialAtpAbnormalVO2> page , WarningIncomeMaterialAtpAbnormalParams params);
    IPage<WarningIncomeMaterialAtpAbnormalVO> selectPageIqc(Page<WarningIncomeMaterialAtpAbnormalVO> page, String dealResult, WarningIncomeMaterialAtpAbnormalParams params);
    IPage<WarningIncomeMaterialAtpAbnormalVO> selectPageMc(Page<WarningIncomeMaterialAtpAbnormalVO> page, String dealResult, WarningIncomeMaterialAtpAbnormalParams params);
}
