package com.lds.oneplanning.wps.warning.workbench.handlers;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.lds.oneplanning.basedata.entity.ProductGroupRel;
import com.lds.oneplanning.basedata.model.UserInfoDTO;
import com.lds.oneplanning.basedata.service.IProductGroupRelService;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.common.service.IBasicUserService;
import com.lds.oneplanning.esb.datafetch.model.EsbFrozenUnfrozenData;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.entity.*;
import com.lds.oneplanning.wps.enums.*;
import com.lds.oneplanning.wps.helper.WpsRowDataMergeHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWarningFrozenUnfrozenAbnormalService;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.utils.OrderNoUtils;
import com.lds.oneplanning.wps.utils.WpsDateUtil;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 冻结、解冻处理器
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class FrozenUnfrozenAbnormalHandler implements IWpsWorkbenchWarningHandler {
    @Value("${wps.mock.switch:0}")
    private Integer isDebug = 0;
    private final EsbDataFetchService esbDataFetchService;
    private final IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    private final WarningTodoListService todoListService;
    private final IWarningFrozenUnfrozenAbnormalService warningFrozenUnfrozenAbnormalService;
    private final IProductGroupRelService productGroupRelService;
    private final IUserInfoService userInfoService;
    private final IBasicUserService basicUserService;

    @Override
    public List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext ctx, Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap) {
        if (ctx == null || CollectionUtils.isEmpty(ctx.getOrders())) {
            return Collections.emptyList();
        }
        List<WpsRowData> orders = ctx.getOrders();
        //去掉没排产的数据
        orders.removeIf(row -> row.getOnlineTime() == null);
        log.info("移除未排产数据后：异常数量:{}", orders.size());
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }
        //合并相同订单的数据
        orders = WpsRowDataMergeHelper.mergeSameOrder(orders);
        List<WpsRowData> filterOrders = orders.stream().filter(row -> StrUtil.isNotEmpty(row.getCommodityId())).collect(Collectors.toList());
        //分析异常数据
        return analyzeAbnormalData(ctx, filterOrders);
    }

    /**
     * 分析异常数据并生成警告信息
     *
     * @param ctx                  工作台警告上下文
     * @param orders  排产订单列表
     * @return 包含警告信息的列表，如果未分析出异常则返回空列表
     */
    private List<WpsOrderPlanWarning> analyzeAbnormalData(WpsWorkbenchWarningContext ctx,
                                                          List<WpsRowData> orders) {

        log.info("开始分析异常数据");
        List<WarningFrozenUnfrozenAbnormal> warningList = new ArrayList<>();
        // 分析在库异常数据
        analyzeInStoreExceptions(orders, warningList);
        log.info("完成在库异常数据分析，warningList异常数据数量: {}", warningList.size());
        //消警
        log.info("开始消警");
        eliminateAlarms(warningList);
        //保存或更新
        log.info("开始保存异常数据");
        saveOrUpdate(warningList);
        log.info("完成保存异常数据，警告列表大小: {}", warningList.size());

        log.info("开始创建待办");
        createTodoList(warningList);
        // 拿出poWarningList的订单号
        List<String> orderNos = warningList.stream().map(WarningFrozenUnfrozenAbnormal::getOrderNo).collect(Collectors.toList());
        orders.removeIf(row -> !orderNos.contains(row.getOrderNo()));
        return convertData(ctx, orders, warningList);
    }

    private List<WpsOrderPlanWarning> convertData(WpsWorkbenchWarningContext ctx, List<WpsRowData> abnormalList, List<WarningFrozenUnfrozenAbnormal> warningList) {
        Map<String, LightColor> lightColorMap = warningList.stream()
                .collect(Collectors.toMap(WarningFrozenUnfrozenAbnormal::getOrderNo, l-> l.getLightColor(), (o, o2) -> o));
        return buildWarning(ctx, abnormalList, lightColorMap);
    }

    /**
     * 创建待办事项列表
     *
     * @param warningList 包含警告信息的列表
     */
    private void createTodoList(List<WarningFrozenUnfrozenAbnormal> warningList) {
        // 将灯色不为黄灯和红灯的记录的推送人员这是为空，不推送
        warningList.forEach(e -> {
            if (e.getLightColor() != LightColor.YELLOW && e.getLightColor() != LightColor.RED) {
                e.setNpiPersonGh("");
                e.setQplPersonGh("");
            }
        });
        List<WarningTodoList> todoList = new ArrayList<>();
        Map<String, Map<String, String>> existWerk = new HashMap<>();
        for (WarningFrozenUnfrozenAbnormal e : warningList){
            if (StrUtil.isEmpty(e.getNpiPersonGh())){
                todoList.add(new WarningTodoList(getWarningType(), e.getFactory(),  e.getId(), ""));
            } else {
                String[] npiGhs = e.getNpiPersonGh().split(",");
                if (npiGhs.length > 0) {
                    Map<String, String> stringStringMap;
                    if (existWerk.containsKey(e.getFactory())){
                        stringStringMap = existWerk.get(e.getFactory());
                    } else {
                        stringStringMap = basicUserService.batchGetLoginNamesByJobNos(Arrays.asList(npiGhs));
                        existWerk.put(e.getFactory(), stringStringMap);
                    }
                    //循环stringStringMap
                    for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
                        String loginName = entry.getValue();
                        WarningTodoList todo = new WarningTodoList(getWarningType(), e.getFactory(),  e.getId(), loginName);
                        todoList.add(todo);
                    }
                }
            }
            if (StrUtil.isEmpty(e.getQplPersonGh())) {
                todoList.add(new WarningTodoList(getWarningType(), e.getFactory(), e.getId(), ""));
            } else {
                String[] qplGhs = e.getQplPersonGh().split(",");
                if (qplGhs.length > 0) {
                    Map<String, String> stringStringMap = new HashMap<>();
                    if (existWerk.containsKey(e.getFactory())){
                        stringStringMap = existWerk.get(e.getFactory());
                    } else {
                        stringStringMap = basicUserService.batchGetLoginNamesByJobNos(Arrays.asList(qplGhs));
                        existWerk.put(e.getFactory(), stringStringMap);
                    }
                    //循环stringStringMap
                    for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
                        String loginName = entry.getValue();
                        WarningTodoList todo = new WarningTodoList(getWarningType(), e.getFactory(),  e.getId(), loginName);
                        todoList.add(todo);
                    }
                }
            }
        }
        //临时处理，过滤掉bizId为空的数据
        todoList.removeIf(e -> e.getBizId() == null);
        List<WarningTodoList> uniqueTodoList = todoList.stream()
//                .filter(e -> e.getBizId() != null && e.getAssignee() != null)
                .collect(Collectors.toMap(
                        t -> new AbstractMap.SimpleEntry<>(t.getBizId(), t.getAssignee()),
                        t -> t,
                        (existing, replacement) -> existing
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        todoListService.saveData(getWarningType(), uniqueTodoList);
    }
    /**
     * 获取待办处理人
     *
     * @param warningList 告警列表
     * @return 一个以 Long 为键、String 为值的 HashMap，键为待办事项的ID，值为待办处理人的员工编号
     */
    private Map<String, String> getAssignee(List<WarningFrozenUnfrozenAbnormal> warningList) {
        return null;
    }

    private void saveOrUpdate(List<WarningFrozenUnfrozenAbnormal> warningList) {

        // 1. 提取订单号列表
        List<String> poOrderNoList = warningList.stream()
                .map(WarningFrozenUnfrozenAbnormal::getOrderNo)
                .distinct()
                .collect(Collectors.toList());
        List<String> poMaterialIdList = warningList.stream()
                .map(WarningFrozenUnfrozenAbnormal::getCommodityId)
                .distinct()
                .collect(Collectors.toList());
        List<WarningFrozenUnfrozenAbnormal> poExistList = warningFrozenUnfrozenAbnormalService.lambdaQuery()
                .in(WarningFrozenUnfrozenAbnormal::getOrderNo, poOrderNoList)
                .in(WarningFrozenUnfrozenAbnormal::getCommodityId, poMaterialIdList)
                .list();
        // 3. 构建已存在记录的映射表（订单号 -> 物料ID -> 实体）
        Table<String, String, WarningFrozenUnfrozenAbnormal> existTable = HashBasedTable.create();
        poExistList.forEach(warning -> existTable.put(warning.getOrderNo(), warning.getCommodityId(), warning));

        // 4. 初始化更新和插入列表
        List<WarningFrozenUnfrozenAbnormal> updateList = new ArrayList<>();
        List<WarningFrozenUnfrozenAbnormal> insertList = new ArrayList<>();

        // 5. 遍历warningList，区分更新和插入
        CopyOptions opt = CopyOptions.create().ignoreNullValue();
        warningList.forEach(warning -> {
            if (existTable.contains(warning.getOrderNo(), warning.getCommodityId())) {
                // 5.1 如果记录已存在，执行更新操作
                WarningFrozenUnfrozenAbnormal entity = existTable.get(warning.getOrderNo(), warning.getCommodityId());
                BeanUtil.copyProperties(warning, entity, opt);
                // 反向更新到warning中，以便外部能获取到id
                BeanUtil.copyProperties(entity, warning);
                warning.setUpdateTime(new Date());
                updateList.add(warning);
            } else {
                // 5.2 如果记录不存在，添加到插入列表
                warning.setUpdateTime(new Date());
                warning.setCreateTime(new Date());
                insertList.add(warning);
            }
        });
        // 6. 批量更新和插入
        if (CollectionUtils.isNotEmpty(updateList)) {
            warningFrozenUnfrozenAbnormalService.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            warningFrozenUnfrozenAbnormalService.saveBatch(insertList);
        }
    }

    /**
     * 消除告警
     *
     * @param warningList 告警信息列表
     */
    private void eliminateAlarms(List<WarningFrozenUnfrozenAbnormal> warningList) {
        //获取所有待办的数据
        List<WarningFrozenUnfrozenAbnormal> existData = warningFrozenUnfrozenAbnormalService.queryUnHandleData();
        log.info("获取未处理的警告数据计数: {}", existData.size());

        //将warningList组装成Table
        Table<String, String, WarningFrozenUnfrozenAbnormal> warningTable = HashBasedTable.create();
        warningList.forEach(warning -> warningTable.put(warning.getOrderNo(), warning.getCommodityId(), warning));

        //如果existData不存在与warningList中的数据，则删除，根据订单号和物料id来判断
        List<Long> toBeRemoveIdsList = existData.stream()
                .filter(e -> !warningTable.contains(e.getOrderNo(), e.getCommodityId()))
                .map(WarningFrozenUnfrozenAbnormal::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(toBeRemoveIdsList)) {
            log.info("消警列表：{}", toBeRemoveIdsList);
            todoListService.lambdaUpdate()
                    .set(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.CLOSED)
                    .eq(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.UN_HANDLE)
                    .in(WarningTodoList::getBizId, toBeRemoveIdsList)
                    .eq(WarningTodoList::getWarningType, this.getWarningType())
                    .update();
            //主表消警
            wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
        }
    }

    private WpsOrderPlanWarning toWarning(String factoryCode, Long userId, WpsRowData order) {
        return buildWpsOrderPlanWarning(factoryCode, userId, order.getOrderNo(), order.getLineUuid(), order.getLineCode(),
                OrderWarningLevelEnum.WARNING, order.get_startProductPeriod());
    }

    /**
     * 分析并处理在库异常数据
     *
     */
    private void analyzeInStoreExceptions(List<WpsRowData> orders,  List<WarningFrozenUnfrozenAbnormal> warningList) {
        // WPS当前的所有销售订单，1.识别物料ID在SAP的跨工厂物料状态为非13的数据
        List<EsbFrozenUnfrozenData> dataList = orders.stream().map(order -> {
            EsbFrozenUnfrozenData esbFrozenUnfrozenData = new EsbFrozenUnfrozenData();
            esbFrozenUnfrozenData.setMATNR(order.getCommodityId());
            esbFrozenUnfrozenData.setWERKS(order.getFactory());
            return esbFrozenUnfrozenData;
        }).distinct().collect(Collectors.toList());
        List<EsbFrozenUnfrozenData> list = esbDataFetchService.frozenUnfrozenSapData(dataList);
        // esbFrozenUnfrozenData1去掉跨工厂物料状态为等于13的数据
        List<EsbFrozenUnfrozenData> esbdataNo13 = list.stream().filter(vo -> {
            return !"13".equals(vo.getMSTAE()) || (StrUtil.isNotEmpty(vo.getMMSTA()) && !"13".equals(vo.getMMSTA()));
        }).collect(Collectors.toList());
        //去掉List<WpsRowData> 里面的商品id不包含在esbdataNo13商品id的数据
        List<WpsRowData> wpsRowDataList = orders.stream().filter(vo -> {
            return esbdataNo13.stream().anyMatch(esb -> esb.getMATNR().equals(vo.getCommodityId()));
        }).collect(Collectors.toList());
        // 获取产品组编码和商品id对应
        List<ProductGroupRel> productGroupRels = productGroupRelService.listByProductIds(wpsRowDataList.stream().map(WpsRowData::getCommodityId).collect(Collectors.toList()));
        wpsRowDataList.forEach(vo -> {
            WarningFrozenUnfrozenAbnormal warning = new WarningFrozenUnfrozenAbnormal();
            warning.setCustomer(vo.getCustomerCode());
            warning.setOrderNo(vo.getOrderNo());
            warning.setCommodityId(vo.getCommodityId());
            warning.setMaterialDesc(vo.getCommodityDesc());
            warning.setSalesOrderNumber(OrderNoUtils.trimPreZero(OrderNoUtils.getUnifyOrderNo(vo)));
            warning.setLineNumber(OrderNoUtils.trimPreZero(vo.getRowItem()));
            warning.setFactory(vo.getFactory());
            warning.setPlannedOnlineTime(WpsDateUtil.getStartScheduleDate(vo.getScheduleDataMap()));
            // 计算距离计划剩余天数 计划时间减去当前时间
            LocalDate planDate = WpsDateUtil.getStartScheduleDate(vo.getScheduleDataMap());
            int days = LocalDate.now().until(planDate).getDays();
            warning.setRemainingDaysToPlan(days);
            warning.setLightColor(calculateLightColor(planDate));
            // 匹配商品id 给产品组编码赋值
            Optional<ProductGroupRel> productGroupRelOptional = productGroupRels.stream().filter(rel -> rel.getProductId().equals(vo.getCommodityId())).findFirst();
            if (productGroupRelOptional.isPresent()) {
                warning.setProductGroupCode(productGroupRelOptional.get().getProductGroupCode());
            }
            warningList.add(warning);
        });
        // 设置QPL人员，按工厂
        List<String> factoryList = warningList.stream().map(po -> po.getFactory()).distinct().collect(Collectors.toList());
        Map<String, List<UserInfoDTO>> userinfoMap = userInfoService.listByFactoryCodes(factoryList);
        warningList.forEach(po -> {
            if (userinfoMap.containsKey(po.getFactory())) {
                List<UserInfoDTO> userInfos = userinfoMap.get(po.getFactory());
                po.setQplPerson(userInfos.stream().map(UserInfoDTO::getUserName).collect(Collectors.joining(",")));
                po.setQplPersonGh(userInfos.stream().map(UserInfoDTO::getEmpNo).collect(Collectors.joining(",")));
                po.setQplPersonUserid(userInfos.stream().map(userinfo -> userinfo.getUserId().toString()).collect(Collectors.joining(",")));
            }
        });
        // 设置NPI人员，按产品组维度 wps暂未支持产品组和人员的关联 todo
    }
    @Override
    public WpsOrderWarningCategoryEnum getWarningCategory() {
        return WpsOrderWarningCategoryEnum.DEFAULT;
    }

    @Override
    public WpsOrderWarningTypeEnum getWarningType() {
        return WpsOrderWarningTypeEnum.FROZEN_UNFROZEN_WARNING;
    }

    public static LightColor calculateLightColor(LocalDate planDate) {
        if (planDate == null) {
            return LightColor.BLANK;
        }
        LocalDate now = LocalDate.now();
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(now ,planDate);
        if (daysBetween <= 7 && daysBetween > 0) {
            return LightColor.RED;
        } else if (daysBetween > 7 && daysBetween <= 14) {
            return LightColor.YELLOW;
        } else {
            return LightColor.BLANK;
        }
    }
}
