package com.lds.oneplanning.wps.warning.workbench.handlers;

import cn.hutool.json.JSONUtil;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.entity.WpsOrderWarningCfg;
import com.lds.oneplanning.wps.enums.OrderWarningLevelEnum;
import com.lds.oneplanning.wps.model.WpsRowData;
import org.apache.commons.collections.MapUtils;

import java.time.LocalDate;
import java.time.Period;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public abstract class AbstractWpsWorkbenchWarningHandler implements IWpsWorkbenchWarningHandler {

    protected void addWarningIfConditionMet(Long userId, WpsRowData order, LocalDate dateToCheck, LocalDate referenceDate, OrderWarningLevelEnum warningLevel, List<WpsOrderPlanWarning> warnings) {
        if (isDateAfterOrEqual(dateToCheck, referenceDate)) {
            warnings.add(buildWpsOrderPlanWarning(userId, order.getOrderNo(), order.getLineUuid(), order.getLineCode(), warningLevel, dateToCheck));
        }
    }

    protected boolean isDateAfterOrEqual(LocalDate dateToCheck, LocalDate referenceDate) {
        return !dateToCheck.isBefore(referenceDate);
    }

    protected boolean isProductionScheduleValid(LocalDate finalInspectionDate, LocalDate latestProductionDate, Integer allowedDaysDifference) {
        Period period = Period.between(latestProductionDate, finalInspectionDate);
        int daysBetween = period.getDays();
        return daysBetween <= allowedDaysDifference;
    }

    protected Integer getCfgIntegerFromJson(String key,Integer level, Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap){
        WpsOrderWarningCfg warningWpsOrderWarningCfg = wpsOrderWarningCfgMap.get(level);
        if (warningWpsOrderWarningCfg == null) {
            return null;
        }
        Map paramMap = JSONUtil.toBean(warningWpsOrderWarningCfg.getParamJson(), Map.class);
        if (MapUtils.isEmpty(paramMap)) {
            // 为空 自己处理
          return null;
        }
        return (Integer) paramMap.get(key);
    }

    protected List<WpsRowData> filterRowData( List<WpsRowData> dirtyList,List<Predicate<WpsRowData>> predicateList){
        Stream<WpsRowData> stream = dirtyList.stream();
        for (Predicate<WpsRowData> predicate: predicateList){
            stream = stream.filter(predicate);
        }
        return stream.collect(Collectors.toList());
    }
}
