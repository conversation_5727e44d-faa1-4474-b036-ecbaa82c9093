package com.lds.oneplanning.wps.req;

import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "船期临近未订舱异常")
@Data
public class ShipBookingUrgentReq extends BasePageReq {

    private static final long serialVersionUID = 5108649708969433287L;

    @ApiModelProperty(value = "工厂代码")
    private String factoryCodes;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "销售订单号")
    private String sellOrderNo;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "订舱状态")
    private String bookingStatus;

    @ApiModelProperty(value = "船期开始日期")
    private String shipScheduleStartDate;

    @ApiModelProperty(value = "船期结束日期")
    private String shipScheduleEndDate;

    @ApiModelProperty(value = "商品ID")
    private String commodityId;

    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "处理状态")
    private OrderWarningHandleStatusEnum processStatus;

    private List<String> factoryCodeList;
}