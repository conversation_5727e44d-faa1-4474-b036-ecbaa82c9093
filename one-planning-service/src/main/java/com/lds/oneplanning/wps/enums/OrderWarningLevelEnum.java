package com.lds.oneplanning.wps.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Getter
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public enum OrderWarningLevelEnum {

    WARNING(1, LightColor.RED),
    ALARM(2, LightColor.YELLOW);

    private final int level;
    private final LightColor lightColor;

    private static final Map<LightColor, OrderWarningLevelEnum> LIGHT_COLOR_MAP = new HashMap<>();

    static {
        // 初始化映射关系
        for (OrderWarningLevelEnum levelEnum : OrderWarningLevelEnum.values()) {
            LightColor lightColor = levelEnum.getLightColor();
            if (LIGHT_COLOR_MAP.containsKey(lightColor)) {
                throw new IllegalStateException("Duplicate LightColor mapping detected: " + lightColor);
            }
            LIGHT_COLOR_MAP.put(lightColor, levelEnum);
        }
    }

    public static OrderWarningLevelEnum getByLight(LightColor light) {
        if (light == null) {
            return null;
        }
        return LIGHT_COLOR_MAP.get(light);
    }

    public static OrderWarningLevelEnum getByCode(Integer warningLevel) {
        for (OrderWarningLevelEnum levelEnum : OrderWarningLevelEnum.values()) {
            if (Objects.equals(levelEnum.getLevel(), warningLevel)) {
                return levelEnum;
            }
        }
        return null;
    }
}