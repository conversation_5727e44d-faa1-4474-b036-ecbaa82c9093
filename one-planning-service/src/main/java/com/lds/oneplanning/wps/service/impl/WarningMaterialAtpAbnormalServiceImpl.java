package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iot.common.exception.BusinessException;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningTodoList;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.exception.WpsExceptionEnum;
import com.lds.oneplanning.wps.helper.UserApiHelper;
import com.lds.oneplanning.wps.mapper.WarningMaterialAtpAbnormalMapper;
import com.lds.oneplanning.wps.req.MaterialAtpAbnormalReq;
import com.lds.oneplanning.wps.service.AffectsPlanService;
import com.lds.oneplanning.wps.service.WarningMaterialAtpAbnormalService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.lds.oneplanning.wps.utils.DateUtils;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalMcVO;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【warning_material_atp_abnormal】的数据库操作Service实现
 * @createDate 2025-05-14 17:29:49
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningMaterialAtpAbnormalServiceImpl extends ServiceImpl<WarningMaterialAtpAbnormalMapper, WarningMaterialAtpAbnormal>
        implements WarningMaterialAtpAbnormalService {
    private final WpsRowDataFacadeService wpsRowDataFacadeService;
    private final WarningTodoListService warningTodoListService;
    private final IFactoryFacadeService factoryFacadeService;
    private final AffectsPlanService affectsPlanService;


    @Override
    public List<WarningMaterialAtpAbnormal> queryUnHandleData() {
        return baseMapper.queryUnHandleData();
    }

    @Override
    public Page<?> queryPage(ViewSource source, MaterialAtpAbnormalReq vo) {
        String userLoginName = null;

        // 如果是MC端，则获取当前登录用户信息
        if (ViewSource.MC.equals(source)) {
            userLoginName = UserApiHelper.getUserLoginName();
        }

        if (StringUtils.isNotEmpty(vo.getFactoryCodes())) {
            vo.setFactoryCodeList(Arrays.asList(StringUtils.split(vo.getFactoryCodes(), ",")));
        } else if (ViewSource.PC.equals(source)) {
            //PC只能看自己关联的工厂
            Long userId = UserContextUtils.getUserId();
            List<Factory> factories = factoryFacadeService.listByUser(userId);
            if (CollectionUtils.isEmpty(factories)) {
                log.info("当前PC用户{}未关联任何工厂，无法查看数据！", userId);
                return new Page<>(vo.getPage(), vo.getPageSize());
            }
            List<String> factoryCodes = factories.stream().map(Factory::getCode).collect(Collectors.toList());
            vo.setFactoryCodeList(factoryCodes);
            log.info("当前PC用户{}关联的工厂为:{}", userId, factoryCodes);
        }


        if (ViewSource.MC.equals(source)) {
            return queryMcPage(vo, userLoginName);
        } else if (ViewSource.PC.equals(source)) {
            return queryPcPage(vo, userLoginName);
        }
        return new Page<>(vo.getPage(), vo.getPageSize());

    }

    private Page<?> queryMcPage(MaterialAtpAbnormalVO vo, String userId) {
        Page<MaterialAtpAbnormalMcVO> pageParam = new Page<>(vo.getPage(), vo.getPageSize());
        Page<MaterialAtpAbnormalMcVO> page = baseMapper.queryMcPage(pageParam, userId, vo);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            page.getRecords()
                    .forEach(e -> {
                        e.setSalesOrderNumberWithLineNumber(getSalesOrderNumber(e.getSalesOrderNumber(), e.getLineNumber()));
                        Optional.ofNullable(e.getProcessStatus()).ifPresent(s -> e.setProcessStatusName(s.getName()));
                    });
        }
        return page;
    }

    private String getSalesOrderNumber(String salesOrderNumber, String lineNumber) {
        return salesOrderNumber + "-" + lineNumber;
    }


    private Page<MaterialAtpAbnormalVO> queryPcPage(MaterialAtpAbnormalVO vo, String userId) {
        Page<MaterialAtpAbnormalVO> pageParam = new Page<>(vo.getPage(), vo.getPageSize());
        Page<MaterialAtpAbnormalVO> page = baseMapper.queryPage(pageParam, userId, vo);

        page.getRecords().forEach(item -> {
            item.setAdjustedOnlineTime(ObjectUtil.defaultIfNull(item.getAdjustedOnlineTimeEdited(), item.getAdjustedOnlineTime()));
            item.setAdjustedGapDays(DateUtils.daysBetween(item.getAdjustedOnlineTime(), item.getPlannedOnlineTime()));
            //用于构建影响类型
            item.setSchedulingDate(item.getPlannedOnlineTime());
            item.setPlannedQuantity(item.getOnlineQuantity());
            item.setSalesOrderNumberWithLineNumber(getSalesOrderNumber(item.getSalesOrderNumber(), item.getLineNumber()));
            //整后完工日期，取值逻辑为“调整后上线时间"日期+1天不可编辑
            item.setAdjustedFinishTime(DateUtils.offsetDays(item.getAdjustedOnlineTime(), 1));

            Optional.ofNullable(item.getProcessStatus()).ifPresent(s -> item.setProcessStatusName(s.getName()));

        });
        affectsPlanService.buildAffectsPlan(page.getRecords(), MaterialAtpAbnormalVO::getAdjustedOnlineTime);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateData(MaterialAtpAbnormalVO vo) {
        Long userId = UserContextUtils.getUserId();
        LocalDate adjustedOnlineTime = vo.getAdjustedOnlineTime();
        Long id = vo.getId();
        if (id == null) {
            throw new BusinessException(WpsExceptionEnum.ID_CANNOT_BE_EMPTY);
        }

        WarningMaterialAtpAbnormal entity = super.getById(id);
        if (entity == null) {
            throw new BusinessException(WpsExceptionEnum.RECORD_NOT_EXIST);
        }
        if (adjustedOnlineTime != null) {
            LocalDate srcAdjustedOnlineTime = ObjectUtil.defaultIfNull(entity.getAdjustedOnlineTimeEdited(), entity.getPlannedOnlineTime());
            entity.setAdjustedOnlineTimeEdited(adjustedOnlineTime);
            //计算gap
            entity.setAdjustedGapDays(DateUtils.daysBetween(entity.getPlannedOnlineTime(), adjustedOnlineTime));
            //调用wps接口更新订单交期
            if (!DateUtils.isSameDay(srcAdjustedOnlineTime, adjustedOnlineTime)) {
                wpsRowDataFacadeService.changeStartScheduleDate(entity.getOrderNumber(), adjustedOnlineTime);
            }
        }
        entity.setAffectsUpperLevelPlan(vo.getAffectsUpperLevelPlan());
        entity.setImpactType(vo.getImpactType());
        entity.setUpdatedBy(userId);
        super.updateById(entity);

        //待办表更新
        warningTodoListService.lambdaQuery()
                .eq(WarningTodoList::getBizId, id)
                .eq(WarningTodoList::getWarningType, WpsOrderWarningTypeEnum.ATP_EXCEPTION)
                .last("limit 1")
                .oneOpt().ifPresent(todo -> {
                    if (OrderWarningHandleStatusEnum.UN_HANDLE.equals(todo.getProcessStatus())) {
                        todo.setProcessStatus(OrderWarningHandleStatusEnum.HANDLED);
                        warningTodoListService.updateById(todo);
                    }
                });
    }
}




