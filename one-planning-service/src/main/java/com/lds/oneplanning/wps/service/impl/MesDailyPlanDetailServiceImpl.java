package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.wps.entity.MesDailyPlanDetail;
import com.lds.oneplanning.wps.mapper.MesDailyPlanDetailMapper;
import com.lds.oneplanning.wps.req.MesDailyPlanDetailReq;
import com.lds.oneplanning.wps.service.IMesDailyPlanDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-24
 */
@Slf4j
@Service
public class MesDailyPlanDetailServiceImpl extends ServiceImpl<MesDailyPlanDetailMapper, MesDailyPlanDetail> implements IMesDailyPlanDetailService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveByPlanDateAndProductionLine(List<MesDailyPlanDetailReq> mesDailyPlanDetailReqs) {
        if (CollectionUtils.isEmpty(mesDailyPlanDetailReqs)) {
            return;
        }
        // 按计划日期+生产线上报<PlanDate, ProductionLine>
        Map<Date, Set<String>> planDateProductionLineMap = mesDailyPlanDetailReqs.stream()
                .collect(Collectors.groupingBy(MesDailyPlanDetailReq::getPlanDate,
                        Collectors.mapping(MesDailyPlanDetailReq::getProductionLine,
                                Collectors.toSet())));
        planDateProductionLineMap.forEach((planDate, productionLines) -> {
            if (CollectionUtils.isEmpty(productionLines)) {
                return;
            }
            log.info("saveByPlanDateAndProductionLine,planDate={}, productionLines={}",
                    planDate, productionLines);
            QueryWrapper<MesDailyPlanDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(MesDailyPlanDetail::getPlanDate, planDate);
            queryWrapper.lambda().in(MesDailyPlanDetail::getProductionLine, productionLines);
            remove(queryWrapper);
        });
        List<MesDailyPlanDetail> mesDailyPlanDetails = BeanUtil.copyToList(mesDailyPlanDetailReqs, MesDailyPlanDetail.class);
        saveBatch(mesDailyPlanDetails);
    }
}