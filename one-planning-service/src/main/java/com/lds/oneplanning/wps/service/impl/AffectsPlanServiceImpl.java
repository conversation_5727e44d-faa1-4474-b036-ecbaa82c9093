package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.google.common.collect.Lists;
import com.lds.oneplanning.esb.datafetch.model.ProdOrderVO;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.wps.service.AffectsPlanService;
import com.lds.oneplanning.wps.vo.AffectsAssemblyPlan;
import com.lds.oneplanning.wps.vo.AffectsPlan;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/28
 */
@Slf4j
@Service
public class AffectsPlanServiceImpl implements AffectsPlanService {

    @Autowired
    private IEsbDataFetchService esbDataFetchService;


    @Override
    public <T extends AffectsPlan> void buildAffectsPlan(Collection<T> affectsPlans,
                                                         Function<T, LocalDate> fieldExtractor) {
        buildAffectsPlan(
                affectsPlans,
                fieldExtractor,
                AffectsPlan::getMaterialId,
                AffectsPlan::getSchedulingDate,
                AffectsPlan::getPlannedQuantity
        );
    }

    @Override
    public <T extends AffectsPlan> void buildAffectsPlan(Collection<T> affectsPlans,
                                                         Function<T, LocalDate> estimatedCompletionDate,
                                                         Function<T, String> materialId,
                                                         Function<T, LocalDate> schedulingDate,
                                                         Function<T, Integer> plannedQuantity
    ) {
        if (CollUtil.isEmpty(affectsPlans)) {
            return;
        }
        List<String> orderNoList = affectsPlans.stream().map(AffectsPlan::getOrderNo).filter(StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(orderNoList)) {
            return;
        }
        //获取订单详情
        log.info("开始获取订单详情");
        List<ProdOrderVO> orderDetailData = esbDataFetchService.getProdOrderList(orderNoList);
        if (CollUtil.isEmpty(orderDetailData)) {
            return;
        }
        Map<String, ProdOrderVO> detailDataVOMap = orderDetailData.stream().collect(Collectors.toMap(ProdOrderVO::getAUFNR,
                vo -> vo, (vo1, vo2) -> vo1));
        affectsPlans.forEach(affectsPlan -> {
            ProdOrderVO detailDataVO = detailDataVOMap.get(affectsPlan.getOrderNo());
            if (detailDataVO != null) {
                affectsPlan.setFinalCompletionDate(detailDataVO.getZZWGRQ());
                affectsPlan.setFinalInspectionDate(detailDataVO.getYZYHRQ());
                // 预计可完工日期
                LocalDate fieldValue = estimatedCompletionDate.apply(affectsPlan);
                //影响整灯计划：预计可完工日期≥最终完工日 期
                if (fieldValue != null && StringUtils.isNotEmpty(affectsPlan.getFinalCompletionDate()) && fieldValue.isAfter(LocalDateTimeUtil.parseDate(affectsPlan.getFinalCompletionDate()))) {
                    affectsPlan.setImpactType("影响整灯计划");
                    affectsPlan.setAffectsFinalProductPlan(Lists.newArrayList(AffectsAssemblyPlan.builder()
                            .id(materialId.apply(affectsPlan))
                            .planDate(schedulingDate.apply(affectsPlan))
                            .quantity(plannedQuantity.apply(affectsPlan))
                            .build()));
                }
            }
        });
    }
}
