package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.iot.common.exception.BusinessException;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.wps.entity.WpsOrderWarningCfg;
import com.lds.oneplanning.wps.exception.WpsExceptionEnum;
import com.lds.oneplanning.wps.mapper.WpsOrderWarningCfgMapper;
import com.lds.oneplanning.wps.model.WpsOrderWarningCfgDTO;
import com.lds.oneplanning.wps.service.IWpsOrderWarningCfgService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-31
 */
@Service
public class WpsOrderWarningCfgServiceImpl extends ServiceImpl<WpsOrderWarningCfgMapper, WpsOrderWarningCfg> implements IWpsOrderWarningCfgService {


    @Resource
    private IFactoryService factoryService;

    @Override
    public WpsOrderWarningCfg getOne(String factoryCode, Integer warningLevel, String warningType) {
        LambdaQueryWrapper<WpsOrderWarningCfg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(factoryCode),WpsOrderWarningCfg::getFactoryCode,factoryCode);
        queryWrapper.eq(warningLevel!=null,WpsOrderWarningCfg::getWarningLevel,warningLevel);
        queryWrapper.eq(StringUtils.isNotBlank(warningType),WpsOrderWarningCfg::getWarningType,warningType);
        queryWrapper.last( " limit 1");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public Long add(WpsOrderWarningCfgDTO dto) {
        WpsOrderWarningCfg   entity = BeanUtil.map(dto,WpsOrderWarningCfg.class);
        WpsOrderWarningCfg one = this.getOne(dto.getFactoryCode(), dto.getWarningLevel(), dto.getWarningType());
        if (one != null) {
            throw new BusinessException(WpsExceptionEnum.RECORD_EXIST);
        }
        baseMapper.insert(entity);
        return entity.getId();
    }

    @Override
    public Integer edit(WpsOrderWarningCfgDTO dto) {
        WpsOrderWarningCfg   entity = BeanUtil.map(dto,WpsOrderWarningCfg.class);
        WpsOrderWarningCfg one = this.getOne(dto.getFactoryCode(), dto.getWarningLevel(), dto.getWarningType());
        if (one != null && !one.getId().equals(dto.getId())) {
            throw new BusinessException(WpsExceptionEnum.RECORD_EXIST);
        }
        return baseMapper.updateById(entity);
    }

    @Override
    public Page<WpsOrderWarningCfgDTO> page(String keyword, Collection<String> factoryCodes, Integer warningLevel, String warningType, Integer status, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<WpsOrderWarningCfg> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<WpsOrderWarningCfg> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(WpsOrderWarningCfg::getFactoryCode,keyword).or()
                    .like(WpsOrderWarningCfg::getWarningType,keyword));
        }
        queryWrapper.in(CollectionUtils.isNotEmpty(factoryCodes),WpsOrderWarningCfg::getFactoryCode,factoryCodes);
        queryWrapper.eq(warningLevel!=null,WpsOrderWarningCfg::getWarningLevel,warningLevel);
        queryWrapper.eq(warningType!=null,WpsOrderWarningCfg::getWarningType,warningType);
        queryWrapper.eq(status!=null,WpsOrderWarningCfg::getStatus,status);
        queryWrapper.orderByDesc(WpsOrderWarningCfg::getUpdateTime).orderByAsc(WpsOrderWarningCfg::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<WpsOrderWarningCfgDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<WpsOrderWarningCfgDTO> results = BeanUtil.mapList(entityPage.getRecords(), WpsOrderWarningCfgDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results);
            resultPage.setResult(results);
        }
        return resultPage;
    }
    
    private void decorate(List<WpsOrderWarningCfgDTO> sourceLis){
        if (CollectionUtils.isEmpty(sourceLis)) {
            return;
        }
       Map<String,String> factoryNameMap = factoryService.listByFactoryCodes(sourceLis.stream().map(WpsOrderWarningCfgDTO::getFactoryCode).collect(Collectors.toSet()))
               .stream().collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        sourceLis.stream().forEach(wpsOrderWarningCfgDTO -> wpsOrderWarningCfgDTO.setFactoryName(factoryNameMap.get(wpsOrderWarningCfgDTO.getFactoryCode())));
    }

    @Override
    public WpsOrderWarningCfgDTO getDetail(Long id) {
        WpsOrderWarningCfg entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        WpsOrderWarningCfgDTO res = BeanUtil.map(entity, WpsOrderWarningCfgDTO.class);
        this.decorate(Lists.newArrayList(res));
        return res;
    }

    @Override
    public List<WpsOrderWarningCfg> listByFactoryCode(String factoryCode) {
        LambdaQueryWrapper<WpsOrderWarningCfg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WpsOrderWarningCfg::getFactoryCode, factoryCode)
                .eq(WpsOrderWarningCfg::getStatus, 1);
        return baseMapper.selectList(queryWrapper);
    }
}
