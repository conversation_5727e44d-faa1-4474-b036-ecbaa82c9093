package com.lds.oneplanning.wps.warning.workbench.handlers;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.lds.basic.account.user.api.UserApi2;
import com.lds.basic.account.user.dto.UserDto;
import com.lds.basic.common.enums.BooleanEnum;
import com.lds.oneplanning.esb.datafetch.model.ProdOrderVO;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.wps.entity.*;
import com.lds.oneplanning.wps.enums.LightColor;
import com.lds.oneplanning.wps.enums.OrderWarningLevelEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningCategoryEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.service.*;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import liquibase.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InProductionHandler implements IWpsWorkbenchWarningHandler {
    private final IWarningInProductionAbnormalService warningInProductionAbnormalService;
    private final WarningTodoListService todoListService;
    private final IMesProcessWorkOrderService mesProcessWorkOrderService;
    private final IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    private final IEsbDataFetchService esbDataFetchService;
    private final IWpsRowExtService wpsRowExtService;
    private final UserApi2 userApi2;
    private static final List<String> VALID_STATUSES = Arrays.asList("DLV", "TECO", "CLSD", "DLIT", "结算", "DLDT", "标记");

    @Override
    public List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext ctx, Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap) {
        return Collections.emptyList();
    }

    public void execute() {
        List<MesProcessWorkOrder> editEntityList = createAbnormal(1);
        if (CollUtil.isEmpty(editEntityList)) {
            return;
        }
        mesProcessWorkOrderService.updateBatchById(editEntityList);
    }

    /**
     * 创建异常
     *
     * @param pageNum
     */
    private List<MesProcessWorkOrder> createAbnormal(int pageNum) {
        List<MesProcessWorkOrder> processWorkOrderList = mesProcessWorkOrderService.findListByPage(pageNum, 1000);
        if (CollUtil.isEmpty(processWorkOrderList)) {
            return Lists.newArrayList();
        }
        pageNum++;
        Map<String, MesProcessWorkOrder> mesProcessWorkOrderMap = processWorkOrderList.stream()
                .collect(Collectors.toMap(MesProcessWorkOrder::getOrderNo, Function.identity(),
                        (v1, v2) -> v1));
        log.info("开始调用Sap查询");
        List<ProdOrderVO> sapOrderList = esbDataFetchService.getProdOrderList(mesProcessWorkOrderMap.keySet());
        List<MesProcessWorkOrder> editProcessWorkOrderList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(sapOrderList)) {
            List<String> validOrderNoList = Lists.newArrayList();
            List<WarningInProductionAbnormal> warningSaveList = Lists.newArrayList();
            sapOrderList.forEach(sapOrder -> {
                MesProcessWorkOrder mesProcessWorkOrder = mesProcessWorkOrderMap.get(sapOrder.getAUFNR());
                if (mesProcessWorkOrder == null){
                    return;
                }
                MesProcessWorkOrder editProcessEntity = MesProcessWorkOrder.builder().id(mesProcessWorkOrder.getId())
                        .inboundQuantity(sapOrder.getWEMNG().intValue())
                        .build();
                if (StringUtils.isNotEmpty(sapOrder.getZTXT())) {
                    editProcessEntity.setSapStatus(sapOrder.getZTXT());
                    boolean containsValidStatus = Arrays.stream(sapOrder.getZTXT().split(","))
                            .anyMatch(VALID_STATUSES::contains);
                    //订单状态已完结
                    if (containsValidStatus) {
                        validOrderNoList.add(sapOrder.getAUFNR());
                        editProcessEntity.setIsCompleted(BooleanEnum.TRUE.getCode());
                        //如果已入库数据大于等于订单数量，也算完结
                    } else if (sapOrder.getWEMNG() >= sapOrder.getPSMNG()) {
                        validOrderNoList.add(sapOrder.getAUFNR());
                        editProcessEntity.setIsCompleted(BooleanEnum.TRUE.getCode());
                    } else {
                        warningSaveList.add(buildInProduction(mesProcessWorkOrder, sapOrder));
                        editProcessEntity.setInProductionException(BooleanEnum.TRUE.getCode());
                    }
                }
                editProcessWorkOrderList.add(editProcessEntity);
            });
            if (CollUtil.isNotEmpty(warningSaveList)) {
                log.info("开始保存异常");
                setPlannerEmpNo(warningSaveList);
                warningInProductionAbnormalService.saveOrUpdate(warningSaveList);
                log.info("开始创建待办");
                createTodoList(warningSaveList);
                log.info("开始创建预警");
                wpsOrderPlanWarningService.batchSaveUnHandlerWarning(buildWpsOrderPlanWarning(warningSaveList));
            }
            if (CollUtil.isNotEmpty(validOrderNoList)) {
                log.info("开始消警");
                List<WarningInProductionAbnormal> existWarning = warningInProductionAbnormalService.listByOrderNo(validOrderNoList);
                log.info("消除Todo表预警");
                todoListService.eliminateAlarms(existWarning.stream().map(WarningInProductionAbnormal::getId).collect(Collectors.toSet()), getWarningType());
                log.info("消除WpsOrderPlanWarning预警");
                wpsOrderPlanWarningService.eliminateAlarms(getWarningType(), validOrderNoList);
            }
        }
        List<MesProcessWorkOrder> editList = createAbnormal(pageNum);
        if (CollUtil.isNotEmpty(editList)){
            editProcessWorkOrderList.addAll(editList);
        }
        return editProcessWorkOrderList;
    }

    /**
     * 设置计划员工号
     *
     * @param warningSaveList
     */
    private void setPlannerEmpNo(List<WarningInProductionAbnormal> warningSaveList) {
        List<String> orderNoList = warningSaveList.stream().map(WarningInProductionAbnormal::getOrderNo).collect(Collectors.toList());
        Map<String, String> orderNoEmpNoMap = wpsRowExtService.listByBizIds(orderNoList).stream()
                .collect(Collectors.toMap(WpsRowExt::getBizId, WpsRowExt::getPlannerEmpNo, (a, b) -> a));
        warningSaveList.forEach(warning -> warning.setPlannerEmpNo(orderNoEmpNoMap.get(warning.getOrderNo())));
    }

    private List<WpsOrderPlanWarning> buildWpsOrderPlanWarning(List<WarningInProductionAbnormal> warningSaveList) {
        return warningSaveList.stream()
                .map(warning -> buildWpsOrderPlanWarning(warning.getFactoryCode(), -1L, warning.getOrderNo(), warning.getLineUuid(), warning.getLineCode(),
                        OrderWarningLevelEnum.WARNING, LocalDateTimeUtil.of(warning.getSchedulingDate()).toLocalDate()))
                .collect(Collectors.toList());
    }

    private WarningInProductionAbnormal buildInProduction(MesProcessWorkOrder mesProcessWorkOrder, ProdOrderVO prodOrder) {
        WarningInProductionAbnormal build = BeanUtil.copyProperties(mesProcessWorkOrder, WarningInProductionAbnormal.class);
        build.setInboundQuantity(prodOrder.getWEMNG().intValue());
        build.setLightColor(LightColor.RED);
        //当前日期-物料下架日期<=2天，则为黄灯
        if (mesProcessWorkOrder.getMaterialOffShelfDate() != null
                && ChronoUnit.DAYS.between(LocalDateTimeUtil.of(mesProcessWorkOrder.getMaterialOffShelfDate()).toLocalDate(), LocalDate.now()) <= 2L) {
            build.setLightColor(LightColor.YELLOW);
        }
        return build;
    }

//    public static void main(String[] args) {
//        LocalDate localDate = LocalDate.of(2025, 5, 25);
//        long between = ChronoUnit.DAYS.between(LocalDate.now(), localDate);
//        System.out.println((int)between);
//    }

    /**
     * 构建异常
     *
     * @param warningList
     */
    private void createTodoList(List<WarningInProductionAbnormal> warningList) {
        Set<String> jobNos = warningList.stream().map(WarningInProductionAbnormal::getLineLeaderGh)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        Map<String, String> jobAssigneeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(jobNos)) {
            UserDto userDto = new UserDto();
            userDto.setJobNos(jobNos);
            userDto.setUserStatus("normal");
            jobAssigneeMap = userApi2.findDto(userDto).stream().collect(Collectors.toMap(UserDto::getJobNo, UserDto::getLoginName));
        }
        Map<String, String> finalJobAssigneeMap = jobAssigneeMap;
        List<WarningTodoList> todoList = warningList.stream()
                //TODO ：如果找不到计划员，写死发给宝庭
                .map(e -> {
                    String assignee = finalJobAssigneeMap.getOrDefault(e.getLineLeaderGh(), "qiubt");
                    return new WarningTodoList(getWarningType(), e.getFactoryCode(), e.getId(), assignee);
                })
                .collect(Collectors.toList());
        todoListService.saveData(getWarningType(), todoList);
    }

    @Override
    public WpsOrderWarningCategoryEnum getWarningCategory() {
        return WpsOrderWarningCategoryEnum.DEFAULT;
    }

    @Override
    public WpsOrderWarningTypeEnum getWarningType() {
        return WpsOrderWarningTypeEnum.IN_PRODUCTION_EXCEPTION;
    }
}
