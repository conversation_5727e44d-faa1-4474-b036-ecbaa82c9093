package com.lds.oneplanning.wps.warning.workbench.handlers;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.common.service.IBasicUserService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.esb.datafetch.model.EsbShipmentData;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.entity.WarningShipBookingUrgentAbnormal;
import com.lds.oneplanning.wps.entity.WarningTodoList;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.entity.WpsOrderWarningCfg;
import com.lds.oneplanning.wps.enums.*;
import com.lds.oneplanning.wps.helper.WpsRowDataMergeHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWarningShipBookingUrgentAbnormalService;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 船期临近未订舱-告警处理记录生成
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ShipBookingUrgentHandler extends AbstractWpsWorkbenchWarningHandler {

    private final IBasicUserService basicUserService;
    private final EsbDataFetchService esbDataFetchService;
    private final WarningTodoListService warningTodoListService;
    private final IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    private final IWarningShipBookingUrgentAbnormalService warningShipBookingUrgentAbnormalService;

    private static final List<String> FILTER_BOOKING_STATUS = Lists.newArrayList("未排载","已排载");

    public WpsOrderWarningCategoryEnum getWarningCategory() {
        return WpsOrderWarningCategoryEnum.DEFAULT;
    }

    @Override
    public WpsOrderWarningTypeEnum getWarningType() {
        return WpsOrderWarningTypeEnum.SHIP_BOOKING_URGENT;
    }

    @Override
    public List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext context,
                                             Map<Integer, WpsOrderWarningCfg> warningCfgMap) {
        log.info("Starting ship booking urgent exception handling");
        try {
            return doExecute(context, warningCfgMap);
        } catch (Exception e) {
            log.error("Failed to process ship booking urgent exceptions", e);
            return Collections.emptyList();
        } finally {
            log.info("Completed ship booking urgent exception handling");
        }
    }

    private List<WpsOrderPlanWarning> doExecute(WpsWorkbenchWarningContext context,
                                                Map<Integer, WpsOrderWarningCfg> warningCfgMap) {
        if (context == null || CollectionUtils.isEmpty(context.getOrders())) {
            log.debug("Context or orders list is empty");
            return Collections.emptyList();
        }
        List<WpsRowData> orders = context.getOrders();
        LocalDate now = LocalDate.now();
        LocalDate endDate = now.plusDays(30);

        List<WpsRowData> filteredOrders = orders.stream()
                .filter(order -> isValidOrder(order, now, endDate))
                .collect(Collectors.toList());
        if (filteredOrders.isEmpty()) {
            log.debug("No orders within 30-day window");
            return Collections.emptyList();
        }
        // 合并多产线订单
        List<WpsRowData> mergedOrders = WpsRowDataMergeHelper.mergeSameOrder(filteredOrders);
        // 查询船期信息
        Map<String, EsbShipmentData> shipmentDataMap = fetchShipmentData(mergedOrders);
        List<WarningShipBookingUrgentAbnormal> warnings = buildWarnings(mergedOrders, shipmentDataMap, warningCfgMap);
        //消警
        eliminateAlarms(warnings);
        SpringUtil.getBean(ShipBookingUrgentHandler.class).saveData(context, warnings);
        // 主表消警
        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
        return convertData(context, mergedOrders, warnings);
    }

    private List<WpsOrderPlanWarning> convertData(WpsWorkbenchWarningContext ctx, List<WpsRowData> abnormalList, List<WarningShipBookingUrgentAbnormal> warningList) {
        Map<String, LightColor> lightColorMap = warningList.stream()
                .filter(w -> w.getOrderNo() != null)
                .collect(Collectors.toMap(WarningShipBookingUrgentAbnormal::getOrderNo, WarningShipBookingUrgentAbnormal::getLightColor, (o, o2) -> o));
        return buildWarning(ctx, abnormalList, lightColorMap);
    }

    private boolean isValidOrder(WpsRowData order, LocalDate startDate, LocalDate endDate) {
        return Optional.ofNullable(order.getOnLineDate())
                .filter(date -> !date.isBefore(startDate) && !date.isAfter(endDate))
                .isPresent() && StrUtil.isNotEmpty(order.getSellOrderNo())
                && StrUtil.isNotEmpty(order.getRowItem());
    }

    /**
     * 消除告警，根据订单号来判断
     *
     * @param warningList 告警信息列表
     */
    private void eliminateAlarms(List<WarningShipBookingUrgentAbnormal> warningList) {
        List<WarningShipBookingUrgentAbnormal> existData = warningShipBookingUrgentAbnormalService.queryUnHandleData();
        log.info("船期临近未订舱异常-获取未处理的警告数据计数: {}", existData.size());

        Set<String> newWarningOrderNoList = warningList.stream()
                .map(WarningShipBookingUrgentAbnormal::getOrderNo)
                .collect(Collectors.toSet());

        Set<Long> toBeRemoveIds = existData.stream()
                .filter(e -> !newWarningOrderNoList.contains(e.getOrderNo()))
                .map(WarningShipBookingUrgentAbnormal::getId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(toBeRemoveIds)) {
            log.info("船期临近未订舱异常-消警列表：{}", toBeRemoveIds);
            warningTodoListService.lambdaUpdate()
                    .set(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.CLOSED)
                    .in(WarningTodoList::getBizId, toBeRemoveIds)
                    .eq(WarningTodoList::getWarningType, this.getWarningType())
                    .update();
        }
        //主表消警
        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
    }

    private List<WarningShipBookingUrgentAbnormal> buildWarnings(List<WpsRowData> orders,
                                                                 Map<String, EsbShipmentData> shipmentDataMap,
                                                                 Map<Integer, WpsOrderWarningCfg> warningCfgMap) {
        return orders.stream()
                .map(order -> createWarning(order, shipmentDataMap.get(order.getSellOrderAndRowItemNo()), warningCfgMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private WarningShipBookingUrgentAbnormal createWarning(WpsRowData order, EsbShipmentData shipmentData,
                                                           Map<Integer, WpsOrderWarningCfg> warningCfgMap) {
        if (shipmentData == null || StrUtil.isEmpty(shipmentData.getZDate())) {
            return null;
        }
        if (StrUtil.isEmpty(order.getOrderNo())) {
            return null;
        }
        // 只处理"未排载"和"已排载"状态的订单
        if (StrUtil.isEmpty(shipmentData.getZState()) || !FILTER_BOOKING_STATUS.contains(shipmentData.getZState())) {
            return null;
        }
        WarningShipBookingUrgentAbnormal warning = new WarningShipBookingUrgentAbnormal();
        warning.setFactoryCode(order.getFactory());
        warning.setOrderNo(order.getOrderNo());
        warning.setSellOrderNo(order.getSellOrderNo());
        warning.setRowItem(order.getRowItem());
        warning.setCustomer(order.getCustomerCode());
        warning.setCommodityId(order.getCommodityId());
        warning.setMaterialDescription(order.getCommodityDesc());
        warning.setOrderQuantity(order.getOrderPcsQty());
        warning.setBookingStatus(shipmentData.getZState());
        warning.setOmJobNos(shipmentData.getZGh());
        warning.setOmNames(shipmentData.getZZwm());
        warning.setOutboundDeliveryOrder(order.getOutDeliveryNo());
        warning.setOutboundDeliveryOrderItem(order.getOutRowItem());
        warning.setCreateTime(new Date());
        Optional.ofNullable(LocalDateTimeUtil.localDateParse(shipmentData.getZDate()))
                .ifPresent(shipScheduleDate -> {
                    warning.setShipScheduleDate(shipScheduleDate);
                    warning.setLightColor(getLightColor(shipScheduleDate, warningCfgMap));
                });
        return warning;
    }

    private Map<String, EsbShipmentData> fetchShipmentData(List<WpsRowData> orders) {
        Map<String, EsbShipmentData> shipmentDataMap = Maps.newHashMap();
        Lists.partition(orders, 100).forEach(subOrders -> {
            List<EsbShipmentData> requests = subOrders.stream()
                    .map(order -> new EsbShipmentData(order.getSellOrderNo(), order.getRowItem(),
                            StrUtil.join(",", FILTER_BOOKING_STATUS)))
                    .collect(Collectors.toList());
            try {
                List<EsbShipmentData> responses = esbDataFetchService.getShipmentDateData(requests);
                if (CollectionUtils.isNotEmpty(responses)) {
                    shipmentDataMap.putAll(responses.stream()
                            .collect(Collectors.toMap(EsbShipmentData::getSellOrderAndRowItemNo, e -> e,
                                    (o, o2) -> o)));
                }
            } catch (Exception e) {
                log.error("Failed to fetch shipment data for batch of size {}", subOrders.size(), e);
            }
        });
        return shipmentDataMap;
    }

    private LightColor getLightColor(LocalDate shipScheduleDate, Map<Integer, WpsOrderWarningCfg> warningCfgMap) {
        LocalDate now = LocalDate.now();
        int warningDays = Optional.ofNullable(super.getCfgIntegerFromJson(
                        WarningConditionEnum.BEFORE_PLAN_SHIP_DATE.getCode(), OrderWarningLevelEnum.WARNING.getLevel(), warningCfgMap))
                .orElse(21);
        int alertDays = Optional.ofNullable(super.getCfgIntegerFromJson(
                        AlertConditionEnum.BEFORE_PLAN_SHIP_DATE.getCode(), OrderWarningLevelEnum.ALARM.getLevel(), warningCfgMap))
                .orElse(14);
        if (shipScheduleDate.isBefore(now.plusDays(alertDays))) {
            return LightColor.RED;
        }
        if (shipScheduleDate.isBefore(now.plusDays(warningDays))) {
            return LightColor.YELLOW;
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveData(WpsWorkbenchWarningContext context, List<WarningShipBookingUrgentAbnormal> warnings) {
        if (CollectionUtils.isEmpty(warnings)) {
            log.debug("No warnings to save");
            return;
        }
        log.info("Saving {} warning records", warnings.size());
        saveOrUpdate(context, warnings);
        createTodoList(warnings);
        wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
    }

    private void saveOrUpdate(WpsWorkbenchWarningContext context, List<WarningShipBookingUrgentAbnormal> warnings) {
        Set<String> orderNos = warnings.stream()
                .map(WarningShipBookingUrgentAbnormal::getOrderNo)
                .collect(Collectors.toSet());

        List<WarningShipBookingUrgentAbnormal> existingRecords = queryExistShipBookingUrgentAbnormal(orderNos);
        Map<String, WarningShipBookingUrgentAbnormal> existingMap = existingRecords.stream()
                .collect(Collectors.toMap(WarningShipBookingUrgentAbnormal::getOrderNo, e -> e));

        CopyOptions copyOptions = CopyOptions.create().ignoreNullValue();
        List<WarningShipBookingUrgentAbnormal> updateRecords = Lists.newArrayList();
        List<WarningShipBookingUrgentAbnormal> insertRecords = Lists.newArrayList();

        warnings.forEach(warning -> {
            warning.setFactoryCode(context.getFactoryCode());
            warning.setUpdateBy(context.getUserId());
            if (existingMap.containsKey(warning.getOrderNo())) {
                WarningShipBookingUrgentAbnormal existing = existingMap.get(warning.getOrderNo());
                BeanUtil.copyProperties(warning, existing, copyOptions);
                // Update warning with ID
                BeanUtil.copyProperties(existing, warning, copyOptions);
                updateRecords.add(warning);
            } else {
                warning.setCreateBy(context.getUserId());
                insertRecords.add(warning);
            }
        });
        if (!updateRecords.isEmpty()) {
            warningShipBookingUrgentAbnormalService.updateBatchById(updateRecords);
            log.info("Updated {} warning records", updateRecords.size());
        }
        if (!insertRecords.isEmpty()) {
            warningShipBookingUrgentAbnormalService.saveBatch(insertRecords);
            log.info("Inserted {} warning records", insertRecords.size());
        }
    }

    private List<WarningShipBookingUrgentAbnormal> queryExistShipBookingUrgentAbnormal(Collection<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return Collections.emptyList();
        }
        return warningShipBookingUrgentAbnormalService.lambdaQuery()
                .in(WarningShipBookingUrgentAbnormal::getOrderNo, orderNoList)
                .list();
    }

    private void createTodoList(List<WarningShipBookingUrgentAbnormal> warnings) {
        if (CollectionUtils.isEmpty(warnings)) {
            log.debug("No warnings provided for to-do list creation");
            return;
        }
        Map<LightColor, List<WarningShipBookingUrgentAbnormal>> warningsByColor = warnings.stream()
                .filter(warning -> warning.getLightColor() == LightColor.YELLOW || warning.getLightColor() == LightColor.RED)
                .collect(Collectors.groupingBy(WarningShipBookingUrgentAbnormal::getLightColor));

        List<WarningTodoList> todos = Lists.newArrayList();
        Optional.ofNullable(warningsByColor.get(LightColor.YELLOW))
                .map(warningList -> createTodosForWarnings(warningList, false))
                .ifPresent(todos::addAll);
        Optional.ofNullable(warningsByColor.get(LightColor.RED))
                .map(warningList -> createTodosForWarnings(warningList, true))
                .ifPresent(todos::addAll);
        if (todos.isEmpty()) {
            log.debug("No to-do items created");
            return;
        }
        log.info("Creating {} to-do items", todos.size());
        warningTodoListService.saveData(getWarningType(), todos);
    }

    private List<WarningTodoList> createTodosForWarnings(List<WarningShipBookingUrgentAbnormal> warnings, boolean useLeader) {
        if (warnings == null || warnings.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, String> assigneeMap = getAssigneeMap(warnings, useLeader);
        return warnings.stream()
                .filter(w -> !StrUtil.isEmpty(w.getOmJobNos()))
                .flatMap(w -> Arrays.stream(w.getOmJobNos().split(","))
                        .map(String::trim)
                        .filter(omJobNo -> !StrUtil.isEmpty(omJobNo))
                        .map(omJobNo -> {
                            String assignee = assigneeMap.getOrDefault(omJobNo, "");
                            return StrUtil.isEmpty(assignee) ? null :
                                    new WarningTodoList(getWarningType(), w.getFactoryCode(), w.getId(), assignee);
                        })
                        .filter(Objects::nonNull))
                .collect(Collectors.toList());
    }

    private Map<String, String> getAssigneeMap(List<WarningShipBookingUrgentAbnormal> warnings, boolean useLeader) {
        if (CollectionUtils.isEmpty(warnings)) {
            log.debug("No warnings provided for assignee lookup (useLeader={})", useLeader);
            return Collections.emptyMap();
        }
        Set<String> omJobNos = warnings.stream()
                .filter(w -> StrUtil.isNotEmpty(w.getOmJobNos()))
                .flatMap(w -> Arrays.stream(w.getOmJobNos().split(","))
                        .map(String::trim)
                        .filter(StrUtil::isNotEmpty))
                .collect(Collectors.toSet());
        if (omJobNos.isEmpty()) {
            log.debug("No valid omJobNo values found (useLeader={})", useLeader);
            return Collections.emptyMap();
        }
        try {
            Map<String, String> assigneeMap = useLeader
                    ? basicUserService.batchGetLeaderLoginNames(omJobNos)
                    : basicUserService.batchGetLoginNamesByJobNos(omJobNos);
            Map<String, String> result = assigneeMap != null ? assigneeMap : Collections.emptyMap();
            log.info("Retrieved {} assignees for {} omJobNo values (useLeader={})", result.size(), omJobNos.size(), useLeader);
            return result;
        } catch (Exception e) {
            log.error("Failed to retrieve assignees (useLeader={}) for {} omJobNo values", useLeader, omJobNos.size(), e);
            return Collections.emptyMap();
        }
    }
}