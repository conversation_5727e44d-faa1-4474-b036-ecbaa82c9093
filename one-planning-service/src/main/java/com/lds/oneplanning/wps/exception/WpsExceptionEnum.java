package com.lds.oneplanning.wps.exception;

import com.iot.common.exception.IBusinessException;

/**
 * <AUTHOR>
 */
public enum WpsExceptionEnum implements IBusinessException {
    CODE_EXIST(5000001, "CODE已存在！"),
    RECORD_EXIST(5000001, "记录已存在！"),
    RECORD_NOT_EXIST(5000002, "记录不存在！"),
    ID_CANNOT_BE_EMPTY(5000004, "id不能为空"),
    DISTRIBUTE_LOCK_EXCEPTION(5000005, "分布式锁异常"),

    ;
    /**
     * 异常代码
     */
    private Integer code;

    /**
     * 异常描述
     */
    private String messageKey;

    /**
     * 描述：构建异常
     *
     * @param code       错误代码
     * @param messageKey 错误描述
     * @return
     * <AUTHOR>
     * @created 2017年3月21日 上午10:50:58
     * @since
     */
    WpsExceptionEnum(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    public static WpsExceptionEnum getByCode(Integer code) {
        for (WpsExceptionEnum businessExceptionEnum : WpsExceptionEnum.values()) {
            if (businessExceptionEnum.code.equals(code)) {
                return businessExceptionEnum;
            }
        }
        return null;
    }
}
