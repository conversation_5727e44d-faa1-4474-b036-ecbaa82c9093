package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.entity.WpsSchedulePlanLog;
import com.lds.oneplanning.wps.model.WpsSchedulePlanLogDTO;
import com.lds.oneplanning.wps.model.WpsSchedulePlanLogQueryDTO;

import java.util.List;

public interface IWpsSchedulePlanLogService extends IService<WpsSchedulePlanLog> {
    /**
     * 分页获取排产错误日志
     * @param query
     * @return
     */
    Page<WpsSchedulePlanLogDTO> findPage(WpsSchedulePlanLogQueryDTO query);

    List<WpsSchedulePlanLogDTO> findList(WpsSchedulePlanLogQueryDTO query);

    void batchSave(List<WpsSchedulePlanLog> logList,String factoryCode);

    /**
     * 通过工厂编码删除排产错误日志
     * @param factoryCode
     */
    void deleteByFactoryCode(String factoryCode);
}
