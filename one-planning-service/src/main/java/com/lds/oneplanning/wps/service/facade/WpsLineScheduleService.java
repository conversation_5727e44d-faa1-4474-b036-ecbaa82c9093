package com.lds.oneplanning.wps.service.facade;

import com.lds.oneplanning.wps.model.OrderScheduleDTO;
import com.lds.oneplanning.wps.model.LineScheduleViewDTO;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: zhuang<PERSON><PERSON><PERSON>
 * @Email: zhuang<PERSON><PERSON><EMAIL>
 * @Date: 2025/5/8 15:04
 */
public interface WpsLineScheduleService {
    List<OrderScheduleDTO> getLineScheduleDetail(Integer datasource, Long userId, Date startTime, Date endTime, String factoryCode, boolean cacheFlag);

    Integer saveScheduleData(List<OrderScheduleDTO> updateList, Long userId);

    Integer saveScheduleData(List<OrderScheduleDTO> updateList, Long userId, String factoryCode);
    List<LineScheduleViewDTO> getLineScheduleView(Integer viewType, Integer source, Long userId, Date startTime, Date endTime, String factoryCode, boolean b);
}
