package com.lds.oneplanning.wps.utils;

import com.lds.oneplanning.basedata.enums.OrderSubTypeEnum;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.common.utils.OrderArithUtil;
import com.lds.oneplanning.esb.model.AtpLackOffMaterialRespVO;
import com.lds.oneplanning.esb.model.AtpSingleMaterialResp;
import com.lds.oneplanning.mps.model.RowSaveData;
import com.lds.oneplanning.wps.entity.WpsDayPlan;
import com.lds.oneplanning.wps.entity.WpsFormInfo;
import com.lds.oneplanning.wps.entity.WpsRowExt;
import com.lds.oneplanning.wps.model.OrderPlanCompleteDTO;
import com.lds.oneplanning.wps.model.OrderReviewData;
import com.lds.oneplanning.wps.model.OrderScheduleDTO;
import com.lds.oneplanning.wps.model.WpsRowData;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;





/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/27 15:12
 */
@Slf4j
public class WpsTransUtils {
    private WpsTransUtils() {
    }

    public static OrderReviewData getRowDatFromOrderReview(Map<String, Object> objectMap) {
        OrderReviewData rowData  =  new OrderReviewData();

        Object sellOrderNo = objectMap.get("XSDDH"); // 销售订单号
        Object rowItem = objectMap.get("HXMH"); // 行项目号
        Object bizSampleQty = objectMap.get("YWLYSL");//业务留样数量
        Object qcSampleQty = objectMap.get("PBLYSL");//品保留样数量
        Object lightDeliveryTime = objectMap.get("GYJQ"); // 光源交期
        Object structDeliveryTime = objectMap.get("JGJJQ"); // 结构件交期
        Object isInspect = objectMap.get("SFYH"); // 是否验货
        Object packageType = objectMap.get("BZFS"); // 包装方式
        Object omRespons = objectMap.get("SQR"); // 申请人
        Object originalInspectTime = objectMap.get("YHRQ"); // 验货交期
        Object originalLoadTime = objectMap.get("ZGRQ"); // 装柜日期
        Object originalShipTime = objectMap.get("CJQ"); // 船交期
        Object originalOnlineTime = objectMap.get("WGRQ"); // 完工日期 -- 相当原始完工日期

        rowData.setSellOrderNo(getStringValue(sellOrderNo));
        rowData.setRowItem(getStringValue(rowItem));
        rowData.setBizSampleQty(getIntegerValue(bizSampleQty));
        rowData.setQcSampleQty(getIntegerValue(qcSampleQty));
//        rowData.setLightDeliveryTime(getDateValue(lightDeliveryTime));
//        rowData.setStructDeliveryTime(getDateValue(structDeliveryTime));
        String inspectDesc = getStringValue(isInspect);
        String inspectValue = StringUtils.isNotBlank(inspectDesc) && (inspectDesc.contains("是")||inspectDesc.contains("Y")) ? "Y" : "N";
        rowData.setIsInspect(inspectValue);
        rowData.setPackageType(getStringValue(packageType));
        rowData.setOmRespons(getStringValue(omRespons));
        rowData.setOriginalInspectTime(getDateValue(originalInspectTime));
        rowData.setOriginalLoadTime(getDateValue(originalLoadTime));
        rowData.setOriginalShipTime(getDateValue(originalShipTime));
        rowData.setOriginalFinishTime(getDateValue(originalOnlineTime));

        return rowData;
    }
    public static void coverOrderReviewData(OrderReviewData source,WpsRowData target){
        // 这两个编号不要覆盖
//        target.setSellOrderNo(source.getSellOrderNo());
//        target.setRowItem(source.getRowItem());
        target.setBizSampleQty(source.getBizSampleQty());
        target.setQcSampleQty(source.getQcSampleQty());
//        target.setLightDeliveryTime(source.getLightDeliveryTime());
//        target.setStructDeliveryTime(source.getStructDeliveryTime());
        target.setIsInspect(source.getIsInspect());
        target.setPackageType(source.getPackageType());
        target.setOmRespons(source.getOmRespons());
        target.setOriginalInspectTime(source.getOriginalInspectTime());
        target.setOriginalLoadTime(source.getOriginalLoadTime());
        target.setOriginalShipTime(source.getOriginalShipTime());
        // 开启原始完工日期的覆盖
        if (source.getOriginalFinishTime() != null ) {
            target.setOriginalFinishTime(source.getOriginalFinishTime());
        }
    }

   public static List<WpsFormInfo> scheduleToFormList(List<OrderScheduleDTO> sourceList,Long userId,String empNo){
       if (sourceList == null ||  sourceList.isEmpty()) {
           return Lists.newArrayList();
       }
       List<WpsFormInfo> resList = Lists.newArrayList();
       for (OrderScheduleDTO scheduleDTO : sourceList){
           WpsFormInfo wpsFormInfo = new WpsFormInfo();
           wpsFormInfo.setBizId(scheduleDTO.getOrderNo());
           wpsFormInfo.setPlannerEmpNo(empNo);
           wpsFormInfo.setLineCode(scheduleDTO.getLineCode());
           wpsFormInfo.setLineUuid(scheduleDTO.getLineUuid());
           wpsFormInfo.setOrderType(scheduleDTO.getOrderType());
           wpsFormInfo.setOrderPcsQty(scheduleDTO.getOrderPcsQty());
           wpsFormInfo.setCreateBy(userId);
           wpsFormInfo.setUpdateBy(userId);
           wpsFormInfo.setUpdateTime(new Date());
           // 其他字段不设置不会更新
           resList.add(wpsFormInfo);
       }
       return resList;
   }
    public static List<WpsDayPlan> scheduleToWpsDayPlanList(List<OrderScheduleDTO> sourceList,Long userId){
        if (sourceList == null ||  sourceList.isEmpty()) {
            return Lists.newArrayList();
        }
        List<WpsDayPlan> targetList  = Lists.newArrayList();
        // 根据订单号+ 线体+日期，更新排产数量
        for (OrderScheduleDTO dto : sourceList){
            Map<LocalDate, Number> scheduleDataMap = dto.getScheduleDataMap();
            for (Map.Entry<LocalDate,Number> entry : scheduleDataMap.entrySet()){
                LocalDate scheduleDay = entry.getKey();
                Number qty = entry.getValue();
                if (qty == null || qty.intValue()==0) {
                    continue;
                }
                WpsDayPlan dayPlan = new WpsDayPlan();
                dayPlan.setBizId(dto.getOrderNo());
                dayPlan.setLineCode(dto.getLineCode());
                dayPlan.setLineUuid(dto.getLineUuid());
                dayPlan.setScheduleDate(scheduleDay);
                dayPlan.setPrePlanQuantity(qty.intValue());
                dayPlan.setCreateBy(userId);
                dayPlan.setUpdateBy(userId);
                targetList.add(dayPlan);
            }
        }
        return targetList;
    }

    public static List<WpsRowExt> scheduleToWpsRowExtList (List<OrderScheduleDTO> sourceList,Long userId,String empNo){
        int seq = 1;
        List<WpsRowExt> totalWpsRowExts = Lists.newArrayList();
        for (OrderScheduleDTO dto  : sourceList){
            LocalDate startScheduleDate = WpsDateUtil.getStartScheduleDate(dto.getScheduleDataMap());
            if (startScheduleDate == null) {
                continue;
            }
            WpsRowExt wpsRowExt = new WpsRowExt();
            wpsRowExt.setBizId(dto.getOrderNo());
            wpsRowExt.setFactoryCode(dto.getFactoryCode());
            wpsRowExt.setPlannerEmpNo(empNo);
            wpsRowExt.setLineCode(dto.getLineCode());
            wpsRowExt.setLineUuid(dto.getLineUuid());
            wpsRowExt.setScheduleDate(startScheduleDate);
            wpsRowExt.setScheduleYear(startScheduleDate.getYear());
            wpsRowExt.setScheduleWeek(LocalDateTimeUtil.getWeekSeqOfYear(startScheduleDate));
            wpsRowExt.setFrozenStatus(dto.getFrozenStatus());
            wpsRowExt.setScheduleSeq(wpsRowExt.getScheduleYear()*1000000000L+startScheduleDate.getMonthValue()*100000000L+startScheduleDate.getDayOfMonth()*100000L+(seq++));
            wpsRowExt.setCreateBy(userId);
            wpsRowExt.setUpdateBy(userId);
            if (MapUtils.isNotEmpty(dto.getScheduleDataMap())){
                dto.getScheduleDataMap().entrySet().stream().filter(entry -> null != entry.getValue())
                        .map(Map.Entry::getKey).max(LocalDate::compareTo).ifPresent(wpsRowExt::setEstProductionFinishDate);
            }
            totalWpsRowExts.add(wpsRowExt);
        }
        return totalWpsRowExts;
    }



    public static List<WpsFormInfo> wpsRowDatasToFormList(List<RowSaveData> sourceList){
        if (sourceList == null ||  sourceList.isEmpty()) {
            return Lists.newArrayList();
        }
        List<WpsFormInfo> resList = Lists.newArrayList();
        sourceList.forEach(saveData -> resList.add(wpsRowDataToForm(saveData)) );
        return resList;
    }
    public static WpsFormInfo wpsRowDataToForm(RowSaveData saveData){
        WpsFormInfo formInfo = new WpsFormInfo();
        formInfo.setLineUuid(saveData.getLineUuid());
        formInfo.setLineCode(saveData.getLineCode());
        formInfo.setPlannerEmpNo(saveData.getEmpNo());
        formInfo.setBizId(saveData.getOrderNo());
        formInfo.setOldReplyCompletionDate(saveData.getOriginalReplyTime());
        formInfo.setNewReplyCompletionDate(saveData.getLatestReplyTime());
        formInfo.setRemarkOne(saveData.getRemark1());
        formInfo.setRemarkTwo(saveData.getRemark2());
        formInfo.setPackagingOrFinalAssembly(saveData.getFinalAssembly());
        formInfo.setRemarkHazardousInfo(saveData.getRiskMaterialRemark());
        formInfo.setGuestSeq(saveData.getCustomerSeq());
        formInfo.setLineCategoryCode(saveData.getLineCategoryCode());
        formInfo.setReportQty(saveData.getReportedPcsQty());
        formInfo.setOrderType(saveData.getOrderType());
        formInfo.setOrderPcsQty(saveData.getOrderPcsQty());
        formInfo.setCreateTime(new Date());
        formInfo.setUpdateTime(new Date());
        return formInfo;
    }

    public static WpsRowData getWpsRowData(Map<String, Object> objMap){
        Object packagePrint = objMap.get("ZCZBM"); // 包材版面
        Object customerGroup = objMap.get("LOCCO"); // 客户组
        Object customerCode = objMap.get("SORTL"); // 客户代码
        Object sellOrderNo = objMap.get("VBELN"); //销售订单号
        Object rowItem = objMap.get("POSNR"); // 行项目
        Object factory = objMap.get("WERKS"); // 工厂(自有或外部)
        Object orderType = objMap.get("ZTYPE"); // 订单类型
        Object orderNo = objMap.get("ZDDH"); //订单号
        Object productOrderType = objMap.get("AUART"); // 生产订单子类型
        Object customerNo = objMap.get("KUNNR"); // 客户编号
        Object outDeliveryNo = objMap.get("WXJHD"); // 外向交货单
        Object outRowItem = objMap.get("WXJHH"); // 外向行项目
        Object commodityId = objMap.get("ZSPID"); // 商品id
        Object commodityDesc = objMap.get("ARKTX"); // 商品描述
        Object crossPlantMaterialStatus = objMap.get("MSTAE"); // 跨工厂物料状态
        Object productLineLevel3 = objMap.get("Z10859"); // 三级产品线
        Object productLineLevel5 = objMap.get("Z10861"); // 五级产品线
        Object category = objMap.get("Z10302"); // 产品大类
        Object productId = objMap.get("CPIDZ"); // 产品I
        Object productDesc = objMap.get("ZCPMS"); // 产品描述
        Object orderUnitQty = objMap.get("ZMENG"); // 套数
        Object orderPcsQty = objMap.get("DDLTS"); // 只数
        Object transQty = objMap.get("Z11165"); // 转化数量
        Object stockedPcsQty = objMap.get("GSMNG"); // 已入库数量
        Object productStartTime = objMap.get("PSTTR"); //  生产开始日期
        Object productEndTime = objMap.get("PEDTR"); //  生产结束日期
        Object productUnitPrice = objMap.get("ZSPDJ"); // 产品单价
        Object orderSignFinishTime = objMap.get("ZPSDAT3"); // 订单签发完成日期



        Object originalOnlineTime = objMap.get("YSWGRQ"); // 原始完工日期
        Object finalFinishTime = objMap.get("ZZWGRQ"); // 最终完工日期
        Object originalLoadTime = objMap.get("YSGZRQ"); // 原始装柜日期
        Object latestLoadTime = objMap.get("ZZZGRQ"); // 最终装柜日期
        Object originalInspectTime = objMap.get("YSYHRQ"); // 原始验货日期
        Object latestInspectTime = objMap.get("YZYHRQ"); // 最终验货日期


        Object packageType = objMap.get("Z10227"); // 包装方式
        Object powerNum = objMap.get("Z10078"); // 高岛雄平瓦数
        Object size = objMap.get("Z10294"); // 尺寸(方形/原型/规格)
        Object virtualOrderNo = objMap.get("VBELN_V"); // 虚拟订单号
        Object virtualRowItem = objMap.get("POSNR_V"); // 虚拟订单项目号
        Object firstReviewShipTime = objMap.get("ZBPDATE1"); // 首次评审船期
        Object customerMaterialNo = objMap.get("KDMAT"); // 客户物料
        Object lightComponentId = objMap.get("ZGYID"); // 光源ID
        Object pono = objMap.get("BSTKD_E"); // 收货方的客户参考

//        Object orderUnitQty = objMap.get("DDLTS"); // 订单套数

        Object MATNR = objMap.get("MATNR"); // 物料编号
        Object ARKTX = objMap.get("ARKTX"); // 销售订单项目短文本

        Object NETWR = objMap.get("NETWR"); // 凭证货币计量的净价值
        Object KPEIN = objMap.get("KPEIN"); // 条件定价单位
        Object WAERK = objMap.get("WAERK"); // SD 凭证货币
        Object VRKME = objMap.get("VRKME"); // 销售单位
        Object ERDAT = objMap.get("ERDAT"); // 记录创建日期
        Object ERZET = objMap.get("ERZET"); // 输入时间
        Object ETTYP = objMap.get("ETTYP"); // 计划行类别

        Object ZWCBZ = objMap.get("ZWCBZ"); // 出货完成标识
        Object IDNRK = objMap.get("IDNRK"); // 组件
        Object ZTYPE = objMap.get("ZTYPE"); // 订单类型
        Object LOCCO = objMap.get("LOCCO"); // 城市协调
        Object WXJHD = objMap.get("WXJHD"); // 交货
        Object WXJHH = objMap.get("WXJHH"); // 交货项目


        Object MAKTX = objMap.get("MAKTX"); // 物料描述
        Object LFDAT = objMap.get("LFDAT"); // 交货日期
        Object ZCHZJ = objMap.get("ZCHZJ"); // 制程组件

        WpsRowData rowData = new WpsRowData();
        rowData.setPackagePrint(getStringValue(packagePrint));
        rowData.setMainPlan("");
        rowData.setWorkshopCode("");
        rowData.setLineCode("");
        rowData.setCustomerGroup(getStringValue(customerGroup));
        rowData.setCustomerCode(getStringValue(customerCode));
        rowData.setSellOrderNo(getStringValue(sellOrderNo));
        rowData.setRowItem(getStringValue(rowItem));
        rowData.setOmRespons("");
        rowData.setFactory(getStringValue(factory));
        rowData.setOrderType(getStringValue(orderType));
        rowData.setOrderNo(getStringValue(orderNo));
        rowData.setProductOrderTypeCode(getStringValue(productOrderType));
        rowData.setProductOrderType(OrderSubTypeEnum.getNameByCode(getStringValue(productOrderType)));
        rowData.setOutDeliveryNo(getStringValue(outDeliveryNo));
        rowData.setOutRowItem(getStringValue(outRowItem));
        // 这个玩意回来的时候补了一堆的0,通过转数字处理前面的0
        String dirtyCommodityId = getStringValue(commodityId);
        rowData.setCommodityId(dirtyCommodityId == null ? null : new BigDecimal(dirtyCommodityId).toString());
        rowData.setCommodityDesc(getStringValue(commodityDesc));
        rowData.setCrossPlantMaterialStatus(getStringValue(crossPlantMaterialStatus));
        rowData.setProductLineLevel3(getStringValue(productLineLevel3));
        rowData.setProductLineLevel5(getStringValue(productLineLevel5));
        rowData.setCategory(getStringValue(category));
        rowData.setMachineModel("");
        rowData.setSpecification("");
        rowData.setProductId(getStringValue(productId));
        rowData.setProductDesc(getStringValue(productDesc));
        rowData.setOrderUnitQty(getIntegerValue(orderUnitQty));
        rowData.setOrderPcsQty(Optional.ofNullable(getIntegerValue(orderPcsQty)).orElse(0));
        //转化数量，如果为空则设置为1
        rowData.setTransQty(Optional.ofNullable(getIntegerValue(transQty)).orElse(1));
        rowData.setReportedPcsQty(0);
        rowData.setStockedPcsQty(Optional.ofNullable(getIntegerValue(stockedPcsQty)).orElse(0));
        rowData.setSchedulePcsQty(0);
        rowData.setBizSampleQty(0);
        rowData.setQcSampleQty(0);
//        rowData.setLightComponentId(getStringValue(lightComponentId));
//        rowData.setLightDeliveryTime(new Date());
//        rowData.setDriverComponentId("");
//        rowData.setDriverDeliveryTime(new Date());
//        rowData.setStructDeliveryTime(new Date());
//        rowData.setPackageDeliveryTime(new Date());
        rowData.setStdWorkHours("");
        rowData.setPackageType(getStringValue(packageType));
        rowData.setCapacityStruct("");
        rowData.setOnlineTime(null);
        rowData.setOriginalFinishTime(getDateValue(originalOnlineTime));
        rowData.setIsInspect("");
        rowData.setOriginalInspectTime(getDateValue(originalInspectTime));
        rowData.setLatestInspectTime(getDateValue(latestInspectTime));
        rowData.setOriginalLoadTime(getDateValue(originalLoadTime));
        rowData.setLatestLoadTime(getDateValue(latestLoadTime));
        rowData.setOriginalShipTime(getDateValue(originalLoadTime));
        rowData.setFinalShipTime(null);
        rowData.setEstFinishTime(getDateValue(finalFinishTime));
        rowData.setProductStartTime(getDateValue(productStartTime));
        rowData.setProductEndTime(getDateValue(productEndTime));
        rowData.setOriginalReplyTime(null);
        rowData.setLatestReplyTime(null);
        rowData.setRemark1("");
        rowData.setRemark2("");

        rowData.setProductUnitPrice(getNumberValue(productUnitPrice));
        rowData.setOrderSignFinishTime(getDateValue(orderSignFinishTime));
        rowData.setFinalAssembly("");
        rowData.setRiskMaterialRemark("");
        rowData.setVirtualOrderNo(getStringValue(virtualOrderNo));
        rowData.setVirtualRowItem(getStringValue(virtualRowItem));
        rowData.setFirstReviewShipTime(getDateValue(firstReviewShipTime));
        rowData.setPono(getStringValue(pono));
        rowData.setCustomerMaterialNo(getStringValue(customerMaterialNo));
        rowData.setCustomerSeq("");
        rowData.setPowerNum(getStringValue(powerNum));
        rowData.setSize(getStringValue(size));
        rowData.setCreateDate(LocalDateTimeUtil.dateToLocalDate(getDateValue(ERDAT)));
        return  rowData ;

    }

    public static List<WpsDayPlan> wpsRowDataToWpsWeekPlanList(RowSaveData wpsRow, Map<String, Map<String, Float>> lineProductUphMap) {
        List<WpsDayPlan> resList = Lists.newArrayList();
        Map<LocalDate, Number> scheduleDataMap = wpsRow.getScheduleDataMap();
        if (scheduleDataMap ==null || scheduleDataMap.isEmpty()) {
            return resList;
        }
        String lineCode = wpsRow.getLineCode();
        String productId = wpsRow.getProductId();
        Map<String, Float> lineUphMap = MapUtils.isNotEmpty(lineProductUphMap) ? lineProductUphMap.get(lineCode) : null;
        for (Map.Entry<LocalDate, Number> entry : scheduleDataMap.entrySet()){
            LocalDate scheduleDate = entry.getKey();
            Number qty = entry.getValue();
            if (qty == null || qty.intValue() ==0) {
                continue;
            }
            String orderNo = wpsRow.getOrderNo();
            WpsDayPlan wpsDayPlan = new WpsDayPlan();
            wpsDayPlan.setBizId(orderNo);
            wpsDayPlan.setFullBizId(convertOrderNo(orderNo));
            wpsDayPlan.setLineUuid(wpsRow.getLineUuid());
            wpsDayPlan.setLineCode(wpsRow.getLineCode());
            wpsDayPlan.setScheduleDate(scheduleDate);
            wpsDayPlan.setPrePlanQuantity(qty.intValue());
            // 线体排产时长
            if (MapUtils.isNotEmpty(lineUphMap)) {
                Float lineUph = lineUphMap.get(productId);
                if (null != lineUph) {
                    wpsDayPlan.setPrePlanDuration(OrderArithUtil.floatDivide(qty.intValue(), lineUph));
                }
            }
            wpsDayPlan.setFrozenQty(0);
            wpsDayPlan.setCreateTime(new Date());
            wpsDayPlan.setUpdateTime(new Date());
            resList.add(wpsDayPlan);
        }
        return resList;
    }

    public static WpsRowExt wpsRowDataToWpsWeekPlanExt(RowSaveData wpsRow, Date date) {
        LocalDate startScheduleDate = getStartScheduleDate(wpsRow.getScheduleDataMap());
        // 非常重要字段，如果没有排产日期，设置为当天，
        startScheduleDate = startScheduleDate == null ? LocalDate.now() :startScheduleDate;
        WpsRowExt wpsRowExt = new WpsRowExt();
        wpsRowExt.setFactoryCode(wpsRow.getFactory());
        wpsRowExt.setPlannerEmpNo(wpsRow.getEmpNo());
        wpsRowExt.setBizId(wpsRow.getOrderNo());
        wpsRowExt.setScheduleDate(startScheduleDate);
        wpsRowExt.setScheduleYear(startScheduleDate.getYear());
        wpsRowExt.setScheduleWeek(LocalDateTimeUtil.getWeekSeqOfYear(startScheduleDate));
        // 排序增加
        wpsRowExt.setScheduleSeq(wpsRowExt.getScheduleYear()*1000000000L+startScheduleDate.getMonthValue()*100000000L+startScheduleDate.getDayOfMonth()*100000L+wpsRow.getScheduleSeq());
        wpsRowExt.setLineCode(wpsRow.getLineCode());
        wpsRowExt.setLineUuid(wpsRow.getLineUuid());
        wpsRowExt.setFrozenStatus(wpsRow.get_frozenStatus());
        wpsRowExt.setCreateTime(new Date());
        wpsRowExt.setUpdateTime(new Date());
        Map<LocalDate, Number> scheduleDataMap = wpsRow.getScheduleDataMap();
        if (MapUtils.isNotEmpty(scheduleDataMap)) {
            scheduleDataMap.entrySet().stream().filter(entry -> null != entry.getValue())
                    .map(Map.Entry::getKey).max(LocalDate::compareTo).ifPresent(wpsRowExt::setEstProductionFinishDate);
        }
        return wpsRowExt;
    }

    /**
     * 获取开始排产日期
     * @param
     * @return
     */
    public static LocalDate getStartScheduleDate(Map<LocalDate,Number> scheduleDataMap){
        return WpsDateUtil.getStartScheduleDate(scheduleDataMap);
    }

    public static Date getDateValue(Object source){
        try {
            if (source == null || StringUtils.isBlank(source.toString())) {
                return null;
            }
            if (source instanceof Long) {
                return  new Date(((Long) source).longValue());
            }
            if (source instanceof Float) {
                return  new Date(((Float) source).longValue());
            }
            if (source instanceof Double) {
                return  new Date(((Double) source).longValue());
            }
            if (source instanceof String ) {
                String formatter = source.toString().contains("-")? "yyyy-MM-dd" : "yyyyMMdd";
                formatter = source.toString().contains("/") ? "yyyy/MM/dd" : formatter;
                return  DateUtils.parseDate(source.toString(),formatter);
            }
        }catch (Exception e){
            log.error("日期转化异常 msg={}",e.getMessage(),e);
        }
        return null;
    }
    public static Number getNumberValue(Object source){
        if (source == null || StringUtils.isBlank(source.toString())) {
            return null;
        }
        try {
            return new BigDecimal(source.toString());
        }catch (Exception e){
            log.error(" 转Number异常 source={},msg={}",source,e.getMessage(),e);
        }
        return null;
    }

    public static Integer getIntegerValue(Object source){
        if (source == null || StringUtils.isBlank(source.toString())) {
            return null;
        }
        try {
            return new BigDecimal(source.toString()).intValue();
        }catch (Exception e){
            log.error(" 转Number异常 source={},msg={}",source,e.getMessage(),e);
        }
        return null;
    }

    public static String getStringValue(Object source){
        if (source == null || StringUtils.isBlank(source.toString())) {
            return null;
        }
        return source.toString();
    }


    public static AtpLackOffMaterialRespVO getAtpData(Map<String, Object> objectMap) {
        AtpLackOffMaterialRespVO dto = new AtpLackOffMaterialRespVO();
// 设置所有字段的值
        dto.setTOP_NO(getStringValue(objectMap.get("TOP_NO")));
        dto.setPLANT(getStringValue(objectMap.get("PLANT")));
        dto.setWORK_TYPE_NAME(getStringValue(objectMap.get("WORK_TYPE_NAME")));
        dto.setWORK_NO(getStringValue(objectMap.get("WORK_NO")));
        dto.setWORK_LINE(getStringValue(objectMap.get("WORK_LINE")));
        dto.setCOVER_SO_NO(getStringValue(objectMap.get("COVER_SO_NO")));
        dto.setCOVER_SO_LINE(getStringValue(objectMap.get("COVER_SO_LINE")));
        dto.setQTY(getNumberValue(objectMap.get("QTY")));
        dto.setITEM_NO(getStringValue(objectMap.get("ITEM_NO")));
        dto.setSUPPLY_LEVEL(getStringValue(objectMap.get("SUPPLY_LEVEL")));
        dto.setPLAN_DATE(getStringValue(objectMap.get("PLAN_DATE")));
        dto.setDELIVERY_PLANT(getStringValue(objectMap.get("DELIVERY_PLANT")));
        dto.setMATERIAL_ITEM_NO(getStringValue(objectMap.get("MATERIAL_ITEM_NO")));
        dto.setMATERIAL_ITEM_NAME(getStringValue(objectMap.get("MATERIAL_ITEM_NAME")));
        dto.setMSTAE(getStringValue(objectMap.get("MSTAE")));
        dto.setITEM_GROUP(getStringValue(objectMap.get("ITEM_GROUP")));
        dto.setMATERIAL_LEAD_TIME_DAYS(getNumberValue(objectMap.get("MATERIAL_LEAD_TIME_DAYS")));
        dto.setMATERIAL_NEED_DATE(getStringValue(objectMap.get("MATERIAL_NEED_DATE")));
        dto.setUSE_DATE(getStringValue(objectMap.get("USE_DATE")));
        dto.setPARENT_QITAO_DATE(getStringValue(objectMap.get("PARENT_QITAO_DATE")));
        dto.setDEF_PLACE(getStringValue(objectMap.get("DEF_PLACE")));
        dto.setDEF_PLACE_NAME(getStringValue(objectMap.get("DEF_PLACE_NAME")));
        dto.setPLACE(getStringValue(objectMap.get("PLACE")));
        dto.setPLACE_NAME(getStringValue(objectMap.get("PLACE_NAME")));
        dto.setITEM_PACK_PLACE(getStringValue(objectMap.get("ITEM_PACK_PLACE")));
        dto.setUSE_TYPE_NAME(getStringValue(objectMap.get("USE_TYPE_NAME")));
        dto.setUSE_NO(getStringValue(objectMap.get("USE_NO")));
        dto.setUSE_NOTE(getStringValue(objectMap.get("USE_NOTE")));
        dto.setSUPPLY(getStringValue(objectMap.get("SUPPLY")));
        dto.setUSE_QTY(getNumberValue(objectMap.get("USE_QTY")));
        dto.setPINGJING(getStringValue(objectMap.get("PINGJING")));
        dto.setPO_GROUP(getStringValue(objectMap.get("PO_GROUP")));
        dto.setPO_GROUP_R(getStringValue(objectMap.get("PO_GROUP_R")));
        dto.setDISPO(getStringValue(objectMap.get("DISPO")));
        dto.setBQTY(getNumberValue(objectMap.get("BQTY")));
        dto.setBBQTY(getNumberValue(objectMap.get("BBQTY")));
        dto.setUNIT(getStringValue(objectMap.get("UNIT")));
        dto.setSCRAP_PERCENT(getNumberValue(objectMap.get("SCRAP_PERCENT")));
        dto.setMRP_REGION(getStringValue(objectMap.get("MRP_REGION")));
        return dto;
    }

    public static OrderPlanCompleteDTO getOrderPlanComplete(Map<String, Object> objectMap) {
        OrderPlanCompleteDTO dto  = new OrderPlanCompleteDTO();
        dto.setSellOrderNo(getStringValue(objectMap.get("vgbel")));
        dto.setSellRowItemNo(getStringValue(objectMap.get("vgpos")));
        dto.setDeliverQty(getNumberValue(objectMap.get("lfimg")));
        dto.setDeliverNo(getStringValue(objectMap.get("vbeln")));
        dto.setDeliverRowItemNo(getStringValue(objectMap.get("posnr")));
        dto.setCompleteDate(LocalDateTimeUtil.dateToLocalDate(getDateValue(objectMap.get("zzwcrq"))));
        return dto;
    }

    /**
     * 转换订单编码（字符串通用版）
     * - 若订单号为null或空，返回null
     * - 若长度≥12位，直接返回原值
     * - 不足12位时，左侧补0至12位（保留所有原字符）
     */
    private static String convertOrderNo(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return null;
        }
        return orderNo.length() >= 12
                ? orderNo
                : String.format("%12s", orderNo).replace(' ', '0');
    }

    public static AtpSingleMaterialResp getAtpSingle(Map<String, Object> objectMap) {
        AtpSingleMaterialResp resp = new AtpSingleMaterialResp();
        resp.setMAKTX(getStringValue(objectMap.get("MAKTX")));
        resp.setVBELN(getStringValue(objectMap.get("VBELN")));
        resp.setETIME(getStringValue(objectMap.get("ETIME")));
        resp.setDDHXM(getStringValue(objectMap.get("DDHXM")));
        resp.setWERKS(getStringValue(objectMap.get("WERKS")));
        resp.setZQTRQ(getStringValue(objectMap.get("ZQTRQ")));
        resp.setZSFJT(getStringValue(objectMap.get("ZSFJT")));
        resp.setAUFNR(getStringValue(objectMap.get("AUFNR")));
        resp.setODTYP(getStringValue(objectMap.get("ODTYP")));
        resp.setMANDT(getStringValue(objectMap.get("MANDT")));
        resp.setPOSNR(getStringValue(objectMap.get("POSNR")));
        resp.setSTIME(getStringValue(objectMap.get("STIME")));
        resp.setGJWLGL(getStringValue(objectMap.get("GJWLGL")));
        resp.setZSORTL(getStringValue(objectMap.get("ZSORTL")));
        resp.setMATNR(getStringValue(objectMap.get("MATNR")));
        return resp;

    }
}