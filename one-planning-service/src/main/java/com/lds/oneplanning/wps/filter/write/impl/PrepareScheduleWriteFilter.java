package com.lds.oneplanning.wps.filter.write.impl;

import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.constants.ScheduleConstant;
import com.lds.oneplanning.basedata.enums.FactoryScheduleBufferBizTypeEnum;
import com.lds.oneplanning.basedata.model.ScheduleDateCfgDTO;
import com.lds.oneplanning.basedata.service.IFactoryScheduleBufferService;
import com.lds.oneplanning.basedata.service.IScheduleDateCfgService;
import com.lds.oneplanning.basedata.service.IScheduleDirectionCfgService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.entity.WpsRowExt;
import com.lds.oneplanning.wps.filter.write.AbstractWpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsRowExtService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/26 15:30
 */
@Slf4j
@Service
public class PrepareScheduleWriteFilter extends AbstractWpsOrderWriteFilter {

    @Resource
    private IScheduleDateCfgService scheduleDateCfgService;
    @Resource
    private IFactoryScheduleBufferService factoryScheduleBufferService;
    @Resource
    private IScheduleDirectionCfgService scheduleDirectionCfgService;
    @Resource
    private IWpsRowExtService wpsRowExtService;

    @Value("${wps.mock.switch:0}")
    private Integer mockSwitch;
    @Value("${wps.schedule.direction_offset_days:2}")
    private Integer directionOffsetDays;
    @Value("${wps.schedule.dps_lock_days :7}")
    private Integer dpsLockDays;

    @Override
    public Integer filterSeq() {
        return 3;
    }

    @Override
    protected List<WpsRowData> doFilter(Long userId, Integer datasource, String factoryCode, List<WpsRowData> wpsRowDatas,boolean cacheFlag, Map<String,Object> params) {
        List<String> bizIds = wpsRowDatas.stream().map(WpsRowData::getOrderNo).collect(Collectors.toList());
        List<WpsRowExt> planExts = wpsRowExtService.listByBizIds(bizIds);
        Map<String, Integer> frozenMap = planExts.stream().collect(Collectors.toMap(WpsRowExt::getBizId, WpsRowExt::getFrozenStatus,(bizId, bizId2) -> bizId2));
        // 读表的日期单独弄
        Map<String, LocalDate> dateMap = planExts.stream().collect(Collectors.toMap(WpsRowExt::getBizId, WpsRowExt::getScheduleDate,(bizId, bizId2) -> bizId2));
        // 设置冻结时间 和onlineTime
        for (WpsRowData rowData :wpsRowDatas){
            String bizId   = rowData.getOrderNo();
            Integer frozenStatus = frozenMap.get(bizId);
            frozenStatus = frozenStatus != null && frozenStatus == 1 ? 1 : 0;
            // 冻结状态处理
            rowData.set_frozenStatus(frozenStatus);
            if (WpsConstants.DATA_SOURCE_STORAGE.equals(datasource)) {
                // 从库表中读取首次排产日期
                rowData.setOnlineTime(LocalDateTimeUtil.localDateToDate(dateMap.get(rowData.getOrderNo())));
            }
        }
        if (WpsConstants.DATA_SOURCE_STORAGE.equals(datasource)) {
            // 读取库表的处理，到处结束
            return wpsRowDatas;
        }
        // 以下是自动排产才需要的逻辑

        List<ScheduleDateCfgDTO> dateCfgs = scheduleDateCfgService.listByFactoryCodes(wpsRowDatas.stream().map(WpsRowData::getFactory).collect(Collectors.toSet()));
        // 销售订单分组
        Map<String,ScheduleDateCfgDTO> sellOrderNoMap = dateCfgs.stream().filter(cfgDTO -> StringUtils.isNotBlank(cfgDTO.getSellOrderNo()))
                .collect(Collectors.toMap(ScheduleDateCfgDTO::getSellOrderNo, scheduleDateCfg -> scheduleDateCfg,(t, t2) -> t2));
        // 订单分组 试样订单使用
        Map<String,ScheduleDateCfgDTO> orderNoMap = dateCfgs.stream().filter(cfgDTO->StringUtils.isNotBlank(cfgDTO.getOrderNo()))
                .collect(Collectors.toMap(ScheduleDateCfgDTO::getOrderNo, scheduleDateCfg -> scheduleDateCfg,(t, t2) -> t2));
        //工厂分组
        Map<String,ScheduleDateCfgDTO> factoryMap = dateCfgs.stream().collect(Collectors.toMap(ScheduleDateCfgDTO::getFactoryCode,scheduleDateCfg -> scheduleDateCfg,(t, t2) -> t2));
        // 按产品组关联的产品id（商品id）集合分组
        Map<List<String>,ScheduleDateCfgDTO> productIdsMap = dateCfgs.stream().collect(Collectors.toMap(ScheduleDateCfgDTO::getProductIds,scheduleDateCfg -> scheduleDateCfg,(t, t2) -> t2));
        Map<Integer, Integer> factoryScheduleBufferMap = factoryScheduleBufferService.getMapByFactoryCode(factoryCode);

        Map<String, WpsRowExt> dateOjbMap = planExts.stream().collect(Collectors.toMap(WpsRowExt::getBizId,wpsRowExt -> wpsRowExt,(t, t2) -> t2));

        // 查询排产方向
        Map<String,Integer> scheduleMap = scheduleDirectionCfgService.getScheduleMap(wpsRowDatas);
        List<WpsRowExt> wpsRowExtUpdateList = Lists.newArrayList();
        for (WpsRowData rowData :wpsRowDatas){
            Map<Integer, Object> startEndDayMap =
                    this.getStartEndDayMap(scheduleMap.get(rowData.getOrderNo()), rowData, factoryMap,
                            productIdsMap, sellOrderNoMap, orderNoMap, factoryScheduleBufferMap,dateOjbMap);

            rowData.set_startProductPeriod((LocalDate)startEndDayMap.get(1));
            rowData.set_endProductPeriod((LocalDate)startEndDayMap.get(2));
            rowData.setPreOnlineTime(startEndDayMap.get(0) == null ? rowData.get_startProductPeriod() : (LocalDate) startEndDayMap.get(0) );
            rowData.setScheduleDirection(Optional.ofNullable(scheduleMap.get(rowData.getOrderNo())).orElse(ScheduleConstant.DEFAULT_DIRECTION));
            if (startEndDayMap.get(3)!=null) {
                wpsRowExtUpdateList.add((WpsRowExt)startEndDayMap.get(3));
            }
        }
        wpsRowExtService.updateBatchById(wpsRowExtUpdateList);
        return wpsRowDatas;
    }

    private Map<Integer,Object> getStartEndDayMap(Integer direction,
                                                  WpsRowData rowData,
                                                  Map<String,ScheduleDateCfgDTO> factoryMap,
                                                  Map<List<String>,ScheduleDateCfgDTO> productIdsMap,
                                                  Map<String,ScheduleDateCfgDTO> sellOrderNoMap,
                                                  Map<String,ScheduleDateCfgDTO> orderNoMap,
                                                  Map<Integer, Integer> factoryScheduleBufferMap,
                                                     Map<String, WpsRowExt> dateOjbMap){
        Map<Integer,Object> resMap = Maps.newHashMap();
        direction = direction ==null ? ScheduleConstant.DEFAULT_DIRECTION : direction;
        //工厂优先级最低
        int startOffset = 7;
        int endOffset = 2;
        startOffset = factoryMap.get(rowData.getFactory()) != null  ? factoryMap.get(rowData.getFactory()).getBeforeStartDays() :startOffset;
        endOffset = factoryMap.get(rowData.getFactory()) != null  ? factoryMap.get(rowData.getFactory()).getBeforeEndDays() :endOffset;
        // 产品组优先级第二
        for (Map.Entry<List<String>,ScheduleDateCfgDTO> entry : productIdsMap.entrySet()){
            List<String> productIds = entry.getKey();
            ScheduleDateCfgDTO cfg = entry.getValue();
            startOffset = productIds.contains(rowData.getProductId()) ? cfg.getBeforeStartDays() : startOffset;
            endOffset =  productIds.contains(rowData.getProductId()) ? cfg.getBeforeEndDays() : endOffset;
        }
        // 销售单号优先级最高
        startOffset = sellOrderNoMap.get(rowData.getSellOrderNo()) != null  ? sellOrderNoMap.get(rowData.getSellOrderNo()).getBeforeStartDays() :startOffset;
        endOffset = sellOrderNoMap.get(rowData.getSellOrderNo()) != null  ? sellOrderNoMap.get(rowData.getSellOrderNo()).getBeforeEndDays() :endOffset;

        // 现在！！！订单号优先级更高了
        ScheduleDateCfgDTO orderDateCfg =  orderNoMap.get(rowData.getOrderNo());
        if (orderDateCfg != null) {
            startOffset =  Optional.ofNullable(orderDateCfg.getBeforeStartDays()).orElse(startOffset);
            endOffset = Optional.ofNullable(orderDateCfg.getBeforeEndDays()).orElse(endOffset);
        }
        // 开始排产日期 结束排产日期计算
        LocalDate calculateOnlineTime = rowData.getCalculateFinishTime();
        if (calculateOnlineTime == null) {
            // 5-16修改，如果没有完工日期，使用生产开始和生产结束日期 作为可以开始排产的日期和结束排产的日期 见https://confluence.leedarson.com/pages/viewpage.action?pageId=195077715
            resMap.put(1, Optional.ofNullable(LocalDateTimeUtil.dateToLocalDate(rowData.getProductStartTime())).orElse(LocalDate.now()));
            resMap.put(2,Optional.ofNullable(LocalDateTimeUtil.dateToLocalDate(rowData.getProductEndTime())).orElse(LocalDate.now()));
            return resMap;
        }
        // 同的方向区别在此
        LocalDate _startProductPeriod;
        LocalDate _endProductPeriod;
        switch (direction){
                     // 正向规则
            case 1: _startProductPeriod = calculateOnlineTime.minusDays(startOffset);
                    _endProductPeriod = calculateOnlineTime.minusDays(endOffset);
                    break;
                    // 逆向1规则
            case -1: _startProductPeriod = calculateOnlineTime.minusDays(startOffset);
                     _endProductPeriod = calculateOnlineTime.minusDays(endOffset);
                     break;
                     // 逆向2 规则
            case -2:  _startProductPeriod = calculateOnlineTime.minusDays(startOffset);
                _endProductPeriod = calculateOnlineTime.minusDays(endOffset);
                break;
            default:
                _startProductPeriod = calculateOnlineTime.minusDays(7);
                _endProductPeriod = calculateOnlineTime;
        }
        // 特殊规则处理： 包材无版面和风险备库处理
        _startProductPeriod = processScheduleBuffer(rowData, factoryScheduleBufferMap, _startProductPeriod);
        // 开始排产日期不得晚于截止日期
        _startProductPeriod = _startProductPeriod.isAfter(_endProductPeriod)? _endProductPeriod :_startProductPeriod;
        if (mockSwitch.equals(1)) {
            LocalDate today = LocalDate.now();
            _startProductPeriod = today ;
            _endProductPeriod  = _startProductPeriod.plusDays(10);
        }
        // 假期判断
        //上一次发布订单在最近7天（DPS锁定期）内的订单按原来的日期计算预排上线日期
        if (dateOjbMap.get(rowData.getOrderNo())!=null ) {
           WpsRowExt wpsRowExt = dateOjbMap.get(rowData.getOrderNo());
           LocalDate dbStartDay = wpsRowExt.getScheduleDate();
           LocalDate today = LocalDate.now();
            //原排产日期已过的修正成当前日期（未生产完工的订单），并把排产序号修正成0，保证优先排
            if (dbStartDay.isBefore(today) ) {
                // 修改排产序号
                wpsRowExt.setScheduleSeq(0l);
                wpsRowExt.setUpdateTime(new Date());
                resMap.put(3,wpsRowExt);
                resMap.put(0,today);
            }
           //上一次发布订单在最近7天包含7天（DPS锁定期）内的订单按原来的日期计算预排上线日期
           if (dbStartDay.compareTo(today)>=0 &&  dbStartDay.compareTo(today.plusDays(dpsLockDays))<=0) {
               resMap.put(0,dbStartDay);
           }
           //未在上一次发布保存在7天内的订单，则使用排产的第一天
            if (dbStartDay.isAfter(today.plusDays(dpsLockDays))) {
                resMap.put(0,_startProductPeriod);
            }
        }
      /*  // 2025-05-26 需求 生产开始时间和计算的开始时间比对，取最晚
        LocalDate productStartLocalDate = Optional.ofNullable(LocalDateTimeUtil.dateToLocalDate(rowData.getProductStartTime())).orElse(_startProductPeriod);
        // 两个时间比对，取最晚的时间
        _startProductPeriod =  _startProductPeriod.isAfter(productStartLocalDate) ? _startProductPeriod : productStartLocalDate ;
        // 同时不得晚于截止时间
        _startProductPeriod = _startProductPeriod.isAfter(_endProductPeriod)? _endProductPeriod : _startProductPeriod;*/
        resMap.put(1, _startProductPeriod );
        resMap.put(2,_endProductPeriod);
        return  resMap;
    }

    private LocalDate processScheduleBuffer(WpsRowData rowData, Map<Integer, Integer> factoryScheduleBufferMap, LocalDate _startProductPeriod) {
        if (Objects.equals(rowData.getPackagePrint(), "不存在")) {
            // 包材无版面
            return addBufferDaysIfPresent(factoryScheduleBufferMap, FactoryScheduleBufferBizTypeEnum.PACKAGE_PRINT.getType(), _startProductPeriod);
        }
        if (StringUtils.isEmpty(rowData.getSellOrderNo())) {
            // 风险备库
            return addBufferDaysIfPresent(factoryScheduleBufferMap, FactoryScheduleBufferBizTypeEnum.RISK_RESERVE.getType(), _startProductPeriod);
        }
        if (rowData.getAtpStatus()>=3){
            //判断物料是否齐套
            return addBufferDaysIfPresent(factoryScheduleBufferMap, FactoryScheduleBufferBizTypeEnum.INCOMPLETE_MATERIALS.getType(), _startProductPeriod);
        }
        return _startProductPeriod;
    }

    private LocalDate addBufferDaysIfPresent(Map<Integer, Integer> bufferMap, int key, LocalDate startDate) {
        return Optional.ofNullable(bufferMap)
                .map(map -> map.get(key))
                .map(startDate::plusDays)
                .orElse(startDate);
    }
}
