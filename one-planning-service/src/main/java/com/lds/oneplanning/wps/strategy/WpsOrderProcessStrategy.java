package com.lds.oneplanning.wps.strategy;

import com.lds.oneplanning.wps.filter.read.WpsOrderReadFilter;
import com.lds.oneplanning.wps.filter.write.WpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/4/7 9:46
 */
public interface WpsOrderProcessStrategy {
    List<WpsRowData> process(Long userId, String factoryCode, List<WpsRowData> dirtyList, boolean cacheFlag, Map<String,Object> params);
    List<WpsOrderReadFilter> listReadFilter();
    List<WpsOrderWriteFilter> listWriteFilter();
}
