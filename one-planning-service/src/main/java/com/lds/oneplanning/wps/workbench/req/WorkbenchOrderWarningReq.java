package com.lds.oneplanning.wps.workbench.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Set;

@Data
public class WorkbenchOrderWarningReq implements Serializable {

    private static final long serialVersionUID = -3404199468778361974L;

    /**
     * 订单异常编码
     */
    @NotEmpty
    private String orderAbnormalCode;

    /**
     * 时间段开始日期
     */
    @NotNull
    private LocalDate startDate;

    /**
     * 时间段结束日期
     */
    @NotNull
    private LocalDate endDate;

    /**
     * 工厂编码
     */
    private String factoryCode;

    /**
     * 内存对象集合
     */
    private Set<String> factoryCodes;
}