package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningShipBookingUrgentAbnormal;
import com.lds.oneplanning.wps.req.ShipBookingUrgentReq;
import com.lds.oneplanning.wps.vo.ShipBookingUrgentVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
public interface WarningShipBookingUrgentAbnormalMapper extends BaseMapper<WarningShipBookingUrgentAbnormal> {

    List<WarningShipBookingUrgentAbnormal> queryUnHandleData();

    Page<ShipBookingUrgentVO> queryPage(@Param("pageParam") IPage<ShipBookingUrgentVO> pageParam, @Param("userId") String userId,
                                        @Param("req") ShipBookingUrgentReq req);
}
