package com.lds.oneplanning.wps.workbench.resp;

import com.lds.oneplanning.common.utils.OrderArithUtil;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class ProductionRatioResp implements Serializable {

    private static final long serialVersionUID = 4617056373328769081L;

    /**
     * 时间段编码
     */
    private PeriodInfo periodInfo;

    /**
     * 排产比例
     */
    private Float ratio;


    /**
     * 实际时长
     */
    private Integer actualHours;
    
    /**
     * 计划时长
     */
    private Integer plannedHours;

    public Float getRatio() {
        return OrderArithUtil.floatDivide(actualHours, plannedHours);
    }
}
