package com.lds.oneplanning.wps.filter.read.impl;

import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.wps.filter.read.AbstractWpsOrderReadFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsOrderDispatchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/25 17:55
 */
@Slf4j
@Service
public class PlannerOrderExcludeReadFilter extends AbstractWpsOrderReadFilter {
    @Resource
    private IPlannerBaseService plannerBaseService;
    @Resource
    private IWpsOrderDispatchService wpsOrderDispatchService;
    @Override
    public Integer filterSeq() {
        return 4;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag) {
        List<WpsRowData> resList = Lists.newArrayList();
        // 集合运算需要使用set
        Set<WpsRowData> resSet = Sets.newLinkedHashSet(dirtyList);
        String empNo = plannerBaseService.getEmpNoByUserId(userId);
        Set<String> orderNos = dirtyList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toSet());
        Set<String> dispatchOrders = wpsOrderDispatchService.getDispatchOrders(empNo, orderNos);
        Set<String> minusOrders = wpsOrderDispatchService.getMinusOrders(empNo, orderNos);
        if (dispatchOrders.isEmpty() && minusOrders.isEmpty()) {
            return dirtyList;
        }
        //分配给自己的加回来
        resSet.addAll(dirtyList.stream().filter(wpsRowData -> dispatchOrders.contains(wpsRowData.getOrderNo())).collect(Collectors.toSet()));
        // 分配给别人的减掉
        resSet.removeIf(wpsRowData -> minusOrders.contains(wpsRowData.getOrderNo()));
        // 清理没有订单号和销售订单号的数据
        resSet = resSet.stream().filter(wpsRowData -> StringUtils.isNotBlank(wpsRowData.getOrderNo()))
                .collect(Collectors.toSet());
        resList.addAll(resSet);
        return resList;
    }


}
