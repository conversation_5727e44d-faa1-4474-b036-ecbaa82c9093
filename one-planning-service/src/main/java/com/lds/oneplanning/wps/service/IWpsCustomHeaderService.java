package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WpsCustomHeaders;
import com.lds.oneplanning.wps.req.WpsCustomHeaderReq;

import java.util.List;

public interface IWpsCustomHeaderService extends IService<WpsCustomHeaders> {

    List<String> listByFactoryCode(Long userId);

    void save(Long userId, WpsCustomHeaderReq wpsCustomHeaderReq);
}