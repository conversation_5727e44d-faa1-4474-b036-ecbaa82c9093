package com.lds.oneplanning.wps.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.common.service.IBasicUserService;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormalShortage;
import com.lds.oneplanning.wps.entity.WarningTodoList;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.mapper.WarningMaterialAtpAbnormalShortageMapper;
import com.lds.oneplanning.wps.model.EsbPoData;
import com.lds.oneplanning.wps.model.EsbPoParams;
import com.lds.oneplanning.wps.service.WarningMaterialAtpAbnormalShortageService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.utils.DateUtils;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalShortageVO;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【warning_material_atp_abnormal】的数据库操作Service实现
 * @createDate 2025-05-14 17:29:49
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningMaterialAtpAbnormalShortageServiceImpl extends ServiceImpl<WarningMaterialAtpAbnormalShortageMapper, WarningMaterialAtpAbnormalShortage>
        implements WarningMaterialAtpAbnormalShortageService {
    private final EsbDataFetchService esbDataFetchService;
    private final WarningTodoListService warningTodoListService;
    private final IBasicUserService basicUserService;

    @Override
    public void updatePoInfo() {
        List<Long> undoList = warningTodoListService.lambdaQuery()
                .in(WarningTodoList::getProcessStatus, Arrays.asList(OrderWarningHandleStatusEnum.UN_HANDLE, OrderWarningHandleStatusEnum.HANDLED))
                .select(WarningTodoList::getId, WarningTodoList::getBizId)
                .eq(WarningTodoList::getWarningType, WpsOrderWarningTypeEnum.ATP_EXCEPTION)
                .list().stream().map(WarningTodoList::getBizId).collect(Collectors.toList());


        if (CollectionUtils.isEmpty(undoList)) {
            return;
        }

        //查询所有数据进行处理
        List<WarningMaterialAtpAbnormalShortage> list = this.lambdaQuery()
                .in(WarningMaterialAtpAbnormalShortage::getId, undoList)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        queryPoInfo(list);
    }


    @Override
    public void queryPoInfo(List<WarningMaterialAtpAbnormalShortage> shortageList) {
        log.info("开始查询采购单信息");
        EsbPoParams params = buildEsbPoParams(shortageList);
        if (CollectionUtils.isEmpty(params.getMaterialInfoList())) {
            return;
        }
        EsbPoData esbPoData = esbDataFetchService.fetchPoInfo(params);
        List<EsbPoData.Detail> otData = esbPoData.getOtData();
        if (CollectionUtils.isEmpty(otData)) {
            log.info("未查询到采购单数据");
            return;
        }

        Table<String, String, List<EsbPoData.Detail>> poTable = groupAndValidatePoData(otData);
        if (poTable.isEmpty()) {
            log.info("采购单数据无效");
            return;
        }

        ProcessResult result = processShortages(shortageList, poTable);
        SpringUtil.getBean(WarningMaterialAtpAbnormalShortageService.class).saveData(result);
        log.info("完成查询采购单信息");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(ProcessResult result) {
        updateData(result.updateList);
        insertData(result.insertList);
    }

    /**
     * 构建ESB采购参数对象
     *
     * @param shortageList 物料短缺列表（输入）
     * @return 包含物料信息的参数对象（输出）
     */
    private EsbPoParams buildEsbPoParams(List<WarningMaterialAtpAbnormalShortage> shortageList) {
        EsbPoParams params = new EsbPoParams();
        shortageList.forEach(item -> params.addMaterialInfo(item.getShortageId(), item.getFactoryCode()));
        return params;
    }


    /**
     * 处理物料短缺数据
     *
     * @param shortageList 物料短缺列表（输入）
     * @param poTable      分组后的采购单数据（输入）
     * @return 包含待更新和插入数据的处理结果（输出）
     */
    private ProcessResult processShortages(List<WarningMaterialAtpAbnormalShortage> shortageList,
                                           Table<String, String, List<EsbPoData.Detail>> poTable) {
        List<WarningMaterialAtpAbnormalShortage> updateList = Lists.newArrayList();
        List<WarningMaterialAtpAbnormalShortage> insertList = Lists.newArrayList();

        for (WarningMaterialAtpAbnormalShortage shortage : shortageList) {
            List<EsbPoData.Detail> details = poTable.get(shortage.getShortageId(), shortage.getFactoryCode());
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            try {
                processSingleShortage(shortage, details, updateList, insertList);
            } catch (Exception e) {
                log.error("处理物料短缺数据时发生异常", e);
            }
        }
        return new ProcessResult(updateList, insertList);
    }

    /**
     * 处理单个物料短缺记录
     *
     * @param shortage   物料短缺对象（输入）
     * @param details    关联的采购明细数据（输入）
     * @param updateList 待更新列表（输入输出）
     * @param insertList 待插入列表（输入输出）
     */
    private void processSingleShortage(WarningMaterialAtpAbnormalShortage shortage,
                                       List<EsbPoData.Detail> details,
                                       List<WarningMaterialAtpAbnormalShortage> updateList,
                                       List<WarningMaterialAtpAbnormalShortage> insertList) {
        // 处理可能的null值排序问题（使用nullsFirst）
        details.sort(Comparator.comparing(EsbPoData.Detail::getDeliveryDate, Comparator.nullsFirst(Comparator.naturalOrder())));

        Integer totalShortageQuantity = shortage.getShortageQuantity();
        for (int i = 0; i < details.size(); i++) {
            EsbPoData.Detail latestData = details.get(i);
            //需求数量，不能超过未收数量
            int shortageQuantity = Math.min(totalShortageQuantity, latestData.getUnReceivedQtyNum());


            if (i == 0) {
                shortage.setShortageQuantity(shortageQuantity);
                handleFirstDeliveryDate(shortage, latestData);
                updateList.add(shortage);
            } else {
                handleQuantityComparison(shortage, insertList, shortageQuantity, latestData);
            }


            totalShortageQuantity -= shortageQuantity;
            if (totalShortageQuantity <= 0) {
                break;
            }
        }
        //如果还有剩余短缺数量，加到最后一条里面
        if (totalShortageQuantity > 0) {
            shortage.setShortageQuantity(shortage.getShortageQuantity() + totalShortageQuantity);
        }
    }

    /**
     * 处理首条交货日期数据
     *
     * @param shortage   物料短缺对象（输入输出）
     * @param updateList 待更新列表（输入输出）
     * @param index      当前明细索引（输入）
     * @param detail     采购明细数据（输入）
     */
    private void handleFirstDeliveryDate(WarningMaterialAtpAbnormalShortage shortage,
                                         EsbPoData.Detail detail) {
        if (StringUtils.isNotEmpty(detail.getConfirmedDeliveryDate())) {
            Date date = DateUtils.parseDate(detail.getConfirmedDeliveryDate());
            shortage.setLatestPurchaseDeliveryTime(DateUtils.parseData(date));
            shortage.setPoNo(detail.getPoNo());
            shortage.setPoItemNo(detail.getPoRowItem());
            shortage.setUnReceivedQtyNum(detail.getUnReceivedQtyNum());
        }
    }

    /**
     * 处理数量比较逻辑
     *
     * @param shortage         物料短缺对象（输入）
     * @param insertList       待插入列表（输入输出）
     * @param shortageQuantity 剩余短缺数量（输入）
     * @param detail           采购明细数据（输入）
     */
    private void handleQuantityComparison(WarningMaterialAtpAbnormalShortage shortage,
                                          List<WarningMaterialAtpAbnormalShortage> insertList,
                                          Integer shortageQuantity,
                                          EsbPoData.Detail detail) {

        WarningMaterialAtpAbnormalShortage newShortage = new WarningMaterialAtpAbnormalShortage();
        BeanUtils.copyProperties(shortage, newShortage);
        newShortage.setCreatedAt(null);
        newShortage.setUpdatedAt(null);
        newShortage.setId(null);
        newShortage.setShortageQuantity(shortageQuantity);
        newShortage.setPoNo(detail.getPoNo());
        newShortage.setPoItemNo(detail.getPoRowItem());
        newShortage.setUnReceivedQtyNum(detail.getUnReceivedQtyNum());

        newShortage.setSupplier(detail.getSupplierName());
        Date date = DateUtils.parseDate(detail.getDeliveryDate());
        newShortage.setPurchaseDeliveryTime(DateUtils.parseData(date));
        String date2 = detail.getConfirmedDeliveryDate();
        newShortage.setLatestPurchaseDeliveryTime(DateUtils.parseData(date2));
        newShortage.setGroupInCharge(detail.getPoGroup());
        newShortage.setPurchaseInCharge(detail.getPoGroupDesc());
        newShortage.setUnReceivedQtyNum(detail.getUnReceivedQtyNum());
        newShortage.setCreatedBy(-2L);

        insertList.add(newShortage);
    }

    /**
     * 处理结果容器类
     */
    @AllArgsConstructor
    public static class ProcessResult {
        final List<WarningMaterialAtpAbnormalShortage> updateList;
        final List<WarningMaterialAtpAbnormalShortage> insertList;
    }


    private void insertData(List<WarningMaterialAtpAbnormalShortage> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return;
        }

        //新增并添加待办
        createTodo(insertList);
        log.info("新增数据: {}", JSON.toJSONString(insertList.stream()
                .map(WarningMaterialAtpAbnormalShortage::getOrderNumber)
                .collect(Collectors.toSet())));
        super.saveBatch(insertList);
    }

    private void createTodo(List<WarningMaterialAtpAbnormalShortage> insertList) {
        Map<String, String> assigneeMap = getShortageAssignee(insertList);
        WpsOrderWarningTypeEnum typeEnum = WpsOrderWarningTypeEnum.ATP_EXCEPTION;

        List<WarningTodoList> todoList = insertList.stream()
                .map(e -> new WarningTodoList(typeEnum, e.getFactoryCode(), e.getId(), assigneeMap.get(e.getGroupInCharge())))
                .collect(Collectors.toList());

        log.info("创建待办事项列表: {}", JSON.toJSONString(todoList));

        //临时处理，过滤掉bizId为空的数据
        todoList.removeIf(e -> e.getBizId() == null);

        warningTodoListService.saveData(typeEnum, todoList);
    }

    private void updateData(List<WarningMaterialAtpAbnormalShortage> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        log.info("更新数据: {}", JSON.toJSONString(updateList.stream()
                .map(WarningMaterialAtpAbnormalShortage::getShortageId)
                .collect(Collectors.toList())));
        super.updateBatchById(updateList);
    }

    private Table<String, String, List<EsbPoData.Detail>> groupAndValidatePoData(List<EsbPoData.Detail> otData) {
        Table<String, String, List<EsbPoData.Detail>> poDetail = HashBasedTable.create();
        otData.forEach(item -> {
            item.setUnReceivedQtyNum(toIntVal(item.getUnReceivedQty()));
            //只保留未收数量>0的数据且最新采购交期不为空
            if (item.getUnReceivedQtyNum() <= 0 || StringUtils.isEmpty(item.getConfirmedDeliveryDate())) {
                return;
            }


            List<EsbPoData.Detail> details = poDetail.get(item.getMaterialId(), item.getFactoryCode());
            if (details == null) {
                details = Lists.newArrayList();
                poDetail.put(item.getMaterialId(), item.getFactoryCode(), details);
            }
            details.add(item);
            poDetail.put(item.getMaterialId(), item.getFactoryCode(), details);
        });
        return poDetail;
    }

    private int toIntVal(String unReceivedQty) {
        if (StringUtils.isEmpty(unReceivedQty)) {
            return 0;
        }
        try {
            return new BigDecimal(unReceivedQty).intValue();
        } catch (NumberFormatException e) {
            return 0;
        }
    }


    @Override
    public List<MaterialAtpAbnormalShortageVO> getShortageList(Long id) {
        return super.lambdaQuery()
                .eq(WarningMaterialAtpAbnormalShortage::getAbnormalId, id)
                .list()
                .stream()
                .sorted(Comparator.comparing(WarningMaterialAtpAbnormalShortage::getShortageId))
                .map(item -> {
                    MaterialAtpAbnormalShortageVO vo = BeanUtil.map(item, MaterialAtpAbnormalShortageVO.class);
                    //交期 - 需求
                    vo.setGapDays(DateUtils.daysBetween(vo.getRequiredTime(), vo.getPurchaseDeliveryTime()));
                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void createTodoList(List<WarningMaterialAtpAbnormal> warningList) {
        List<WarningMaterialAtpAbnormalShortage> shortageList = warningList.stream()
                .flatMap(e -> e.getShortageList().stream())
                .collect(Collectors.toList());

        createTodo(shortageList);
    }

    /**
     * 获取待办处理人
     *
     * @param warningList 告警列表
     * @return 一个以 Long 为键、String 为值的 HashMap，键为待办事项的ID，值为待办处理人的员工编号
     */
    private Map<String, String> getShortageAssignee(List<WarningMaterialAtpAbnormalShortage> warningList) {
        List<String> groups = warningList.stream()
                .map(WarningMaterialAtpAbnormalShortage::getGroupInCharge)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groups)) {
            return Maps.newHashMap();
        }
        Map<String, String> result = basicUserService.batchGetLoginNamesByPoGroups(groups);
        log.info("获取待办处理人{},结果: {}", groups, JSON.toJSONString(result));
        return result;
    }
}




