package com.lds.oneplanning.wps.lock.example;

import com.lds.oneplanning.wps.lock.DistributedLock;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 分布式锁使用示例
 * <p>
 * 本类展示了如何使用 {@link DistributedLock} 注解实现分布式锁
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
@Slf4j
@Service
public class DistributedLockExample {

    /**
     * 使用固定key的分布式锁示例
     */
    @DistributedLock(key = "exampleLock")
    public void methodWithFixedKey() {
        log.info("执行带有固定key分布式锁的方法");
        // 业务逻辑
    }

    /**
     * 使用基于参数的分布式锁示例
     *
     * @param userId 用户ID
     */
    @DistributedLock(key = "user:#{#userId}")
    public void methodWithParameterKey(String userId) {
        log.info("执行带有参数key分布式锁的方法，userId: {}", userId);
        // 业务逻辑
    }

    /**
     * 使用基于对象属性的分布式锁示例
     *
     * @param user 用户对象
     */
    @DistributedLock(key = "user:#{#user.id}")
    public void methodWithObjectKey(User user) {
        log.info("执行带有对象属性key分布式锁的方法，userId: {}", user.getId());
        // 业务逻辑
    }

    /**
     * 使用自定义等待时间和持有时间的分布式锁示例
     *
     * @param orderId 订单ID
     */
    @DistributedLock(key = "order:#{#orderId}", waitTime = 5, leaseTime = 60)
    public void methodWithCustomTimes(String orderId) {
        log.info("执行带有自定义时间的分布式锁方法，orderId: {}", orderId);
        // 业务逻辑
    }

    /**
     * 示例用户类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class User {
        private String id;
        private String name;
    }
}
