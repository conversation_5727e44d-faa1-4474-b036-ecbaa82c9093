package com.lds.oneplanning.wps.strategy.concrete;

import com.google.common.collect.Lists;
import com.lds.oneplanning.wps.filter.read.WpsOrderReadFilter;
import com.lds.oneplanning.wps.filter.read.impl.*;
import com.lds.oneplanning.wps.filter.write.WpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/4/7 9:57
 */
@Deprecated
@Slf4j
@Service
public class AtpCheckStrategy implements WpsOrderProcessStrategy {

    @Resource
    private OrderTypeReadFilter orderTypeReadFilter; // 1
    @Resource
    private ProductOrderSubTypeReadFilter productOrderSubTypeReadFilter; //2
    @Resource
    private PlannerDataPermissionReadFilter plannerDataPermissionReadFilter; //3
    @Resource
    private PlannerOrderExcludeReadFilter plannerOrderExcludeReadFilter; //4
    @Resource
    private AtpCheckReadFilter atpCheckReadFilter;  //  7
    @Resource
    private OtherStatusReadFilter otherStatusReadFilter;  //  8


    @Override
    public List<WpsRowData> process(Long userId, String factoryCode, List<WpsRowData> dirtyList, boolean cacheFlag, Map<String,Object> params) {
        WpsOrderReadFilter filter = orderTypeReadFilter;
        filter.setNext(productOrderSubTypeReadFilter)
                .setNext(plannerDataPermissionReadFilter)
                .setNext(plannerOrderExcludeReadFilter)
                .setNext(atpCheckReadFilter)
                .setNext(otherStatusReadFilter)
        ;
        return filter.filter(userId, factoryCode, dirtyList,cacheFlag);
    }
    @Override
    public List<WpsOrderReadFilter> listReadFilter() {
        return Lists.newArrayList(orderTypeReadFilter,productOrderSubTypeReadFilter,
                plannerDataPermissionReadFilter,plannerOrderExcludeReadFilter,atpCheckReadFilter, otherStatusReadFilter);
    }

    @Override
    public List<WpsOrderWriteFilter> listWriteFilter() {
        return Lists.newArrayList();
    }
}
