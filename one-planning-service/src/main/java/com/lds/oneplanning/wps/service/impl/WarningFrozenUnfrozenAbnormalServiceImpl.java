package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningFrozenUnfrozenAbnormal;
import com.lds.oneplanning.wps.mapper.WarningFrozenUnfrozenAbnormalMapper;
import com.lds.oneplanning.wps.service.IWarningFrozenUnfrozenAbnormalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.wps.vo.FreezeUnFreezeAbnormalVO;
import com.lds.oneplanning.wps.vo.WarningFrozenUnfrozenAbnormalParams;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
@Service
public class WarningFrozenUnfrozenAbnormalServiceImpl extends ServiceImpl<WarningFrozenUnfrozenAbnormalMapper, WarningFrozenUnfrozenAbnormal> implements IWarningFrozenUnfrozenAbnormalService {

    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningFrozenUnfrozenAbnormal }>
     */
    @Override
    public List<WarningFrozenUnfrozenAbnormal> queryUnHandleData() {
        return baseMapper.queryUnHandleData();
    }

    @Override
    public IPage<FreezeUnFreezeAbnormalVO> selectPage(Page<FreezeUnFreezeAbnormalVO> page, WarningFrozenUnfrozenAbnormalParams params) {
        IPage<FreezeUnFreezeAbnormalVO> pageList = baseMapper.selectPage(page, params);
        if (CollectionUtils.isNotEmpty(pageList.getRecords())) {
            page.getRecords()
                    .forEach(e -> {
                        Optional.ofNullable(e.getProcessStatus()).ifPresent(s -> e.setProcessStatusName(s.getName()));
                    });
        }
        return pageList;
    }
}
