package com.lds.oneplanning.wps.workbench.resp;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Builder
public class PeriodInfo implements Serializable {

    private static final long serialVersionUID = 2838724718690119239L;

    private String code;

    private String name;

    private LocalDate startDate;

    private LocalDate endDate;
}
