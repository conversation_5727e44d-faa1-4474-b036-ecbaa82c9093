package com.lds.oneplanning.wps.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/6/2
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QualityHistoryRiskReq extends BasePageReq {

    @ApiModelProperty(value = "工厂编码，用，隔开")
    private String factoryCodes;

    @ApiModelProperty(value = "产品ID")
    private String productId;

    @ApiModelProperty(value = "计划开始时间")
    private String planStartTime;

    @ApiModelProperty(value = "计划结束时间")
    private String planEndTime;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "品质负责人")
    private String qualityPerson;
}
