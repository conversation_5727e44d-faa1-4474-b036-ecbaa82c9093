package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WpsSchedulePlanLog对象", description="")
public class WpsSchedulePlanLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "销售订单号")
    private String sellOrderNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;


    @ApiModelProperty(value = "行项目")
    private String rowItem;

    @ApiModelProperty(value = "产品id")
    private String productId;

    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "错误码")
    private String errorCode;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}
