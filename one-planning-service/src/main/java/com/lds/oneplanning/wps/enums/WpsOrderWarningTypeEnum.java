package com.lds.oneplanning.wps.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;


/**
 * 订单告警类型枚举
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum WpsOrderWarningTypeEnum {
    //以上都是旧的，作废不管，最好把代码清理掉，浪费性能
    IN_PRODUCTION_EXCEPTION("IN_PRODUCTION_EXCEPTION", "在制工单",
            "/mps/warning/work-order-inprocess?type=1", true, buildMap(Lists.newArrayList(WarningConditionEnum.IN_PRODUCTION_SHELVE_DATE_OVER_DAYS),
            Lists.newArrayList(AlertConditionEnum.IN_PRODUCTION_SHELVE_DATE_START_DAYS, AlertConditionEnum.IN_PRODUCTION_SHELVE_DATE_END_DAYS))),
    ATP_EXCEPTION("ATP_EXCEPTION", "物料不齐套",
            "/mps/warning/apt-exception", true, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    PACKAGE_LAYOUT_ABNORMAL("PACKAGE_LAYOUT_ABNORMAL", "包材版面异常",
            "/mps/warning/package-layout-abnormal", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    FROZEN_UNFROZEN_WARNING("FROZEN_UNFROZEN_WARNING", "订单冻结解冻预警",
            "/mps/warning/frozen-unfrozen-warning", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    PROCESS_ROUTE_ABNORMAL("PROCESS_ROUTE_ABNORMAL", "工艺路线异常",
            "/mps/warning/process-route-abnormal", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    EMERGENCY_ORDER_ADDITION_REVIEW("EMERGENCY_ORDER_ADDITION_REVIEW", "紧急加单评审",
            "/mps/warning/emergency-order-addition-review", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    //    来料检验异常,品质履历,设计BOM异常,包材BOM异常,交期异常,船期临近未定舱异常,独立备料SO未转正,入库未完成异常
    MATERIAL_INSPECTION_ABNORMAL("MATERIAL_INSPECTION_ABNORMAL", "来料检验异常",
            "/mps/warning/material-inspection-abnormal", true, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    QUALITY_HISTORY_RISK("QUALITY_HISTORY_RISK", "品质履历异常",
            "/mps/warning/quality-history-risk", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    DESIGN_BOM_ABNORMAL("DESIGN_BOM_ABNORMAL", "设计BOM异常",
            "/mps/warning/design-bom-abnormal", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),

    PACKAGE_BOM_ABNORMAL("PACKAGE_BOM_ABNORMAL", "包材BOM异常",//PackageBomAbnormal
            "/mps/warning/package-bom-abnormal", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    DELIVERY_DATE_ABNORMAL("DELIVERY_DATE_ABNORMAL", "交期异常",//DeliveryDateAbnormal
            "/mps/warning/delivery-date-abnormal", true, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    SHIP_BOOKING_URGENT("SHIP_BOOKING_URGENT", "船期临近未定舱异常",//ShipBookingUrgent
            "/mps/warning/ship-booking-urgent", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    INDEPENDENT_PREPARE_MATERIAL_ABNORMAL("INDEPENDENT_PREPARE_MATERIAL_ABNORMAL", "独立备料SO未转正",//IndependentPrepareMaterialAbnormal
            "/mps/warning/independent-prepare-material-abnormal", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    STORAGE_NO_ACHIEVED_ABNORMAL("STORAGE_NO_ACHIEVED_ABNORMAL", "入库未完成异常",//StorageNoAchievedAbnormal
            "/mps/warning/storage-no-achieved-abnormal", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),
    PROCESS_WORK_ORDER("PROCESS_WORK_ORDER", "在制工单",
            "/mps/warning/work-order-inprocess", false, buildMap(Lists.newArrayList(WarningConditionEnum.UN_SCHEDULE_MIN_DAYS),
            Lists.newArrayList(AlertConditionEnum.UN_SCHEDULE_MIN_DAYS))),

    ;

    private final String code;
    private final String name;
    private final String viewPageUri;
    private final boolean todoUpgradeable;
    private final Map<String, List<Map<String, String>>> paramMap;

    public static final Map<String, WpsOrderWarningTypeEnum> WPS_ORDER_WARNING_TYPE_ENUM_MAP = Maps.newHashMap();

    static {
        for (WpsOrderWarningTypeEnum typeEnum : WpsOrderWarningTypeEnum.values()) {
            WPS_ORDER_WARNING_TYPE_ENUM_MAP.put(typeEnum.getCode(), typeEnum);
        }
    }

    private static Map<String, List<Map<String, String>>> buildMap(List<WarningConditionEnum> warnings, List<AlertConditionEnum> alarms) {
        Map<String, List<Map<String, String>>> resMap = Maps.newLinkedHashMap();
        resMap.put("warning", getWarnings(warnings));
        resMap.put("alert", getAlarms(alarms));
        return resMap;
    }


    private static List<Map<String, String>> getWarnings(List<WarningConditionEnum> jsonTypeEnums) {
        if (CollectionUtils.isEmpty(jsonTypeEnums)) {
            return Lists.newArrayList();
        }
        List<Map<String, String>> resList = Lists.newArrayList();
        jsonTypeEnums.forEach(jsonTypeEnum -> {
            Map<String, String> innerMap = Maps.newLinkedHashMap();
            innerMap.put("code", jsonTypeEnum.getCode());
            innerMap.put("name", jsonTypeEnum.getName());
            resList.add(innerMap);
        });
        return resList;
    }

    private static List<Map<String, String>> getAlarms(List<AlertConditionEnum> alarms) {
        if (CollectionUtils.isEmpty(alarms)) {
            return Lists.newArrayList();
        }
        List<Map<String, String>> resList = Lists.newArrayList();
        alarms.forEach(jsonTypeEnum -> {
            Map<String, String> innerMap = Maps.newLinkedHashMap();
            innerMap.put("code", jsonTypeEnum.getCode());
            innerMap.put("name", jsonTypeEnum.getName());
            resList.add(innerMap);
        });
        return resList;
    }

    public static String getNameByCode(String code) {
        for (WpsOrderWarningTypeEnum typeEnum : WpsOrderWarningTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getName();
            }
        }
        return null;
    }

    public static WpsOrderWarningTypeEnum getByCode(String code) {
        return WPS_ORDER_WARNING_TYPE_ENUM_MAP.get(code);
    }
}