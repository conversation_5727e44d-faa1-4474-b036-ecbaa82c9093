package com.lds.oneplanning.wps.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预警灯色枚举
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">da<PERSON><PERSON><PERSON>n</a>
 * @since 2025/5/13
 */
@Getter
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public enum LightColor implements IEnum<Integer> {
    RED(1),
    YELLOW(2),
    BLANK(3),
    ;

    private final int value;

    @Override
    public Integer getValue() {
        return this.value;
    }
}
