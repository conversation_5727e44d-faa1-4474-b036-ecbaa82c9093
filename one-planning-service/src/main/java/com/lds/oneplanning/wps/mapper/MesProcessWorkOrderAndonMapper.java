package com.lds.oneplanning.wps.mapper;

import com.lds.oneplanning.wps.entity.MesProcessWorkOrderAndon;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
public interface MesProcessWorkOrderAndonMapper extends BaseMapper<MesProcessWorkOrderAndon> {

    /**
     * 根据工单号查询
     * @param workOrderNumberList
     * @return
     */
    List<MesProcessWorkOrderAndon> findNewestList(@Param("workOrderNumberList") List<String> workOrderNumberList);

}
