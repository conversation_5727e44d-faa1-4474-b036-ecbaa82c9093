package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningDeliveryDateAbnormal;
import com.lds.oneplanning.wps.req.DeliveryDateAbnormalReq;
import com.lds.oneplanning.wps.vo.DeliveryDateAbnormalVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【warning_delivery_date_abnormal(交期异常)】的数据库操作Mapper
* @createDate 2025-05-20 09:05:31
* @Entity com.lds.oneplanning.wps.entity.WarningDeliveryDateAbnormal
*/
public interface WarningDeliveryDateAbnormalMapper extends BaseMapper<WarningDeliveryDateAbnormal> {

    List<WarningDeliveryDateAbnormal> queryUnHandleData();

    Page<DeliveryDateAbnormalVO> queryPage(@Param("pageParam") IPage<DeliveryDateAbnormalVO> pageParam, @Param("userId") String userId, @Param("vo") DeliveryDateAbnormalReq vo);
}




