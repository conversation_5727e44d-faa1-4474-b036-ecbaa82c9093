package com.lds.oneplanning.wps.job;

import com.lds.coral.job.annotation.JobRegister;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.utils.TraceIdUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 每天8点推送一次排产异常处理消息
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/19
 */
@JobRegister(value = "WarningPushHandler", jobName = "WARNING_PUSH", cron = "0 30 8 * * ? *")
@Component
@Slf4j
public class WarningPushHandler extends IJobHandler {
    @Resource
    private WarningTodoListService warningTodoListService;

    @Override
    public ReturnT<String> execute(String s) {
        ReturnT<String> returnInfo = SUCCESS;
        String id = TraceIdUtils.setTraceId();
        XxlJobLogger.log("开始执行: " + id);
        try {
            log.info("开始推送LCP消息");
            warningTodoListService.pushLcp();

        } catch (Exception e) {
            log.error("WarningPushHandler 执行失败 -->{}", e.getMessage(), e);
            returnInfo = ReturnT.FAIL;
        }
        return returnInfo;
    }

}
