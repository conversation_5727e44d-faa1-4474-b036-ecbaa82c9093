package com.lds.oneplanning.wps.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预警灯色枚举
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">da<PERSON><PERSON><PERSON><PERSON></a>
 * @since 2025/5/13
 */
@Getter
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public enum StagnantRiskLevel implements IEnum<Integer> {
    //中风险，高风险，报废风险，呆死
    MEDIUM_RISK(1),
    HIGH_RISK(2),
    SCRAP_RISK(3),
    STAGNANT(4)
    ;

    private final int value;

    @Override
    public Integer getValue() {
        return this.value;
    }
}
