package com.lds.oneplanning.wps.utils;

import com.lds.oneplanning.wps.enums.Country;
import com.lds.oneplanning.wps.model.WpsRowData;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

@UtilityClass
public class OrderNoUtils {
    public String trimPreZero(String orderNo) {
        return orderNo.replaceAll("^0+", "");
    }

    public Country getCountry(String orderNo) {
        if (isThailand(orderNo)) {
            return Country.TH;
        }
        return Country.CN;
    }

    public boolean isThailand(String orderNo) {
        // 泰国：26/27开头的工厂编号
        return StringUtils.isNotEmpty(orderNo) && (orderNo.startsWith("26") || orderNo.startsWith("27"));
    }

    /**
     * 获取统一的订单号
     *
     * @param wpsRowData WpsRowData对象，包含订单相关信息的实体类
     * @return 返回统一的订单号，如果sellOrderNo为空，则返回orderNo；如果两者都为空，则返回空字符串
     */
    public String getUnifyOrderNo(WpsRowData wpsRowData) {
        return StringUtils.defaultIfBlank(wpsRowData.getSellOrderNo(), wpsRowData.getOrderNo());
    }
}
