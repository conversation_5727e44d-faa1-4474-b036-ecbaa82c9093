package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.req.MaterialAtpAbnormalReq;
import com.lds.oneplanning.wps.vo.MaterialAtpAbnormalVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【warning_material_atp_abnormal】的数据库操作Service
* @createDate 2025-05-14 17:29:49
*/
public interface WarningMaterialAtpAbnormalService extends IService<WarningMaterialAtpAbnormal> {

    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningMaterialAtpAbnormal }>
     */
    List<WarningMaterialAtpAbnormal> queryUnHandleData();

    /**
     * 查询页面
     *
     * @param source 来源
     * @param vo     vo
     * @return {@link Page }<{@link MaterialAtpAbnormalVO }>
     */
    Page<?> queryPage(ViewSource source, MaterialAtpAbnormalReq vo);

    /**
     * 更新数据
     *
     * @param vo vo
     */
    void updateData(MaterialAtpAbnormalVO vo);

}
