package com.lds.oneplanning.wps.workbench.service.impl;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.WorkbenchOrderPeriodEnum;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessEnum;
import com.lds.oneplanning.wps.workbench.req.HandleWarningOrderReq;
import com.lds.oneplanning.wps.workbench.req.WorkbenchOrderWarningReq;
import com.lds.oneplanning.wps.workbench.resp.*;
import com.lds.oneplanning.wps.workbench.service.IWorkbenchOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkbenchOrderServiceImpl implements IWorkbenchOrderService {

    @Autowired
    private WpsRowDataFacadeService wpsRowDataFacadeService;

    @Autowired
    private IWpsOrderPlanWarningService wpsOrderPlanWarningService;

    @Autowired
    private IPlannerDataPermissionService plannerDataPermissionService;

    @Override
    public List<OrderStatusResp> listOrderStatus(Long userId) {
        if (null == userId) {
            return Collections.emptyList();
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getFactoryCodeByUserId");
        Set<String> factoryCodes = plannerDataPermissionService.getFactoryCodeByUserId(userId);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(factoryCodes)) {
            return Collections.emptyList();
        }
        log.info("WorkbenchOrderService listOrderStatus,userId={}, factoryCodes={}.", userId, factoryCodes);
        stopWatch.start("queryWpsRowDataByUserIdAndPeriod");
        List<WpsRowData> wpsRowDataList = queryWpsRowDataByUserIdAndPeriod(factoryCodes, userId, WorkbenchOrderPeriodEnum.getMinDate(),
                WorkbenchOrderPeriodEnum.getMaxDate());
        stopWatch.stop();
        if (CollectionUtils.isEmpty(wpsRowDataList)) {
            return Collections.emptyList();
        }
        log.info("WorkbenchOrderService listOrderStatus,订单数量={}, 订单号列表={}.", wpsRowDataList.size(),
                JSON.toJSONString(wpsRowDataList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toList())));
        stopWatch.start("buildOrderStatusRspList");
        List<OrderStatusResp> respList = Arrays.stream(WorkbenchOrderPeriodEnum.values())
                .map(periodEnum -> buildOrderStatusRsp(periodEnum, wpsRowDataList))
                .collect(Collectors.toList());
        stopWatch.stop();
        log.info("WorkbenchOrderService listOrderStatus,订单数量={}, 订单号列表={}, 执行耗时={}.", wpsRowDataList.size(),
                wpsRowDataList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toList()),
                stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return respList;
    }

    @Override
    public List<ProductionRatioResp> productionRatio(Long userId) {
        // TODO mock data
        List<ProductionRatioResp> productionRatioRespList = Lists.newArrayList();
        Arrays.stream(WorkbenchOrderPeriodEnum.values()).forEach(periodEnum -> {
            switch (periodEnum) {
                case LAST_MONTH:
                    productionRatioRespList.add(ProductionRatioResp.builder().periodInfo(
                                    PeriodInfo.builder()
                                            .code(periodEnum.getCode()).name(periodEnum.getDescription())
                                            .startDate(periodEnum.getStartDate()).endDate(periodEnum.getEndDate())
                                            .build())
                            .actualHours(2000).plannedHours(2000)
                            .build());
                    break;
                case LAST_THREE_DAYS:
                    productionRatioRespList.add(ProductionRatioResp.builder().periodInfo(
                                    PeriodInfo.builder()
                                            .code(periodEnum.getCode()).name(periodEnum.getDescription())
                                            .startDate(periodEnum.getStartDate()).endDate(periodEnum.getEndDate())
                                            .build())
                            .actualHours(363).plannedHours(330)
                            .build());
                    break;
                case FOUR_TO_SEVEN_DAYS:
                    productionRatioRespList.add(ProductionRatioResp.builder().periodInfo(
                                    PeriodInfo.builder()
                                            .code(periodEnum.getCode()).name(periodEnum.getDescription())
                                            .startDate(periodEnum.getStartDate()).endDate(periodEnum.getEndDate())
                                            .build())
                            .actualHours(387).plannedHours(440)
                            .build());
                    break;
                case SECOND_WEEK:
                    productionRatioRespList.add(ProductionRatioResp.builder().periodInfo(
                                    PeriodInfo.builder()
                                            .code(periodEnum.getCode()).name(periodEnum.getDescription())
                                            .startDate(periodEnum.getStartDate()).endDate(periodEnum.getEndDate())
                                            .build())
                            .actualHours(693).plannedHours(660)
                            .build());
                    break;
                case THIRD_WEEK:
                    productionRatioRespList.add(ProductionRatioResp.builder().periodInfo(
                                    PeriodInfo.builder()
                                            .code(periodEnum.getCode()).name(periodEnum.getDescription())
                                            .startDate(periodEnum.getStartDate()).endDate(periodEnum.getEndDate())
                                            .build())
                            .actualHours(647).plannedHours(660)
                            .build());
                    break;
                case FOURTH_WEEK:
                    productionRatioRespList.add(ProductionRatioResp.builder().periodInfo(
                                    PeriodInfo.builder()
                                            .code(periodEnum.getCode()).name(periodEnum.getDescription())
                                            .startDate(periodEnum.getStartDate()).endDate(periodEnum.getEndDate())
                                            .build())
                            .actualHours(792).plannedHours(660)
                            .build());
                    break;
                case SECOND_MONTH:
                    productionRatioRespList.add(ProductionRatioResp.builder().periodInfo(
                                    PeriodInfo.builder()
                                            .code(periodEnum.getCode()).name(periodEnum.getDescription())
                                            .startDate(periodEnum.getStartDate()).endDate(periodEnum.getEndDate())
                                            .build())
                            .actualHours(2112).plannedHours(2640)
                            .build());
                    break;
                case THIRD_MONTH:
                    productionRatioRespList.add(ProductionRatioResp.builder().periodInfo(
                                    PeriodInfo.builder()
                                            .code(periodEnum.getCode()).name(periodEnum.getDescription())
                                            .startDate(periodEnum.getStartDate()).endDate(periodEnum.getEndDate())
                                            .build())
                            .actualHours(2957).plannedHours(2640)
                            .build());
                    break;
                default:
                    break;
            }
        });
        return productionRatioRespList;
    }

    @Override
    public List<UnMatchOrderResp> listUnMatchOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        List<WpsRowData> filteredWpsRowDataList = listWarningOrder(userId, workbenchOrderWarningReq);
        if (CollectionUtils.isEmpty(filteredWpsRowDataList)) {
            return Collections.emptyList();
        }
        return buildUnMatchOrderRespList(filteredWpsRowDataList);
    }

    @Override
    public List<UnStoreOrderResp> listUnStoreWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        List<WpsRowData> filteredWpsRowDataList = listWarningOrder(userId, workbenchOrderWarningReq);
        if (CollectionUtils.isEmpty(filteredWpsRowDataList)) {
            return Collections.emptyList();
        }
        List<UnStoreOrderResp> resList = Lists.newArrayList();
        filteredWpsRowDataList.forEach(wpsRowData -> {
            UnStoreOrderResp resp = new UnStoreOrderResp();
            resp.setCustomer(wpsRowData.getCustomerCode());
            resp.setFactory(wpsRowData.getFactory());
            resp.setSaleOrderNo(wpsRowData.getSellOrderNo());
            resp.setLineItemNo(wpsRowData.getRowItem());
            resp.setDeliveryOrderNo(wpsRowData.getOutDeliveryNo());
            resp.setDeliveryOrderLineItemNo(wpsRowData.getOutRowItem());
            resp.setOrderNo(wpsRowData.getOrderNo());
            resp.setOrderQuantity(Optional.ofNullable(wpsRowData.getOrderPcsQty()).orElse(0).intValue());
            resp.setInStockQuantity(Optional.ofNullable(wpsRowData.getStockedPcsQty()).orElse(0).intValue());
            resp.setFinalShippingTime(wpsRowData.getWarningFinalShipDate());
            resp.setProcessStatus(OrderWarningHandleStatusEnum.UN_HANDLE.getCode());
            resList.add(resp);
        });
        return resList;
    }

    @Override
    public List<InspectionWarningOrderResp> listInspectionWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        List<WpsRowData> filteredWpsRowDataList = listWarningOrder(userId, workbenchOrderWarningReq);
        if (CollectionUtils.isEmpty(filteredWpsRowDataList)) {
            return Collections.emptyList();
        }
        return buildInspectionWarningOrderRespList(filteredWpsRowDataList);
    }

    @Override
    public List<PackingNoPrintResp> listUnPackingPrintWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        List<WpsRowData> filteredWpsRowDataList = listWarningOrder(userId, workbenchOrderWarningReq);
        if (CollectionUtils.isEmpty(filteredWpsRowDataList)) {
            return Collections.emptyList();
        }
        List<PackingNoPrintResp> resList = Lists.newArrayList();
        filteredWpsRowDataList.forEach(wpsRowData -> {
            PackingNoPrintResp resp = new PackingNoPrintResp();
            resp.setCustomer(wpsRowData.getCustomerCode());
            resp.setFactory(wpsRowData.getFactory());
            resp.setSaleOrderNo(wpsRowData.getSellOrderNo());
            resp.setLineItemNo(wpsRowData.getRowItem());
            resp.setOrderNo(wpsRowData.getOrderNo());
            resp.setOrderQuantity(Optional.ofNullable(wpsRowData.getOrderPcsQty()).orElse(0).intValue());
            resp.setEarlyOnlineTime(LocalDateTimeUtil.dateToLocalDate(wpsRowData.getOriginalFinishTime()));
            resp.setPrintDesc(wpsRowData.getPackagePrint());
            resp.setProcessStatus(OrderWarningHandleStatusEnum.UN_HANDLE.getCode());
            resList.add(resp);
        });
        return resList;
    }

    @Override
    public List<AptExceptionOrderResp> listAtpExceptionOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        List<WpsRowData> filteredWpsRowDataList = listWarningOrder(userId, workbenchOrderWarningReq);
        if (CollectionUtils.isEmpty(filteredWpsRowDataList)) {
            return Collections.emptyList();
        }
        return buildAtpExceptionOrderRespList(filteredWpsRowDataList);
    }

    @Override
    public List<BookingUrgentResp> listBookingUrgentWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        List<WpsRowData> filteredWpsRowDataList = listWarningOrder(userId, workbenchOrderWarningReq);
        if (CollectionUtils.isEmpty(filteredWpsRowDataList)) {
            return Collections.emptyList();
        }
        List<BookingUrgentResp> resList = Lists.newArrayList();
        filteredWpsRowDataList.forEach(wpsRowData -> {
            BookingUrgentResp resp = new BookingUrgentResp();
            resp.setCustomer(wpsRowData.getCustomerCode());
            resp.setFactory(wpsRowData.getFactory());
            resp.setSaleOrderNo(wpsRowData.getSellOrderNo());
            resp.setLineItemNo(wpsRowData.getRowItem());
            resp.setBookingStatus("待开发");
            resp.setProcessStatus(OrderWarningHandleStatusEnum.UN_HANDLE.getCode());
            resList.add(resp);
        });
        return resList;
    }

    @Override
    public void handleWarningOrder(Long userId, HandleWarningOrderReq handleWarningOrderReq) {
        if (null == userId || null == handleWarningOrderReq) {
            return;
        }
        String orderNo = handleWarningOrderReq.getOrderNo();
        String warningType = handleWarningOrderReq.getWarningType();
        Integer handleStatus = handleWarningOrderReq.getHandleStatus();
        String handleContent = handleWarningOrderReq.getHandleContent();
        WpsOrderPlanWarning wpsOrderPlanWarning = wpsOrderPlanWarningService.getOne(Wrappers.<WpsOrderPlanWarning>lambdaQuery()
                .eq(WpsOrderPlanWarning::getOrderNo, orderNo)
                .eq(WpsOrderPlanWarning::getWarningType, warningType)
                .eq(WpsOrderPlanWarning::getHandleStatus, OrderWarningHandleStatusEnum.UN_HANDLE.getCode())
                .last("LIMIT 1"));
        if (null == wpsOrderPlanWarning) {
            return;
        }
        wpsOrderPlanWarning.setHandleUser(userId);
        wpsOrderPlanWarning.setHandleStatus(handleStatus);
        wpsOrderPlanWarning.setHandleTime(new Date());
        wpsOrderPlanWarning.setHandleContent(handleContent);
        wpsOrderPlanWarningService.updateById(wpsOrderPlanWarning);
    }

    @Override
    public List<SectionPayloadWarningOrderResp> listSectionPayloadWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        if (null == userId || null == workbenchOrderWarningReq) {
            return Collections.emptyList();
        }
        List<WpsOrderPlanWarning> wpsOrderPlanWarningList = listWpsOrderPlanWarning(userId, workbenchOrderWarningReq);
        if (CollectionUtils.isEmpty(wpsOrderPlanWarningList)) {
            return Collections.emptyList();
        }
        Map<String, List<WpsOrderPlanWarning>> wpsOrderPlanWarningMap = wpsOrderPlanWarningList.stream()
                .collect(Collectors.groupingBy(WpsOrderPlanWarning::getLineUuid));
        List<SectionPayloadWarningOrderResp> resList = Lists.newArrayList();
        wpsOrderPlanWarningMap.forEach((lineUuid, subWpsOrderPlanWarningList) -> {
            if (CollectionUtils.isEmpty(subWpsOrderPlanWarningList)) {
                return;
            }
            WpsOrderPlanWarning wpsOrderPlanWarning = subWpsOrderPlanWarningList.get(0);
            SectionPayloadWarningOrderResp sectionPayloadWarningOrderResp = new SectionPayloadWarningOrderResp();
            sectionPayloadWarningOrderResp.setLineUuid(lineUuid);
            sectionPayloadWarningOrderResp.setLineCode(wpsOrderPlanWarning.getLineCode());
            sectionPayloadWarningOrderResp.setOrderNo(wpsOrderPlanWarning.getOrderNo());
            sectionPayloadWarningOrderResp.setScheduledDate(wpsOrderPlanWarning.getScheduleDate());
            resList.add(sectionPayloadWarningOrderResp);
        });
        return resList;
    }

    private List<WpsOrderPlanWarning> listWpsOrderPlanWarning(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        if (null == userId || null == workbenchOrderWarningReq) {
            return Collections.emptyList();
        }
        Set<String> factoryCodes = plannerDataPermissionService.getFactoryCodeByUserId(userId);
        if (CollectionUtils.isEmpty(factoryCodes)) {
            return Collections.emptyList();
        }
        LocalDate startDate = workbenchOrderWarningReq.getStartDate();
        LocalDate endDate = workbenchOrderWarningReq.getEndDate();
        String orderAbnormalCode = workbenchOrderWarningReq.getOrderAbnormalCode();
        String factoryCode = workbenchOrderWarningReq.getFactoryCode();
        if (StringUtils.isNotEmpty(factoryCode)) {
            factoryCodes.retainAll(Collections.singletonList(factoryCode));
        }
        if (CollectionUtils.isEmpty(factoryCodes)) {
            return Collections.emptyList();
        }
        List<String> orderNos = listOrderNosByUserIdAndPeriod(userId, new ArrayList<>(factoryCodes), startDate, endDate);
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }
        return wpsOrderPlanWarningService.listByWarningTypeAndOrderNoAndDate(
                orderNos, orderAbnormalCode, startDate, endDate);
    }

    private List<WpsRowData> listWarningOrder(Long userId, WorkbenchOrderWarningReq workbenchOrderWarningReq) {
        List<WpsOrderPlanWarning> wpsOrderPlanWarningList = listWpsOrderPlanWarning(userId, workbenchOrderWarningReq);
        if (CollectionUtils.isEmpty(wpsOrderPlanWarningList)) {
            return Collections.emptyList();
        }
        LocalDate startDate = workbenchOrderWarningReq.getStartDate();
        LocalDate endDate = workbenchOrderWarningReq.getEndDate();
        Set<String> factoryCodes = workbenchOrderWarningReq.getFactoryCodes();
        List<String> orderNoList = wpsOrderPlanWarningList.stream().map(WpsOrderPlanWarning::getOrderNo)
                .distinct().collect(Collectors.toList());
        return queryAndFilterWpsRowData(factoryCodes, userId, startDate, endDate, orderNoList);
    }

    private List<String> listOrderNosByUserIdAndPeriod(Long userId, List<String> factoryCodes, LocalDate startDate, LocalDate endDate) {
        List<WpsRowData> wpsRowDataList = wpsRowDataFacadeService.listRowDataByDate(userId, startDate,
                endDate, factoryCodes);
        if (CollectionUtils.isEmpty(wpsRowDataList)) {
            return Lists.newArrayList();
        }
        return wpsRowDataList.stream().map(WpsRowData::getOrderNo).distinct().collect(Collectors.toList());
    }

    private List<WpsRowData> queryWpsRowDataByUserIdAndPeriod(Set<String> factoryCodes, Long userId, LocalDate startDate, LocalDate endDate) {
        List<WpsRowData> wpsRowDataList = Lists.newArrayList();
        factoryCodes.forEach(subFactoryCode -> {
            List<WpsOrderProcessEnum> processEnums = Lists.newArrayList();
            processEnums.add(WpsOrderProcessEnum.PURE);
            processEnums.add(WpsOrderProcessEnum.ATP_CHECK);
            processEnums.add(WpsOrderProcessEnum.READ_STORAGE);
            List<WpsRowData> subWpsRowDataList = wpsRowDataFacadeService.customListOrder(processEnums, userId,
                    startDate, endDate, subFactoryCode,true, Maps.newHashMap());
            if (CollectionUtils.isNotEmpty(subWpsRowDataList)) {
                wpsRowDataList.addAll(subWpsRowDataList);
            }
        });
        return wpsRowDataList;
    }

    private List<WpsRowData> queryAndFilterWpsRowData(Set<String> factoryCodes, Long userId, LocalDate startDate, LocalDate endDate,
                                                      List<String> orderNoList) {
        List<WpsRowData> wpsRowDataList = queryWpsRowDataByUserIdAndPeriod(factoryCodes, userId, startDate, endDate);
        return wpsRowDataList.stream()
                .filter(wpsRowData -> orderNoList.contains(wpsRowData.getOrderNo()))
                .collect(Collectors.toList());
    }

    private List<UnMatchOrderResp> buildUnMatchOrderRespList(List<WpsRowData> filteredWpsRowDataList) {
        return filteredWpsRowDataList.stream().map(wpsRowData -> {
            UnMatchOrderResp unMatchOrderResp = new UnMatchOrderResp();
            unMatchOrderResp.setCustomer(wpsRowData.getCustomerCode());
            unMatchOrderResp.setFactory(wpsRowData.getFactory());
            unMatchOrderResp.setSaleOrderNo(wpsRowData.getSellOrderNo());
            unMatchOrderResp.setLineItemNo(wpsRowData.getRowItem());
            unMatchOrderResp.setDeliveryOrderNo(wpsRowData.getOutDeliveryNo());
            unMatchOrderResp.setDeliveryOrderLineItemNo(wpsRowData.getOutRowItem());
            unMatchOrderResp.setOrderNo(wpsRowData.getOrderNo());
            unMatchOrderResp.setOrderQuantity(Optional.ofNullable(wpsRowData.getOrderPcsQty()).orElse(0).intValue());
            unMatchOrderResp.setInStockQuantity(Optional.ofNullable(wpsRowData.getStockedPcsQty()).orElse(0).intValue());
            unMatchOrderResp.setLatestPlanDate(wpsRowData.get_endProductPeriod());
            unMatchOrderResp.setFinalShippingTime(wpsRowData.getWarningFinalShipDate());
            unMatchOrderResp.setProcessStatus(OrderWarningHandleStatusEnum.UN_HANDLE.getCode());
            return unMatchOrderResp;
        }).collect(Collectors.toList());
    }

    private List<InspectionWarningOrderResp> buildInspectionWarningOrderRespList(List<WpsRowData> filteredWpsRowDataList) {
        return filteredWpsRowDataList.stream().map(wpsRowData -> {
            InspectionWarningOrderResp inspectionWarningOrderResp = new InspectionWarningOrderResp();
            inspectionWarningOrderResp.setCustomer(wpsRowData.getCustomerCode());
            inspectionWarningOrderResp.setFactory(wpsRowData.getFactory());
            inspectionWarningOrderResp.setSaleOrderNo(wpsRowData.getSellOrderNo());
            inspectionWarningOrderResp.setLineItemNo(wpsRowData.getRowItem());
            inspectionWarningOrderResp.setOrderNo(wpsRowData.getOrderNo());
            inspectionWarningOrderResp.setWorkshop(wpsRowData.getWorkshopCode());
            inspectionWarningOrderResp.setLine(wpsRowData.getLineCode());
            inspectionWarningOrderResp.setInStockQuantity(Optional.ofNullable(wpsRowData.getStockedPcsQty()).orElse(0).intValue());
            inspectionWarningOrderResp.setProcessStatus(OrderWarningHandleStatusEnum.UN_HANDLE.getCode());
            return inspectionWarningOrderResp;
        }).collect(Collectors.toList());
    }

    private List<AptExceptionOrderResp> buildAtpExceptionOrderRespList(List<WpsRowData> filteredWpsRowDataList) {
        List<AptExceptionOrderResp> resList = Lists.newArrayList();
        for (WpsRowData wpsRowData : filteredWpsRowDataList) {
            Map<String, Set<String>> materialCodeMap = wpsRowData.getMaterialCodeMap();
            if (MapUtils.isEmpty(materialCodeMap)) {
                continue;
            }
            materialCodeMap.forEach((materialId, materialCodeSet) -> {
                if (CollectionUtils.isEmpty(materialCodeSet)) {
                    return;
                }
                materialCodeSet.forEach(materialCode -> {
                    AptExceptionOrderResp aptExceptionOrderResp = new AptExceptionOrderResp();
                    aptExceptionOrderResp.setCustomer(wpsRowData.getCustomerCode());
                    aptExceptionOrderResp.setSaleOrderNo(wpsRowData.getSellOrderNo());
                    aptExceptionOrderResp.setLineItemNo(wpsRowData.getRowItem());
                    aptExceptionOrderResp.setOrderNo(wpsRowData.getOrderNo());
                    aptExceptionOrderResp.setMaterialId(materialCode);
                    // TODO 物料信息填充
                    aptExceptionOrderResp.setMaterialName("");
                    aptExceptionOrderResp.setMaterialDemandTime(null);
                    aptExceptionOrderResp.setMaterialDemandQuantity(0);
                    aptExceptionOrderResp.setSupplierName("");
                    aptExceptionOrderResp.setPurchaseGroup("");
                    aptExceptionOrderResp.setPoDueDate(null);
                    aptExceptionOrderResp.setProcessStatus(OrderWarningHandleStatusEnum.UN_HANDLE.getCode());
                    resList.add(aptExceptionOrderResp);
                });
            });
        }
        return resList;
    }

    private OrderStatusResp buildOrderStatusRsp(WorkbenchOrderPeriodEnum periodEnum, List<WpsRowData> wpsRowDataList) {
        List<String> orderNos = wpsRowDataList.stream()
                .filter(wpsRowData ->
                        null != wpsRowData.get_startProductPeriod() &&
                                !wpsRowData.get_startProductPeriod().isBefore(periodEnum.getStartDate())
                                && !wpsRowData.get_endProductPeriod().isAfter(periodEnum.getEndDate())
                ).map(WpsRowData::getOrderNo).distinct().collect(Collectors.toList());
        log.info("buildOrderStatusRsp orderNos={}, startDate={}, endDate={}.", orderNos, periodEnum.getStartDate(), periodEnum.getEndDate());
        List<WpsOrderPlanWarning> wpsOrderPlanWarningList = wpsOrderPlanWarningService
                .listByOrderNosAndDates(orderNos, periodEnum.getStartDate(), periodEnum.getEndDate());
        OrderStatusResp orderStatusResp = new OrderStatusResp();
        orderStatusResp.setPeriodInfo(PeriodInfo.builder()
                .code(periodEnum.getCode())
                .name(periodEnum.getDescription())
                .startDate(periodEnum.getStartDate())
                .endDate(periodEnum.getEndDate())
                .build());
        orderStatusResp.setTotalOrderNum(orderNos.size());
        orderStatusResp.setAbnormalOrderNum(CollectionUtils.isEmpty(wpsOrderPlanWarningList) ? 0 : wpsOrderPlanWarningList.size());
        orderStatusResp.setAbnormalOrderNumDetails(buildAbnormalOrderNumDetails(wpsOrderPlanWarningList));
        return orderStatusResp;
    }

    private List<AbnormalOrderNumDetail> buildAbnormalOrderNumDetails(List<WpsOrderPlanWarning> wpsOrderPlanWarningList) {
        if (CollectionUtils.isEmpty(wpsOrderPlanWarningList)) {
            return Lists.newArrayList();
        }
        Map<String, Integer> abnormalOrderMap = wpsOrderPlanWarningList.parallelStream()
                .collect(Collectors.groupingBy(
                        WpsOrderPlanWarning::getWarningType,
                        Collectors.collectingAndThen(
                                Collectors.mapping(WpsOrderPlanWarning::getOrderNo, Collectors.toList()),
                                List::size
                        )
                ));
        return abnormalOrderMap.entrySet().stream()
                .map(entry -> new AbnormalOrderNumDetail(entry.getKey(), WpsOrderWarningTypeEnum.getNameByCode(entry.getKey()), entry.getValue()))
                .collect(Collectors.toList());
    }
}