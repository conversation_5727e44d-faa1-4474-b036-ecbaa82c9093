package com.lds.oneplanning.wps.enums;

/**
 * 订单告警类型枚举
 */
public enum WarningJsonTypeEnum {
    UN_SCHEDULE_SHIP_DATE("unScheduleShipDate", "未排产船期"),
    UN_FINISH_SHIP_DATE("unFinishShipDate", "未完工船期"),
    UN_STORE_SHIP_DATE("unStoreShipDate", "未入库船期"),
    UN_SCHEDULE_INSPECT_DATE("unScheduleInspectDate", "未排产验货日期") ;

    private String code;
    private String name;
    WarningJsonTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    public static String getNameByCode(String code){
        for (WarningJsonTypeEnum typeEnum : WarningJsonTypeEnum.values()){
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getName();
            }
        }
        return null;
    }

}