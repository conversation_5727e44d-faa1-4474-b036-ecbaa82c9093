package com.lds.oneplanning.wps.controller;

import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.req.InProductionAbnormalReq;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderService;
import com.lds.oneplanning.wps.vo.ProcessWorkOrderVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/5/29
 */
@Api("在制工单")
@RestController
@RequestMapping("/wps/processWordOrder")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ProcessWorkOrderController {

    private final IMesProcessWorkOrderService mesProcessWorkOrderService;
    private final IUserInfoService userInfoService;
    private final IFactoryFacadeService factoryFacadeService;

    @PostMapping("/page")
    @ApiOperation("在制工单分页查询")
    public Page<ProcessWorkOrderVO> page(@RequestBody InProductionAbnormalReq req) {
        ViewSource source = userInfoService.getCurrentUserType();
        if (Objects.requireNonNull(source) == ViewSource.PC) {
            Set<String> userFactoryCodes = factoryFacadeService.getUserFactoryCodes(UserContextUtils.getUserId());
            req.setFactoryCodeList(userFactoryCodes);
        }
        return mesProcessWorkOrderService.queryPage(req);
    }
}
