package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningFrozenUnfrozenAbnormal;
import com.lds.oneplanning.wps.entity.WarningProcessRouteAbnormal;
import com.lds.oneplanning.wps.mapper.WarningProcessRouteAbnormalMapper;
import com.lds.oneplanning.wps.service.IWarningProcessRouteAbnormalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.wps.vo.WarningProcessRouteAbnormalVO;
import com.lds.oneplanning.wps.vo.WarningProcessRouteParams;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-06-04
 */
@Service
public class WarningProcessRouteAbnormalServiceImpl extends ServiceImpl<WarningProcessRouteAbnormalMapper, WarningProcessRouteAbnormal> implements IWarningProcessRouteAbnormalService {

    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningProcessRouteAbnormal }>
     */
    @Override
    public List<WarningProcessRouteAbnormal> queryUnHandleData() {
        return baseMapper.queryUnHandleData();
    }

    @Override
    public IPage<WarningProcessRouteAbnormalVO> selectPage(Page<WarningProcessRouteAbnormalVO> page, WarningProcessRouteParams params) {
        return baseMapper.selectPage(page,params);
    }
}
