package com.lds.oneplanning.wps.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/5/16 9:43
 */
@ApiModel(value="订单告警对象", description="订单告警对象")
@Data
public class OrderWarningDTO {

    private String desc ;
    private String orderNo;
    private String viewPageUri;
    @ApiModelProperty(value = "预警级别:1红 2黄")
    private Integer warningLevel;
}
