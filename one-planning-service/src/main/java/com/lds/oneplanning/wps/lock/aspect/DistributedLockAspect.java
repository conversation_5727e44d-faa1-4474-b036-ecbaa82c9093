package com.lds.oneplanning.wps.lock.aspect;


import com.iot.common.exception.BusinessException;
import com.lds.oneplanning.wps.lock.DistributedLock;
import com.lds.oneplanning.wps.lock.support.LockKeyGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

import static com.lds.oneplanning.wps.exception.WpsExceptionEnum.DISTRIBUTE_LOCK_EXCEPTION;

/**
 * 分布式锁切面
 * <p>
 * 处理 {@link DistributedLock} 注解的AOP实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class DistributedLockAspect {

    private final RedissonClient redissonClient;
    private final LockKeyGenerator lockKeyGenerator;

    /**
     * 环绕通知处理分布式锁
     *
     * @param point 切点
     * @return 方法执行结果
     * @throws Throwable 执行过程中的异常
     */
    @Around("@annotation(com.lds.oneplanning.wps.lock.DistributedLock)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        // 获取注解
        DistributedLock lockAnnotation = method.getAnnotation(DistributedLock.class);

        // 生成锁的key
        String lockKey = lockKeyGenerator.generateKey(
                lockAnnotation.prefix(),
                lockAnnotation.key(),
                point
        );

        // 获取锁
        RLock lock = redissonClient.getLock(lockKey);
        boolean isLocked = false;

        try {
            log.debug("尝试获取分布式锁，key: {}", lockKey);

            // 尝试获取锁
            isLocked = lock.tryLock(
                    lockAnnotation.waitTime(),
                    lockAnnotation.leaseTime(),
                    lockAnnotation.timeUnit()
            );

            if (!isLocked) {
                log.warn("获取分布式锁失败，key: {}", lockKey);
                throw new BusinessException(DISTRIBUTE_LOCK_EXCEPTION);
            }

            log.debug("成功获取分布式锁，key: {}", lockKey);

            // 执行目标方法
            return point.proceed();
        } catch (InterruptedException e) {
            log.error("获取分布式锁时线程被中断，key: {}", lockKey, e);
            Thread.currentThread().interrupt();
            throw new BusinessException(DISTRIBUTE_LOCK_EXCEPTION);
        } catch (BusinessException e) {
            // 直接抛出业务异常
            throw e;
        } catch (Throwable e) {
            log.error("执行分布式锁方法时发生异常，key: {}", lockKey, e);
            throw e;
        } finally {
            // 释放锁
            releaseLock(lock, isLocked, lockKey);
        }
    }

    private void releaseLock(RLock lock, boolean isLocked, String lockKey) {
        if (lock != null && lock.isHeldByCurrentThread()) {
            try {
                lock.unlock();
                log.debug("成功释放分布式锁，key: {}", lockKey);
            } catch (IllegalMonitorStateException e) {
                log.warn("释放分布式锁失败，锁可能已自动释放，key: {}", lockKey, e);
            }
        }
    }
}
