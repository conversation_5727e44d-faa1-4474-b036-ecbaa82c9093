package com.lds.oneplanning.wps.schedule.handlers.round1;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.entity.SchedulePriority;
import com.lds.oneplanning.basedata.service.ISchedulePriorityService;
import com.lds.oneplanning.wps.enums.SchedulePlanErrorCodeEnum;
import com.lds.oneplanning.wps.helper.WpsSchedulePlanLogHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 产品组优先级处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsProductGroupPriorityHandler implements IWpsAutoScheduleHandler {

    @Autowired
    private ISchedulePriorityService schedulePriorityService;
    @Autowired
    WpsSchedulePlanLogHelper wpsSchedulePlanLogHelper;

    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        List<String> lineUuids = context.getLineUuids();
        if (CollectionUtils.isEmpty(orderList) || CollectionUtils.isEmpty(lineUuids)) {
            return;
        }
        Set<String> customerCodes = Sets.newHashSet();
        Set<String> productGroupCodes = Sets.newHashSet();
        orderList.forEach(order -> {
            if (StringUtils.isNotEmpty(order.getCustomerCode())) {
                customerCodes.add(order.getCustomerCode());
            }
            if (StringUtils.isNotEmpty(order.get_productGroupCode())) {
                productGroupCodes.add(order.get_productGroupCode());
            }else{
                context.getSchedulePlanLogMap().putAll(wpsSchedulePlanLogHelper.createWpsSchedulePlanLog(order, null, SchedulePlanErrorCodeEnum.PRODUCT_GORUP_NOT_EXIST,context));
            }
        });
        log.info("WPS排产,产品组优先级处理器,productGroupCodes:{}.", productGroupCodes);
        if (CollectionUtils.isEmpty(productGroupCodes)) {
            return;
        }
        List<SchedulePriority> schedulePriorities = schedulePriorityService.listByProductGroupCodes(lineUuids, productGroupCodes);
        if (CollectionUtils.isEmpty(schedulePriorities)) {
            return;
        }
        initLineCodePriorityMap(context, schedulePriorities, customerCodes);
    }

    private void initLineCodePriorityMap(WpsAutoScheduleContext context, List<SchedulePriority> schedulePriorities, Set<String> customerCodes) {
        schedulePriorities.forEach(schedulePriority -> {
            String customerCode = schedulePriority.getCustomerCode();
            String productGroupCode = schedulePriority.getProductGroupCode();
            Integer prioritySeq = schedulePriority.getPrioritySeq();
            String lineUuid = schedulePriority.getLineUuid();
            if (isCustomerValid(customerCode, customerCodes)) {
                if (StringUtils.isNotEmpty(productGroupCode)) {
                    updateCustomerProductGroupPriorityMap(context, customerCode, productGroupCode, prioritySeq, lineUuid);
                } else {
                    updateCustomerLinePriorityMap(context, customerCode, prioritySeq, lineUuid);
                }
            } else {
                updateProductGroupPriorityMap(context, productGroupCode, prioritySeq, lineUuid);
            }
        });
    }

    private boolean isCustomerValid(String customerCode, Set<String> customerCodes) {
        return StringUtils.isNotEmpty(customerCode) && CollectionUtils.isNotEmpty(customerCodes) && customerCodes.contains(customerCode);
    }

    private void updateCustomerProductGroupPriorityMap(WpsAutoScheduleContext context, String customerCode, String productGroupCode, Integer prioritySeq, String lineUuid) {
        context.getCustomerProductGroupPriorityMap()
                .computeIfAbsent(customerCode, k -> Maps.newHashMap())
                .computeIfAbsent(productGroupCode, k -> Maps.newHashMap())
                .computeIfAbsent(prioritySeq, k -> Sets.newHashSet())
                .add(lineUuid);
    }

    private void updateCustomerLinePriorityMap(WpsAutoScheduleContext context, String customerCode, Integer prioritySeq, String lineUuid) {
        context.getCustomerLinePriorityMap()
                .computeIfAbsent(customerCode, k -> Maps.newHashMap())
                .computeIfAbsent(prioritySeq, k -> Sets.newHashSet())
                .add(lineUuid);
    }

    private void updateProductGroupPriorityMap(WpsAutoScheduleContext context, String productGroupCode, Integer prioritySeq, String lineUuid) {
        context.getProductGroupPriorityMap()
                .computeIfAbsent(productGroupCode, k -> Maps.newHashMap())
                .computeIfAbsent(prioritySeq, k -> Sets.newHashSet())
                .add(lineUuid);
    }

    @Override
    public int getOrder() {
        return 3;
    }

    @Override
    public int getRound() {
        return 1;
    }
}