package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lds.basic.account.user.api.UserApi2;
import com.lds.basic.account.user.dto.UserDto;
import com.lds.basic.account.user.enums.UserStatusEnum;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.wps.entity.WpsOrderDispatch;
import com.lds.oneplanning.wps.mapper.WpsOrderDispatchMapper;
import com.lds.oneplanning.wps.model.WpsOrderDispatchDTO;
import com.lds.oneplanning.wps.service.IWpsOrderDispatchService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
@Service
public class WpsOrderDispatchServiceImpl extends ServiceImpl<WpsOrderDispatchMapper, WpsOrderDispatch> implements IWpsOrderDispatchService {

    @Resource
    private UserApi2 userApi2;
    @Value("${ehr.locationId:516}")
    private Long ehrLocationId;
    @Override
    public Page<WpsOrderDispatchDTO> page(String keyword, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<WpsOrderDispatch> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<WpsOrderDispatch> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(WpsOrderDispatch::getOrderNo,keyword).or()
                    .like(WpsOrderDispatch::getPlannerEmpNo,keyword));
        }
        queryWrapper.orderByDesc(WpsOrderDispatch::getUpdateTime).orderByAsc(WpsOrderDispatch::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<WpsOrderDispatchDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<WpsOrderDispatchDTO> results = BeanUtil.mapList(entityPage.getRecords(), WpsOrderDispatchDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<WpsOrderDispatchDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> empNos = sourceList.stream().map(WpsOrderDispatchDTO::getOrderNo).collect(Collectors.toSet());
        if (empNos.isEmpty()) {
            return;
        }
        UserDto param = new UserDto();
        param.setJobNos(empNos);
        param.setLocationId(ehrLocationId);
        param.setUserStatus(UserStatusEnum.NORMAL.getCode());
        List<UserDto> userList = userApi2.findDto(param);
        Map<String,String> empNoUserNameMap = userList.stream().collect(Collectors.toMap(UserDto::getJobNo,UserDto::getName,(s, s2) -> s2));
        sourceList.stream().forEach(dto -> {
            dto.setPlannerName(empNoUserNameMap.get(dto.getPlannerEmpNo()));
        });
    }
    @Override
    public WpsOrderDispatchDTO detail(Long id) {
        WpsOrderDispatch entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        WpsOrderDispatchDTO res = BeanUtil.map(entity, WpsOrderDispatchDTO.class);
        this.decorate(Lists.newArrayList(res));
        return res;
    }

    @Override
    public void dispatch(WpsOrderDispatchDTO dto) {
        if (CollectionUtils.isEmpty(dto.getOrderNos())) {
            return;
        }
        // 使用覆盖的模式，如果有分配的话，一个订单只分配给一个计划员
        baseMapper.delete(Wrappers.<WpsOrderDispatch>lambdaQuery().in(WpsOrderDispatch::getOrderNo,dto.getOrderNos()));
        List<WpsOrderDispatch> targetList =  Lists.newArrayList();
        dto.getOrderNos().stream().forEach(orderNo ->{
            WpsOrderDispatch entity = BeanUtil.map(dto, WpsOrderDispatch.class);
            entity.setOrderNo(orderNo);
            targetList.add(entity);
        });
      this.saveBatch(targetList);
    }

    @Override
    public Set<String> getDispatchOrders(String empNo, Collection<String> targetOrders) {
        if (StringUtils.isBlank(empNo) || CollectionUtils.isEmpty(targetOrders)) {
            return Sets.newHashSet();
        }
        LambdaQueryWrapper<WpsOrderDispatch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(WpsOrderDispatch::getOrderNo);
        queryWrapper.eq(WpsOrderDispatch ::getPlannerEmpNo,empNo);
        queryWrapper.in(WpsOrderDispatch::getOrderNo,targetOrders);
        return baseMapper.selectList(queryWrapper).stream().map(WpsOrderDispatch::getOrderNo).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getMinusOrders(String empNo, Collection<String> sourceList) {
        if (StringUtils.isBlank(empNo) || CollectionUtils.isEmpty(sourceList)) {
            return Sets.newHashSet();
        }
        LambdaQueryWrapper<WpsOrderDispatch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(WpsOrderDispatch::getOrderNo);
        queryWrapper.ne(WpsOrderDispatch ::getPlannerEmpNo,empNo);
        queryWrapper.in(WpsOrderDispatch::getOrderNo,sourceList);
        return baseMapper.selectList(queryWrapper).stream().map(WpsOrderDispatch::getOrderNo).collect(Collectors.toSet());
    }
}
