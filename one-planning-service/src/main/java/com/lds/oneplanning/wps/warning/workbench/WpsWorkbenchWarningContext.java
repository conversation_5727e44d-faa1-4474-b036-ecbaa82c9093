package com.lds.oneplanning.wps.warning.workbench;

import com.lds.oneplanning.wps.enums.WpsPlanTypeEnum;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
public class WpsWorkbenchWarningContext implements Serializable {

    private static final long serialVersionUID = 8745964683277872586L;

    private Long userId;

    private String factoryCode;

    /**
     * 订单+线体区分一条数据
     */
    private List<WpsRowData> orders;

    /**
     * 排产场景 (整机/组件/部件)
     */
    private WpsPlanTypeEnum productType;

    /**
     * 生产线预排产数据
     * key: 产线UUID
     * value: <日期，排产数据>
     */
    private Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap;

    public Map<String, List<WpsRowData>> getWarningOrderMap() {
        // 按销售订单号(sellOrderNo),行项目(rowItem),外向交货单(outDeliveryNo),外向行项目(outRowItem)
        return Optional.ofNullable(orders)
                .orElse(Collections.emptyList())
                .stream()
                .filter(order -> order != null && order.getWarningFinalShipDate() != null
                        && order.getSellOrderNo() != null && order.getRowItem() != null
                        && order.getSchedulePcsQty() != null && order.getSchedulePcsQty().intValue() > 0)
                .collect(Collectors.groupingBy(this::buildOrderKey));
    }

    private String buildOrderKey(WpsRowData order) {
        return String.join("-",
                order.getSellOrderNo(),
                order.getRowItem(),
                Optional.ofNullable(order.getOutDeliveryNo()).orElse("nil"),
                Optional.ofNullable(order.getOutRowItem()).orElse("nil")
        );
    }
}