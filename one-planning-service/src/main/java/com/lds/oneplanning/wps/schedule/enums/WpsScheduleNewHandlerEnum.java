package com.lds.oneplanning.wps.schedule.enums;

import com.lds.oneplanning.wps.schedule.handlers.round1.*;

public enum WpsScheduleNewHandlerEnum {
    WpsScheduleOrderHandler("自动排产", WpsAutoScheduleOrderHandler.class, WpsAutoScheduleOrderHandlerV2.class),
    WpsEndOrderHandler("尾单处理", WpsEndProductPeriodOrderHandler.class, WpsEndProductPeriodOrderHandlerV2.class),
    WpsExpireOrderHandler("过期订单处理",WpsExpireOrderHandler.class,WpsExpireOrderHandlerV2.class),
    ;


    private WpsScheduleNewHandlerEnum(String name,Class clazz,Class newClazz){
        this.name=name;
        this.clazz=clazz;
        this.newClazz=newClazz;
    }
    private String name;
    private Class  clazz;
    private Class newClazz;

    public String getName() {
        return name;
    }

    public Class getClazz() {
        return clazz;
    }

    public Class getNewClazz() {
        return newClazz;
    }
}
