package com.lds.oneplanning.wps.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.wps.model.WpsPlanVersionDTO;
import com.lds.oneplanning.wps.service.IWpsPlanVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-01-21
 */
@Api(value = "WpsPlanVersionController", tags = "WpsPlanVersionController")
@RestController
@RequestMapping("/wps/plan/version")
public class WpsPlanVersionController {


    @Resource
    IWpsPlanVersionService wpsPlanVersionService;
    @Resource
    IPlannerBaseService plannerBaseService;

    @ApiOperation(value = "获取用户的排产版本列表")
    @GetMapping("/findUserPlanVersions")
    public List<WpsPlanVersionDTO> findUserPlanVersions(@RequestParam("factoryCode")String factoryCode){
        String empNo = plannerBaseService.getEmpNoByUserId(UserContextUtils.getUserId());
        return wpsPlanVersionService.findList(empNo,factoryCode);
    }

}
