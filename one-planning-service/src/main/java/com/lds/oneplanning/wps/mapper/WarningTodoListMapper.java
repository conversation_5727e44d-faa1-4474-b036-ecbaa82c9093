package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lds.oneplanning.wps.entity.WarningTodoList;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.po.WarningTodoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【warning_todo_list】的数据库操作Mapper
* @createDate 2025-05-16 09:58:56
* @Entity com.lds.oneplanning.wps.entity.WarningTodoList
*/
public interface WarningTodoListMapper extends BaseMapper<WarningTodoList> {
    List<WarningTodoPO> queryUnHandleData(@Param("pushStatus") Integer pushStatus, @Param("types") List<WpsOrderWarningTypeEnum> types);

    /**
     * 更新指定条件的记录的push_status字段为1
     * @param assignee 分配人
     * @param warningType 警告类型
     * @param factoryCode 工厂代码
     * @return 影响的行数
     */
    int updatePushStatusByAssigneeWarningTypeFactory(@Param("assignee") String assignee, 
                                                    @Param("warningType") String warningType, 
                                                    @Param("factoryCode") String factoryCode);

    /**
     * 查询未关闭数据
     *
     * @param type   类型
     * @param bizIds BIZ ID
     * @return {@link List }<{@link WarningTodoPO }>
     */
    List<WarningTodoPO> queryUnClosedData(@Param("type") WpsOrderWarningTypeEnum type, @Param("bizIds") Set<Long> bizIds);
}
