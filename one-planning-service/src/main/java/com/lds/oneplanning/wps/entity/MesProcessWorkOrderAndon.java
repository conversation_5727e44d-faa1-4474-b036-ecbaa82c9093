package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MesProcessWorkOrderAndon对象", description="")
public class MesProcessWorkOrderAndon implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "异常事项")
    private String exceptionDetails;

    @ApiModelProperty(value = "是否停线")
    private Integer isProductionStopped;

    @ApiModelProperty(value = "排序")
    private Integer sortNo;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "预计关闭时间")
    private Date estimatedResolveTime;


}
