package com.lds.oneplanning.wps.enums;

import lombok.Getter;

/**
 * 预警条件
 */
@Getter
public enum AlertConditionEnum {

    UN_SCHEDULE_MIN_DAYS("UN_SCHEDULE_MIN_DAYS", "未排产告警最小天数"),
    UN_SCHEDULE_MAX_DAYS("UN_SCHEDULE_MAX_DAYS", "未排产告警最大天数"),

    UN_STORE_SHIP_DAYS("UN_STORE_SHIP_DAYS", "未入库船期提前天数"),
    BEFORE_PLAN_SHIP_DATE("BEFORE_PLAN_SHIP_DATE", "计划装柜提前日期"),
    BEFORE_EARLY_ONLINE_DAYS("BEFORE_EARLY_ONLINE_DAYS", "最早上线日期提前天数"),

    /**
     * 验货预警
     */
    INSPECT_DAYS("INSPECT_DAYS", "验货预警天数"),

    /**
     * 在制工单
     */
    //当前时间-物料下架时间区间
    IN_PRODUCTION_SHELVE_DATE_START_DAYS("IN_PRODUCTION_SHELVE_DATE_START_DAYS", "当前时间-物料下架时间区间起始天数"),
    IN_PRODUCTION_SHELVE_DATE_END_DAYS("IN_PRODUCTION_SHELVE_DATE_END_DAYS", "当前时间-物料下架时间区间截止天数");




    private String code;
    private String name;

    AlertConditionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}