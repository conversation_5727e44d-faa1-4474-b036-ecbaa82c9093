package com.lds.oneplanning.wps.warning.auto.handlers;

import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsProductionCapacityWarningEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 排产日期超出上线范围预警（上线开始日期 > 上线结束日期）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WpsWarningScheduleDateOverRangeHandler implements IWpsAutoPlanWarningHandler {

    @Override
    public void execute(WpsAutoScheduleContext context) {
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        orderList.forEach(order -> {
            LocalDate startProductPeriod = order.get_startProductPeriod();
            LocalDate endProductPeriod = order.get_endProductPeriod();
            if (null == startProductPeriod || null == endProductPeriod) {
                return;
            }
            if (startProductPeriod.isAfter(endProductPeriod)) {
                order.getWarningColorMap().put("row", WpsProductionCapacityWarningEnum.SCHEDULE_DATE_OUT_OF_RANGE.getColorValue());
                log.debug("WPS告警,排产日期超出上线范围预警,订单号:{},开始日期:{},结束日期:{}.",
                        order.getOrderNo(), startProductPeriod.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        endProductPeriod.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
        });
    }

    @Override
    public int getOrder() {
        return 3;
    }
}