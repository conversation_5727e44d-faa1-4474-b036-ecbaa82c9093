package com.lds.oneplanning.wps.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物料不齐异常类型
 */
@Getter
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum MaterialAtpAbnormalType implements IEnum<Integer> {
    IN_STOCK_NOT_COMPLETE_1(1, "次日在库不齐套"),
    IN_STOCK_NOT_COMPLETE_3(2, "3日内在库不齐套"),
    ORDER_NOT_COMPLETE_7(3, "7天内订单实物不齐套"),
    INFORMATION_NOT_COMPLETE_14(4, "双周内信息不齐套");

    private final int value;
    @JsonValue
    private final String name;

    @Override
    public Integer getValue() {
        return this.value;
    }
}
