package com.lds.oneplanning.wps.controller;


import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.model.WpsOrderDispatchDTO;
import com.lds.oneplanning.wps.service.IWpsOrderDispatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
@Api(value = "WpsOrderDispatchController", tags = "wps订单分配")
@RestController
@RequestMapping("/wps/orderDispatch")
public class WpsOrderDispatchController {

    @Resource
    private IWpsOrderDispatchService wpsOrderDispatchService;


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<WpsOrderDispatchDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                 @RequestParam(value = "pageNum")Integer pageNum,
                                 @RequestParam(value = "pageSize")Integer pageSize
    ){
        return wpsOrderDispatchService.page(keyword,pageNum,pageSize);
    }

    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public WpsOrderDispatchDTO detail(@PathVariable("id")Long id){
        return  wpsOrderDispatchService.detail(id);
    }

    @ApiOperation(value = "订单分配", notes = "订单分配")
    @PostMapping("/dispatch")
    public void dispatch(@RequestBody WpsOrderDispatchDTO dto){
         wpsOrderDispatchService.dispatch(dto);
    }


}
