package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 物料不齐异常
 * <p>
 * 搜索：
 * 工厂,订单号,时间（计划上线日期）,客户,物料ID,异常类型
 * <p>
 * 排序：灯色、计划上线日期
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableHeader(type = WpsOrderWarningTypeEnum.ATP_EXCEPTION, source = {ViewSource.PC, ViewSource.MANAGER})
public class MaterialAtpAbnormalVO extends AffectsPlan {
    private Long id;
    /**
     * 调整后上线时间-前端编辑
     */
    private LocalDate adjustedOnlineTimeEdited;


    @ApiModelProperty(value = "预警灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "计划上线时间")
    private LocalDate plannedOnlineTime;

    @ApiModelProperty(value = "客户")
    private String customer;

    /**
     * 销售订单
     */
    private String salesOrderNumber;

    @ApiModelProperty(value = "销售订单-行项目")
    private String salesOrderNumberWithLineNumber;

    /**
     * 采购订单
     */
    private String purchaseOrderNumber;

    @ApiModelProperty(value = "采购订单-行项目")
    private String purchaseOrderNumberWithLineNumber;

    private String lineNumber;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;


    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    private String materialDescription;

    /**
     * TODO 原本是上线数量，现在改成计划数量，需要确认取值是否一样
     */
    @ApiModelProperty(value = "计划数量")
    private Integer onlineQuantity;

    @ApiModelProperty(value = "异常类型")
    private MaterialAtpAbnormalType abnormalType;

    @ApiModelProperty(value = "欠料明细")
    private String shortage;

    @ApiModelProperty(value = "预计可计划日期")
    private LocalDate estimatedPlanDate;

    @ApiModelProperty(value = "调整后上线时间", notes = "edit")
    private LocalDate adjustedOnlineTime;

    /**
     * 整后完工日期，取值逻辑为“调整后上线时间"日期+1天不可编辑
     */
    @ApiModelProperty(value = "调整后完工时间")
    private LocalDate adjustedFinishTime;

    @ApiModelProperty(value = "GAP天数")
    private Integer adjustedGapDays;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    private OrderWarningHandleStatusEnum processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

    @ApiModelProperty(value = "是否发起船期变更")
    private Boolean initiateShipmentChange;
}
