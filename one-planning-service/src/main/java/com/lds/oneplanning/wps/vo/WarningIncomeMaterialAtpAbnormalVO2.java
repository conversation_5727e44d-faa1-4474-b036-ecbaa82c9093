package com.lds.oneplanning.wps.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.OrderWarningHandleStatusEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
@Data
@ApiModel(value="来料异常检验计划人员表单", description="来料异常检验计划人员表单")
@TableHeader(type = WpsOrderWarningTypeEnum.MATERIAL_INSPECTION_ABNORMAL, source = ViewSource.PC)
public class WarningIncomeMaterialAtpAbnormalVO2 extends AffectsPlan{

    private Long id;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "销售订单-行项目")
    private String xsddhxm;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "工厂")
    private String factory;

    @ApiModelProperty(value = "物料ID")
    private String productId;

    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "计划日期")
    private LocalDate planDate;

    @ApiModelProperty(value = "计划数量")
    private Integer planQuantity;

    @ApiModelProperty(value = "最终完工日期")
    private String finalCompletionDate;

    @ApiModelProperty(value = "最终验货日期")
    private String finalInspectionDate;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    @ApiModelProperty(value = "预计可完工日期")
    private LocalDate expectedCompletionDate;

    private OrderWarningHandleStatusEnum processStatus;
    @ApiModelProperty(value = "处理状态")
    private String processStatusName;

    @ApiModelProperty(value = "是否发起船期变更")
    private Boolean initiateShipmentChange;

}
