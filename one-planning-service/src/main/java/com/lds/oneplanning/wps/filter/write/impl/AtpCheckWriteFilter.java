package com.lds.oneplanning.wps.filter.write.impl;

import cn.hutool.core.date.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.entity.MaterialGroupRel;
import com.lds.oneplanning.basedata.model.MaterialGroupDTO;
import com.lds.oneplanning.basedata.service.IMaterialGroupRelService;
import com.lds.oneplanning.basedata.service.IMaterialGroupService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.esb.cache.WpsOrderCacheUtils;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.esb.model.AtpLackOffMaterialRespVO;
import com.lds.oneplanning.wps.filter.write.AbstractWpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.utils.WpsTransUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/6/3 17:14
 */
@Slf4j
@Service
public class AtpCheckWriteFilter extends AbstractWpsOrderWriteFilter {

    @Resource
    private IMaterialGroupService materialGroupService;
    @Resource
    private IMaterialGroupRelService materialGroupRelService;
    @Resource
    private IEsbDataFetchService dataFetchService;

    @Override
    public Integer filterSeq() {
        return 8;
    }

    @Override
    protected List<WpsRowData> doFilter(Long userId, Integer datasource, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag, Map<String,Object> params) {
        // atp 结果设置 暂不使用缓存
        Set<String> orderNos = dirtyList.stream().map(WpsRowData::getOrderNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 使用外部指定的工厂编码进行过滤
        List<MaterialGroupDTO> materialGroupDTOS = materialGroupService.listByFactoryCodes(Sets.newHashSet(factoryCode));
        Set<String> materialGroupCodes = materialGroupDTOS.stream().map(MaterialGroupDTO::getCode).collect(Collectors.toSet());
        List<MaterialGroupRel> materialGroupRels = materialGroupRelService.listByGroupCodes(materialGroupCodes);
        Map<String,List<MaterialGroupRel>> materialGroupMap =materialGroupRels.stream().collect(Collectors.groupingBy(MaterialGroupRel::getMaterialGroupCode));
        // 有缓存的时候从缓存中获取，缓存没有命中，则继续往下调用接口 ，实践中超过30条直接走接口查询
        if (cacheFlag) {
            List<AtpLackOffMaterialRespVO> cacheAtpList = WpsOrderCacheUtils.getSourceAtpFromCache(orderNos);
            if (CollectionUtils.isNotEmpty(cacheAtpList)) {
                Map<String,List<AtpLackOffMaterialRespVO>> atpMap = cacheAtpList.stream().collect(Collectors.groupingBy(AtpLackOffMaterialRespVO::getTOP_NO));
                dirtyList.stream().filter(wpsRowData -> atpMap.containsKey(wpsRowData.getOrderNo())).forEach(wpsRowData -> {
                    List<AtpLackOffMaterialRespVO>  atpLackOffMaterialRespVOList = atpMap.get(wpsRowData.getOrderNo());
                    this.fillMaterialMap(wpsRowData,materialGroupCodes,materialGroupMap,atpLackOffMaterialRespVOList);
                    this.fillInfoMaterialMap(wpsRowData,materialGroupCodes,materialGroupMap,atpLackOffMaterialRespVOList);
                    this.fillMaterialSet(wpsRowData,atpLackOffMaterialRespVOList);
                    this.fillInfoMaterialSet(wpsRowData,atpLackOffMaterialRespVOList);
                });
                return  dirtyList;
            }
        }
        StopWatch stopWatch = new StopWatch();
        // atp 欠料检查
        stopWatch.start("listUserApiRowData apt欠料接口查询");
        List<AtpLackOffMaterialRespVO> atpList = dataFetchService.fetchAtpCheckList(orderNos, 1, 1000000);
        stopWatch.stop();
        log.info("listUserApiRowData apt欠料接口查询 耗时={}",stopWatch.prettyPrint(TimeUnit.MILLISECONDS ));
        dirtyList.stream().forEach(wpsRowData -> {
            // atp
            this.fillMaterialMap(wpsRowData,materialGroupCodes,materialGroupMap,atpList);
            this.fillInfoMaterialMap(wpsRowData,materialGroupCodes,materialGroupMap,atpList);
            this.fillMaterialSet(wpsRowData,atpList);
            this.fillInfoMaterialSet(wpsRowData,atpList);
        });
        WpsOrderCacheUtils.setSourceAtpToCache(atpList);
        return dirtyList;
    }

    private void fillMaterialMap(WpsRowData wpsRowData, Set<String> materialGroupCodeSet, Map<String,List<MaterialGroupRel>> materialGroupMap, List<AtpLackOffMaterialRespVO> atpList ){
        if (atpList == null || atpList.isEmpty()) {
            return;
        }
        String orderNo = wpsRowData.getOrderNo();
        Map<String, String> materialMap = wpsRowData.getMaterialMap();
        Map<String, Set<String>> materialCodeMap = wpsRowData.getMaterialCodeMap();
        List<String> targetUserTypeNames = Lists.newArrayList("工单", "限制工单", "外协采购单", "限制外协采购单", "采购单", "限制采购单", "客户组采购单", "采购排程","需要生产","需要采购","计划订单","采购申请","限制计划订单","没有BOM");
        for (AtpLackOffMaterialRespVO atpLackOffMaterialRespVO : atpList){
            if (!orderNo.equals(atpLackOffMaterialRespVO.getTOP_NO())) {
                // 销售订单号过滤
                continue;
            }
            if (!targetUserTypeNames.contains(atpLackOffMaterialRespVO.getUSE_TYPE_NAME())) {
                // 单据类型过滤
                continue;
            }
            if (StringUtils.isEmpty(atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO())) {
                // 物料编号过滤
                continue;
            }
            String fullMaterialItemNo = atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO();
            String desc = Optional.ofNullable(atpLackOffMaterialRespVO.getUSE_NOTE()).orElse("") +
                    "&" + atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO() +
                    "&" + atpLackOffMaterialRespVO.getUSE_DATE();

            for (Map.Entry<String,List<MaterialGroupRel>> entry : materialGroupMap.entrySet()){
                String  materialGroupCode = entry.getKey();
                Set<String> materialCodes = entry.getValue().stream().map(MaterialGroupRel::getMaterialCode).collect(Collectors.toSet());
                long count = materialCodes.stream().filter(materialCode -> fullMaterialItemNo.startsWith(materialCode)).count();
                if (count == 0l) {
                    // 一个都没命中 则跳过
                    continue;
                }
                if (materialGroupCodeSet.contains(materialGroupCode)) {
                    // 存在 追加在后面（如不存在不会发生效果）
                    materialMap.computeIfPresent(materialGroupCode,(key, value) -> value+","+desc);
                    // 不存在，则直接添加（如已存在也不会发生效果）
                    materialMap.putIfAbsent(materialGroupCode,desc);

                    if (materialCodeMap.containsKey(materialGroupCode)) {
                        materialCodeMap.get(materialGroupCode).addAll(materialCodes);
                    }else{
                        materialCodeMap.put(materialGroupCode,Sets.newHashSet(materialCodes));
                    }
                }
            }
        }
        if (MapUtils.isEmpty(wpsRowData.getMaterialMap())) {
            wpsRowData.setAtpStatus(1);
        }
    }

    private void fillInfoMaterialMap(WpsRowData wpsRowData, Set<String> materialGroupCodeSet, Map<String,List<MaterialGroupRel>> materialGroupMap, List<AtpLackOffMaterialRespVO> atpList ){
        if (atpList == null || atpList.isEmpty()) {
            // 未入库 状态设置为未知
            wpsRowData.setAtpStatus(4);
            return;
        }
        String orderNo = wpsRowData.getOrderNo();
        Map<String, String> infoMaterialMap = wpsRowData.getInfoMaterialMap();
        Map<String, Set<String>> infoMaterialCodeMap = wpsRowData.getInfoMaterialCodeMap();
        LocalDate onLineDate = wpsRowData.getOnLineDate();

        List<String> targetUserTypeNames = Lists.newArrayList("工单", "限制工单", "外协采购单", "限制外协采购单", "采购单", "限制采购单", "客户组采购单", "采购排程","需要生产","需要采购","计划订单","采购申请","限制计划订单","没有BOM");
        for (AtpLackOffMaterialRespVO atpLackOffMaterialRespVO : atpList){
            if (!orderNo.equals(atpLackOffMaterialRespVO.getTOP_NO())) {
                // 销售订单号过滤
                continue;
            }
            if (!targetUserTypeNames.contains(atpLackOffMaterialRespVO.getUSE_TYPE_NAME())) {
                // 单据类型过滤
                continue;
            }
            if (StringUtils.isEmpty(atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO())) {
                // 物料编号过滤
                continue;
            }
            Date planGetDate = WpsTransUtils.getDateValue(atpLackOffMaterialRespVO.getPARENT_QITAO_DATE());
            if (planGetDate != null && onLineDate != null  && LocalDateTimeUtil.dateToLocalDate(planGetDate).isBefore(onLineDate)) {
                // 可得日期在开始排产日期之前，也算齐套
                continue;
            }

            String fullMaterialItemNo = atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO();
            String desc = Optional.ofNullable(atpLackOffMaterialRespVO.getUSE_NOTE()).orElse("") +
                    "&" + atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO() +
                    "&" + atpLackOffMaterialRespVO.getUSE_DATE();

            for (Map.Entry<String,List<MaterialGroupRel>> entry : materialGroupMap.entrySet()){
                String  materialGroupCode = entry.getKey();
                Set<String> materialCodes = entry.getValue().stream().map(MaterialGroupRel::getMaterialCode).collect(Collectors.toSet());
                long count = materialCodes.stream().filter(materialCode -> fullMaterialItemNo.startsWith(materialCode)).count();
                if (count == 0l) {
                    // 一个都没命中 则跳过
                    continue;
                }
                if (materialGroupCodeSet.contains(materialGroupCode)) {
                    // 存在 追加在后面（如不存在不会发生效果）
                    infoMaterialMap.computeIfPresent(materialGroupCode,(key, value) -> value+","+desc);
                    // 不存在，则直接添加（如已存在也不会发生效果）
                    infoMaterialMap.putIfAbsent(materialGroupCode,desc);

                    if (infoMaterialCodeMap.containsKey(materialGroupCode)) {
                        infoMaterialCodeMap.get(materialGroupCode).addAll(materialCodes);
                    }else{
                        infoMaterialCodeMap.put(materialGroupCode,Sets.newHashSet(materialCodes));
                    }
                }
            }
        }

        log.info("atp物料齐套 orderno ={},preOnlineTime={},infoMaterialMap={}",wpsRowData.getOrderNo(),onLineDate,infoMaterialMap);
        if (MapUtils.isEmpty(wpsRowData.getMaterialMap())) {
            wpsRowData.setAtpStatus(1);
        }else{
            if (MapUtils.isEmpty(infoMaterialMap)) {
                wpsRowData.setAtpStatus(2);
            }else{
                wpsRowData.setAtpStatus(3);
            }
        }
    }

    private void fillMaterialSet(WpsRowData wpsRowData,List<AtpLackOffMaterialRespVO> atpList ){
        if (atpList == null || atpList.isEmpty()) {
            // 未入库 状态设置为未知
            return;
        }
        String orderNo = wpsRowData.getOrderNo();
        Set<String> lackOfMaterialIds = wpsRowData.getLackOfMaterialIds();
        List<String> targetUserTypeNames = Lists.newArrayList("工单", "限制工单", "外协采购单", "限制外协采购单", "采购单", "限制采购单", "客户组采购单", "采购排程","需要生产","需要采购","计划订单","采购申请","限制计划订单","没有BOM");
        for (AtpLackOffMaterialRespVO atpLackOffMaterialRespVO : atpList){
            if (!orderNo.equals(atpLackOffMaterialRespVO.getTOP_NO())) {
                // 销售订单号过滤
                continue;
            }
            if (!targetUserTypeNames.contains(atpLackOffMaterialRespVO.getUSE_TYPE_NAME())) {
                // 单据类型过滤
                continue;
            }
            if (StringUtils.isEmpty(atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO())) {
                // 物料编号过滤
                continue;
            }
            String fullMaterialItemNo = atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO();
            lackOfMaterialIds.add(fullMaterialItemNo);
        }
    }

    private void fillInfoMaterialSet(WpsRowData wpsRowData,List<AtpLackOffMaterialRespVO> atpList ){
        if (atpList == null || atpList.isEmpty()) {
            // 未入库 状态设置为未知
            return;
        }
        String orderNo = wpsRowData.getOrderNo();
        Set<String> lackOfInfoMaterialIds = wpsRowData.getLackOfInfoMaterialIds();
        LocalDate onLineDate = wpsRowData.getOnLineDate();

        List<String> targetUserTypeNames = Lists.newArrayList("工单", "限制工单", "外协采购单", "限制外协采购单", "采购单", "限制采购单", "客户组采购单", "采购排程","需要生产","需要采购","计划订单","采购申请","限制计划订单","没有BOM");
        for (AtpLackOffMaterialRespVO atpLackOffMaterialRespVO : atpList){
            if (!orderNo.equals(atpLackOffMaterialRespVO.getTOP_NO())) {
                // 销售订单号过滤
                continue;
            }
            if (!targetUserTypeNames.contains(atpLackOffMaterialRespVO.getUSE_TYPE_NAME())) {
                // 单据类型过滤
                continue;
            }
            if (StringUtils.isEmpty(atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO())) {
                // 物料编号过滤
                continue;
            }
            Date planGetDate = WpsTransUtils.getDateValue(atpLackOffMaterialRespVO.getPARENT_QITAO_DATE());
            if (planGetDate != null && onLineDate != null  && LocalDateTimeUtil.dateToLocalDate(planGetDate).isBefore(onLineDate)) {
                // 可得日期在真正排产日期之前，也算齐套
                continue;
            }
            String fullMaterialItemNo = atpLackOffMaterialRespVO.getMATERIAL_ITEM_NO();
            lackOfInfoMaterialIds.add(fullMaterialItemNo);
        }
    }

}
