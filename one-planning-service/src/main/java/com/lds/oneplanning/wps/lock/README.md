# 分布式锁注解使用指南

本模块提供了基于Redisson实现的分布式锁注解功能，可以方便地在方法上添加分布式锁，防止并发操作导致的数据不一致问题。

## 功能特点

- 基于Redisson实现，支持Redis集群
- 支持SpEL表达式动态生成锁的key
- 支持自定义锁的等待时间和持有时间
- 自动处理锁的获取和释放，包括异常情况

## 使用方法

### 1. 基本用法

在需要加锁的方法上添加`@DistributedLock`注解，并指定锁的key：

```java
@DistributedLock(key = "myLock")
public void doSomething() {
    // 业务逻辑
}
```

### 2. 使用SpEL表达式引用方法参数

可以使用SpEL表达式引用方法参数，动态生成锁的key：

```java
@DistributedLock(key = "user:#{#userId}")
public void processUser(String userId) {
    // 业务逻辑
}
```

### 3. 使用SpEL表达式引用对象属性

可以使用SpEL表达式引用对象的属性，动态生成锁的key：

```java
@DistributedLock(key = "order:#{#order.id}")
public void processOrder(Order order) {
    // 业务逻辑
}
```

### 4. 自定义锁的等待时间和持有时间

可以自定义锁的等待时间和持有时间：

```java
@DistributedLock(key = "myLock", waitTime = 5, leaseTime = 60)
public void doSomething() {
    // 业务逻辑
}
```

默认的等待时间是10秒，持有时间是30秒，时间单位是秒。

### 5. 自定义锁的前缀

可以自定义锁的前缀，默认是`lock:`：

```java
@DistributedLock(key = "myLock", prefix = "customPrefix:")
public void doSomething() {
    // 业务逻辑
}
```

## 注意事项

1. 分布式锁只能保证在分布式环境中的互斥性，不能保证方法的幂等性
2. 锁的持有时间应该设置为方法可能的最长执行时间，以防止方法执行时间过长导致锁被自动释放
3. 锁的key应该具有唯一性，避免不相关的操作使用相同的锁
4. 在使用SpEL表达式时，确保表达式能够正确解析，否则可能导致锁的key不符合预期

## 示例代码

完整的示例代码可以参考`com.ldx.qms.lock.example.DistributedLockExample`类。
