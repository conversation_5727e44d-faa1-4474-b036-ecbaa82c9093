package com.lds.oneplanning.wps.lock;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁注解
 * <p>
 * 该注解用于在方法上标注需要加锁的操作，使用Redisson实现分布式锁。
 * 支持使用SpEL表达式动态生成锁的key。
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedLock {

    /**
     * 锁的key
     * <p>
     * 可以是固定字符串，也可以是SpEL表达式。
     * 如果是SpEL表达式，可以使用 #{} 引用方法参数。
     * </p>
     * <p>
     * 示例：
     * <ul>
     *     <li>固定key: "myLock"</li>
     *     <li>使用方法参数: "user:#{#userId}"</li>
     *     <li>使用对象属性: "order:#{#order.id}"</li>
     * </ul>
     * </p>
     *
     * @return 锁的key
     */
    String key();

    /**
     * 获取锁等待时间，默认10秒
     *
     * @return 获取锁等待时间
     */
    long waitTime() default 10L;

    /**
     * 锁持有时间，超过这个时间锁会自动释放，默认30秒
     *
     * @return 锁持有时间
     */
    long leaseTime() default 30L;

    /**
     * 时间单位，默认为秒
     *
     * @return 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 锁前缀，默认为 "lock:"
     * <p>
     * 最终的锁key会是 prefix + key
     * </p>
     *
     * @return 锁前缀
     */
    String prefix() default "lock:";
}
