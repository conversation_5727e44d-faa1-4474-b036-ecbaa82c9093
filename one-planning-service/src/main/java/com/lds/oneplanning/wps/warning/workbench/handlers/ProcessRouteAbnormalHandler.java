package com.lds.oneplanning.wps.warning.workbench.handlers;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.lds.oneplanning.basedata.service.IProductGroupRelService;
import com.lds.oneplanning.common.service.IBasicUserService;
import com.lds.oneplanning.esb.datafetch.model.EsbProcessRouteAbnormalSapData;
import com.lds.oneplanning.esb.datafetch.service.impl.EsbDataFetchService;
import com.lds.oneplanning.wps.entity.*;
import com.lds.oneplanning.wps.enums.*;
import com.lds.oneplanning.wps.helper.WpsRowDataMergeHelper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWarningProcessRouteAbnormalService;
import com.lds.oneplanning.wps.service.IWpsOrderPlanWarningService;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import com.lds.oneplanning.wps.utils.WpsDateUtil;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 冻结、解冻处理器
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ProcessRouteAbnormalHandler implements IWpsWorkbenchWarningHandler {
    @Value("${wps.mock.switch:0}")
    private Integer isDebug = 0;
    private final EsbDataFetchService esbDataFetchService;
    private final IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    private final WarningTodoListService todoListService;
    private final IWarningProcessRouteAbnormalService warningProcessRouteAbnormalService;
    private final IBasicUserService basicUserService;

    @Override
    public List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext ctx, Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap) {
        if (ctx == null || CollectionUtils.isEmpty(ctx.getOrders())) {
            return Collections.emptyList();
        }
        List<WpsRowData> orders = ctx.getOrders();
        //去掉没排产的数据
        orders.removeIf(row -> row.getOnlineTime() == null);
        log.info("移除未排产数据后：异常数量:{}", orders.size());
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }
        //合并相同订单的数据
        orders = WpsRowDataMergeHelper.mergeSameOrder(orders);
        // 取类型为计划订单的数据
        List<WpsRowData> wpsRowDataList = orders.stream().filter(row -> "计划订单".equals(row.getOrderType())).collect(Collectors.toList());
        //分析异常数据
        return analyzeAbnormalData(ctx, wpsRowDataList);
    }

    /**
     * 分析异常数据并生成警告信息
     *
     * @param ctx                  工作台警告上下文
     * @param orders  排产订单列表
     * @return 包含警告信息的列表，如果未分析出异常则返回空列表
     */
    private List<WpsOrderPlanWarning> analyzeAbnormalData(WpsWorkbenchWarningContext ctx,
                                                          List<WpsRowData> orders) {

        log.info("开始分析异常数据");
        List<WarningProcessRouteAbnormal> warningList = new ArrayList<>();
        // 分析在库异常数据
        List<WarningProcessRouteAbnormal> finallist = analyzeInStoreExceptions(orders, warningList);
        log.info("完成在库异常数据分析，warningList异常数据数量: {}", warningList.size());
        List<WarningProcessRouteAbnormal> warningListN = finallist.stream().filter(vo -> "N".equals(vo.getSfwhgylx())).collect(Collectors.toList());
        //消警
        log.info("开始消警");
        eliminateAlarms(warningListN);
        //保存或更新
        log.info("开始保存异常数据");
        saveOrUpdate(finallist);
        log.info("完成保存异常数据，警告列表大小: {}", finallist.size());

        log.info("开始创建待办");
        createTodoList(warningListN);
        return convertData(ctx, orders, warningListN);
    }

    private List<WpsOrderPlanWarning> convertData(WpsWorkbenchWarningContext ctx, List<WpsRowData> abnormalList, List<WarningProcessRouteAbnormal> warningList) {
        Map<String, LightColor> lightColorMap = warningList.stream()
                .collect(Collectors.toMap(WarningProcessRouteAbnormal::getPlannedOrder, l-> l.getLightColor(), (o, o2) -> o));
        return buildWarning(ctx, abnormalList, lightColorMap);
    }

    /**
     * 创建待办事项列表
     *
     * @param warningList 包含警告信息的列表
     */
    private void createTodoList(List<WarningProcessRouteAbnormal> warningList) {
        // 将灯色不为黄灯和红灯的记录的推送人员这是为空，不推送
        warningList.forEach(e -> {
            if (e.getLightColor() != LightColor.YELLOW && e.getLightColor() != LightColor.RED) {
                e.setNpiPerson("");
            }
        });
        List<WarningTodoList> todoList = new ArrayList<>();
        Map<String, Map<String, String>> existWerk = new HashMap<>();
        for (WarningProcessRouteAbnormal e : warningList){
            if (StrUtil.isEmpty(e.getNpiPersonGh())){
                todoList.add(new WarningTodoList(getWarningType(), e.getFactory(),  e.getId(), ""));
            } else {
                String[] ghs = e.getNpiPersonGh().split(",");
                if (ghs.length > 0) {
                    Map<String, String> stringStringMap = new HashMap<>();
                    if (existWerk.containsKey(e.getFactory())){
                        stringStringMap = existWerk.get(e.getFactory());
                    } else {
                        stringStringMap = basicUserService.batchGetLoginNamesByJobNos(Arrays.asList(ghs));
                        existWerk.put(e.getFactory(), stringStringMap);
                    }
                    //循环stringStringMap
                    for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
                        String loginName = entry.getValue();
                        WarningTodoList todo = new WarningTodoList(getWarningType(), e.getFactory(),  e.getId(), loginName);
                        todoList.add(todo);
                    }
                }
            }
        }
        //临时处理，过滤掉bizId为空的数据
        todoList.removeIf(e -> e.getBizId() == null);
        todoListService.saveData(getWarningType(), todoList);
    }
    /**
     * 获取待办处理人
     *
     * @param warningList 告警列表
     * @return 一个以 Long 为键、String 为值的 HashMap，键为待办事项的ID，值为待办处理人的员工编号
     */
    private Map<String, String> getAssignee(List<WarningProcessRouteAbnormal> warningList) {
        return null;
    }

    private void saveOrUpdate(List<WarningProcessRouteAbnormal> warningList) {

        // 1. 提取订单号列表
        List<String> poOrderNoList = warningList.stream()
                .map(WarningProcessRouteAbnormal::getPlannedOrder)
                .distinct()
                .collect(Collectors.toList());
        List<String> poMaterialIdList = warningList.stream()
                .map(WarningProcessRouteAbnormal::getMaterialId)
                .distinct()
                .collect(Collectors.toList());
        List<WarningProcessRouteAbnormal> poExistList = warningProcessRouteAbnormalService.lambdaQuery()
                .in(WarningProcessRouteAbnormal::getPlannedOrder, poOrderNoList)
                .in(WarningProcessRouteAbnormal::getMaterialId, poMaterialIdList)
                .list();
        // 3. 构建已存在记录的映射表（订单号 -> 物料ID -> 实体）
        Table<String, String, WarningProcessRouteAbnormal> existTable = HashBasedTable.create();
        poExistList.forEach(warning -> existTable.put(warning.getPlannedOrder(), warning.getMaterialId(), warning));

        // 4. 初始化更新和插入列表
        List<WarningProcessRouteAbnormal> updateList = new ArrayList<>();
        List<WarningProcessRouteAbnormal> insertList = new ArrayList<>();

        // 5. 遍历warningList，区分更新和插入
        CopyOptions opt = CopyOptions.create().ignoreNullValue();
        warningList.forEach(warning -> {
            if (existTable.contains(warning.getPlannedOrder(), warning.getMaterialId())) {
                // 5.1 如果记录已存在，执行更新操作
                WarningProcessRouteAbnormal entity = existTable.get(warning.getPlannedOrder(), warning.getMaterialId());
                BeanUtil.copyProperties(warning, entity, opt);
                // 反向更新到warning中，以便外部能获取到id
                BeanUtil.copyProperties(entity, warning);
                warning.setUpdateTime(new Date());
                updateList.add(warning);
            } else {
                // 5.2 如果记录不存在，添加到插入列表
                warning.setUpdateTime(new Date());
                warning.setCreateTime(new Date());
                insertList.add(warning);
            }
        });
        // 6. 批量更新和插入
        if (CollectionUtils.isNotEmpty(updateList)) {
            warningProcessRouteAbnormalService.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            warningProcessRouteAbnormalService.saveBatch(insertList);
        }
    }

    /**
     * 消除告警
     *
     * @param warningList 告警信息列表
     */
    private void eliminateAlarms(List<WarningProcessRouteAbnormal> warningList) {
        //获取所有待办的数据
        List<WarningProcessRouteAbnormal> existData = warningProcessRouteAbnormalService.queryUnHandleData();
        log.info("获取未处理的警告数据计数: {}", existData.size());

        //将warningList组装成Table
        Table<String, String, WarningProcessRouteAbnormal> warningTable = HashBasedTable.create();
        warningList.forEach(warning -> warningTable.put(warning.getPlannedOrder(), warning.getMaterialId(), warning));

        //如果existData不存在与warningList中的数据，则删除，根据订单号和物料id来判断
        List<Long> toBeRemoveIdsList = existData.stream()
                .filter(e -> !warningTable.contains(e.getPlannedOrder(), e.getMaterialId()))
                .map(WarningProcessRouteAbnormal::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(toBeRemoveIdsList)) {
            log.info("消警列表：{}", toBeRemoveIdsList);
            todoListService.lambdaUpdate()
                    .set(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.CLOSED)
                    .eq(WarningTodoList::getProcessStatus, OrderWarningHandleStatusEnum.UN_HANDLE)
                    .in(WarningTodoList::getBizId, toBeRemoveIdsList)
                    .eq(WarningTodoList::getWarningType, this.getWarningType())
                    .update();
            //主表消警
            wpsOrderPlanWarningService.eliminateAlarms(getWarningType());
        }
    }
    /**
     * 分析并处理在库异常数据
     *
     */
    private  List<WarningProcessRouteAbnormal> analyzeInStoreExceptions(List<WpsRowData> orders,  List<WarningProcessRouteAbnormal> warningList) {
        // 查出已经查询过的为Y的数据
        QueryWrapper<WarningProcessRouteAbnormal> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WarningProcessRouteAbnormal::getSfwhgylx, "Y");
        List<WarningProcessRouteAbnormal> existData = warningProcessRouteAbnormalService.getBaseMapper().selectList(queryWrapper);
        // WPS当前的所有销售订单，1.识别物料ID在SAP的跨工厂物料状态为非13的数据
        List<EsbProcessRouteAbnormalSapData> sapData = new ArrayList<>();
        orders.forEach(order -> {
            WarningProcessRouteAbnormal warning = new WarningProcessRouteAbnormal();
            warning.setCustomer(order.getCustomerCode());
            warning.setProductionLine(order.getLineCode());
            warning.setLineName(order.getLineName());
            warning.setPlannedOrder(order.getOrderNo());
            warning.setFactory(order.getFactory());
            warning.setMaterialId(order.getCommodityId());
            warning.setMaterialDesc(order.getCommodityDesc());
            warning.setPlannedOnlineTime(WpsDateUtil.getStartScheduleDate(order.getScheduleDataMap()));
            //计划上线时间减去当前时间
            int days = LocalDate.now().until(warning.getPlannedOnlineTime()).getDays();
            warning.setDays(days);
            warning.setLightColor(calculateLightColor(warning.getPlannedOnlineTime()));
            warningList.add(warning);
        });
        // 将 existData 转换为 Map<Factory, Set<MaterialId>>，以便快速查找
        Map<String, Set<String>> existDataMap = existData.stream()
                .collect(Collectors.groupingBy(
                        WarningProcessRouteAbnormal::getFactory,
                        Collectors.mapping(WarningProcessRouteAbnormal::getMaterialId, Collectors.toSet())
                ));

        // 过滤 warningList，排除在 existData 中已存在的 (factory, materialId) 组合
        List<WarningProcessRouteAbnormal> filteredWarningList = warningList.stream()
                .filter(warning -> {
                    String factory = warning.getFactory();
                    String materialId = warning.getMaterialId();
                    // 检查是否存在该 factory 以及对应的 materialId 是否不在 existData 中
                    return !existDataMap.containsKey(factory) || !existDataMap.get(factory).contains(materialId);
                })
                .collect(Collectors.toList());
        filteredWarningList.forEach(warning -> {
            EsbProcessRouteAbnormalSapData params = new EsbProcessRouteAbnormalSapData();
            params.setMATNR(warning.getMaterialId());
            params.setWERKS(warning.getFactory());
            sapData.add(params);
        });
        // 根据物料ID和工厂去sap查询是否有工艺路线
        List<EsbProcessRouteAbnormalSapData> dataList = esbDataFetchService.processRouteAbnormalFromSap(sapData);
        // 根据物料ID和工厂去匹配warningList
        for (WarningProcessRouteAbnormal warning : filteredWarningList){
            for (EsbProcessRouteAbnormalSapData data : dataList) {
                if (data.getMATNR().equals(warning.getMaterialId()) && data.getWERKS().equals(warning.getFactory())) {
                    warning.setSfwhgylx(data.getZSFYGY());
                    break;
                }
            }
        }
        return filteredWarningList;
    }
    @Override
    public WpsOrderWarningCategoryEnum getWarningCategory() {
        return WpsOrderWarningCategoryEnum.DEFAULT;
    }

    @Override
    public WpsOrderWarningTypeEnum getWarningType() {
        return WpsOrderWarningTypeEnum.PROCESS_ROUTE_ABNORMAL;
    }

    public static LightColor calculateLightColor(LocalDate planDate) {
        if (planDate == null) {
            return LightColor.BLANK;
        }
        LocalDate now = LocalDate.now();
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(now ,planDate);
        if (daysBetween <= 7 && daysBetween > 0) {
            return LightColor.RED;
        } else if (daysBetween > 7 && daysBetween <= 14) {
            return LightColor.YELLOW;
        } else {
            return LightColor.BLANK;
        }
    }
}
