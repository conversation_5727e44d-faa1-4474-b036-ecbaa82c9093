package com.lds.oneplanning.wps.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.model.MaterialGroupDTO;
import com.lds.oneplanning.basedata.service.IMaterialGroupService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.esb.datafetch.model.EsbUpdatePlanOrderTime;
import com.lds.oneplanning.esb.datafetch.model.EsbUpdateProductionOrderTime;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.mps.constans.MpsExcelConstant;
import com.lds.oneplanning.mps.enums.ExcelDataTypeEnum;
import com.lds.oneplanning.mps.enums.ExcelHeadFiledEnums_WPS1;
import com.lds.oneplanning.mps.enums.ExcelHeadFiledEnums_WPS3;
import com.lds.oneplanning.mps.enums.ExcelHeadFiledEnums_WPS5;
import com.lds.oneplanning.mps.model.CellModel;
import com.lds.oneplanning.mps.model.RowSaveData;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.entity.WpsPublishedOrder;
import com.lds.oneplanning.wps.enums.WpsPlanTypeEnum;
import com.lds.oneplanning.wps.model.WpsData;
import com.lds.oneplanning.wps.model.WpsExcelHeader;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.req.ReleaseWpsRequest;
import com.lds.oneplanning.wps.schedule.enums.WpsPublishTargetEnum;
import com.lds.oneplanning.wps.service.IWpsPublishedOrderService;
import com.lds.oneplanning.wps.service.WpsExcelService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessContext;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessEnum;
import com.lds.oneplanning.wps.utils.WpsDateUtil;
import com.lds.oneplanning.wps.warning.WpsPlanWarningManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/25 13:40
 */
@Slf4j
@Service
public class WpsExcelServiceImpl implements WpsExcelService {

    @Resource
    private WpsRowDataFacadeService wpsRowDataFacadeService;
    @Resource
    private IMaterialGroupService materialGroupService;
    @Autowired
    private IWpsPublishedOrderService wpsPublishedOrderService;

    @Autowired
    private IEsbDataFetchService esbDataFetchService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private WpsOrderProcessContext wpsOrderProcessContext;

    @Value("${esb.wpsPublishToDpsEnable:false}")
    private boolean wpsPublishToDpsEnable;

    @Autowired
    private WpsPlanWarningManager wpsPlanWarningManager;

    private static final String LOCK_PREFIX = "lock:wpsExcelService:getData:";
    private static final String CACHE_PREFIX = "cache:wpsExcelService:getData:";
    private static final int LOCK_EXPIRE_TIME = 30; // 锁的过期时间，单位：秒
    private static final int CACHE_EXPIRE_TIME = 60; // 缓存结果的过期时间，单位：秒
    private static final int WAIT_TIME = 100; // 等待锁的时间，单位：毫秒

    @Override
    public WpsData getData(Long userId,Integer datasource,  Date startTime, Date endTime,String factoryCode,Boolean cacheFlag,Map<String,Object> params) {
        WpsData wpsData = new WpsData();
        wpsData.setHeader(this.buildHeader(userId,new Date(),factoryCode));
        wpsData.setBody(this.getBody(userId,datasource, startTime, endTime,factoryCode,cacheFlag,params));
        return wpsData;
    }

    @Override
    public List<CellModel> getHeader(Long userId, Date startTime, Date endTime,String factoryCode) {
        return this.buildHeader(userId, new Date(),factoryCode);
    }

    @Override
    public List<WpsRowData> getBody(Long userId,Integer datasource, Date startTime, Date endTime,String factoryCode,Boolean cacheFlag,Map<String,Object> params) {
        WpsOrderProcessEnum processEnum = WpsConstants.DATA_SOURCE_STORAGE.equals(datasource) ? WpsOrderProcessEnum.READ_STORAGE : WpsOrderProcessEnum.AUTO_SCHEDULE;
        List<WpsRowData> wpsRowData = wpsOrderProcessContext
                .process(processEnum, userId, LocalDateTimeUtil.dateToLocalDate(startTime), LocalDateTimeUtil.dateToLocalDate(endTime), factoryCode, cacheFlag, params);
        wpsRowData= wpsRowDataFacadeService.sortWpsRowData(wpsRowData,datasource);
        return wpsRowData;
    }

    @Override
    public void saveData(String factoryCode, Long userId, List<RowSaveData> mpsRowDatas, Date date) {
        wpsRowDataFacadeService.saveRowData(mpsRowDatas, date, userId);
        // 发送消息
        wpsPlanWarningManager.notify(factoryCode, userId, WpsPlanTypeEnum.WHOLE_MACHINE);
    }

    @Override
    public void releaseWps(String factoryCode, Long userId, ReleaseWpsRequest releaseWpsRequest) {
        if (null == releaseWpsRequest || CollectionUtils.isEmpty(releaseWpsRequest.getWpsRowDatas())) {
            return;
        }
        LocalDate releaseStartDate = releaseWpsRequest.getReleaseStartDate();
        LocalDate releaseEndDate = releaseWpsRequest.getReleaseEndDate();
        List<RowSaveData> wpsRowDatas = releaseWpsRequest.getWpsRowDatas();
        // 先保存数据
        this.saveData(factoryCode, userId, wpsRowDatas, new Date());
        // 正常发布的订单只有一个线体
        List<WpsPublishedOrder> wpsPublishedOrderList = Lists.newArrayList();
        for (RowSaveData rowSaveData : wpsRowDatas) {
            String lineCode = rowSaveData.getLineCode();
            Map<LocalDate, Number> scheduleDataMap = rowSaveData.getScheduleDataMap();
            if (MapUtils.isEmpty(scheduleDataMap)) {
                continue;
            }
            LocalDate firstDate = scheduleDataMap.entrySet().stream().filter(entry ->
                            (entry.getKey().isAfter(releaseStartDate) || entry.getKey().isEqual(releaseStartDate))
                                    && null != entry.getValue())
                    .map(Map.Entry::getKey).min(LocalDate::compareTo).orElse(null);
            if (null == firstDate) {
                continue;
            }
            LocalDate lastDate = scheduleDataMap.entrySet().stream().filter(entry ->
                            (entry.getKey().isBefore(releaseEndDate) || entry.getKey().isEqual(releaseEndDate))
                                    && null != entry.getValue())
                    .map(Map.Entry::getKey).max(LocalDate::compareTo).orElse(null);
            if (null == lastDate) {
                continue;
            }
            WpsPublishedOrder wpsPublishedOrder = new WpsPublishedOrder();
            wpsPublishedOrder.setBizId(rowSaveData.getOrderNo());
            wpsPublishedOrder.setLineCode(lineCode);
            wpsPublishedOrder.setPublishTarget(WpsPublishTargetEnum.dps.name());
            wpsPublishedOrder.setStartDate(firstDate);
            wpsPublishedOrder.setEndDate(lastDate);
            wpsPublishedOrder.setCreateBy(userId);
            wpsPublishedOrder.setUpdateBy(userId);
            wpsPublishedOrderList.add(wpsPublishedOrder);
        }
        if (CollectionUtils.isNotEmpty(wpsPublishedOrderList)) {
            wpsPublishedOrderService.saveOrUpdateBatch(wpsPublishedOrderList);
        }
        // 推送到DPS TODO from黄灵聪要求生产环境先做注释
        //publishToDps(wpsRowDatas, wpsPublishedOrderList);
        // 发送消息
        wpsPlanWarningManager.notify(factoryCode, userId, WpsPlanTypeEnum.WHOLE_MACHINE);
    }

    @Override
    public List<WpsExcelHeader> getPendingHeader() {
        List<WpsExcelHeader> headerList = Lists.newLinkedList();
        for (ExcelHeadFiledEnums_WPS1 part1 : ExcelHeadFiledEnums_WPS1.values()) {
            headerList.add(WpsExcelHeader.builder().code(part1.getCode()).name(part1.getDesc()).build());
        }
        headerList.add(WpsExcelHeader.builder().code("materialMap").name("ATP欠料").build());
        for (ExcelHeadFiledEnums_WPS3 part2 : ExcelHeadFiledEnums_WPS3.values()) {
            headerList.add(WpsExcelHeader.builder().code(part2.getCode()).name(part2.getDesc()).build());
        }
        headerList.add(WpsExcelHeader.builder().code("scheduleDataMap").name("周计划").build());
        for (ExcelHeadFiledEnums_WPS5 part3 : ExcelHeadFiledEnums_WPS5.values()) {
            headerList.add(WpsExcelHeader.builder().code(part3.getCode()).name(part3.getDesc()).build());
        }
        return headerList;
    }

    @Override
    public WpsData getDataWithLock(Long userId, Integer datasource, Date startTime, Date endTime, String factoryCode,Boolean cacheFlag,Map<String,Object> params) {
        String lockKey = LOCK_PREFIX + userId + ":" + factoryCode;
        String cacheKey = CACHE_PREFIX + userId + ":" + factoryCode;
        String lockValue = String.valueOf(System.currentTimeMillis());
        try {
            // 尝试获取锁
            boolean locked = Boolean.TRUE.equals(
                    stringRedisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, LOCK_EXPIRE_TIME, TimeUnit.SECONDS));
            if (!locked) {
                String cachedResult = stringRedisTemplate.opsForValue().get(cacheKey);
                if (StringUtils.isNotEmpty(cachedResult)) {
                    return JSON.parseObject(cachedResult, WpsData.class);
                }
                Thread.sleep(WAIT_TIME);
                return getData(userId, datasource, startTime, endTime, factoryCode,cacheFlag,params);
            }
            // 执行业务逻辑
            WpsData wpsData = getData(userId, datasource, startTime, endTime, factoryCode,cacheFlag,params);
            stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(wpsData), CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            return wpsData;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取分布式锁时被中断", e);
        } catch (Exception e){
            log.error("排产异常",e);
            throw new RuntimeException("排产异常", e);
        }finally {
            // 释放锁
            if (lockValue.equals(stringRedisTemplate.opsForValue().get(lockKey))) {
                stringRedisTemplate.delete(lockKey);
            }
        }
    }

    private void publishToDps(List<RowSaveData> wpsRowDatas, List<WpsPublishedOrder> wpsPublishedOrderList) {
        if (!wpsPublishToDpsEnable) {
            return;
        }
        if (CollectionUtils.isEmpty(wpsRowDatas) || CollectionUtils.isEmpty(wpsPublishedOrderList)) {
            return;
        }
        Map<String, String> orderTypeMap = wpsRowDatas.stream().collect(
                Collectors.toMap(RowSaveData::getOrderNo, RowSaveData::getOrderType, (s, s2) -> s2));
        List<EsbUpdatePlanOrderTime> esbUpdatePlanOrderTimes = Lists.newArrayList();
        List<EsbUpdateProductionOrderTime> esbUpdateProductionOrderTimes = Lists.newArrayList();
        for (WpsPublishedOrder wpsPublishedOrder : wpsPublishedOrderList) {
            String orderType = orderTypeMap.get(wpsPublishedOrder.getBizId());
            if (null == orderType) {
                continue;
            }
            if (orderType.equals("计划订单")) {
                EsbUpdatePlanOrderTime esbUpdatePlanOrderTime = new EsbUpdatePlanOrderTime();
                esbUpdatePlanOrderTime.setOrderNo(wpsPublishedOrder.getBizId());
                esbUpdatePlanOrderTime.setStartDate(wpsPublishedOrder.getStartDate());
                esbUpdatePlanOrderTime.setEndDate(wpsPublishedOrder.getEndDate());
                esbUpdatePlanOrderTimes.add(esbUpdatePlanOrderTime);
            } else if (orderType.equals("生产订单")) {
                EsbUpdateProductionOrderTime esbUpdateProductionOrderTime = new EsbUpdateProductionOrderTime();
                esbUpdateProductionOrderTime.setOrderNo(wpsPublishedOrder.getBizId());
                esbUpdateProductionOrderTime.setStartDate(wpsPublishedOrder.getStartDate());
                esbUpdateProductionOrderTime.setFinishedDate(wpsPublishedOrder.getEndDate());
                esbUpdateProductionOrderTimes.add(esbUpdateProductionOrderTime);
            }
        }
        // 推送计划订单到DPS
        if (CollectionUtils.isNotEmpty(esbUpdatePlanOrderTimes)) {
            esbDataFetchService.batchUpdatePlanOrderTime(esbUpdatePlanOrderTimes);
        }
        // 推送生产订单到DPS
        if (CollectionUtils.isNotEmpty(esbUpdateProductionOrderTimes)) {
            esbDataFetchService.batchUpdateProductionOrderTime(esbUpdateProductionOrderTimes);
        }
    }

    private List<CellModel> buildHeader(Long userId, Date queryTime,String factoryCode){
        List<CellModel> header = Lists.newArrayList();
        Integer columnIndex = MpsExcelConstant.COLUMN_OFFSET;
        // 第一部分头部 两行一列
        for (ExcelHeadFiledEnums_WPS1 part1 : ExcelHeadFiledEnums_WPS1.values()){
            // 两行一列
            header.add(new CellModel(part1.getDesc(),part1.getCode(),part1.getDataType(),columnIndex,part1.isEditAble(),part1.isSaveAble()));
            columnIndex++;
        }
        // 第二部分，动态部分 ATP欠料部分 ,使用外部传参 不使用userId配置的关联工厂
        List<MaterialGroupDTO> materialGroupDTOS = materialGroupService.listByFactoryCodes(Sets.newHashSet(factoryCode));
        Map<String,String> materialMap = materialGroupDTOS.stream().distinct().
                collect(Collectors.toMap(MaterialGroupDTO::getCode, MaterialGroupDTO::getName,(s, s2) -> s2));
        String preKey = "materialMap.";
        for (Map.Entry<String,String> entry : materialMap.entrySet()){
            //两行一列
            String materialGroupCode = entry.getKey();
            String materialGroupName = entry.getValue();
            header.add(new CellModel(materialGroupName,preKey+materialGroupCode,ExcelDataTypeEnum.STRING.getCode(),columnIndex,false,false));
            columnIndex++;
        }

        // 第三部分
        for (ExcelHeadFiledEnums_WPS3 part2 : ExcelHeadFiledEnums_WPS3.values()){
            //两行一列
            header.add(new CellModel(part2.getDesc(),part2.getCode(),part2.getDataType(),columnIndex,part2.isEditAble(),part2.isSaveAble()));
            columnIndex++;
        }

        // 第四部分，周计划部分
        Integer rowStartIndex = MpsExcelConstant.HEAD_ROW_START_INDEX;
        String preWK = "WK";
        LocalDate startDate = LocalDateTimeUtil.dateToLocalDate(queryTime);
        LocalDate endDate = startDate.plusDays(89);
        List<LocalDate> dateStacks = WpsDateUtil.getDateStacks(LocalDate.now(), 89);
        int startWeek = LocalDateTimeUtil.getWeekSeqOfYear(startDate);
        int endWeek = LocalDateTimeUtil.getWeekSeqOfYear(endDate);
        String weekCode= "scheduleDataMap";
        for (int i = startWeek; i <= endWeek ; i++) {
            int finalI = i;
            List<LocalDate> targetDates =  dateStacks.stream().filter(localDate -> LocalDateTimeUtil.getWeekSeqOfYear(localDate) == finalI).collect(Collectors.toList());
            CellModel wkCell = new CellModel(preWK+i,weekCode , ExcelDataTypeEnum.NUMBER.getCode(), rowStartIndex, rowStartIndex, columnIndex , columnIndex + targetDates.size()-1,true,true);
            List<CellModel> subCells = Lists.newArrayList();
            for (LocalDate singleDate :targetDates){
                subCells.add(new CellModel(singleDate, singleDate.toString(),ExcelDataTypeEnum.NUMBER.getCode(), rowStartIndex+1, rowStartIndex+1, columnIndex , columnIndex ,true,true));
               // 注意 ！！ 真正的列增加在此操作
                columnIndex = columnIndex +1;
            }
            wkCell.setSubCells(subCells);
            header.add(wkCell);
        }

        // 第五部分
        for (ExcelHeadFiledEnums_WPS5 part3 : ExcelHeadFiledEnums_WPS5.values()){
            //两行一列
            header.add(new CellModel(part3.getDesc(),part3.getCode(),part3.getDataType(),columnIndex,part3.isEditAble(),part3.isSaveAble()));
            columnIndex++;
        }
        return header;
    }
}
