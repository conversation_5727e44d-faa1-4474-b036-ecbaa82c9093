package com.lds.oneplanning.wps.utils;

import cn.hutool.core.date.DateUtil;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;

@UtilityClass
@Slf4j
public class DateUtils {
    /**
     * 计算两个日期之间的天数差。
     * <p>
     * end - start
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return 两个日期之间的天数差。如果任一日期为null，则返回0。
     */
    public int daysBetween(LocalDate start, LocalDate end) {
        if (start == null || end == null) {
            return 0;
        }
        return (int) ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 将字符串格式的日期转换为LocalDate对象
     *
     * @param dateStr 日期字符串
     * @return 转换后的LocalDate对象，如果输入的字符串为空或null，则返回null
     */
    public LocalDate parseData(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }

        Date date = parseDate(dateStr);

        return parseData(date);
    }

    public LocalDate parseData(Date date) {
        if (date == null) {
            return null;
        }

        return LocalDateTimeUtil.dateToLocalDate(date);
    }


    /**
     * 返回两个日期中较早的一个。
     *
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 返回两个日期中较早的一个。如果任一日期为null，则返回非null的日期。如果两个日期都为null，则抛出NullPointerException。
     */
    public LocalDate min(LocalDate date1, LocalDate date2) {
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        return date1.isBefore(date2) ? date1 : date2;
    }

    /**
     * 比较两个LocalDate对象，返回较晚的日期。
     *
     * @param date1 第一个日期对象
     * @param date2 第二个日期对象
     * @return 如果date1为null，返回date2；
     * 如果date2为null，返回date1；
     * 否则返回date1和date2中较晚的日期。
     */
    public LocalDate max(LocalDate date1, LocalDate date2) {
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        return date1.isAfter(date2) ? date1 : date2;
    }

    public LocalDate offsetDays(LocalDate date, int offset) {
        //计算日期，dateStr的格式为"yyyy-MM-dd"，但是得考虑异常场景，异常情况下返回null
        if (date == null) {
            return null;
        }
        return date.plusDays(offset);
    }

    /**
     * 计算两个日期之间的天数差。
     *
     * @param start 开始日期，格式为"yyyy-MM-dd"
     * @param end   结束日期，格式为"yyyy-MM-dd"
     * @return 两个日期之间的天数差，如果发生异常则返回0
     * @throws IllegalArgumentException 如果start或end的格式不符合"yyyy-MM-dd"
     */
    public int daysBetween(String start, String end) {
        //计算天数差，start和end的格式为"yyyy-MM-dd"，但是得考虑异常场景，异常情况下返回0
        try {
            LocalDate startDate = LocalDate.parse(start);
            LocalDate endDate = LocalDate.parse(end);
            return daysBetween(startDate, endDate);
        } catch (Exception e) {
            log.error("计算天数差异常，start={}, end={}", start, end, e);
            return 0;
        }
    }

    /**
     * 判断给定的日期是否与基准日期相同。
     *
     * @param baseDate 基准日期
     * @param date     需要判断的日期
     * @return 如果传入的日期与基准日期相同，则返回true；否则返回false
     */
    public boolean isSameDay(LocalDate baseDate, Date date) {
        // 将传入的日期转换为LocalDate
        LocalDate targetDate = LocalDateTimeUtil.dateToLocalDate(date);
        // 比较两个日期是否相等
        return baseDate.equals(targetDate);
    }


    /**
     * 判断给定日期是否在指定的两个日期之间（包括边界日期）。
     *
     * @param start 起始日期（包括此日期）
     * @param end   结束日期（包括此日期）
     * @param date  待判断的日期
     * @return 如果日期在起始日期和结束日期之间，则返回true；否则返回false
     */
    public boolean isBetween(LocalDate start, LocalDate end, Date date) {
        if (start == null || end == null) {
            return false;
        }
        // 将传入的日期转换为LocalDate
        LocalDate targetDate = LocalDateTimeUtil.dateToLocalDate(date);
        return !targetDate.isBefore(start) && !targetDate.isAfter(end);
    }


    /**
     * 判断一个日期是否在另一个日期之后。
     *
     * @param date1 要比较的日期1
     * @param date2 要比较的日期2
     * @return 如果date1在date2之后，则返回true；否则返回false。
     * 如果date1或date2为null，则返回false。
     */
    public static boolean isAfter(LocalDate date1, LocalDate date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        return date1.isAfter(date2);
    }

    /**
     * 判断两个日期是否为同一天
     *
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 如果两个日期为同一天，则返回true；否则返回false
     */
    public static boolean isSameDay(LocalDate date1, LocalDate date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        return date1.equals(date2);
    }

    public static Date parseDate(String dt) {
        if (StringUtils.isEmpty(dt)) {
            return null;
        }
        //判断是不是时间戳
        if (dt.matches("\\d+")) {
            //时间戳格式化成日期
            if (dt.length() == 10) {
                //时间戳格式化成日期
                return new Date(Long.parseLong(dt) * 1000);
            }
            return new Date(Long.parseLong(dt));
        }
        return DateUtil.parseDate(dt);
    }

}
