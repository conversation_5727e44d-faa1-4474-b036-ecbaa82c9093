package com.lds.oneplanning.wps.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 工艺路线异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13 16:20
 */
@ApiModel(description = "品质履历异常")
@TableHeader(type = WpsOrderWarningTypeEnum.QUALITY_HISTORY_RISK, source = ViewSource.DEFAULT)
@Data
public class QualityHistoryRiskVO {

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划时间")
    private LocalDateTime planTime;

    @ApiModelProperty(value = "生产车间")
    private String productionWorkshop;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "工厂")
    private String factoryCode;

    @ApiModelProperty(value = "产品ID")
    private String productId;

    @ApiModelProperty(value = "产品品类")
    private String productCategory;

    @ApiModelProperty(value = "计划数量")
    private Integer planQty;

    @ApiModelProperty(value = "异常原因")
    private String exceptionReason;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "异常发生日期")
    private LocalDate exceptionDate;

    @ApiModelProperty(value = "对应对策")
    private String measure;

    @ApiModelProperty(value = "异常责任人")
    private String exceptionPerson;

    @ApiModelProperty(value = "品质负责人")
    private String qualityPerson;

    @ApiModelProperty(value = "效果验证")
    private String effectVerification;

    @ApiModelProperty(value = "系统推送")
    private Boolean systemPush;
}
