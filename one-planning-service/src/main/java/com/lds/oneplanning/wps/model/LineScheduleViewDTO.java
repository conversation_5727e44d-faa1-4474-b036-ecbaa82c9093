package com.lds.oneplanning.wps.model;

import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * @Description: 排产视图对象
 * @Author: zhuang<PERSON><PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025/5/20 11:06
 */
@ApiModel(value="线体排产视图", description="线体排产视图")
@Data
public class LineScheduleViewDTO {
    @ApiModelProperty(value = "线体UUID")
    private String lineUuid;
    @ApiModelProperty(value = "线体编码")
    private String lineCode;
    @ApiModelProperty(value = "线体名称")
    private String lineName;
    @ApiModelProperty(value = "线体负载")
    private Number lineCapacity;
    @ApiModelProperty(value = "排产视图按白夜班map：日期-白夜班-订单-数量")
    private Map<LocalDate,Map<Integer,Map<String,TimeQtyDTO>>> scheduleDayShiftMap = Maps.newLinkedHashMap() ;

    @ApiModelProperty(value = "排产视图按天分map：日期-订单-数量")
    private Map<LocalDate,Map<String,TimeQtyDTO>> scheduleDayMap = Maps.newLinkedHashMap() ;

    @ApiModelProperty(value = "排产视图按半周分map：周-上下半周-订单-数量")
    private Map<String, Map<List<LocalDate>, Map<String, TimeQtyDTO>>>  scheduleWeekMap = Maps.newLinkedHashMap() ;
    @ApiModelProperty(value = "线体描述")
    public String getLineDesc(){
        return getLineName()+" "+getLineCode();
    }

}
