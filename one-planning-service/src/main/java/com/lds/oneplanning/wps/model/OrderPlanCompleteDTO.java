package com.lds.oneplanning.wps.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * @Description: 出货计划完工日期
 * @Author: zhuang<PERSON><PERSON>in
 * @Email: zhuang<PERSON><PERSON><EMAIL>
 * @Date: 2025/3/25 11:06
 */


@Data
public class OrderPlanCompleteDTO {
    @ApiModelProperty(value = "销售订单-对应 vgbel")
    private String sellOrderNo;
    @ApiModelProperty(value = "销售订单行项目-对应 vgpos")
    private String sellRowItemNo;
    @ApiModelProperty(value = "交货单数量-对应 lfimg")
    private Number deliverQty ;
    @ApiModelProperty(value = "交货单行项目-对应 vbeln")
    private String deliverNo;
    @ApiModelProperty(value = "交货单行项目-对应 posnr")
    private String deliverRowItemNo;
    @ApiModelProperty(value = "交货单行项目-对应 zzwcrq")
    private LocalDate completeDate;



    public String getSellOrderAndRowItemNo(){
        return this.getSellOrderNo()+"-"+this.getSellRowItemNo();
    }
}
