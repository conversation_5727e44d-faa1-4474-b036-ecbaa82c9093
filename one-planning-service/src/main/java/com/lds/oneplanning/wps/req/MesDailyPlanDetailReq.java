package com.lds.oneplanning.wps.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MesDailyPlanDetail对象", description = "")
public class MesDailyPlanDetailReq implements Serializable {

    private static final long serialVersionUID = 1962314725600913214L;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "计划人员-工号")
    private String plannerEmpNo;

    @ApiModelProperty(value = "计划人员")
    private String planner;

    @ApiModelProperty(value = "计划日期")
    private Date planDate;

    @ApiModelProperty(value = "生产车间")
    private String productionWorkshop;

    @ApiModelProperty(value = "生产线体")
    private String productionLine;

    @ApiModelProperty(value = "生产线长(工号)")
    private String lineLeaderNo;

    @ApiModelProperty(value = "生产线长(名字)")
    private String lineLeaderName;

    @ApiModelProperty(value = "生产课长工号")
    private String foremanNo;

    @ApiModelProperty(value = "生产课长名称")
    private String productionForeman;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "计划数量")
    private Integer plannedQuantity;

    @ApiModelProperty(value = "实际投入数量")
    private Integer actualInputQuantity;

    @ApiModelProperty(value = "实际报工数量")
    private Integer actualReportedQuantity;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "班别")
    private String shift;

    @ApiModelProperty(value = "需求类型（0:整机，1:组件，2：部件）")
    private Integer planType;
}