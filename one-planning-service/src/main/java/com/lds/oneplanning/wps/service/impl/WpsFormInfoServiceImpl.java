package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.esb.datafetch.service.IEsbMesDataFetchService;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.entity.WpsFormInfo;
import com.lds.oneplanning.wps.mapper.WpsFormInfoMapper;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsFormInfoService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-27
 */
@Slf4j
@Service
public class WpsFormInfoServiceImpl extends ServiceImpl<WpsFormInfoMapper, WpsFormInfo> implements IWpsFormInfoService {
    @Resource
    private IEsbMesDataFetchService esbMesDataFetchService;
    @Resource
    private WpsRowDataFacadeService wpsRowDataFacadeService;

    @Override
    public WpsFormInfo getByBizId(String bizId) {
        LambdaQueryWrapper<WpsFormInfo> queryWrapper = Wrappers.<WpsFormInfo>lambdaQuery()
                .eq(WpsFormInfo::getBizId, bizId
                ).last(" limit 1");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<WpsFormInfo> listByBizIds(Collection<String> bizIds) {
        if (bizIds == null || bizIds.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<WpsFormInfo>lambdaQuery().in(WpsFormInfo::getBizId,bizIds));
    }

    @Override
    public void saveOrUpdateBatchByBizId(List<WpsFormInfo> wpsFormInfos) {
        List<String> bizIds = wpsFormInfos.stream().map(WpsFormInfo::getBizId).distinct().collect(Collectors.toList());
        List<WpsFormInfo> existList = baseMapper.selectList(Wrappers.<WpsFormInfo>lambdaQuery().in(WpsFormInfo::getBizId, bizIds));
        Map<String,Long> updateBizMap = existList.stream().collect(Collectors.toMap(WpsFormInfo::getBizId, WpsFormInfo::getId,(aLong, aLong2) -> aLong2));
        List<WpsFormInfo> insertList = Lists.newArrayList();
        List<WpsFormInfo> updateList = Lists.newArrayList();
        for (WpsFormInfo formInfo : wpsFormInfos){
            if (updateBizMap.containsKey(formInfo.getBizId())) {
                formInfo.setId(updateBizMap.get(formInfo.getBizId()));
                formInfo.setUpdateTime(new Date());
                updateList.add(formInfo);
            }else{
                insertList.add(formInfo);
            }
        }

        if (!insertList.isEmpty()) {
            this.saveBatch(insertList);
        }
        if (!updateList.isEmpty()) {
            this.updateBatchById(updateList);
        }

    }


    /**
     * 根据订单信息更新报工数量
     * @param WpsFormInfos
     */
    public void updateReportedByBizData(List<WpsFormInfo> WpsFormInfos) {
        //过滤出计划订单数据
        WpsFormInfos = WpsFormInfos.stream().filter(vo -> "生产订单".equals(vo.getOrderType())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(WpsFormInfos)){
            return;
        }
        //取出未完工的订单（需排产数量>0）
        List<String> unfinishedBizIds = Lists.newArrayList();
        for (WpsFormInfo vo : WpsFormInfos) {
            //订单数量
            Integer orderQty = vo.getOrderPcsQty();
            if(orderQty == null){
                orderQty = 0;
            }
            //报工数量
            Integer reportedQty = vo.getReportQty();
            if(reportedQty == null){
                reportedQty = 0;
            }
            if (orderQty - reportedQty > 0) {
                unfinishedBizIds.add(vo.getBizId());
            }
        }
        if(CollectionUtils.isEmpty(unfinishedBizIds)){
            log.info("没有未完工的订单");
            return;
        }
        // 获取订单报工数量
        Map<String, Integer> finishMap = esbMesDataFetchService.fetchOrderCompletList(unfinishedBizIds);
        log.info("未完工订单：{},订单报工数量：{}", unfinishedBizIds, finishMap);
        //更新订单报工数量
        List<WpsFormInfo> updateList = Lists.newArrayList();
        for (WpsFormInfo vo : WpsFormInfos) {
            if(unfinishedBizIds.contains(vo.getBizId()) && finishMap.containsKey(vo.getBizId())){
                vo.setReportQty(finishMap.get(vo.getBizId()));
                updateList.add(vo);
            }
        }
        //批量更新
        if(!CollectionUtils.isEmpty(updateList)){
            updateBatchById(updateList);
        }
    }
    @Override
    public void synReportedQtyData() {
        try{
            //取出全部订单
            List<WpsFormInfo> WpsFormInfos = this.list();
            if(CollectionUtils.isNotEmpty(WpsFormInfos)){
                updateReportedByBizData(WpsFormInfos);
            }
        }catch (Exception e){
            log.error("定时更新报工数量失败",e);
        }
    }

    @Override
    public void updateReportedByUserId(Long userId,String factoryCode) {
        try{
            //取出用户有权限的订单数据
            Date startTime = LocalDateTimeUtil.localDateToDate(LocalDate.now().minusDays(30));
            Date endTime =  new Date();
            List<WpsRowData> rowDataList = wpsRowDataFacadeService.listUserApiRowData(userId, startTime, endTime,factoryCode);
            if(CollectionUtils.isEmpty(rowDataList)){
                return;
            }
            //取出订单ID列表
            List<String> bizIds = rowDataList.stream().map(WpsRowData::getOrderNo).distinct().collect(Collectors.toList());
            List<WpsFormInfo> WpsFormInfos = baseMapper.selectList(Wrappers.<WpsFormInfo>lambdaQuery().in(WpsFormInfo::getBizId, bizIds));
            updateReportedByBizData(WpsFormInfos);
        }catch (Exception e){
            log.error("更新报工数量失败",e);
        }
    }
}
