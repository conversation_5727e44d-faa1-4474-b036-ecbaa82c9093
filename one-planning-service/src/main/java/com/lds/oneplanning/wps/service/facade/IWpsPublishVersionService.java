package com.lds.oneplanning.wps.service.facade;

import com.lds.oneplanning.wps.model.OrderScheduleDTO;

import java.util.List;

public interface IWpsPublishVersionService {
    /**
     * 保存排产计划版本
     * @param userId  用户ID
     * @param factoryCode 工厂代码
     * @param version 工厂代码
     */
    void savePlanSnapshot(Long userId,String factoryCode,String version);

    /**
     * 版本比较
     * @param factoryCode
     * @param version
     * @param orderScheduleDTOS
     */
    void compareVersion(String factoryCode, String version, List<OrderScheduleDTO> orderScheduleList);


}
