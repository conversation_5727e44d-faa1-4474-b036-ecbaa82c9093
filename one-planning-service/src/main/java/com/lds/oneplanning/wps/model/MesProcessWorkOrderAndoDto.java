package com.lds.oneplanning.wps.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="在制工单andon信息", description="")
public class MesProcessWorkOrderAndoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "异常事项")
    private String exceptionDetails;

    @ApiModelProperty(value = "是否停线")
    private Integer isProductionStopped;

    @ApiModelProperty(value = "预计关闭时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date estimatedResolveTime;


}
