package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.annotation.TableHeader;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 工艺路线异常
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daishaokun</a>
 * @since 2025/5/13 16:20
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "入库未完成异常")
@TableHeader(type = WpsOrderWarningTypeEnum.STORAGE_NO_ACHIEVED_ABNORMAL, source = ViewSource.DEFAULT)
@Data
public class StorageNoAchievedAbnormalVO extends AffectsPlan {
    @ApiModelProperty(value = "计划日期")
    private LocalDateTime plannedDate;

    @ApiModelProperty(value = "工单号")
    private String workOrderNumber;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "关联订单号")
    private String relatedOrderNumber;

    @ApiModelProperty(value = "计划数量")
    private Integer plannedQuantity;

    @ApiModelProperty(value = "实际投入数量")
    private BigDecimal actualInputQuantity;

    @ApiModelProperty(value = "gap")
    private BigDecimal actualInputQuantityGap;

    @ApiModelProperty(value = "实际产出数量")
    private BigDecimal actualOutputQuantity;

    @ApiModelProperty(value = "gap")
    private BigDecimal actualOutputQuantityGap;

    @ApiModelProperty(value = "已报工未入库数量")
    private BigDecimal reportedNotWarehousedQuantity;

    @ApiModelProperty(value = "gap（加已报工未入库）")
    private BigDecimal totalGapWithReported;

    @ApiModelProperty(value = "关联andon数量")
    private RelatedAndonQuantity relatedAndonQuantity;

    @ApiModelProperty(value = "建议再计划时间")
    private LocalDateTime suggestedReplanTime;

    @ApiModelProperty(value = "确认再计划时间")
    private LocalDateTime confirmedReplanTime;


    @ApiModelProperty(value = "是否影响上下层计划", notes = "edit")
    private Boolean affectsUpperLevelPlan;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    @ApiModelProperty(value = "是否发起船期变更")
    private Boolean initiateShipmentChange;
}
