package com.lds.oneplanning.wps.controller;

import com.lds.oneplanning.wps.req.TodoHandleReq;
import com.lds.oneplanning.wps.service.WarningTodoListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api("物料不齐套异常")
@RestController
@RequestMapping("/wps/warning/todo")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningTodoController {
    private final WarningTodoListService todoListService;

    @PostMapping("/handle")
    @ApiOperation("批量处理")
    public void handle(@Valid @ApiParam @RequestBody TodoHandleReq req) {
        todoListService.handle(req);
    }


    @PostMapping("/pushLcp")
    @ApiOperation("推送LCP消息")
    public void pushLcp() {
        todoListService.pushLcp();
    }

    @PostMapping("/upgrade")
    @ApiOperation("推送LCP消息")
    public void upgrade(@RequestParam(defaultValue = "1") Integer times) {
        switch (times) {
            case 1:
                todoListService.firstUpgrade();
                break;
            case 2:
                todoListService.secondUpgrade();
                break;
            case 3:
                todoListService.thirdUpgrade();
                break;
            case 4:
                todoListService.fourthUpgrade();
                break;
            default:
                log.warn("不支持的升级类型");
        }
    }

}
