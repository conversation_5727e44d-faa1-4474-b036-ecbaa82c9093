package com.lds.oneplanning.wps.schedule.handlers.round1;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.service.ILineCapacityService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.mps.date.YearWeekMap;
import com.lds.oneplanning.mps.utils.MpsDateUtil;
import com.lds.oneplanning.wps.model.WpsDateRange;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsPublishTargetEnum;
import com.lds.oneplanning.wps.schedule.handlers.IWpsAutoScheduleHandler;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import com.lds.oneplanning.wps.service.IWpsDayPlanService;
import com.lds.oneplanning.wps.service.IWpsPublishedOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WpsInitScheduleDataHandler implements IWpsAutoScheduleHandler {

    @Autowired
    private IWpsDayPlanService wpsDayPlanService;

    @Autowired
    private IWpsPublishedOrderService wpsPublishedOrderService;

    @Autowired
    private ILineCapacityService lineCapacityService;

    @Override
    public void execute(WpsAutoScheduleContext context) {
        LocalDate currentLocalDate = context.getCurrentDate();
        int weeksToPush = context.getWeeksToPush();
        List<WpsRowData> orderList = context.getOrderList();
        if (CollectionUtils.isEmpty(orderList) || CollectionUtils.isEmpty(context.getLineUuids())) {
            return;
        }
        Date currentDate = Date.from(currentLocalDate.atStartOfDay(MpsDateUtil.ZONE_ID).toInstant());
        YearWeekMap yearWeekMap = MpsDateUtil.getAllDatesFromCurrentDate(currentDate, weeksToPush);
        context.setYearWeekMap(yearWeekMap);
        List<Date> allDates = yearWeekMap.getYearMap().values().stream()
                .flatMap(weekMap -> weekMap.getWeekMap().values().stream())
                .flatMap(halfWeekMap -> halfWeekMap.getHalfWeekMap().values().stream())
                .filter(dates -> !CollectionUtils.isEmpty(dates))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        // 计算并设置排产日期
        calculateAndSetScheduleDates(context, allDates);
        // 转换日期为 LocalDate
        LocalDate startScheduleLocalDate = Optional.ofNullable(context.getStartScheduleDate())
                .map(LocalDateTimeUtil::dateToLocalDate)
                .orElse(null);
        LocalDate endScheduleLocalDate = Optional.ofNullable(context.getEndScheduleDate())
                .map(LocalDateTimeUtil::dateToLocalDate)
                .orElse(null);
        List<String> orderNoList = orderList.stream()
                .map(WpsRowData::getOrderNo)
                .distinct()
                .collect(Collectors.toList());
        // 获取订单日计划数量 Map
        Map<String, Map<String, Map<LocalDate, Integer>>> orderDailyPrePlanQuantityMap =
                wpsDayPlanService.getMapByBizIdsAndDates(orderNoList, startScheduleLocalDate, endScheduleLocalDate);
        if (MapUtils.isNotEmpty(orderDailyPrePlanQuantityMap)) {
            context.setOrderDailyPrePlanQuantityMap(orderDailyPrePlanQuantityMap);
            log.info("WPS排产获取订单日计划数量userId:{},orderDailyPrePlanQuantityMap:{}", context.getUserId(),JSON.toJSONString(orderDailyPrePlanQuantityMap));
        }
        // 获取发布订单日期范围 Map
        Map<String, List<WpsDateRange>> publishOrderDateRangeMap = wpsPublishedOrderService.getMapByBizIdsAndPublishTargetAndDates(
                orderNoList, WpsPublishTargetEnum.dps.name(), startScheduleLocalDate, endScheduleLocalDate);
        if (MapUtils.isNotEmpty(publishOrderDateRangeMap)) {
            context.setPublishOrderDateRangeMap(publishOrderDateRangeMap);
        }
        // 初始化日生产线 Map
        initDailyProductionLineMap(context, allDates);
    }

    private void calculateAndSetScheduleDates(WpsAutoScheduleContext context, List<Date> allDates) {
        // 计算最小值和最大值
        Date startScheduleDate = allDates.stream()
                .min(Date::compareTo)
                .orElse(null);
        Date endScheduleDate = allDates.stream()
                .max(Date::compareTo)
                .orElse(null);
        context.setStartScheduleDate(startScheduleDate);
        context.setEndScheduleDate(endScheduleDate);
    }

    private void initDailyProductionLineMap(WpsAutoScheduleContext context, List<Date> allDates) {
        List<String> lineUuids = context.getLineUuids();
        List<LocalDate> localDates =allDates.parallelStream()
                .map(LocalDateTimeUtil::dateToLocalDate)
                .collect(Collectors.toList());
//        Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap = lineCapacityService
//                .getDailyProductionLineMap(BaseDataConstant.CONFIG_TYPE_LINE, lineUuids,null, localDates);
        Map<String, Map<LocalDate, WpsProductionLine>> dailyProductionLineMap = lineCapacityService
                .getGroupDailyProductionLineMap(BaseDataConstant.CONFIG_TYPE_LINE, lineUuids,null, localDates);
        if (MapUtils.isNotEmpty(dailyProductionLineMap)) {
            context.setDailyProductionLineMap(dailyProductionLineMap);
            log.info("WPS排产,initDailyProductionLineMap success. dailyProductionLineMap:{}，userId：{}，factoryCode:{},allDates:{}",
                    JSONUtil.toJsonStr(dailyProductionLineMap),context.getUserId(),context.getCurrentFactoryCode(), JSON.toJSONString(allDates));
        }
    }

    @Override
    public int getOrder() {
        return 6;
    }

    @Override
    public int getRound() {
        return 1;
    }
}