package com.lds.oneplanning.wps.vo;

import com.lds.oneplanning.wps.req.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AffectsPlan extends BasePageReq {

//    @ApiModelProperty(value = "是否影响上下层计划", notes = "edit")
    private Boolean affectsUpperLevelPlan;

//    @ApiModelProperty(value = "影响类型", notes = "edit")
    private String impactType;

//    @ApiModelProperty(value = "最终完工日期")
    private String finalCompletionDate;

//    @ApiModelProperty(value = "最终验货日期")
    private String finalInspectionDate;

    private String orderNo;

//    @ApiModelProperty(value = "影响部件计划")
//    private AffectsAssemblyPlan affectsComponentPlan;
//
//    @ApiModelProperty(value = "是否发起部件计划调整")
//    private Boolean initiateComponentPlanAdjustment;
//
//    @ApiModelProperty(value = "影响组件计划")
//    private AffectsAssemblyPlan affectsAssemblyPlan;
//
//    @ApiModelProperty(value = "是否发起组件计划调整")
//    private Boolean initiateAssemblyPlanAdjustment;

    /**
     * 物料ID
     */
    private String materialId;
    /**
     * 排产日期
     */
    private LocalDate schedulingDate;
    /**
     * 计划数量
     */
    private Integer plannedQuantity;
    /**
     * 影响整灯计划
     */
    private List<AffectsAssemblyPlan> affectsFinalProductPlan;
//
//    @ApiModelProperty(value = "是否发齐整灯计划调整")
//    private Boolean initiateFinalProductPlanAdjustment;
//
//    @ApiModelProperty(value = "影响验货计划")
//    private AffectsInspectionPlan affectsInspectionPlan;
//
//    @ApiModelProperty(value = "是否发齐验货计划调整")
//    private Boolean initiateInspectionPlanAdjustment;
//
//    @ApiModelProperty(value = "影响装柜计划")
//    private AffectsInspectionPlan affectsLoadingPlan;

//    @ApiModelProperty(value = "是否发起船期变更")
    private Boolean initiateShipmentChange;

}
