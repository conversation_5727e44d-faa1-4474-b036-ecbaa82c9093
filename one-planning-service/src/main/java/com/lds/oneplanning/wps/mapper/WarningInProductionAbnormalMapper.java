package com.lds.oneplanning.wps.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lds.oneplanning.wps.entity.WarningInProductionAbnormal;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.model.InProductionAbnormalDTO;
import com.lds.oneplanning.wps.req.InProductionAbnormalReq;
import com.lds.oneplanning.wps.vo.InProductionAbnormalVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
public interface WarningInProductionAbnormalMapper extends BaseMapper<WarningInProductionAbnormal> {
    /**
     * 查询在制工单异常
     * @return
     */
    List<InProductionAbnormalDTO> findList(InProductionAbnormalReq req);
}
