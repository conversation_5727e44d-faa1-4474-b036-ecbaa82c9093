package com.lds.oneplanning.wps.utils;

import com.google.common.collect.Maps;
import com.lds.oneplanning.wps.model.OrderScheduleDTO;
import com.lds.oneplanning.wps.model.WpsRowData;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/5/27 9:29
 */
public class LineScheduleBizUtils {
    private LineScheduleBizUtils() {
    }


/*    public static Comparator<String> getLineNameComparator(){
        return (k1, k2) -> {
            Pattern pattern = Pattern.compile("(\\d+)线");
            Matcher m1 = pattern.matcher(k1);
            Matcher m2 = pattern.matcher(k2);
            if ( m1.find() && m2.find()) {
                int num1 = Integer.parseInt(m1.group(1));
                int num2 = Integer.parseInt(m2.group(1));
                return Integer.compare(num1, num2);
            }else{
                return k1.compareTo(k2);
            }
        };
    }*/

    public static Comparator<String> getLineNameComparator() {
        return Comparator.nullsLast((k1, k2) -> {
            Pattern pattern = Pattern.compile("(\\d+)线");

            // 提取数字部分
            Integer num1 = extractNumber(k1, pattern);
            Integer num2 = extractNumber(k2, pattern);

            // 如果都能提取到数字，则按数字排序
            if (num1 != null && num2 != null) {
                return Integer.compare(num1, num2);
            }
            // 否则按原始字符串排序（注意 null 已被 Comparator.nullsFirst 处理）
            return k1.compareTo(k2);
        });
    }

    // 辅助方法：从字符串中提取“数字”
    private static Integer extractNumber(String str, Pattern pattern) {
        if (str == null) return null;
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return null;
    }

    public static OrderScheduleDTO getOrderSchedule(WpsRowData source,LocalDate startDate,LocalDate endDate){
        OrderScheduleDTO target = new OrderScheduleDTO();
        target.setLineUuid(source.getLineUuid());
        target.setLineCode(source.getLineCode());
        target.setOrderType(source.getOrderType());
        target.setOrderNo(source.getOrderNo());
        target.setSellOrderNo(source.getSellOrderNo());
        target.setRowItem(source.getRowItem());
        target.setProductGroupCode(source.getProductGroupCode());
        target.setProductGroupName(source.getProductGroupName());
        target.setOrderUnitQty(source.getOrderUnitQty());
        target.setOrderPcsQty(source.getOrderPcsQty());
        target.setSchedulePcsQty(source.getSchedulePcsQty());
        target.setCalculateFinishTime(Optional.ofNullable(source.getCalculateFinishTime()).orElse(LocalDate.now()));
        target.setOnlineTime(source.getOnlineTime());
        target.setProductId(source.getProductId());
        target.setCommodityId(source.getCommodityId());
        target.setFrozenStatus(Optional.ofNullable(source.get_frozenStatus()).orElse(0));
        target.setStartProductPeriod(source.get_startProductPeriod());
        target.setEndProductPeriod(source.get_endProductPeriod());
        target.setScheduleDataMap(getScheduleDataMap(source.getScheduleDataMap(),startDate,endDate));
        return target;
    }

    public static Map<LocalDate,Number> getScheduleDataMap(Map<LocalDate,Number> sourceMap, LocalDate startDate, LocalDate endDate){
        Map<LocalDate,Number> targetMap = Maps.newLinkedHashMap();
        LocalDate index = startDate;
        while (index.compareTo(endDate)<=0){
            targetMap.put(index,sourceMap.get(index));
            index = index.plusDays(1);
        }
        return targetMap;
    }
}
