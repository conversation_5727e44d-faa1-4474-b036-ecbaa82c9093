package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.entity.WpsOrderWarningCfg;
import com.lds.oneplanning.wps.model.WpsOrderWarningCfgDTO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-31
 */
public interface IWpsOrderWarningCfgService extends IService<WpsOrderWarningCfg> {

    WpsOrderWarningCfg getOne(String factoryCode,Integer warningLevel,String warningType);

    Long add(WpsOrderWarningCfgDTO dto);

    Integer edit(WpsOrderWarningCfgDTO dto);

    Page<WpsOrderWarningCfgDTO> page(String keyword, Collection<String> factoryCodes, Integer warningLevel, String warningType, Integer status, Integer pageNum, Integer pageSize);

    WpsOrderWarningCfgDTO getDetail(Long id);

    List<WpsOrderWarningCfg> listByFactoryCode(String factoryCode);
}