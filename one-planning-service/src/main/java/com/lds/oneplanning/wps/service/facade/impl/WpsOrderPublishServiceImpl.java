package com.lds.oneplanning.wps.service.facade.impl;

import com.google.common.collect.Lists;
import com.lds.oneplanning.wps.model.WpsDateRange;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.enums.WpsOrderPublishStatusEnum;
import com.lds.oneplanning.wps.schedule.enums.WpsOrderTypeEnum;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import com.lds.oneplanning.wps.service.facade.IWpsOrderPublishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WpsOrderPublishServiceImpl implements IWpsOrderPublishService {

    @Autowired
    private IWpsOrderCommonService wpsOrderCommonService;

    @Override
    public void publish(WpsAutoScheduleContext context, List<LocalDate> localDates) {
        if (CollectionUtils.isEmpty(context.getOrderList()) || CollectionUtils.isEmpty(localDates)) {
            return;
        }
        LocalDate startDate = localDates.get(0);
        LocalDate endDate = localDates.get(localDates.size() - 1);
        resetPublishedOrders(context);
        List<String> publishedOrderNos = checkPublishedOrders(context, startDate, endDate);
        if (CollectionUtils.isEmpty(publishedOrderNos)) {
            return;
        }
        runPublishedOrders(context, localDates);
    }
    @Override
    public void resetPublishedOrders(WpsAutoScheduleContext context) {
        context.getOrderList().forEach(order -> order.set_publishStatus(WpsOrderPublishStatusEnum.UNPUBLISHED.getValue()));
    }
    @Override
    public List<String> checkPublishedOrders(WpsAutoScheduleContext context, LocalDate startDate, LocalDate endDate) {
        Map<String, List<WpsDateRange>> publishOrderDateRangeMap = context.getPublishOrderDateRangeMap();
        if (MapUtils.isEmpty(publishOrderDateRangeMap)) {
            return Lists.newArrayList();
        }
        return context.getOrderList().stream()
                .filter(order -> {
                    List<WpsDateRange> dateRangeList = publishOrderDateRangeMap.get(order.getOrderNo());
                    return !CollectionUtils.isEmpty(dateRangeList) &&
                            dateRangeList.stream().anyMatch(dateRange -> isDateRangeOverlap(dateRange, startDate, endDate));
                })
                .map(WpsRowData::getOrderNo)
                .distinct()
                .collect(Collectors.toList());
    }

    private boolean isDateRangeOverlap(WpsDateRange dateRange, LocalDate startDate, LocalDate endDate) {
        LocalDate startDateRange = dateRange.getStartDate();
        LocalDate endDateRange = dateRange.getEndDate();
        return (startDateRange.isBefore(startDate) || startDateRange.isEqual(startDate)) &&
                (endDateRange.isAfter(endDate) || endDateRange.isEqual(endDate));
    }

    private void runPublishedOrders(WpsAutoScheduleContext context, List<LocalDate> localDates) {
        // 获取业务id、产线编码、排产日期、预产数量的map
        Map<String, Map<String, Map<LocalDate, Integer>>> orderDailyPrePlanQuantityMap = context.getOrderDailyPrePlanQuantityMap();
        if (MapUtils.isEmpty(orderDailyPrePlanQuantityMap)) {
            return;
        }
        context.getOrderList().forEach(order -> {
            wpsOrderCommonService.processOrder(context, order, WpsOrderTypeEnum.PUBLISHED, orderDailyPrePlanQuantityMap, localDates);
            order.set_publishStatus(WpsOrderPublishStatusEnum.PUBLISHED.getValue());
        });
    }
}
