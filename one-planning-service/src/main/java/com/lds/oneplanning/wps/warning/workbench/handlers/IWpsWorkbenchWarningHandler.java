package com.lds.oneplanning.wps.warning.workbench.handlers;

import com.lds.oneplanning.wps.entity.WpsOrderPlanWarning;
import com.lds.oneplanning.wps.entity.WpsOrderWarningCfg;
import com.lds.oneplanning.wps.enums.*;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.warning.workbench.WpsWorkbenchWarningContext;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface IWpsWorkbenchWarningHandler {

    List<WpsOrderPlanWarning> execute(WpsWorkbenchWarningContext wpsWorkbenchWarningContext,
                                      Map<Integer, WpsOrderWarningCfg> wpsOrderWarningCfgMap);

    WpsOrderWarningCategoryEnum getWarningCategory();

    WpsOrderWarningTypeEnum getWarningType();

    default WpsOrderPlanWarning buildWpsOrderPlanWarning(Long userId, String orderNo, String lineUuid, String lineCode,
                                                         OrderWarningLevelEnum orderWarningLevelEnum, LocalDate scheduleDate) {
        return buildWpsOrderPlanWarning("", userId, orderNo, lineUuid, lineCode, orderWarningLevelEnum, scheduleDate);
    }

    default WpsOrderPlanWarning buildWpsOrderPlanWarning(String factoryCode, Long userId, String orderNo, String lineUuid, String lineCode,
                                                         OrderWarningLevelEnum orderWarningLevelEnum, LocalDate scheduleDate) {
        WpsOrderPlanWarning wpsOrderPlanWarning = new WpsOrderPlanWarning();
        wpsOrderPlanWarning.setOrderNo(orderNo);
        wpsOrderPlanWarning.setLineUuid(lineUuid);
        wpsOrderPlanWarning.setLineCode(lineCode);
        wpsOrderPlanWarning.setWarningCategory(getWarningCategory().getCode());
        wpsOrderPlanWarning.setWarningType(getWarningType().getCode());
        if (null != orderWarningLevelEnum){
            wpsOrderPlanWarning.setWarningLevel(orderWarningLevelEnum.getLevel());
        }
        wpsOrderPlanWarning.setHandleStatus(OrderWarningHandleStatusEnum.UN_HANDLE.getCode());
        wpsOrderPlanWarning.setScheduleDate(scheduleDate);
        wpsOrderPlanWarning.setTriggerTime(new Date());
        wpsOrderPlanWarning.setCreateBy(userId);
        wpsOrderPlanWarning.setUpdateBy(userId);
        wpsOrderPlanWarning.setFactoryCode(factoryCode);
        return wpsOrderPlanWarning;
    }

        /**
     * 构建异常数据对应的警告信息列表。
     *
     * @param ctx 上下文对象，包含工厂代码和用户ID等基础信息
     * @param abnormalList 异常数据列表，每个元素包含需要转换为警告的原始数据
     * @param lightColorMap 订单号与灯光颜色的映射关系，用于确定警告显示颜色
     * @return 转换后的警告对象列表，每个元素包含工厂、用户、订单维度的警告信息
     */
    default List<WpsOrderPlanWarning> buildWarning(WpsWorkbenchWarningContext ctx, List<WpsRowData> abnormalList, Map<String, LightColor> lightColorMap) {
        return abnormalList.stream()
                .map(e -> toWarning(ctx.getFactoryCode(), ctx.getUserId(), e, lightColorMap.get(e.getOrderNo())))
                .collect(Collectors.toList());
    }


    default WpsOrderPlanWarning toWarning(String factoryCode, Long userId, WpsRowData order, LightColor lightColor) {
        return buildWpsOrderPlanWarning(factoryCode, userId, order.getOrderNo(), order.getLineUuid(), order.getLineCode(),
                OrderWarningLevelEnum.getByLight(lightColor), order.get_startProductPeriod());
    }


    /**
     * 将给定的数字对象转换为整数。
     *
     * @param number 要转换的数字对象
     * @return 如果number为null，则返回null；否则返回number的整数值
     */
    default Integer toInt(Number number) {
        return number == null ? null : number.intValue();
    }
}