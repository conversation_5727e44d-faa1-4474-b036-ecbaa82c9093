package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.entity.WarningInProductionAbnormal;
import com.lds.oneplanning.wps.req.InProductionAbnormalReq;
import com.lds.oneplanning.wps.vo.InProductionAbnormalToDoVO;
import com.lds.oneplanning.wps.vo.InProductionAbnormalVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
public interface IWarningInProductionAbnormalService extends IService<WarningInProductionAbnormal> {
    /**
     * 保存或更新
     * @param warningInProductionAbnormalList
     */
    void saveOrUpdate(List<WarningInProductionAbnormal> warningInProductionAbnormalList);

    /**
     * 根据订单号查询
     * @param orderNoList
     * @return
     */
    List<WarningInProductionAbnormal> listByOrderNo(List<String> orderNoList);

    /**
     *
     * @param req
     * @return
     */
    <T> Page<T> queryPage(InProductionAbnormalReq req, Class<T> clazz);

    /**
     * 修改数据
     * @param vo
     */
    void updateData(InProductionAbnormalVO vo);

}


