package com.lds.oneplanning.wps.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iot.common.exception.BusinessException;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.wps.entity.WarningDeliveryDateAbnormal;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.exception.WpsExceptionEnum;
import com.lds.oneplanning.wps.helper.UserApiHelper;
import com.lds.oneplanning.wps.mapper.WarningDeliveryDateAbnormalMapper;
import com.lds.oneplanning.wps.req.DeliveryDateAbnormalReq;
import com.lds.oneplanning.wps.service.AffectsPlanService;
import com.lds.oneplanning.wps.service.WarningDeliveryDateAbnormalService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.lds.oneplanning.wps.utils.DateUtils;
import com.lds.oneplanning.wps.vo.DeliveryDateAbnormalVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【warning_delivery_date_abnormal(交期异常)】的数据库操作Service实现
 * @createDate 2025-05-20 09:05:31
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningDeliveryDateAbnormalServiceImpl extends
        ServiceImpl<WarningDeliveryDateAbnormalMapper, WarningDeliveryDateAbnormal>
        implements WarningDeliveryDateAbnormalService {
    private final WpsRowDataFacadeService wpsRowDataFacadeService;
    private final IFactoryFacadeService factoryFacadeService;
    private final AffectsPlanService affectsPlanService;

    @Override
    public List<WarningDeliveryDateAbnormal> queryUnHandleData() {
        return baseMapper.queryUnHandleData();
    }

    @Override
    public Page<DeliveryDateAbnormalVO> queryPage(ViewSource source, DeliveryDateAbnormalReq vo) {
        String userLoginName = null;

        // 如果是MC端，则获取当前登录用户信息
        if (ViewSource.MC.equals(source)) {
            userLoginName = UserApiHelper.getUserLoginName();
        }

        if (StringUtils.isNotEmpty(vo.getFactoryCodes())) {
            vo.setFactoryCodeList(Arrays.asList(StringUtils.split(vo.getFactoryCodes(), ",")));
        }
        if (ViewSource.PC.equals(source)) {
            //PC只能看自己关联的工厂
            Long userId = UserContextUtils.getUserId();
            List<Factory> factories = factoryFacadeService.listByUser(userId);
            if (CollectionUtils.isEmpty(factories)) {
                log.info("当前PC用户{}未关联任何工厂，无法查看数据！", userId);
                return new Page<>(vo.getPage(), vo.getPageSize());
            }
            List<String> factoryCodes = factories.stream().map(Factory::getCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(vo.getFactoryCodeList())) {
                factoryCodes = factoryCodes.stream().filter(vo.getFactoryCodeList()::contains)
                        .collect(Collectors.toList());
            }
            vo.setFactoryCodeList(factoryCodes);
            log.info("当前PC用户{}关联的工厂为:{}", userId, factoryCodes);
        }
        if (CollUtil.isEmpty(vo.getFactoryCodeList())) {
            return new Page<>(vo.getPage(), vo.getPageSize());
        }
        IPage<DeliveryDateAbnormalVO> pageParam = new Page<>(vo.getPage(), vo.getPageSize());
        Page<DeliveryDateAbnormalVO> page = baseMapper.queryPage(pageParam, userLoginName, vo);

        page.getRecords().forEach(item -> {
            item.setAdjustedOnlineTime(ObjectUtil.defaultIfNull(item.getAdjustedOnlineTimeEdited(), item.getAdjustedOnlineTime()));
            item.setEstFinishTime(ObjectUtil.defaultIfNull(item.getEstFinishTimeEdited(), item.getEstFinishTime()));
            item.setCalculateFinishTime(item.getCalculateFinishTime());
            item.setSalesOrderNumberWithLineNumber(getSalesOrderNumber(item.getSalesOrderNumber(), item.getLineNumber()));
            item.setSchedulingDate(item.getPlannedOnlineTime());
            item.setPlannedQuantity(item.getOrderUnitQty());
            Optional.ofNullable(item.getProcessStatus()).ifPresent(s -> item.setProcessStatusName(s.getName()));
        });
        affectsPlanService.buildAffectsPlan(page.getRecords(), DeliveryDateAbnormalVO::getAdjustedOnlineTime);
        return page;
    }

    private String getSalesOrderNumber(String salesOrderNumber, String lineNumber) {
        return salesOrderNumber + "-" + lineNumber;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateData(DeliveryDateAbnormalVO vo) {
        Long userId = UserContextUtils.getUserId();
        //只能更新责任人回复，
        LocalDate adjustedOnlineTime = vo.getAdjustedOnlineTime();
        Long id = vo.getId();
        if (id == null) {
            throw new BusinessException(WpsExceptionEnum.ID_CANNOT_BE_EMPTY);
        }

        WarningDeliveryDateAbnormal entity = super.getById(id);
        if (entity == null) {
            throw new BusinessException(WpsExceptionEnum.RECORD_NOT_EXIST);
        }
        if (adjustedOnlineTime != null) {
            LocalDate srcAdjustedOnlineTime = ObjectUtil.defaultIfNull(entity.getAdjustedOnlineTimeEdited(), entity.getPlannedOnlineTime());
            entity.setAdjustedOnlineTimeEdited(adjustedOnlineTime);
            //调用wps接口更新订单交期
            if (!DateUtils.isSameDay(srcAdjustedOnlineTime, adjustedOnlineTime)) {
                wpsRowDataFacadeService.changeStartScheduleDate(entity.getOrderNumber(), adjustedOnlineTime);
            }
        }
        entity.setAffectsUpperLevelPlan(vo.getAffectsUpperLevelPlan());
        entity.setImpactType(vo.getImpactType());
        entity.setUpdatedBy(userId);
        super.updateById(entity);
    }


}




