package com.lds.oneplanning.wps.schedule.enums;

import lombok.Getter;

@Getter
public enum WpsProductionCapacityWarningEnum {

    /**
     * 排产产能比例<100%,无填充色
     */
    LESS_THAN_100(0),
    /**
     * 排产产能比例:100%-110%,黄色填充
     */
    BETWEEN_100_AND_110(1),
    /**
     * 排产产能比例:>110%,红色填充
     */
    GREATER_THAN_110(2),
    /**
     * 需排产数量和订单实际排产数量不相等
     */
    ORDER_SCHEDULE_EXCEPTION(3),

    /**
     * 排产日期超出上线范围
     */
    SCHEDULE_DATE_OUT_OF_RANGE(4);

    private final int colorValue;

    WpsProductionCapacityWarningEnum(int colorValue) {
        this.colorValue = colorValue;
    }

    public static WpsProductionCapacityWarningEnum getEnumByCapacity(float capacity) {
        capacity = Math.round(capacity * 100);
        if (capacity < 100) {
            return LESS_THAN_100;
        } else if (capacity >= 100 && capacity < 110) {
            return BETWEEN_100_AND_110;
        } else if (capacity >= 110) {
            return GREATER_THAN_110;
        }
        return LESS_THAN_100;
    }
}
