package com.lds.oneplanning.wps.workbench.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class BookingUrgentResp implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 客户
     */
    private String customer;

    /**
     * 工厂
     */
    private String factory;

    /**
     * 销售订单号
     */
    private String saleOrderNo;

    /**
     * 行项目号
     */
    private String lineItemNo;


    /**
     * 订舱状态 todo 待确认
     */
    private String bookingStatus;

    /**
     * 处理状态
     */
    private Integer processStatus;
}