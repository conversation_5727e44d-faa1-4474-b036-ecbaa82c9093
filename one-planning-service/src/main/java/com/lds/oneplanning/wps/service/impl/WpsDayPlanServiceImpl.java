package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.wps.entity.WpsDayPlan;
import com.lds.oneplanning.wps.mapper.WpsDayPlanMapper;
import com.lds.oneplanning.wps.service.IMesProcessWorkOrderService;
import com.lds.oneplanning.wps.service.IWpsDayPlanService;
import com.lds.oneplanning.wps.service.IWpsFormInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-25
 */
@Slf4j
@Service
public class WpsDayPlanServiceImpl extends ServiceImpl<WpsDayPlanMapper, WpsDayPlan> implements IWpsDayPlanService {

    @Resource
    private IWpsFormInfoService wpsFormInfoService;
    @Resource
    private IMesProcessWorkOrderService mesProcessWorkOrderService;

    @Override
    public Map<String, List<WpsDayPlan>> groupByBizId(Collection<String> bizIds, Collection<String> lineCodes) {
        if (bizIds == null || bizIds.isEmpty()) {
            return Maps.newLinkedHashMap();
        }
        return baseMapper.selectList(Wrappers.<WpsDayPlan>lambdaQuery()
                        .in(WpsDayPlan::getBizId,bizIds)
                        .in(CollectionUtils.isNotEmpty(lineCodes), WpsDayPlan::getLineCode,lineCodes)
                ).stream().collect(Collectors.groupingBy(WpsDayPlan::getBizId));
    }

    @Override
    public void batchSaveByBizId(List<WpsDayPlan> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> bizIds =sourceList.stream().map(WpsDayPlan::getBizId).collect(Collectors.toSet());
        LambdaQueryWrapper<WpsDayPlan> deleteQuery = Wrappers.<WpsDayPlan>lambdaQuery().in(WpsDayPlan::getBizId,bizIds);
        baseMapper.delete(deleteQuery);
        // 重新新增
        this.saveBatch(sourceList);
    }

    @Override
    public List<WpsDayPlan> listByBizIdsAndDates(List<String> bizIds, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(bizIds) || startDate == null || endDate == null) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<WpsDayPlan>lambdaQuery()
                .in(WpsDayPlan::getBizId, bizIds)
                .between(WpsDayPlan::getScheduleDate, startDate, endDate)
        );
    }

    @Override
    public Map<String, Map<String, Map<LocalDate, Integer>>> getMapByBizIdsAndDates(List<String> bizIds, LocalDate startDate, LocalDate endDate) {
        Map<String, Map<String, Map<LocalDate, Integer>>> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(bizIds)
                || null == startDate || null == endDate) {
            return resultMap;
        }
        log.info("获取指定时间段内的排产计划，startDate:{},endDate:{}",startDate,endDate);
        List<WpsDayPlan> wpsWeekPlans = this.listByBizIdsAndDates(bizIds, startDate, endDate);
        if (CollectionUtils.isEmpty(wpsWeekPlans)) {
            return resultMap;
        }
        for (WpsDayPlan plan : wpsWeekPlans) {
            String bizId = plan.getBizId();
            String lineUuid = plan.getLineUuid();
            LocalDate planDate = plan.getScheduleDate();
            Integer prePlanQuantity = plan.getPrePlanQuantity();
            Map<String, Map<LocalDate, Integer>> bizIdMap = resultMap.computeIfAbsent(bizId, k -> Maps.newHashMap());
            Map<LocalDate, Integer> dateMap = bizIdMap.computeIfAbsent(lineUuid, k -> Maps.newHashMap());
            dateMap.put(planDate, prePlanQuantity);
        }
        return resultMap;
    }

    @Override
    public Integer batchSaveOrUpdate4Schedule(List<WpsDayPlan> weekPlans) {
        if (CollectionUtils.isEmpty(weekPlans)) {
            return 0;
        }
        int count = 0;
        List<WpsDayPlan> insertList = Lists.newArrayList();
        LocalDate today = LocalDate.now();
/*        List<WpsFormInfo> wpsFormInfos = wpsFormInfoService.listByBizIds(weekPlans.stream().map(WpsDayPlan::getBizId).collect(Collectors.toSet()));
        Map<String, Integer> reportedQtyMap = wpsFormInfos.stream().filter(wpsFormInfo -> wpsFormInfo.getBizId() != null && wpsFormInfo.getReportQty() != null)
                .collect(Collectors.toMap(WpsFormInfo::getBizId, WpsFormInfo::getOrderPcsQty, (integer, integer2) -> integer2));*/
        Map<String, Integer> reportedQtyMap = mesProcessWorkOrderService.getReportQtyMap(weekPlans.stream().map(WpsDayPlan::getBizId).collect(Collectors.toSet()));
        for (WpsDayPlan target : weekPlans){
            // 指定时间删除，过去的 如果报工数量为空 也要删除
            if (target.getScheduleDate().isBefore(today)) {
                Integer reportQty = reportedQtyMap.getOrDefault(target.getBizId(),0);
                if (reportQty > 0) {
                    // 有报工的情况 才不删除，否则一样进行删除
                    continue;
                }
            }
            LambdaQueryWrapper<WpsDayPlan> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WpsDayPlan::getBizId,target.getBizId());
            queryWrapper.eq(target.getLineCode()!=null,WpsDayPlan::getLineCode,target.getLineCode());
            queryWrapper.eq(WpsDayPlan::getScheduleDate,target.getScheduleDate());
            // 根据订单号+ 线体+日期，更新排产数量-  不能使用更新的方式，因为前端可能做线体的切换
            baseMapper.delete(queryWrapper);
            insertList.add(target);
            count ++;
        }
        if (!insertList.isEmpty()) {
            this.saveBatch(insertList);
        }
        return count;
    }

    @Override
    public List<WpsDayPlan> listByDates(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<WpsDayPlan>lambdaQuery()
                .between(WpsDayPlan::getScheduleDate, startDate, endDate)
        );
    }
}
