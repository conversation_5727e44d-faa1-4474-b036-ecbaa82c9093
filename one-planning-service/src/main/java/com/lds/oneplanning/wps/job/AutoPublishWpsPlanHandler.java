package com.lds.oneplanning.wps.job;

import com.lds.coral.job.annotation.JobRegister;
import com.lds.oneplanning.basedata.model.PlannerBaseDTO;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.wps.helper.WpsPlanVersionHelper;
import com.lds.oneplanning.wps.service.facade.IWpsPublishVersionService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 每天13:30执行排产计划发布
 *
 * <AUTHOR>
 * @Date 2025/05/08
 */
@JobRegister(value = "AutoPublishWpsPlanHandler", jobName = "AutoPublishWpsPlanHandlerJob", cron = "0 0 12 * * ? *")
@Component
@Slf4j
public class AutoPublishWpsPlanHandler extends IJobHandler {
    @Autowired
    IPlannerBaseService plannerBaseService;
    @Autowired
    IPlannerDataPermissionService plannerDataPermissionService;
    @Autowired
    IWpsPublishVersionService wpsPublishVersionService;
    @Autowired
    WpsPlanVersionHelper wpsPlanVersionHelper;
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        handle();
        return SUCCESS;
    }

    private void handle() {
        List<PlannerBaseDTO> plannerBaseDTOS = plannerBaseService.findList(null, null);
        String version = wpsPlanVersionHelper.createVersion();
        for (PlannerBaseDTO plannerBaseDto : plannerBaseDTOS) {
            try {
                Long startTime = System.currentTimeMillis();
                if(Objects.isNull(plannerBaseDto.getUserId())|| Objects.isNull(plannerBaseDto.getFactoryCode())){
                    log.info("savePlanVersion-》userId不能为空userId:{},factoryCode:{}",plannerBaseDto.getUserId(),plannerBaseDto.getFactoryCode());
                }
                wpsPublishVersionService.savePlanSnapshot(plannerBaseDto.getUserId(),plannerBaseDto.getFactoryCode(),version);
                Long endTime = System.currentTimeMillis();
                log.info("排产计划版本发布成功,用户id:{},工厂编码:{},version:{},耗时:{}ms", plannerBaseDto.getUserId(), plannerBaseDto.getFactoryCode(),version,endTime - startTime);
            } catch (Exception e) {
                log.error("排产计划版本发布失败", e);
            }
        }
    }
}
