package com.lds.oneplanning.wps.annotation;

import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;

import java.lang.annotation.*;

/**
 * 异常表头注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
public @interface TableHeader {
    WpsOrderWarningTypeEnum type();

    /**
     * 默认DEFAULT，不区分角色
     *
     * @return 角色
     */
    ViewSource[] source() default {ViewSource.DEFAULT};
}
