package com.lds.oneplanning.wps.model;

import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WpsOrderWarningCfg对象", description="")
public class WpsOrderWarningCfgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "告警等级：1预警 2告警")
    private Integer warningLevel;

    @ApiModelProperty(value = "排产场景（0:整机，1:组件，2：部件）")
    private Integer planType;

    @ApiModelProperty(value = "预警类型")
    private String warningType;

    @ApiModelProperty(value = "启用状态：0否 1是")
    private Integer status;

    @ApiModelProperty(value = "参数字段")
    private String paramJson;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // 以下非库表字段
    private String factoryName;


    @ApiModelProperty(value = "预警类型名称")
    public String getWarningTypeName() {
        return WpsOrderWarningTypeEnum.getNameByCode(this.getWarningType());
    }
}
