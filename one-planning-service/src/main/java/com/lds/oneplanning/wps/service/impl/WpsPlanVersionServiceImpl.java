package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.wps.entity.WpsPlanVersion;
import com.lds.oneplanning.wps.enums.WpsPlanSourceEnum;
import com.lds.oneplanning.wps.mapper.WpsPlanVersionMapper;
import com.lds.oneplanning.wps.model.WpsPlanVersionDTO;
import com.lds.oneplanning.wps.service.IWpsPlanVersionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【wps_plan_version】的数据库操作Service实现
* @createDate 2025-05-08 11:38:52
*/
@Slf4j
@Service
public class WpsPlanVersionServiceImpl extends ServiceImpl<WpsPlanVersionMapper, WpsPlanVersion> implements IWpsPlanVersionService {


    @Override
    public void savePlanVersion(WpsPlanVersion wpsPlanVersion) {
        //保存的版本只保留最新的一条
        if(WpsPlanSourceEnum.SAVE.getCode().equals(wpsPlanVersion.getSource())){
            this.remove(Wrappers.<WpsPlanVersion>lambdaQuery().eq(WpsPlanVersion::getPlannerEmpNo,wpsPlanVersion.getPlannerEmpNo())
                    .eq(WpsPlanVersion::getFactoryCode,wpsPlanVersion.getFactoryCode())
                    .eq(WpsPlanVersion::getSource,wpsPlanVersion.getSource())
            );
        }
        this.save(wpsPlanVersion);
    }

    @Override
    public String getLastPlanVersion(String plannerEmpNo, String factoryCode, WpsPlanSourceEnum source) {
        WpsPlanVersion wpsPlanVersion = this.getOne(Wrappers.<WpsPlanVersion>lambdaQuery()
                .eq(StringUtils.isNotBlank(plannerEmpNo), WpsPlanVersion::getPlannerEmpNo, plannerEmpNo)
                .eq(StringUtils.isNotBlank(factoryCode), WpsPlanVersion::getFactoryCode, factoryCode)
                .eq(Objects.nonNull(source), WpsPlanVersion::getSource, source.getCode())
                .orderByDesc(WpsPlanVersion::getCreateTime).last(" limit 1"));
        return wpsPlanVersion != null ? wpsPlanVersion.getVersion() : null;
    }

    @Override
    public List<WpsPlanVersionDTO> findList(String plannerEmpNo, String factoryCode) {
        List<WpsPlanVersion> wpsPlanVersions =this.list(Wrappers.<WpsPlanVersion>lambdaQuery()
                .eq(StringUtils.isNotBlank(plannerEmpNo),WpsPlanVersion::getPlannerEmpNo,plannerEmpNo)
                .eq(StringUtils.isNotBlank(factoryCode),WpsPlanVersion::getFactoryCode,factoryCode)
                .orderByDesc(WpsPlanVersion::getCreateTime));
        return BeanUtil.mapList(wpsPlanVersions,WpsPlanVersionDTO.class);
    }
}




