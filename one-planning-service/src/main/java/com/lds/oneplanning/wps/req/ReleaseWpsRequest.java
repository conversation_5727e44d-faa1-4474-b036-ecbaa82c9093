package com.lds.oneplanning.wps.req;

import com.lds.oneplanning.mps.model.RowSaveData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
public class ReleaseWpsRequest implements Serializable {

    private static final long serialVersionUID = -3472940662102307939L;

    @ApiModelProperty(value = "发布开始时间")
    @NotNull
    private LocalDate releaseStartDate;

    @ApiModelProperty(value = "发布结束时间")
    @NotNull
    private LocalDate releaseEndDate;

    @ApiModelProperty(value = "WPS行数据")
    private List<RowSaveData> wpsRowDatas;
}
