package com.lds.oneplanning.wps.controller;

import com.google.common.collect.Lists;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.common.utils.ExcelUtils;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.common.utils.model.ExcelData;
import com.lds.oneplanning.esb.cache.ScheduleOrderCacheUtils;
import com.lds.oneplanning.wps.model.LineScheduleViewDTO;
import com.lds.oneplanning.wps.model.OrderScheduleDTO;
import com.lds.oneplanning.wps.service.IWpsRowExtService;
import com.lds.oneplanning.wps.service.facade.WpsLineScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/5/8 10:51
 */
@Api(value = "AutoScheduleController", tags = "自动排产")
@RestController
@RequestMapping("/wps/auto/schedule")
public class AutoScheduleController {

    @Resource
    private WpsLineScheduleService wpsLineScheduleService;
    @Resource
    private IWpsRowExtService wpsRowExtService;

    private static final String XLS = ".xls";

    @ApiOperation(value = "获取线体详细排程", notes = "获取线体详细排程")
    @GetMapping("/getLineScheduleDetail")
    public List<OrderScheduleDTO> getLineScheduleDetail(
                                                @RequestParam(value = "source",required = false,defaultValue = "1") Integer source,
                                                @RequestParam(value = "startTime",required = false) Date startTime,
                                              @RequestParam(value = "endTime",required = false) Date endTime,
                                              @RequestParam(value = "factoryCode",required = false) String factoryCode){
        startTime = startTime == null ? new Date() : startTime;
        endTime = endTime == null ? LocalDateTimeUtil.localDateToDate(LocalDate.now().plusDays(7)) : endTime;
        return wpsLineScheduleService.getLineScheduleDetail(source,UserContextUtils.getUserId(),startTime,endTime,factoryCode,true);
    }

    @ApiOperation(value = "排产数据导出", notes = "排产数据导出")
    @GetMapping( "/export")
    public void export(HttpServletResponse response,  @RequestParam(value = "source",required = false,defaultValue = "1") Integer source,
                       @RequestParam(value = "startTime",required = false) Date startTime,
                       @RequestParam(value = "endTime",required = false) Date endTime,
                       @RequestParam(value = "factoryCode",required = false) String factoryCode) throws Exception{

        startTime = startTime == null ? new Date() : startTime;
        endTime = endTime == null ? LocalDateTimeUtil.localDateToDate(LocalDate.now().plusDays(7)) : endTime;
        List<List<Object>> rows = Lists.newArrayList();
        ExcelData data = new ExcelData();
        String name = "排产数据记录";
        data.setName(name);
        List<String> titleList = Lists.newArrayList("序号","线体名称","订单类型","订单号","订单数量","需排产日期","完工日期","产品id","商品id");
        LocalDate start = LocalDateTimeUtil.dateToLocalDate(startTime);
        LocalDate end = LocalDateTimeUtil.dateToLocalDate(endTime);
        List<LocalDate> dateList = Lists.newArrayList();
        while (start.compareTo(end)<=0){
            titleList.add(start.toString());
            dateList.add(start);
            start = start.plusDays(1);
        }
        // 添加头
        data.setTitles(titleList);
        //添加数据
        List<OrderScheduleDTO> datas = wpsLineScheduleService.getLineScheduleDetail(source,UserContextUtils.getUserId(),startTime,endTime,factoryCode,true);
        Integer index = 1;
        for (OrderScheduleDTO dto : datas){
            List<Object> row  =   Lists.newArrayList(index,dto.getLineName(),dto.getOrderType(),dto.getOrderNo(),dto.getOrderPcsQty(),
                    dto.getSchedulePcsQty(),dto.getCalculateFinishTime(),dto.getProductId(),dto.getCommodityId());
            for (LocalDate localDate : dateList){
                if (dto.getScheduleDataMap().containsKey(localDate)) {
                    row.add(dto.getScheduleDataMap().get(localDate));
                }
            }
            index++ ;
            rows.add(row);
        }
        data.setRows(rows);
        String fileName = name + XLS;
        ExcelUtils.exportExcel(response,fileName,data);
    }


    @ApiOperation(value = "获取线体排程视图", notes = "获取线体排程视图")
    @GetMapping("/getLineScheduleView")
    public List<LineScheduleViewDTO> getLineScheduleView(
            @RequestParam(value = "viewType",required = false,defaultValue = "1") Integer viewType,
            @RequestParam(value = "source",required = false,defaultValue = "1") Integer source,
            @RequestParam(value = "startTime",required = false) Date startTime,
                                                          @RequestParam(value = "endTime",required = false) Date endTime,
                                                          @RequestParam(value = "factoryCode",required = false) String factoryCode){
        startTime = startTime == null ? LocalDateTimeUtil.localDateToDate(LocalDate.now().minusDays(3)) : startTime;
        endTime = endTime == null ? new Date() : endTime;
        return wpsLineScheduleService.getLineScheduleView(viewType,source,UserContextUtils.getUserId(),startTime,endTime,factoryCode,true);
    }


    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/getOrderDetail")
    public OrderScheduleDTO getOrderDetail(@RequestParam("orderNo") String orderNo){
        return ScheduleOrderCacheUtils.getScheduleOrderFromCache(orderNo);
    }

    @ApiOperation(value = "批量设置冻结状态", notes = "批量设置冻结状态")
    @PostMapping("/batchSetFrozenStatus")
    public Integer batchFrozenStatus(@RequestBody OrderScheduleDTO dto){
        return wpsRowExtService.batchFrozenStatus(dto.getFrozenStatus(),dto.getOrderNos());
    }

    @ApiOperation(value = "批量更新", notes = "批量更新")
    @PostMapping("/batchUpdate")
    public Integer batchUpdate(@RequestBody List<OrderScheduleDTO>  updateList,@RequestParam("factoryCode")String factoryCode){
        return wpsLineScheduleService.saveScheduleData(updateList,UserContextUtils.getUserId(),factoryCode);
    }

}
