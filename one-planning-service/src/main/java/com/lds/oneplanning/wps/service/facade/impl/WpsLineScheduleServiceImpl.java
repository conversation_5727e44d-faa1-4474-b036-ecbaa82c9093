package com.lds.oneplanning.wps.service.facade.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lds.oneplanning.basedata.entity.LineCapacity;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.entity.LineUph;
import com.lds.oneplanning.basedata.service.ILineCapacityService;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.basedata.service.ILineUphService;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.esb.cache.ScheduleOrderCacheUtils;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.entity.WpsDayPlan;
import com.lds.oneplanning.wps.entity.WpsFormInfo;
import com.lds.oneplanning.wps.entity.WpsPlanVersion;
import com.lds.oneplanning.wps.entity.WpsRowExt;
import com.lds.oneplanning.wps.enums.WpsOrderWarningTypeEnum;
import com.lds.oneplanning.wps.enums.WpsPlanSourceEnum;
import com.lds.oneplanning.wps.helper.ScheduleViewHelper;
import com.lds.oneplanning.wps.helper.WpsPlanVersionHelper;
import com.lds.oneplanning.wps.model.LineScheduleViewDTO;
import com.lds.oneplanning.wps.model.OrderScheduleDTO;
import com.lds.oneplanning.wps.model.OrderWarningDTO;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.*;
import com.lds.oneplanning.wps.service.facade.IWpsPublishVersionService;
import com.lds.oneplanning.wps.service.facade.WpsLineScheduleService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessContext;
import com.lds.oneplanning.wps.strategy.WpsOrderProcessEnum;
import com.lds.oneplanning.wps.utils.LineScheduleBizUtils;
import com.lds.oneplanning.wps.utils.WpsTransUtils;
import com.lds.oneplanning.wps.vo.WpsOrderPlanWarningCountVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;







/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/5/8 15:04
 */
@Slf4j
@Service
public class WpsLineScheduleServiceImpl implements WpsLineScheduleService {

    @Resource
    private WpsOrderProcessContext wpsOrderProcessContext;
    @Resource
    private WpsRowDataFacadeService wpsRowDataFacadeService;
    @Resource
    private ILineInfoService lineInfoService;
    @Resource
    private IWpsDayPlanService wpsDayPlanService;
    @Resource
    private IWpsRowExtService wpsWeekPlanExtService;
    @Resource
    private IPlannerBaseService plannerBaseService;
    @Resource
    private ScheduleViewHelper scheduleViewHelper;
    @Resource
    private IWpsOrderPlanWarningService wpsOrderPlanWarningService;
    @Resource
    private ILineCapacityService lineCapacityService;
    @Resource
    private ILineUphService lineUphService;
    @Resource
    private IWpsRowExtService wpsRowExtService;
    @Resource
    private IWpsSnapshotService wpsSnapshotService;
    @Resource
    private IEsbDataFetchService esbDataFetchService;
    @Resource
    private IWpsFormInfoService wpsFormInfoService;
    @Resource
    IWpsPlanVersionService wpsPlanVersionService;
    @Resource
    WpsPlanVersionHelper wpsPlanVersionHelper;
    @Resource
    IWpsPublishVersionService wpsPublishVersionService;

    @Override
    public List<OrderScheduleDTO> getLineScheduleDetail(Integer datasource, Long userId, Date startTime, Date endTime, String factoryCode, boolean cacheFlag) {
        LocalDate orderStartDay = LocalDate.of(LocalDate.now().getYear(), 1, 1);
        LocalDate orderEndDay = LocalDate.now();
        WpsOrderProcessEnum processEnum = WpsConstants.DATA_SOURCE_STORAGE.equals(datasource) ? WpsOrderProcessEnum.READ_STORAGE : WpsOrderProcessEnum.AUTO_SCHEDULE;
        List<WpsRowData> userWpsRowDatas = wpsOrderProcessContext
                .process(processEnum, userId, orderStartDay, orderEndDay, factoryCode, cacheFlag,Maps.newHashMap());
        userWpsRowDatas= wpsRowDataFacadeService.sortWpsRowData(userWpsRowDatas, WpsConstants.DATA_SOURCE_AUTO);
        LocalDate startDate = LocalDateTimeUtil.dateToLocalDate(startTime);
        LocalDate endDate = LocalDateTimeUtil.dateToLocalDate(endTime);

        //  数据封装处理  优先设置历史数据
        Set<OrderScheduleDTO> dirtyOrderScheduleSet = this.getHistoryOrders(factoryCode,startDate, endDate);
        // 非历史数据
        userWpsRowDatas.stream().map(source -> LineScheduleBizUtils.getOrderSchedule(source, startDate, endDate)).forEach(dirtyOrderScheduleSet::add);

        List<OrderScheduleDTO> resultList = Lists.newArrayList();
        // 上线日期在开始和结束日期之间的 或者预排产日期在开始和结束之间
        for (OrderScheduleDTO dto : dirtyOrderScheduleSet) {
            // 判断是否满足时间范围条件 有onlineTime 使用真正排产时间进行过滤
            boolean matchOnlineTime = dto.getOnlineTime() != null
                    && dto.getOnlineTime().compareTo(startTime) >= 0
                    && dto.getOnlineTime().compareTo(endTime) <= 0;
            // 没有onlineTime 使用开始排产日期进行过滤
            boolean matchStartProductPeriod = dto.getStartProductPeriod() != null
                    && dto.getStartProductPeriod().compareTo(startDate) >= 0
                    && dto.getStartProductPeriod().compareTo(endDate) <= 0;

            if (matchOnlineTime || (dto.getOnlineTime() == null && matchStartProductPeriod)) {
                resultList.add(dto);
            }
        }
        Set<String> lineUuids = resultList.stream().map(OrderScheduleDTO::getLineUuid).collect(Collectors.toSet());
        Map<String,String> lineNameMap = lineInfoService.listByUuids(lineUuids).stream().collect(Collectors.toMap(LineInfo::getLineUuid,LineInfo::getName,(s, s2) -> s2));
        resultList.stream().filter(scheduleDTO -> StringUtils.isNotBlank(scheduleDTO.getLineUuid()))
                .forEach(orderScheduleDTO -> orderScheduleDTO.setLineName(lineNameMap.get(orderScheduleDTO.getLineUuid())));
        this.setWarningInfo(resultList);
        //增加跟上一次的版本差异比较
        String version = wpsPlanVersionService.getLastPlanVersion(plannerBaseService.getEmpNoByUserId(userId),factoryCode,WpsPlanSourceEnum.PUBLISH);
        wpsPublishVersionService.compareVersion(factoryCode,version,resultList);
        ScheduleOrderCacheUtils.setScheduleOrderFromCache(resultList);
        return resultList;
    }

    private Set<OrderScheduleDTO> getHistoryOrders(String factoryCode,LocalDate startDate,LocalDate endDate){
        Set<OrderScheduleDTO> orderScheduleDTOSets = Sets.newLinkedHashSet();

        // 20250604 找出前面两天的排产数据(不包含今天)，已完工的 标记给前端进行灰色色块标记，未完成的保留蓝色。
        LocalDate today = LocalDate.now();
        LocalDate historyStart = startDate.isBefore(today) ? startDate : null;
        if (historyStart != null) {
            List<WpsRowData> historyData =  wpsSnapshotService.listByCreateTime(factoryCode,WpsPlanSourceEnum.PUBLISH, historyStart,today);
            if (!historyData.isEmpty()) {
                // 获取sap完整数据 用于比对 这个获取之后不会有线体编码，因此不能通过集合的contain进行判断，而仅需通过订单号判断
                List<WpsRowData> sapOrderList = esbDataFetchService.fetchWpsOrderList(LocalDate.of(LocalDate.now().getYear(),1,1),
                        LocalDate.now(), Lists.newArrayList(factoryCode));
                Set<String> orderSets = sapOrderList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toSet());
                for (WpsRowData wpsRowData : historyData){
                    OrderScheduleDTO target =LineScheduleBizUtils.getOrderSchedule(wpsRowData,startDate, endDate);
                    // sap 是否存在该订单 存在设置为1表示还需排产，设置2表示sap不存在已经完工，
                    target.setOrderProcessStatus(orderSets.contains(target.getOrderNo()) ? 1 : 2);
                    log.info("查询历史订单数据 order={}", JSON.toJSONString(target));
                    orderScheduleDTOSets.add(target);
                }
            }
        }
        return orderScheduleDTOSets;
    }
    private void setWarningInfo(List<OrderScheduleDTO> resultList){
        if (resultList == null || resultList.isEmpty()) {
            return;
        }
        List<String> orderNos = resultList.stream().map(OrderScheduleDTO::getOrderNo).distinct().collect(Collectors.toList());
        List<WpsOrderPlanWarningCountVO>  warnings = wpsOrderPlanWarningService.countByOrderNos(orderNos);
        Map<String,List<WpsOrderPlanWarningCountVO>>  groupMap = warnings.stream().collect(Collectors.groupingBy(WpsOrderPlanWarningCountVO::getOrderNo));
        for (OrderScheduleDTO orderScheduleDTO : resultList){
            String orderNo = orderScheduleDTO.getOrderNo();
            List<WpsOrderPlanWarningCountVO> warning = groupMap.get(orderNo);
            if (CollectionUtils.isEmpty(warning)) {
                continue;
            }
            List<OrderWarningDTO> warningDTOS = Lists.newArrayList();
            warning.forEach(vo ->{
                OrderWarningDTO warningDTO  = new OrderWarningDTO();
                WpsOrderWarningTypeEnum warningType = vo.getWarningType();
                warningDTO.setDesc(warningType.getName());
                warningDTO.setOrderNo(orderNo);
                warningDTO.setViewPageUri(warningType.getViewPageUri());
                warningDTO.setWarningLevel(vo.getWarningLevel());
                warningDTOS.add(warningDTO);
            } );
            orderScheduleDTO.setWarnings(warningDTOS);
        }
    }


    @Override
    public Integer saveScheduleData(List<OrderScheduleDTO> updateList, Long userId) {
        String empNo = plannerBaseService.getEmpNoByUserId(userId);
        // 以下处理wps_day_plan
        List<WpsDayPlan> targetList = WpsTransUtils.scheduleToWpsDayPlanList(updateList,userId);
        Integer res= wpsDayPlanService.batchSaveOrUpdate4Schedule(targetList);

        // 处理 wps_form_info 信息
        List<WpsFormInfo> wpsFormInfos = WpsTransUtils.scheduleToFormList(updateList,userId,empNo);
        wpsFormInfoService.saveOrUpdateBatchByBizId(wpsFormInfos);

        // 以下处理 wps_row_ext 库表
        List<WpsRowExt> totalWpsRowExts = WpsTransUtils.scheduleToWpsRowExtList(updateList,userId,empNo);
        wpsWeekPlanExtService.batchSaveByBizId(totalWpsRowExts);
        return res;
    }
    @Override
    public Integer saveScheduleData(List<OrderScheduleDTO> updateList, Long userId, String factoryCode) {
        updateList.forEach(dto -> dto.setFactoryCode(factoryCode));
        Integer res = this.saveScheduleData(updateList,userId);
        String empNo = plannerBaseService.getEmpNoByUserId(userId);
        WpsPlanVersion wpsPlanVersion = wpsPlanVersionHelper.createWpsPlanVersion(empNo,factoryCode);
        wpsPlanVersion.setSource(WpsPlanSourceEnum.SAVE.getCode());
        wpsPlanVersion.setCreateBy(userId);
        wpsPlanVersion.setUpdateBy(userId);
        wpsPlanVersionService.savePlanVersion(wpsPlanVersion);
        return res;
    }

    @Override
    public List<LineScheduleViewDTO> getLineScheduleView(Integer viewType, Integer source, Long userId, Date startTime, Date endTime, String factoryCode, boolean cacheFlag) {
        List<OrderScheduleDTO>  detailList=  this.getLineScheduleDetail(source, userId, startTime, endTime, factoryCode, cacheFlag);
        LocalDate startDate = LocalDateTimeUtil.dateToLocalDate(startTime);
        LocalDate endDate = LocalDateTimeUtil.dateToLocalDate(endTime);
        // 没有线体 直接过滤  以下保证必须有线体
        detailList = detailList.stream().filter(orderScheduleDTO -> StringUtils.isNotBlank(orderScheduleDTO.getLineUuid())).collect(Collectors.toList());
        if (detailList.isEmpty()) {
            return Collections.emptyList();
        }
        Set<String> lineUUids = detailList.stream().map(OrderScheduleDTO::getLineUuid).collect(Collectors.toSet());
        Map<String,List<OrderScheduleDTO>> groupByLineUUid = detailList.stream().collect(Collectors.groupingBy(OrderScheduleDTO::getLineUuid));
        List<LineScheduleViewDTO> resList = Lists.newArrayList();
        List<LineInfo> lineInfos = lineInfoService.listByUuids(lineUUids);
        Map<String, List<LineUph>>  uphMap = lineUphService.groupByUuid(lineUUids);
        Map<String, List<LineCapacity>> capacityMap = lineCapacityService.groupByLineUuid(lineUUids, startDate, endDate);
        List<WpsRowExt> wpsRowExts = wpsRowExtService.listByBizIds(detailList.stream().map(OrderScheduleDTO::getOrderNo).collect(Collectors.toSet()));
        Map<String,Integer>  frozenStatusMap = wpsRowExts.stream().filter(wpsRowExt -> wpsRowExt.getBizId()!=null).collect(Collectors.toMap(WpsRowExt::getBizId,WpsRowExt::getFrozenStatus,(integer, integer2) -> integer2));
        Map<String,LineInfo> lineInfoMap= lineInfos
                .stream().collect(Collectors.toMap(LineInfo::getLineUuid,lineInfo -> lineInfo,(t, t2) -> t2));
        Map<String,Number> lineCapacityContext = Maps.newHashMap();
        //以下操作针对单个线体
        for(Map.Entry<String,List<OrderScheduleDTO>> entry : groupByLineUUid.entrySet()){
            String lineUuid = entry.getKey();
            LineInfo lineInfo = lineInfoMap.get(lineUuid);
            LineScheduleViewDTO viewDTO = new LineScheduleViewDTO();
            if (lineInfo != null) {
                viewDTO.setLineUuid(lineInfo.getLineUuid());
                viewDTO.setLineCode(lineInfo.getCode());
                viewDTO.setLineName(lineInfo.getName());
            }
            viewDTO.setScheduleDayShiftMap(scheduleViewHelper.getScheduleDayShiftMap(lineUuid,entry.getValue(),startDate,endDate,lineCapacityContext,uphMap,capacityMap,frozenStatusMap));
            viewDTO.setScheduleDayMap(scheduleViewHelper.getScheduleDayMap(lineUuid,entry.getValue(),startDate,endDate,uphMap,capacityMap,frozenStatusMap));
            viewDTO.setScheduleWeekMap(scheduleViewHelper.getScheduleWeekMap(lineUuid,entry.getValue(),startDate,endDate,uphMap,capacityMap,frozenStatusMap));
            viewDTO.setLineCapacity(lineCapacityContext.get(lineUuid));
            resList.add(viewDTO);
        }
        // 最后跟进线体名称排序
        resList.sort((o1, o2) -> LineScheduleBizUtils.getLineNameComparator().compare(o1.getLineName(),o2.getLineName()));
        return resList;
    }
}
