package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="WarningIncomeMaterialAtpAbnormal对象", description="")
public class WarningIncomeMaterialAtpAbnormal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "客户")
    private String customer;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "订单id（商品id）")
    private String productId;

    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "计划日期")
    private LocalDate planDate;

    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;

    @ApiModelProperty(value = "最晚需求时间")
    private LocalDate latestDemandTime;

    @ApiModelProperty(value = "计划数量")
    private Integer planQuantity;

    @ApiModelProperty(value = "工厂")
    private String factory;
    /**
     * 行项目
     */
    @TableField(value = "line_number")
    @ApiModelProperty(value = "销售订单行项目")
    private String lineNumber;


    @ApiModelProperty(value = "销售订单-行项目")
    @TableField(exist = false)
    private String xsddhxm;

    /**
     * 销售订单/采购单号
     */
    @TableField(value = "sales_order_number")
    @ApiModelProperty(value = "销售订单号")
    private String salesOrderNumber;

    @ApiModelProperty(value = "子表poList")
    @TableField(exist = false)
    private List<WarningIncomeMaterialPoAtpAbnormal> poList = new ArrayList<>();


}
