package com.lds.oneplanning.wps.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.wps.entity.WarningIndependentPrepareMaterialAbnormal;
import com.lds.oneplanning.wps.mapper.WarningIndependentPrepareMaterialAbnormalMapper;
import com.lds.oneplanning.wps.service.WarningIndependentPrepareMaterialAbnormalService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【warning_independent_prepare_material_abnormal(独立备料SO未转正)】的数据库操作Service实现
* @createDate 2025-05-28 13:52:31
*/
@Service
public class WarningIndependentPrepareMaterialAbnormalServiceImpl extends ServiceImpl<WarningIndependentPrepareMaterialAbnormalMapper, WarningIndependentPrepareMaterialAbnormal>
    implements WarningIndependentPrepareMaterialAbnormalService{

    @Override
    public List<WarningIndependentPrepareMaterialAbnormal> queryUnHandleData() {
        return baseMapper.queryUnHandleData();
    }
}




