package com.lds.oneplanning.wps.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "采购待办回写处理结果和时间")
public class IncomeMateialUpdateVO {

    @ApiModelProperty(value = "处理结果")
    private String dealResult;

    @ApiModelProperty(value = "下一批最快到货时间")
    private String nextArrivalDate;

    @ApiModelProperty(value = "生产订单")
    private String orderNo;

    @ApiModelProperty(value = "物料id")
    private String materialId;

    //PC角色编辑是否影响上下层计划和影响类型
    @ApiModelProperty(value = "是否影响上下层计划，0是，1否")
    private Integer sfyxsxcjh;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    @ApiModelProperty(value = "确定再计划日期")
    private String sureReplanDate;


}
