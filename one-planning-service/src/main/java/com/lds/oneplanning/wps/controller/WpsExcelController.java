package com.lds.oneplanning.wps.controller;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.common.utils.ExcelUtils;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.common.utils.model.ExcelData;
import com.lds.oneplanning.mps.model.CellModel;
import com.lds.oneplanning.mps.model.RowSaveData;
import com.lds.oneplanning.wps.cache.WpsAutoScheduleCache;
import com.lds.oneplanning.wps.model.WpsData;
import com.lds.oneplanning.wps.model.WpsExcelHeader;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.req.ReleaseWpsRequest;
import com.lds.oneplanning.wps.req.WpsCustomHeaderReq;
import com.lds.oneplanning.wps.service.IWpsCustomHeaderService;
import com.lds.oneplanning.wps.service.IWpsFormInfoService;
import com.lds.oneplanning.wps.service.WpsExcelService;
import com.lds.oneplanning.wps.service.facade.IWpsSyncLineProductUphService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-01-21
 */
@Api(value = "WpsExcelController", tags = "wps-excel-controller")
@RestController
@RequestMapping("/wps/excel")
public class WpsExcelController {

    @Resource
    private WpsExcelService wpsExcelService;

    @Resource
    private IWpsSyncLineProductUphService wpsSyncLineProductUphService;

    @Resource
    private IWpsFormInfoService wpsFormInfoService;

    @Resource
    private IWpsCustomHeaderService wpsCustomHeaderService;
    @Resource
    WpsAutoScheduleCache wpsAutoScheduleCache;

    private static final Integer before_day = 30;
    private static final String XLS = ".xls";

    @ApiOperation(value = "head查询", notes = "head查询")
    @GetMapping("/getHeader")
    public List<CellModel> getHeader(@RequestParam(value = "startTime",required = false) Date startTime,
                                     @RequestParam(value = "endTime",required = false) Date endTime,
                                     @RequestParam(value = "factoryCode",required = false) String factoryCode){
        startTime = startTime == null ? LocalDateTimeUtil.localDateToDate(LocalDate.now().minusDays(before_day)) : startTime;
        endTime = endTime == null ? new Date() : endTime;
        return  wpsExcelService.getHeader(UserContextUtils.getUserId(),startTime,endTime,factoryCode);
    }
    @ApiOperation(value = "body查询", notes = "body查询")
    @GetMapping("/getBody")
    public List<WpsRowData> getBody(@RequestParam(value = "startTime",required = false) Date startTime,
                                    @RequestParam(value = "endTime",required = false) Date endTime,
                                    @RequestParam(value = "source",required = false,defaultValue = "2") Integer source,
                                    @RequestParam(value = "factoryCode",required = false) String factoryCode,
                                    @RequestParam(value = "cacheFlag",required = false,defaultValue = "true") Boolean cacheFlag){
        startTime = startTime == null ? LocalDateTimeUtil.localDateToDate(LocalDate.now().minusDays(before_day)) : startTime;
        endTime = endTime == null ? new Date() : endTime;
        return  wpsExcelService.getBody(UserContextUtils.getUserId(),source,startTime,endTime,factoryCode,cacheFlag, Maps.newHashMap());
    }

    @ApiOperation(value = "数据查询", notes = "数据查询")
    @GetMapping("/getData")
    public WpsData getData(@RequestParam(value = "source",required = false,defaultValue = "2") Integer source,
                           @RequestParam(value = "startTime",required = false) Date startTime,
                           @RequestParam(value = "endTime",required = false) Date endTime,
                           @RequestParam(value = "factoryCode",required = false) String factoryCode,
                           @RequestParam(value = "cacheFlag",required = false,defaultValue = "true") Boolean cacheFlag){
        // 改为查询当前年的一月1号
        startTime = startTime == null ? LocalDateTimeUtil.localDateToDate(LocalDate.of(LocalDate.now().getYear(),1,1)) : startTime;
        endTime = endTime == null ? new Date() : endTime;
        return  wpsExcelService.getDataWithLock(UserContextUtils.getUserId(),source,startTime,endTime,factoryCode,cacheFlag, Maps.newHashMap());
    }


    @ApiOperation(value = "排产数据导出", notes = "排产数据导出")
    @GetMapping( "/export")
    public void export(HttpServletResponse response,@RequestParam(value = "source",required = false,defaultValue = "2") Integer source,
                       @RequestParam(value = "startTime",required = false) Date startTime,
                       @RequestParam(value = "endTime",required = false) Date endTime,
                       @RequestParam(value = "factoryCode",required = false) String factoryCode,
                       @RequestParam(value = "cacheFlag",required = false,defaultValue = "true") Boolean cacheFlag) throws Exception{
        List<List<Object>> rows = Lists.newArrayList();
        List<CellModel> headers = wpsExcelService.getHeader(UserContextUtils.getUserId(), startTime, endTime, factoryCode);

        ExcelData data = new ExcelData();
        String name = "排产底表记录";
        data.setName(name);
        List<String> titleList = Lists.newArrayList();
        for (CellModel cell : headers){
            String code = cell.getCode();
            if (!code.equals("scheduleDataMap")) {
                titleList.add(cell.getValue().toString());
            }
            if (code.equals("scheduleDataMap")){
                List<CellModel> subCells = cell.getSubCells();
                subCells.forEach(cellModel -> {
                    titleList.add(cellModel.getValue().toString());
                });
            }
        }
        // 添加头
        data.setTitles(titleList);
        //添加数据+
        startTime = startTime == null ? LocalDateTimeUtil.localDateToDate(LocalDate.of(LocalDate.now().getYear(),1,1)) : startTime;
        endTime = endTime == null ? new Date() : endTime;
        List<WpsRowData> datas = wpsExcelService.getBody(1367766865382353155L, source, startTime, endTime, factoryCode, cacheFlag, Maps.newHashMap());
        for (WpsRowData rowData : datas){
            List<Object> row =  Lists.newArrayList();
            rows.add(row);
        }
        data.setRows(rows);
        String fileName = name + XLS;
        ExcelUtils.exportExcel(response,fileName,data);
    }


    @ApiOperation(value = "数据保存", notes = "数据保存")
    @PostMapping("/saveData/{factoryCode}")
    public void saveData(@PathVariable("factoryCode") String factoryCode, @RequestBody List<RowSaveData> wpsRowDatas){
        wpsExcelService.saveData(factoryCode, UserContextUtils.getUserId(),wpsRowDatas,new Date());
    }

    @ApiOperation(value = "发布wps", notes = "发布wps")
    @PostMapping("/releaseWps/{factoryCode}")
    public void releaseWps(@PathVariable("factoryCode") String factoryCode,
                           @RequestBody @Validated ReleaseWpsRequest releaseWpsRequest) {
        wpsExcelService.releaseWps(factoryCode, UserContextUtils.getUserId(), releaseWpsRequest);
    }

    @ApiOperation(value = "同步产线UPH数据", notes = "同步产线UPH数据")
    @GetMapping("/syncLineUph")
    public void syncLineUph(@RequestParam(value = "factoryCode") String factoryCode){
        wpsSyncLineProductUphService.syncLineProductUphByFactoryCode(factoryCode);
    }

    @ApiOperation(value = "更新报工数据", notes = "更新报工数据")
    @GetMapping("/updateReportedNum")
    public Boolean updateReportedNum(@RequestParam(value = "factoryCode") String factoryCode) {
        Long userId = UserContextUtils.getUserId();
        wpsFormInfoService.updateReportedByUserId(userId,factoryCode);
        return true;
    }

    @ApiOperation(value = "获取待配置的表头", notes = "获取待配置的表头")
    @GetMapping("/getPendingHeader")
    public List<WpsExcelHeader> getPendingHeader() {
        return wpsExcelService.getPendingHeader();
    }

    @ApiOperation(value = "获取自定义表头", notes = "获取自定义表头")
    @GetMapping("/getCustomHeader")
    public List<String> getCustomHeader() {
        Long userId = UserContextUtils.getUserId();
        return wpsCustomHeaderService.listByFactoryCode(userId);
    }

    @ApiOperation(value = "保存自定义表头", notes = "保存自定义表头")
    @PostMapping("/saveCustomHeader")
    public void saveCustomHeader(@RequestBody WpsCustomHeaderReq wpsCustomHeaderReq) {
        Long userId = UserContextUtils.getUserId();
        wpsCustomHeaderService.save(userId, wpsCustomHeaderReq);
    }

    @ApiOperation(value = "是否启动新版自动排产", notes = "是否启动新版自动排产")
    @PostMapping("/changeAutoSchedule")
    public void changeAutoSchedule(@RequestParam("isOpen") Boolean isOpen) {
        wpsAutoScheduleCache.changeAutoScheduleCache(UserContextUtils.getUserId(),isOpen);
    }
}
