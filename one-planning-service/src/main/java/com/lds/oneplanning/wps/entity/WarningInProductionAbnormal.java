package com.lds.oneplanning.wps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lds.oneplanning.wps.enums.LightColor;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@TableName(value = "warning_in_production_abnormal")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WarningInProductionAbnormal implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "是否影响上层计划：0=否，1=是")
    private Boolean affectsUpperLevelPlan;

    @ApiModelProperty(value = "影响类型")
    private String impactType;

    @ApiModelProperty(value = "未满单原因")
    private String insufficientOrderReason;

    @ApiModelProperty(value = "原因分类")
    private String reasonCategory;

    @ApiModelProperty(value = "预计可完工日期")
    private LocalDate estimatedCompletionDate;

    @ApiModelProperty(value = "线体编号")
    private String lineCode;

    @ApiModelProperty(value = "线体UUID")
    private String lineUuid;

    @ApiModelProperty(value = "排产日期")
    private Date schedulingDate;

    @ApiModelProperty(value = "物料下架日期")
    private Date materialOffShelfDate;

    @ApiModelProperty(value = "上线日期")
    private Date onlineDate;

    @ApiModelProperty(value = "生产车间")
    private String productionWorkshop;

    @ApiModelProperty(value = "生产课长")
    private String productionForeman;

    @ApiModelProperty(value = "生产线体")
    private String productionLine;

    @ApiModelProperty(value = "物料ID")
    private String materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "关联订单号")
    private String associatedOrderNo;

    @ApiModelProperty(value = "计划数量")
    private Integer plannedQuantity;

    @ApiModelProperty(value = "实际投入数量")
    private Integer actualInputQuantity;

    @ApiModelProperty(value = "实际报工数量")
    private Integer actualReportingQuantity;

    @ApiModelProperty(value = "排产场景（0:整机，1:组件，2：部件）")
    private Integer planType;

    @ApiModelProperty(value = "灯色")
    private LightColor lightColor;

    @ApiModelProperty(value = "已入库数量")
    private Integer inboundQuantity;

    @ApiModelProperty(value = "计划员工号")
    private String plannerEmpNo;

    @ApiModelProperty(value = "调整后计划日期")
    private LocalDate adjustPlanDate;

    @ApiModelProperty(value = "预计可完工日期")
    private LocalDate pcEstimatedCompletionDate;

    @ApiModelProperty(value = "生产课长工号")
    private String foremanGh;

    @ApiModelProperty(value = "线长工号")
    private String lineLeaderGh;

    @ApiModelProperty(value = "线长名称")
    private String lineLeaderName;

    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者id
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

}
