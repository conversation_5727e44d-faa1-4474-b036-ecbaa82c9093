package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lds.oneplanning.wps.entity.WarningFrozenUnfrozenAbnormal;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.vo.FreezeUnFreezeAbnormalVO;
import com.lds.oneplanning.wps.vo.WarningFrozenUnfrozenAbnormalParams;
import com.lds.oneplanning.wps.vo.WarningIncomeMaterialAtpAbnormalParams;
import com.lds.oneplanning.wps.vo.WarningIncomeMaterialAtpAbnormalVO2;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-27
 */
public interface IWarningFrozenUnfrozenAbnormalService extends IService<WarningFrozenUnfrozenAbnormal> {

    /**
     * 查询未处理数据
     *
     * @return {@link List }<{@link WarningMaterialAtpAbnormal }>
     */
    List<WarningFrozenUnfrozenAbnormal> queryUnHandleData();
    IPage<FreezeUnFreezeAbnormalVO> selectPage(Page<FreezeUnFreezeAbnormalVO> page, WarningFrozenUnfrozenAbnormalParams params);
}
