package com.lds.oneplanning.wps.service;

import com.lds.oneplanning.wps.entity.MesProcessWorkOrderProcedure;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-15
 */
public interface IMesProcessWorkOrderProcedureService extends IService<MesProcessWorkOrderProcedure> {

    /**
     * 根据工单号删除
     * @param workOrderNoList
     */
    void removeByWorkOrderNoList(List<String> workOrderNoList);

    /**
     * 根据工单号查询
     * @param workOrderNumberList
     * @return
     */
    Map<String, List<MesProcessWorkOrderProcedure>> findMapByWorkOrderNumber(List<String> workOrderNumberList);
}
