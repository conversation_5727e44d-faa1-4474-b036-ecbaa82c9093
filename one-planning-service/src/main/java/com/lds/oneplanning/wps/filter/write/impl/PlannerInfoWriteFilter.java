package com.lds.oneplanning.wps.filter.write.impl;

import com.lds.oneplanning.basedata.model.PlannerBaseDTO;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.wps.constants.WpsConstants;
import com.lds.oneplanning.wps.entity.WpsFormInfo;
import com.lds.oneplanning.wps.filter.write.AbstractWpsOrderWriteFilter;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.service.IWpsFormInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/27 15:23
 */
@Slf4j
@Service
public class PlannerInfoWriteFilter  extends AbstractWpsOrderWriteFilter {

    @Resource
    private IWpsFormInfoService wpsFormInfoService;
    @Resource
    private IPlannerBaseService plannerBaseService;

    @Override
    public Integer filterSeq() {
        return 0;
    }
    @Override
    protected List<WpsRowData> doFilter(Long userId, Integer datasource, String factoryCode, List<WpsRowData> dirtyList,boolean cacheFlag, Map<String,Object> params) {

        if ( WpsConstants.DATA_SOURCE_AUTO.equals(datasource)) {
            //自动排产 直接使用当前用户的姓名
            String plannerName = plannerBaseService.getNameNoByUserId(userId);
            String empNo = plannerBaseService.getEmpNoByUserId(userId);
            dirtyList.forEach(wpsRowData -> {
                wpsRowData.setMainPlan(plannerName);
                wpsRowData.setPlannerEmpNo(empNo);
            });
            return dirtyList;
        }
        // 读取数据库，
        List<WpsFormInfo> wpsFormInfos = wpsFormInfoService.listByBizIds(dirtyList.stream().map(WpsRowData::getOrderNo).collect(Collectors.toSet()));
        Map<String,String> plannerMap = wpsFormInfos.stream().filter(wpsFormInfo -> StringUtils.isNotBlank(wpsFormInfo.getPlannerEmpNo()))
                .collect(Collectors.toMap(wpsFrom -> wpsFrom.getBizId(),o -> o.getPlannerEmpNo(),(s, s2) -> s2 ));
        Map<String,String> empNoNameMap = plannerBaseService.findList(null, null).stream().collect(Collectors.toMap(PlannerBaseDTO::getEmpNo,PlannerBaseDTO::getUserName,(s, s2) -> s2));
        dirtyList.stream().forEach(wpsRowData -> {
            // 主计划员信息回填
            if (plannerMap.get(wpsRowData.getOrderNo()+wpsRowData.getLineCode())!=null) {
                String plannerCode= plannerMap.get(wpsRowData.getOrderNo());
                wpsRowData.setMainPlan(empNoNameMap.get(plannerCode));
                wpsRowData.setPlannerEmpNo(plannerCode);
            }
        });
        return dirtyList;
    }


}
