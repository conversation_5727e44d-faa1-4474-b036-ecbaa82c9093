package com.lds.oneplanning.wps.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.lds.oneplanning.wps.entity.WarningMaterialAtpAbnormal;
import com.lds.oneplanning.wps.job.WpsAutoScheduleHandler;
import com.lds.oneplanning.wps.model.OrderScheduleDTO;
import com.lds.oneplanning.wps.model.WpsRowData;
import com.lds.oneplanning.wps.schedule.WpsAutoScheduleContext;
import com.lds.oneplanning.wps.schedule.WpsAutoSchedulePipeline;
import com.lds.oneplanning.wps.service.WarningMaterialAtpAbnormalService;
import com.lds.oneplanning.wps.service.facade.IWpsPublishVersionService;
import com.lds.oneplanning.wps.utils.MockDataGenerator;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Api("测试接口")
@RestController
@RequestMapping("/wps/test")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WpsTestController {
    private final WarningMaterialAtpAbnormalService warningMaterialAtpAbnormalService;

    @Autowired
    WpsAutoSchedulePipeline wpsAutoSchedulePipeline;
    @Autowired
    IWpsPublishVersionService wpsPublishVersionService;
    @Autowired
    WpsAutoScheduleHandler wpsAutoScheduleHandler;
    @GetMapping("/test1")
    public void test1() {
        WarningMaterialAtpAbnormal entity = MockDataGenerator.mockAny(WarningMaterialAtpAbnormal.class);
        entity.setId(null);
        warningMaterialAtpAbnormalService.save(entity);

        entity = warningMaterialAtpAbnormalService.getById(entity.getId());
        log.info("{}", entity);
    }
    @GetMapping("/testAutoSchedule")
    public void testAutoSchedule(){
        List<WpsRowData> wpsRowDatas = JSON.parseArray(getWpsOrder(),WpsRowData.class);
//        wpsRowData.set_productGroupCode("330100030");
        WpsAutoScheduleContext context = new WpsAutoScheduleContext(wpsRowDatas);
        context.setCurrentFactoryCode("3301");
        context.setUserId(1367766865382353155L);
        context.setCurrentDate(LocalDate.now());
        context.setWeeksToPush(12);
        wpsAutoSchedulePipeline.execute(context);
        log.info(JSON.toJSONString(context));
    }

    @GetMapping("/tesPlanCompare")
    public void tesPlanCompare(@RequestParam("version")String version, @RequestParam("factoryCode")String factoryCode, @RequestBody List<OrderScheduleDTO> orderScheduleList){
        wpsPublishVersionService.compareVersion(factoryCode,version,orderScheduleList);

    }

    @GetMapping("/testAutoScheduleJob")
    public void testAutoScheduleJob(){
        try{
            wpsAutoScheduleHandler.execute(null);
        }catch ( Exception e){
            log.info("执行自动排产JOB异常",e);
        }
    }

    private String getWpsOrder(){
        return "[" +
//                "{\n" +
//                "\t\t\"_endProductPeriod\": \"2025-04-23\",\n" +
//                "\t\t\"_frozenStatus\": 0,\n" +
//                "\t\t\"_startProductPeriod\": \"2025-04-16\",\n" +
//                "\t\t\"atpInfoResult\": true,\n" +
//                "\t\t\"atpResult\": true,\n" +
//                "\t\t\"atpStoreResult\": true,\n" +
//                "\t\t\"calculateFinishTime\": \"2025-04-03\",\n" +
//                "\t\t\"capacityStruct\": \"\",\n" +
//                "\t\t\"category\": \"专业照明\",\n" +
//                "\t\t\"_productGroupName\": \"330100030\",\n" +
//                "\t\t\"commodityDesc\": \"格栅透镜反光杯组件_TRI-RG36C-4300-220-34-230108\",\n" +
//                "\t\t\"commodityId\": \"2999814625\",\n" +
//                "\t\t\"createDate\": \"2025-01-24\",\n" +
//                "\t\t\"crossPlantMaterialStatus\": \"22\",\n" +
//                "\t\t\"customerCode\": \"C164\",\n" +
//                "\t\t\"customerGroup\": \"C164\",\n" +
//                "\t\t\"customerMaterialNo\": \"15020500\",\n" +
//                "\t\t\"customerSeq\": \"\",\n" +
//                "\t\t\"estFinishTime\": 1743609600000,\n" +
//                "\t\t\"factory\": \"3301\",\n" +
//                "\t\t\"finalAssembly\": \"\",\n" +
//                "\t\t\"finalInspectDate\": \"2025-04-04\",\n" +
//                "\t\t\"firstReviewShipTime\": 1745942400000,\n" +
//                "\t\t\"infoMaterialCodeMap\": {},\n" +
//                "\t\t\"infoMaterialMap\": {},\n" +
//                "\t\t\"latestInspectTime\": 1743696000000,\n" +
//                "\t\t\"latestLoadTime\": 1743955200000,\n" +
//                "\t\t\"lineCode\": \"\",\n" +
//                "\t\t\"machineModel\": \"\",\n" +
//                "\t\t\"mainPlan\": \"庄钾寅\",\n" +
//                "\t\t\"materialCodeMap\": {},\n" +
//                "\t\t\"materialMap\": {},\n" +
//                "\t\t\"orderNo\": \"10576611-10\",\n" +
//                "\t\t\"orderPcsQty\": 5004.0,\n" +
//                "\t\t\"orderSignFinishTime\": 1737561600000,\n" +
//                "\t\t\"orderType\": \"生产订单\",\n" +
//                "\t\t\"orderUnitQty\": 5004.0,\n" +
//                "\t\t\"originalFinishTime\": 1743609600000,\n" +
//                "\t\t\"outDeliverSetFlag\": false,\n" +
//                "\t\t\"outDeliveryNo\": \"0080276963\",\n" +
//                "\t\t\"outRowItem\": \"000010\",\n" +
//                "\t\t\"packagePrint\": \"存在\",\n" +
//                "\t\t\"planLoadDate\": \"2025-04-07\",\n" +
//                "\t\t\"plannerEmpNo\": \"100000527\",\n" +
//                "\t\t\"pono\": \"5349265\",\n" +
//                "\t\t\"productDesc\": \"PL-FP01-3300-220-33-230901_40K\",\n" +
//                "\t\t\"productEndTime\": 1741276800000,\n" +
//                "\t\t\"productId\": \"1070412153\",\n" +
//                "\t\t\"productLineLevel3\": \"办公灯具\",\n" +
//                "\t\t\"productLineLevel5\": \"大面板灯\",\n" +
//                "\t\t\"productStartTime\": 1737648000000,\n" +
//                "\t\t\"productUnitPrice\": 0.0,\n" +
//                "\t\t\"recordDate\": \"2025-01-23\",\n" +
//                "\t\t\"remark1\": \"\",\n" +
//                "\t\t\"remark2\": \"\",\n" +
//                "\t\t\"reportedPcsQty\": 0,\n" +
//                "\t\t\"riskMaterialRemark\": \"\",\n" +
//                "\t\t\"rowItem\": \"000010\",\n" +
//                "\t\t\"scheduleDataMap\": {},\n" +
//                "\t\t\"scheduleDirection\": -1,\n" +
//                "\t\t\"schedulePcsQty\": 5004.0,\n" +
//                "\t\t\"sellOrderAndRowItemNo\": \"0010407052-000010\",\n" +
//                "\t\t\"sellOrderNo\": \"0010407052\",\n" +
//                "\t\t\"specification\": \"\",\n" +
//                "\t\t\"stdWorkHours\": \"\",\n" +
//                "\t\t\"stockedPcsQty\": 864.0,\n" +
//                "\t\t\"transQty\": 1,\n" +
//                "\t\t\"unifyOrderNo\": \"0010407052\",\n" +
//                "\t\t\"virtualRowItem\": \"000000\",\n" +
//                "\t\t\"warningColorMap\": {},\n" +
//                "\t\t\"workshopCode\": \"\",\n" +
//                "\t\t\"waitingOrderQty\": \"10000\"\n" +
//                "\t},\n" +
                "\t{\n" +
                "\t\t\"_endProductPeriod\": \"2025-05-25\",\n" +
                "\t\t\"_frozenStatus\": 0,\n" +
                "\t\t\"_startProductPeriod\": \"2025-04-25\",\n" +
                "\t\t\"atpInfoResult\": true,\n" +
                "\t\t\"_productGroupCode\": \"330100030\",\n" +
                "\t\t\"atpResult\": true,\n" +
                "\t\t\"atpStoreResult\": true,\n" +
                "\t\t\"calculateFinishTime\": \"2025-04-03\",\n" +
                "\t\t\"capacityStruct\": \"\",\n" +
                "\t\t\"category\": \"专业照明\",\n" +
                "\t\t\"_productGroupName\": \"330100030\",\n" +
                "\t\t\"commodityDesc\": \"格栅透镜反光杯组件_TRI-RG36C-4300-220-34-230108\",\n" +
                "\t\t\"commodityId\": \"1111111\",\n" +
                "\t\t\"createDate\": \"2025-01-24\",\n" +
                "\t\t\"crossPlantMaterialStatus\": \"22\",\n" +
                "\t\t\"customerCode\": \"C164\",\n" +
                "\t\t\"customerGroup\": \"C164\",\n" +
                "\t\t\"customerMaterialNo\": \"15020500\",\n" +
                "\t\t\"customerSeq\": \"\",\n" +
                "\t\t\"estFinishTime\": 1743609600000,\n" +
                "\t\t\"factory\": \"3301\",\n" +
                "\t\t\"finalAssembly\": \"\",\n" +
                "\t\t\"finalInspectDate\": \"2025-04-04\",\n" +
                "\t\t\"firstReviewShipTime\": 1745942400000,\n" +
                "\t\t\"infoMaterialCodeMap\": {},\n" +
                "\t\t\"infoMaterialMap\": {},\n" +
                "\t\t\"latestInspectTime\": 1743696000000,\n" +
                "\t\t\"latestLoadTime\": 1743955200000,\n" +
                "\t\t\"lineCode\": \"\",\n" +
                "\t\t\"machineModel\": \"\",\n" +
                "\t\t\"mainPlan\": \"庄钾寅\",\n" +
                "\t\t\"materialCodeMap\": {},\n" +
                "\t\t\"materialMap\": {},\n" +
                "\t\t\"orderNo\": \"10576611-11\",\n" +
                "\t\t\"orderPcsQty\": 5004.0,\n" +
                "\t\t\"orderSignFinishTime\": 1737561600000,\n" +
                "\t\t\"orderType\": \"生产订单\",\n" +
                "\t\t\"orderUnitQty\": 5004.0,\n" +
                "\t\t\"originalFinishTime\": 1743609600000,\n" +
                "\t\t\"outDeliverSetFlag\": false,\n" +
                "\t\t\"outDeliveryNo\": \"0080276963\",\n" +
                "\t\t\"outRowItem\": \"000010\",\n" +
                "\t\t\"packagePrint\": \"存在\",\n" +
                "\t\t\"planLoadDate\": \"2025-04-07\",\n" +
                "\t\t\"plannerEmpNo\": \"100000527\",\n" +
                "\t\t\"pono\": \"5349265\",\n" +
                "\t\t\"productDesc\": \"PL-FP01-3300-220-33-230901_40K\",\n" +
                "\t\t\"productEndTime\": 1741276800000,\n" +
                "\t\t\"productId\": \"1111111\",\n" +
                "\t\t\"productLineLevel3\": \"办公灯具\",\n" +
                "\t\t\"productLineLevel5\": \"大面板灯\",\n" +
                "\t\t\"productStartTime\": 1737648000000,\n" +
                "\t\t\"productUnitPrice\": 0.0,\n" +
                "\t\t\"recordDate\": \"2025-01-23\",\n" +
                "\t\t\"remark1\": \"\",\n" +
                "\t\t\"remark2\": \"\",\n" +
                "\t\t\"reportedPcsQty\": 0,\n" +
                "\t\t\"riskMaterialRemark\": \"\",\n" +
                "\t\t\"rowItem\": \"000010\",\n" +
                "\t\t\"scheduleDataMap\": {},\n" +
                "\t\t\"scheduleDirection\": -1,\n" +
                "\t\t\"schedulePcsQty\": 5004.0,\n" +
                "\t\t\"sellOrderAndRowItemNo\": \"0010407052-000010\",\n" +
                "\t\t\"sellOrderNo\": \"0010407052\",\n" +
                "\t\t\"specification\": \"\",\n" +
                "\t\t\"stdWorkHours\": \"\",\n" +
                "\t\t\"stockedPcsQty\": 864.0,\n" +
                "\t\t\"transQty\": 1,\n" +
                "\t\t\"unifyOrderNo\": \"0010407052\",\n" +
                "\t\t\"virtualRowItem\": \"000000\",\n" +
                "\t\t\"warningColorMap\": {},\n" +
                "\t\t\"workshopCode\": \"\",\n" +
                "\t\t\"waitingOrderQty\": \"10000\"\n" +
                "\t}\n" +
                "]";
    }
}
