package com.lds.oneplanning.wps.service;

import java.util.List;

/**
 * <p>
 *  推送消息到LCP公众号
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface IPushMsgToOAService {
    /**
     * 推送消息到LCP公众号-传登录名
     * @param text 内容
     * @param type  5推送含链接，2，推送文本
     * @param url 回调链接
     */
    boolean pushMsgToOAByLoginName(String text, List<String> loginNames, int type, String url);
    /**
     * 推送消息到LCP公众号-传openid
     * @param text 内容
     * @param toUserList 接收用户的openid
     * @param type  5推送含链接，2，推送文本
     * @param url 回调链接
     */
    boolean pushMsgToOAByOpenId(String text, List<String> toUserList, int type, String url);
}
