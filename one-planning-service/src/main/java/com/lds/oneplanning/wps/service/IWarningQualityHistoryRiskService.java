package com.lds.oneplanning.wps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.wps.entity.WarningQualityHistoryRisk;
import com.lds.oneplanning.wps.req.QualityHistoryRiskReq;
import com.lds.oneplanning.wps.vo.QualityHistoryRiskVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/2
 */
public interface IWarningQualityHistoryRiskService extends IService<WarningQualityHistoryRisk> {

    /**
     * 查询
     * @param req
     * @return
     */
    Page<QualityHistoryRiskVO> queryPage(QualityHistoryRiskReq req);

    /**
     * 保存或更新
     * @param riskList
     */
    void saveOrUpdate(List<WarningQualityHistoryRisk> riskList);
}
