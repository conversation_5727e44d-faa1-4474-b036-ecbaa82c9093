package com.lds.oneplanning.wps.controller;

import cn.hutool.core.collection.CollUtil;
import com.iot.common.util.ValidateUtil;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.wps.enums.PlanTypeEnum;
import com.lds.oneplanning.wps.enums.ViewSource;
import com.lds.oneplanning.wps.helper.UserApiHelper;
import com.lds.oneplanning.wps.req.InProductionAbnormalReq;
import com.lds.oneplanning.wps.req.SyncScheduleDateReq;
import com.lds.oneplanning.wps.service.AffectsPlanService;
import com.lds.oneplanning.wps.service.IWarningInProductionAbnormalService;
import com.lds.oneplanning.wps.service.facade.WpsRowDataFacadeService;
import com.lds.oneplanning.wps.vo.InProductionAbnormalToDoVO;
import com.lds.oneplanning.wps.vo.InProductionAbnormalVO;
import com.lds.oneplanning.wps.warning.workbench.handlers.InProductionHandler;
import io.fabric8.kubernetes.api.builder.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Api("在制工单异常")
@RestController
@RequestMapping("/wps/warning/inProduction")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WarningInProductionAbnormalController {

    private final IWarningInProductionAbnormalService warningInProductionAbnormalService;
    private final IUserInfoService userInfoService;
    private final InProductionHandler inProductionHandler;
    private final IFactoryFacadeService factoryFacadeService;
    private final AffectsPlanService affectsPlanService;
    private final WpsRowDataFacadeService wpsRowDataFacadeService;

    @PostMapping("/page")
    @ApiOperation("在制工单异常异常分页查询")
    public Page<?> page(@RequestBody InProductionAbnormalReq req) {
        req.setPlanType(PlanTypeEnum.WHOLE_MACHINE.getCode());
        ViewSource source = userInfoService.getCurrentUserType();
        switch (source) {
            case PC:
                Set<String> userFactoryCodes = factoryFacadeService.getUserFactoryCodes(UserContextUtils.getUserId());
                req.setFactoryCodeList(userFactoryCodes);
            case MANAGER:
                Page<InProductionAbnormalVO> result = warningInProductionAbnormalService.queryPage(req, InProductionAbnormalVO.class);
                if (CollUtil.isNotEmpty(result.getResult())) {
                    affectsPlanService.buildAffectsPlan(result.getResult(), InProductionAbnormalVO::getEstimatedCompletionDate);
                }
                return result;
            case PLL:
            case PK:
            case PM:
                String userLoginName = UserApiHelper.getUserLoginName();
                req.setAssignee(userLoginName);
                return warningInProductionAbnormalService.queryPage(req, InProductionAbnormalToDoVO.class);
            default:
                return new Page<>();
        }
    }

    @PostMapping("/update")
    @ApiOperation("在制工单异常异常更新")
    public void update(@RequestBody InProductionAbnormalVO vo) {
        warningInProductionAbnormalService.updateData(vo);
    }

    @PostMapping("/syncScheduleDate")
    @ApiOperation("同步dps排产日期")
    public void syncScheduleDate(@Validated @RequestBody SyncScheduleDateReq req) {
        req.getSyncDpsList().forEach(ValidationUtils::validate);
        req.getSyncDpsList().forEach(syncDps ->
                wpsRowDataFacadeService.changeStartScheduleDate(syncDps.getOrderNo(),syncDps.getScheduleDate()));
    }

    @GetMapping("/execute")
    @ApiOperation("TestExecute")
    public void execute() {
        inProductionHandler.execute();
    }

}
