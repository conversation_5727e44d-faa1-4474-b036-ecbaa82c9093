package com.lds.oneplanning;

import com.iot.common.config.AbstractWebConfig;
import com.lds.coral.i18n.core.interceptor.CustomLocaleChangeInterceptor;
import com.lds.oneplanning.log.interceptor.ApiLoggingInterceptor;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.MQProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

@Configuration
public class WebConfig extends AbstractWebConfig {

    @Value("${rocketmq.name-server}")
    private String namesrvAddr;
    @Value("${rocketmq.producer.access-key}")
    private String accessKey;
    @Value("${rocketmq.producer.secret-key}")
    private String secretKey;


    @Resource
    private ApiLoggingInterceptor apiLoggingInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加拦截器，并指定拦截路径
        registry.addInterceptor(apiLoggingInterceptor)
//                .addPathPatterns("/**") // 拦截所有路径
                .addPathPatterns("/api/common/invoke/**") // 拦截请求 内部的请求不生效，因此不通过这来处理
                .excludePathPatterns("/static/**", "/error"); // 排除静态资源和错误页面
    }



    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver slr = new SessionLocaleResolver();
        //设置默认区域,
        slr.setDefaultLocale(Locale.CHINESE);
        return slr;
    }

    @Override
    protected void addCustomInterceptors(InterceptorRegistry registry) {
        CustomLocaleChangeInterceptor localeInterceptor = new CustomLocaleChangeInterceptor();
        registry.addInterceptor(localeInterceptor);
    }

    @Bean
    protected MQProducer delayRocketMqProducer() {
        DefaultMQProducer producer = null;
        if(StringUtils.isNotBlank(accessKey) && StringUtils.isNotBlank(secretKey)){
            SessionCredentials sessionCredentials = new SessionCredentials(accessKey, secretKey);
            AclClientRPCHook aclClientRPCHook = new AclClientRPCHook(sessionCredentials);
            producer = new DefaultMQProducer("custom_producer", aclClientRPCHook);
        } else {
            producer = new DefaultMQProducer("custom_producer");
        }
        try {
            producer.setNamesrvAddr(namesrvAddr);
            producer.start();
        } catch (MQClientException e) {
            throw new RuntimeException(e);
        }
        return producer;
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
        registrar.setDateFormatter(DateTimeFormatter.ofPattern("yyyy-MM-dd")); // 根据实际格式修改
        registrar.registerFormatters(registry);
    }

}
