package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.basedata.model.SysParamCfgDTO;
import com.lds.oneplanning.basedata.service.ISysParamCfgService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-21
 */
@Slf4j
@Api(value = "SysParamCfgController", tags = "参数配置")
@RestController
@RequestMapping("/basedata/sysParamCfg")
public class SysParamCfgController {

    @Resource
    private ISysParamCfgService sysParamCfgService;

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "删除参数配置",operation = "删除")
    public void delete(@PathVariable("id")Long id ){
        sysParamCfgService.deleteById(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    public Long add(@RequestBody SysParamCfgDTO dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return sysParamCfgService.saveSysParamCfg(dto);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    public Long edit(@RequestBody SysParamCfgDTO dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  sysParamCfgService.updateSysParamCfg(dto);
    }

}

