package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iot.common.exception.BusinessException;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.coral.website.common.util.RedisCacheUtil;
import com.lds.oneplanning.basedata.entity.*;
import com.lds.oneplanning.basedata.mapper.LineChangeCfgMapper;
import com.lds.oneplanning.basedata.model.LineChangeCfgDTO;
import com.lds.oneplanning.basedata.service.*;
import com.lds.oneplanning.wps.exception.WpsExceptionEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.util.*;

/**
 * <p>
 * 缓存换线时长配置
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-19
 */
@Service
public class LineChangeCfgServiceImpl extends ServiceImpl<LineChangeCfgMapper, LineChangeCfg> implements ILineChangeCfgService {

    public static final String LINE_CHANGE_CFG_CACHE = "lineChangeCfg:%s";
    /**
     * 缓存过期时间
     */
    public static final Long CACHE_TIME_OUT = 24*60*60L;

    @Override
    public Long add(LineChangeCfgDTO dto) {
        LineChangeCfg lineChangeCfg = BeanUtil.map(dto, LineChangeCfg.class);
        this.save(lineChangeCfg);
        deleteCache(lineChangeCfg.getFactoryCode());
        return lineChangeCfg.getId();
    }

    @Override
    public Long edit(LineChangeCfgDTO dto) {
        LineChangeCfg oldLineChangeCfg = this.getById(dto.getId());
        if (Objects.isNull(oldLineChangeCfg)) {
            throw new BusinessException(WpsExceptionEnum.RECORD_NOT_EXIST);
        }
        oldLineChangeCfg.setHour(dto.getHour());
        oldLineChangeCfg.setFactoryCode(dto.getFactoryCode());
        oldLineChangeCfg.setUpdateBy(dto.getUpdateBy());
        oldLineChangeCfg.setUpdateTime(new Date());
        this.updateById(oldLineChangeCfg);
        deleteCache(oldLineChangeCfg.getFactoryCode());
        return oldLineChangeCfg.getId();
    }

    @Override
    public Page<LineChangeCfgDTO> page(String factoryCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<LineChangeCfg> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<LineChangeCfg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(factoryCode),LineChangeCfg::getFactoryCode,factoryCode);
        queryWrapper.orderByDesc(LineChangeCfg::getUpdateTime);
        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<LineChangeCfgDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<LineChangeCfgDTO> results = BeanUtil.mapList(entityPage.getRecords(), LineChangeCfgDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    @Override
    public LineChangeCfgDTO findById(Long id) {
        LineChangeCfg lineChangeCfg = this.getById(id);
        return BeanUtil.map(lineChangeCfg, LineChangeCfgDTO.class);
    }

    @Override
    public void delete(Long id) {
        LineChangeCfg lineChangeCfg = this.getById(id);
        if(Objects.isNull(lineChangeCfg)){
            throw new BusinessException(WpsExceptionEnum.RECORD_NOT_EXIST);
        }
        this.removeById(id);
        this.deleteCache(lineChangeCfg.getFactoryCode());
    }

    @Override
    public LineChangeCfgDTO getCacheLineChangeCfg(String factoryCode) {
        String key = String.format(LINE_CHANGE_CFG_CACHE,factoryCode);
        LineChangeCfgDTO lineChangeCfgDTO = RedisCacheUtil.valueObjGet(key, LineChangeCfgDTO.class);
        if (Objects.nonNull(lineChangeCfgDTO)) {
            return lineChangeCfgDTO;
        }
        LambdaQueryWrapper<LineChangeCfg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LineChangeCfg::getFactoryCode, factoryCode);
        List<LineChangeCfg> lineChangeCfgList = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(lineChangeCfgList)) {
            lineChangeCfgDTO = BeanUtil.map(lineChangeCfgList.get(0), LineChangeCfgDTO.class);
            RedisCacheUtil.valueObjSet(key, lineChangeCfgDTO, CACHE_TIME_OUT);
        }
        return lineChangeCfgDTO;
    }

    /**
     * 删除缓存
     *
     * @param factoryCode
     */

    private void deleteCache(String factoryCode) {
        String key = String.format(LINE_CHANGE_CFG_CACHE, factoryCode);
        RedisCacheUtil.delete(key);
    }

}
