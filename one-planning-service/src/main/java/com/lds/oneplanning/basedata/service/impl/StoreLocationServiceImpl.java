package com.lds.oneplanning.basedata.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iot.common.exception.BusinessException;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.StoreLocation;
import com.lds.oneplanning.basedata.mapper.StoreLocationMapper;
import com.lds.oneplanning.basedata.model.StoreLocationDTO;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.IStoreLocationService;
import com.lds.oneplanning.esb.datafetch.model.EsbData;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.mps.exception.MpsExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2024-09-27
 */
@Slf4j
@Service
public class StoreLocationServiceImpl extends ServiceImpl<StoreLocationMapper, StoreLocation> implements IStoreLocationService {
    @Resource
    private IEsbDataFetchService esbDataFetchService;
    @Resource
    private IFactoryService factoryService;

    private static final Integer BATCH_SIZE = 1000 ;

    @Override
    public void syncFromSap() {
        List<EsbData> sourceDatas = esbDataFetchService.fetchStoreLocationList();
        Set<String> uniqueCodes = sourceDatas.stream().map(EsbData::getUniqueCode).collect(Collectors.toSet());
        List<StoreLocation> insertList = Lists.newArrayList();
        List<StoreLocation> updateList = Lists.newArrayList();
        List<Long>  deleteIds = Lists.newArrayList();
        List<StoreLocation> endityList = baseMapper.selectList(null);
        List<StoreLocationDTO> existList = BeanUtil.mapList(endityList, StoreLocationDTO.class);
        Map<String, StoreLocationDTO> mapTarget = existList.stream().collect(Collectors.toMap(StoreLocationDTO::getUniqueCode, dto -> dto,(t, t2) -> t2));
        // 新增编辑收集
        sourceDatas.forEach(esbData -> {
            String uniqueCode = esbData.getUniqueCode();
            if (mapTarget.containsKey(uniqueCode)) {
                StoreLocationDTO updateTarget = mapTarget.get(uniqueCode);
                updateTarget.setFactoryUnitCode(esbData.getParentCode());
                updateTarget.setName(esbData.getName());
                updateTarget.setUpdateTime(new Date());
                updateList.add(BeanUtil.map(updateTarget, StoreLocation.class));
            }else {
                StoreLocation insert = new StoreLocation();
                insert.setName(esbData.getName());
                insert.setCode(esbData.getCode());
                insert.setFactoryUnitCode(esbData.getParentCode());
                insertList.add(insert);
            }
        });
        // 修改收集
        existList.forEach(dto -> {
            if (!uniqueCodes.contains(dto.getUniqueCode())) {
                deleteIds.add(dto.getId());
            }
        });
        // 删除进行
        if (!deleteIds.isEmpty()) {
            baseMapper.deleteBatchIds(deleteIds);
        }
        // 新增
        if (!insertList.isEmpty()) {
            this.saveBatch(insertList,BATCH_SIZE);
        }
        //编辑
        if (!updateList.isEmpty()) {
            this.updateBatchById(updateList,BATCH_SIZE);
        }
    }

    @Override
    public Page<StoreLocationDTO> page(String keyword, String factoryUnitCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<StoreLocation> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<StoreLocation> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like( StoreLocation::getName,keyword).or()
                    .like( StoreLocation::getCode,keyword));
        }
        queryWrapper.eq(StringUtils.isNotBlank(factoryUnitCode), StoreLocation::getFactoryUnitCode,factoryUnitCode);
        queryWrapper.orderByDesc( StoreLocation::getUpdateTime).orderByAsc(StoreLocation::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<StoreLocationDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<StoreLocationDTO> results = BeanUtil.mapList(entityPage.getRecords(),  StoreLocationDTO.class);
            this.decorate(results);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    @Override
    public StoreLocationDTO detail(Long id) {
         StoreLocation StoreLocation = baseMapper.selectById(id);
        if ( StoreLocation == null) {
            return null;
        }
         StoreLocationDTO res = BeanUtil.map(StoreLocation,  StoreLocationDTO.class);
        this.decorate(Lists.newArrayList(res));
        return  res;
    }

    @Override
    public Long add(StoreLocationDTO dto) {
         StoreLocation byCode = this.getByCodeAndLocation(dto.getFactoryUnitCode(),dto.getCode());
        if (byCode!=null) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
         StoreLocation entity = BeanUtil.map(dto,  StoreLocation.class);
        baseMapper.insert(entity);
        return entity.getId();
    }

    @Override
    public Integer edit(StoreLocationDTO dto) {
         StoreLocation byCode = this.getByCodeAndLocation(dto.getFactoryUnitCode(),dto.getCode());
        if (byCode!=null && !dto.getId().equals(byCode.getId())) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
         StoreLocation entity = BeanUtil.map(dto, StoreLocation.class);
        entity.setUpdateTime(new Date());
        return baseMapper.updateById(entity);
    }

    private StoreLocation getByCodeAndLocation(String factoryUnitCode, String code) {
        return baseMapper.selectOne(Wrappers.<StoreLocation>lambdaQuery()
                .eq( StoreLocation::getFactoryUnitCode,factoryUnitCode)
                .eq( StoreLocation::getCode,code)
        );
    }

    @Override
    public List<StoreLocationDTO> listByFactoryUnitCode(String factoryUnitCodes) {
        List<String> factoryUnitCodeList = StringUtils.isNotBlank(factoryUnitCodes) ? Arrays.asList(factoryUnitCodes.split(",")) : null;
        List<StoreLocation> entityList = baseMapper.selectList(Wrappers.<StoreLocation>lambdaQuery()
                .in(CollUtil.isNotEmpty(factoryUnitCodeList), StoreLocation::getFactoryUnitCode, factoryUnitCodeList)
        );
        return BeanUtil.mapList(entityList, StoreLocationDTO.class);
    }

    @Override
    public StoreLocationDTO getByCode(String factoryCode,String code) {
        StoreLocation entity = baseMapper.selectOne(Wrappers.<StoreLocation>lambdaQuery()
                        .eq(StoreLocation::getFactoryUnitCode,factoryCode)
                .eq(StoreLocation::getCode,code)
        );
        if (entity == null) {
            log.warn("不能根据工厂编码和库位编码找到对应的信息  factoryCode={} code={}",factoryCode,code);
            return null;
        }
        StoreLocationDTO res = BeanUtil.map(entity, StoreLocationDTO.class);
        decorate(Lists.newArrayList(res));
        return res;
    }

    @Override
    public Map<String, StoreLocation> getMapByCodes(List<String> storeLocationCodes) {
        if (CollectionUtils.isEmpty(storeLocationCodes)) {
            return Maps.newHashMap();
        }
        List<StoreLocation> entityList =  baseMapper.selectList(Wrappers.<StoreLocation>lambdaQuery()
                .in(StoreLocation::getCode,storeLocationCodes)
        );
        if (entityList != null && !entityList.isEmpty()) {
            return entityList.stream().collect(Collectors.toMap(StoreLocation::getCode, factoryUnit -> factoryUnit,(t, t2) -> t2));
        }
        return Maps.newHashMap();
    }

    private void decorate(List<StoreLocationDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        List<String> unitCodes = sourceList.stream().map( StoreLocationDTO::getFactoryUnitCode).distinct().collect(Collectors.toList());
        List<Factory> unitList = factoryService.listByFactoryCodes(unitCodes);
        Map<String, String> unitNameMap = unitList.stream().collect(Collectors.toMap(Factory::getCode, Factory::getName,(o, o2) -> o2));
          sourceList.forEach(dto -> {
            dto.setFactoryUnitName(unitNameMap.get(dto.getFactoryUnitCode()));
        });
    }
}
