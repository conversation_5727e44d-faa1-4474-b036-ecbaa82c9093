package com.lds.oneplanning.basedata.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.model.LineUphBatchDTO;
import com.lds.oneplanning.basedata.model.excel.LineInfoExcel;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.basedata.service.impl.ImportExportService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-19
 */
@Slf4j
@Api(value = "LineInfoController", tags = "产线信息管理")
@RestController
@RequestMapping("/basedata/lineInfo")
public class LineInfoController {


    @Resource
    private ILineInfoService lineInfoService;
    @Resource
    private ImportExportService importExportService;

    @ApiOperation(value = "根据当前用户进行过滤", notes = "根据当前用户进行过滤")
    @GetMapping("/listFilterByUser")
    public List<LineInfoDTO> list(@RequestParam(value = "factoryCode",required = false)String factoryCode,
                               @RequestParam(value = "workshopCode",required = false)String workshopCode){
        return  lineInfoService.listFilterByUser(UserContextUtils.getUserId(),factoryCode,workshopCode);
    }

    @ApiOperation(value = "获取产线列表-指定工厂或车间", notes = "获取产线列表-指定工厂或车间")
    @GetMapping("/list")
    public List<LineInfo> listByFactoryCode(@RequestParam(value = "factoryCode",required = false)String factoryCode,
                                            @RequestParam(value = "workshopCode",required = false)String workshopCode){
        return lineInfoService.list(Wrappers.<LineInfo>lambdaQuery()
                .eq(StringUtils.isNotBlank(factoryCode),LineInfo::getFactoryCode,factoryCode)
                .eq(StringUtils.isNotBlank(workshopCode),LineInfo::getWorkshopCode,workshopCode)
        );
    }


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<LineInfoDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                  @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                  @RequestParam(value = "workshopCode",required = false)String workshopCode,
                                  @RequestParam(value = "pageNum")Integer pageNum,
                                  @RequestParam(value = "pageSize")Integer pageSize
    ){
        return lineInfoService.page(keyword,factoryCode,workshopCode,pageNum,pageSize);
    }

    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public LineInfoDTO detail(@PathVariable("id")Long id){
        return  lineInfoService.detail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "线体信息",operation = "新增")
    public Long add(@RequestBody LineInfoDTO dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return  lineInfoService.add(dto);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "线体信息",operation = "编辑")
    public Integer edit(@RequestBody LineInfoDTO dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setId(id);
        return  lineInfoService.edit(dto);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "线体信息",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  lineInfoService.delete(id);
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "线体信息",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  lineInfoService.batchDelete(ids);
    }


    @ApiOperation(value = "批量设置线体关联", notes = "批量设置线体关联")
    @PostMapping("/batchBind")
    @Loggable(businessName = "线体信息",operation = "批量设置线体关联")
    public Integer batchBind(@RequestBody LineUphBatchDTO batchDTO ){
        return  lineInfoService.batchBind(batchDTO);
    }

    @ApiOperation(value = "导入-线体导入", notes = "导入-线体导入")
    @PostMapping("/import")
    @Loggable(businessName = "线体信息",operation = "导入")
    public Boolean  importExcel(@RequestParam("file") MultipartFile file) {
        List<LineInfoExcel> datas = importExportService.importData(file, LineInfoExcel.class);
        log.info("导入数据计划员线体关联：{}", datas);
        // STEP 2 数据组装
        List<LineInfo> targetList = Lists.newArrayList();
        Set<String> lineCodes = Sets.newHashSet();
        for (LineInfoExcel excel : datas){
            if (StringUtils.isBlank(excel.getLineUuid())) {
                continue;
            }
            LineInfo lineInfo = new LineInfo();
            lineInfo.setLineUuid(excel.getLineUuid());
            lineInfo.setCode(excel.getCode());
            lineInfo.setName(excel.getName());
            lineInfo.setVirtualStatus(excel.getVirtualStatus());
            lineInfo.setLineCategoryCode(excel.getLineCategoryCode());
            lineInfo.setWorkshopCode(excel.getWorkshopCode());
            lineInfo.setFactoryCode(excel.getFactoryCode());
            lineInfo.setCreateBy(UserContextUtils.getUserId());
            lineInfo.setCreateTime(new Date());
            lineInfo.setUpdateBy(UserContextUtils.getUserId());
            lineInfo.setUpdateTime(new Date());
            targetList.add(lineInfo);
            lineCodes.add(excel.getCode());
        }
        // STEP 3 数据删除和处理
        if (!lineCodes.isEmpty()) {
            lineInfoService.deleteByCodes(lineCodes);
        }
        if (targetList.isEmpty()) {
            return false;
        }
        return lineInfoService.saveBatch(targetList);
    }

    @ApiOperation(value = "导出-线体导出", notes = "导出-线体导出")
    @GetMapping("/export")
    @Loggable(businessName = "线体信息",operation = "导出")
    public ResponseEntity exportExcel(@RequestParam(value = "keyword",required = false)String keyword,
                                      @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                      @RequestParam(value = "workshopCode",required = false)String workshopCode){
        //请求参数
        log.info("线体数据导出请求参数：keyword={},factoryCode={}", keyword,factoryCode);

        // STEP 1 数据查询
        LambdaQueryWrapper<LineInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(factoryCode),LineInfo::getFactoryCode,factoryCode);
        queryWrapper.eq(StringUtils.isNotBlank(workshopCode),LineInfo::getWorkshopCode,workshopCode);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(LineInfo::getCode,keyword).or()
                    .like(LineInfo::getName,keyword));
        }
        queryWrapper.orderByDesc(LineInfo::getUpdateTime).orderByAsc(LineInfo::getId);
        List<LineInfo> dbList  = lineInfoService.list(queryWrapper);
        //STEP 2 对象转换
        List<LineInfoExcel> data = Lists.newArrayList();
        dbList.stream().filter(lineInfo -> StringUtils.isNotBlank(lineInfo.getLineUuid())).forEach(item -> {
            LineInfoExcel excel = new LineInfoExcel();
            excel.setLineUuid(item.getLineUuid());
            excel.setCode(item.getCode());
            excel.setName(item.getName());
            excel.setVirtualStatus(item.getVirtualStatus());
            excel.setLineCategoryCode(item.getLineCategoryCode());
            excel.setWorkshopCode(item.getWorkshopCode());
            excel.setFactoryCode(item.getFactoryCode());
            data.add(excel);
        });
        return importExportService.exportData(data, LineInfoExcel.class,"线体信息");
    }


    @ApiOperation(value = "同步Mes线体信息列表", notes = "同步Mes线体信息列表")
    @GetMapping("/sync")
    @Loggable(businessName = "线体信息",operation = "同步Mes线体信息列表")
    public Integer syncMesLineList(){
        return lineInfoService.syncMesLineList();
    }

}
