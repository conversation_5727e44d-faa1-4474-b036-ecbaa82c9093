package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.StoreLocation;
import com.lds.oneplanning.basedata.model.StoreLocationDTO;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2024-09-27
 */
public interface IStoreLocationService extends IService<StoreLocation> {

    @Async
    void syncFromSap();

    Page<StoreLocationDTO> page(String keyword, String factoryUnitCode, Integer pageNum, Integer pageSize);

    StoreLocationDTO detail(Long id);

    Long add(StoreLocationDTO dto);

    Integer edit(StoreLocationDTO dto);

    List<StoreLocationDTO> listByFactoryUnitCode(String factoryUnitCodes);

    StoreLocationDTO getByCode(String factoryCode,String code);

    /**
     * 根据库位编码获取库位信息
     * @param storeLocationCodes
     * @return
     */
    Map<String, StoreLocation> getMapByCodes(List<String> storeLocationCodes);


}
