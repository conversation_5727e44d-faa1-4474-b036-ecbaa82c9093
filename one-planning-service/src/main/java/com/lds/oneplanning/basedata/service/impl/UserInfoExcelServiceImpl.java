package com.lds.oneplanning.basedata.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lds.basic.account.user.dto.UserDto;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.StoreLocation;
import com.lds.oneplanning.basedata.entity.UserFactoryRel;
import com.lds.oneplanning.basedata.entity.UserInfo;
import com.lds.oneplanning.basedata.model.UserFactoryStoreLocationDTO;
import com.lds.oneplanning.basedata.model.excel.UserInfoExcel;
import com.lds.oneplanning.basedata.service.*;
import com.lds.oneplanning.common.service.IBasicUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserInfoExcelServiceImpl implements IUserInfoExcelService {

    private static final String EXPORT_USER_INFO_EXCEL_NAME = "用户身份信息";

    @Autowired
    private ImportExportService importExportService;

    @Autowired
    private IBasicUserService basicUserService;

    @Autowired
    private IStoreLocationService storeLocationService;

    @Autowired
    private IUserInfoService userInfoService;

    @Autowired
    private IUserFactoryRelService userFactoryRelService;

    @Autowired
    private IFactoryService factoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean importExcel(String userType, MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.warn("Empty or null Excel file provided for import");
            return true;
        }
        List<UserInfoExcel> userInfoExcelList = importExportService.importData(file, UserInfoExcel.class);
        if (CollUtil.isEmpty(userInfoExcelList)) {
            log.info("No data found in Excel file for import");
            return true;
        }
        try {
            return processUserData(userType, userInfoExcelList);
        } catch (Exception e) {
            log.error("Failed to process Excel import for user type: {}", userType, e);
            return false;
        }
    }

    @Override
    public ResponseEntity<byte[]> exportExcel(String keyword, String userType, String factoryCode) {
        List<UserInfoExcel> userInfoExcelList = Lists.newArrayList();
        try {
            List<UserInfo> userInfos = queryUsers(keyword, userType);
            if (CollUtil.isEmpty(userInfos)) {
                return exportEmptyResult(userInfoExcelList);
            }
            Map<Long, UserInfo> userInfoMap = userInfos.stream()
                    .collect(Collectors.toMap(UserInfo::getUserId, Function.identity()));
            List<UserFactoryRel> userFactoryRels = queryUserFactoryRelations(userInfos, factoryCode);
            if (CollUtil.isEmpty(userFactoryRels)) {
                return exportEmptyResult(userInfoExcelList);
            }
            populateExcelData(userInfoExcelList, userFactoryRels, userInfoMap);
            return importExportService.exportData(userInfoExcelList, UserInfoExcel.class, EXPORT_USER_INFO_EXCEL_NAME);
        } catch (Exception e) {
            log.error("Failed to export Excel for keyword: {}, userType: {}, factoryCode: {}",
                    keyword, userType, factoryCode, e);
            return exportEmptyResult(userInfoExcelList);
        }
    }

    private List<UserInfo> queryUsers(String keyword, String userType) {
        LambdaQueryWrapper<UserInfo> wrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(keyword)) {
            wrapper.and(w -> w.like(UserInfo::getUserName, keyword)
                    .or()
                    .like(UserInfo::getEmpNo, keyword));
        }
        if (StrUtil.isNotEmpty(userType)) {
            wrapper.eq(UserInfo::getUserType, userType);
        }
        return userInfoService.list(wrapper);
    }

    private List<UserFactoryRel> queryUserFactoryRelations(List<UserInfo> userInfos, String factoryCode) {
        List<Long> userIds = userInfos.stream()
                .map(UserInfo::getUserId)
                .distinct()
                .collect(Collectors.toList());
        LambdaQueryWrapper<UserFactoryRel> wrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(factoryCode)) {
            wrapper.eq(UserFactoryRel::getFactoryCode, factoryCode);
        }
        if (CollUtil.isNotEmpty(userIds)) {
            wrapper.in(UserFactoryRel::getUserId, userIds);
        }
        return userFactoryRelService.list(wrapper);
    }

    private void populateExcelData(List<UserInfoExcel> excelList,
                                   List<UserFactoryRel> userFactoryRels,
                                   Map<Long, UserInfo> userInfoMap) {
        Map<String, String> factoryNameMap = fetchFactoryNames(userFactoryRels);
        Map<String, StoreLocation> storeLocationMap = fetchStoreLocations(userFactoryRels);
        userFactoryRels.forEach(rel -> {
            UserInfo userInfo = userInfoMap.get(rel.getUserId());
            if (userInfo == null) {
                log.warn("User not found for ID: {}", rel.getUserId());
                return;
            }
            String storeLocationCodes = rel.getStoreLocationCodes();
            if (StrUtil.isNotEmpty(storeLocationCodes)) {
                Arrays.stream(storeLocationCodes.split(","))
                        .filter(Objects::nonNull)
                        .forEach(code -> excelList.add(createExcelRow(userInfo, rel, factoryNameMap,
                                storeLocationMap, code)));
            } else {
                excelList.add(createExcelRow(userInfo, rel, factoryNameMap, storeLocationMap, null));
            }
        });
    }

    private Map<String, String> fetchFactoryNames(List<UserFactoryRel> userFactoryRels) {
        List<String> factoryCodes = userFactoryRels.stream()
                .map(UserFactoryRel::getFactoryCode)
                .distinct()
                .collect(Collectors.toList());
        return factoryService.listByFactoryCodes(factoryCodes).stream()
                .collect(Collectors.toMap(Factory::getCode, Factory::getName));
    }

    private Map<String, StoreLocation> fetchStoreLocations(List<UserFactoryRel> userFactoryRels) {
        List<String> storeLocationCodes = userFactoryRels.stream()
                .map(UserFactoryRel::getStoreLocationCodes)
                .filter(Objects::nonNull)
                .flatMap(codes -> Arrays.stream(codes.split(",")))
                .distinct()
                .collect(Collectors.toList());
        return storeLocationService.getMapByCodes(storeLocationCodes);
    }

    private UserInfoExcel createExcelRow(UserInfo userInfo, UserFactoryRel rel,
                                         Map<String, String> factoryNameMap,
                                         Map<String, StoreLocation> storeLocationMap,
                                         String storeLocationCode) {
        UserInfoExcel excel = new UserInfoExcel();
        excel.setEmpNo(userInfo.getEmpNo());
        excel.setUserName(userInfo.getUserName());
        excel.setFactoryCode(rel.getFactoryCode());
        excel.setFactoryName(factoryNameMap.get(rel.getFactoryCode()));
        if (storeLocationCode != null) {
            excel.setStoreLocationCode(storeLocationCode);
            StoreLocation storeLocation = storeLocationMap.get(storeLocationCode);
            excel.setStoreLocationName(storeLocation != null ? storeLocation.getName() : "");
        }
        return excel;
    }

    private ResponseEntity<byte[]> exportEmptyResult(List<UserInfoExcel> excelList) {
        return importExportService.exportData(excelList, UserInfoExcel.class, EXPORT_USER_INFO_EXCEL_NAME);
    }

    private boolean processUserData(String userType, List<UserInfoExcel> userInfoExcelList) {
        List<String> empNoList = userInfoExcelList.stream()
                .map(UserInfoExcel::getEmpNo)
                .distinct()
                .collect(Collectors.toList());
        Map<String, UserDto> userDtoMap = basicUserService.batchGetUserInfoByJobNos(empNoList);
        List<UserInfo> existingUsers = userInfoService.list(Wrappers.<UserInfo>lambdaQuery()
                .in(UserInfo::getEmpNo, empNoList));
        // 处理用户信息
        List<UserInfo> newUsers = buildNewUsers(userInfoExcelList, existingUsers, userDtoMap, userType);
        if (!newUsers.isEmpty()) {
            userInfoService.saveBatch(newUsers);
        }
        // 处理用户-工厂关联关系
        List<UserFactoryStoreLocationDTO> factoryRelations = buildFactoryRelations(userInfoExcelList, userDtoMap);
        if (!factoryRelations.isEmpty()) {
            userFactoryRelService.batchSaveUserFactoryDtos(userType, factoryRelations);
        }
        return true;
    }

    private List<UserInfo> buildNewUsers(List<UserInfoExcel> userInfoExcelList,
                                         List<UserInfo> existingUsers,
                                         Map<String, UserDto> userDtoMap,
                                         String userType) {
        Set<String> existingEmpNos = existingUsers.stream()
                .map(UserInfo::getEmpNo)
                .collect(Collectors.toSet());
        Set<String> processedEmpNos = new LinkedHashSet<>();
        return userInfoExcelList.stream()
                .filter(excel -> {
                    String empNo = excel.getEmpNo();
                    return !existingEmpNos.contains(empNo) && processedEmpNos.add(empNo);
                })
                .map(excel -> createUserInfo(excel, userDtoMap.get(excel.getEmpNo()), userType))
                .collect(Collectors.toList());
    }

    private UserInfo createUserInfo(UserInfoExcel excel, UserDto userDto, String userType) {
        if (userDto == null) {
            log.error("User data not found for employee number: {}", excel.getEmpNo());
            return null;
        }
        UserInfo userInfo = new UserInfo();
        userInfo.setEmpNo(excel.getEmpNo());
        userInfo.setUserName(userDto.getName());
        userInfo.setUserType(userType);
        userInfo.setUserId(userDto.getId());
        userInfo.setCreateBy(UserContextUtils.getUserId());
        userInfo.setCreateTime(new Date());
        return userInfo;
    }

    private List<UserFactoryStoreLocationDTO> buildFactoryRelations(List<UserInfoExcel> userInfoExcelList,
                                                                    Map<String, UserDto> userDtoMap) {
        List<UserFactoryStoreLocationDTO> relations = Lists.newArrayList();
        for (UserInfoExcel excel : userInfoExcelList) {
            UserDto userDto = userDtoMap.get(excel.getEmpNo());
            if (userDto == null) {
                log.error("User data not found for factory relation, employee number: {}", excel.getEmpNo());
                continue;
            }
            UserFactoryStoreLocationDTO dto = new UserFactoryStoreLocationDTO();
            dto.setUserId(userDto.getId());
            dto.setUserName(userDto.getUserName());
            dto.setFactoryCode(excel.getFactoryCode());
            dto.setStoreLocationCode(excel.getStoreLocationCode());
            relations.add(dto);
        }
        return relations;
    }
}
