package com.lds.oneplanning.basedata.model;

import com.lds.oneplanning.basedata.model.base.ConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PlannerOrderCfgDTO", description="")
public class PlannerOrderCfgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划员工号")
    private String empNo;

    @ApiModelProperty(value = "计划员名称")
    private String userName;

    @ApiModelProperty(value = "订单类型大类")
    private List<ConfigDTO> orderTypeList;


    @ApiModelProperty(value = "生产订单子类型")
    private List<ConfigDTO> productOrderSubTypeList;

}
