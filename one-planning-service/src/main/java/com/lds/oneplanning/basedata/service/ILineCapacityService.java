package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.LineCapacity;
import com.lds.oneplanning.basedata.model.LineCapacityBatchDTO;
import com.lds.oneplanning.basedata.model.LineCapacityDTO;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface ILineCapacityService extends IService<LineCapacity> {
    void batchCreate(LineCapacityBatchDTO createDTO, Long userId);
    void batchUpdate(LineCapacityBatchDTO batchDTO, Long userId);
    Page<LineCapacityDTO> page(Long userId,String keyword, String factoryCode,String workshopCode,LocalDate startDate,LocalDate endDate,
                               Integer configType, String configCode,Integer dayType, Integer pageNum, Integer pageSize);


    Map<String,List<LineCapacity>> groupByTypeAndCodes(Integer configType, Collection<String> configCodes);

    LineCapacityDTO getDetail(Long id);

    Long add(LineCapacityDTO dto);

    Integer edit(LineCapacityDTO dto);

    LineCapacity getByCodeAndDate(Integer configType,String configCode,Integer dayType, LocalDate scheduleDate);

    Map<LocalDate, LineCapacity> getByCodeAndDates(Integer configType,String configCode,Integer dayType, List<LocalDate> scheduleDateList);

    Map<String, Map<LocalDate, WpsProductionLine>> getDailyProductionLineMap(Integer configType, List<String> lineUuids,Integer dayType,
                                                                             List<LocalDate> localDates);

    List<LineCapacity> listBylineUuid(String lineUUid,LocalDate startDate, LocalDate endDate);
    Map<String,List<LineCapacity>> groupByLineUuid(Collection<String> lineUUid,LocalDate startDate, LocalDate endDate);

    /**
     * 获取分组后每个线体每天的排产时长（白班、夜班合并）
     * @param configType
     * @param lineUuids
     * @param dayType
     * @param localDates
     * @return
     */
    Map<String, Map<LocalDate, WpsProductionLine>> getGroupDailyProductionLineMap(Integer configType, List<String> lineUuids,Integer dayType, List<LocalDate> localDates);
}
