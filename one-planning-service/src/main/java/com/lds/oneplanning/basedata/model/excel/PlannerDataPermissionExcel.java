package com.lds.oneplanning.basedata.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述：工厂Excel表格
 * 创建人： LaiGuiMing
 * 创建时间： 2025/2/21
 */
@Data
public class PlannerDataPermissionExcel {


    @ExcelProperty("计划员工号")
    @ApiModelProperty(value = "计划员工号")
    private String empNo;

    @ExcelProperty("车间编码")
    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    @ExcelProperty("产品组编码")
    @ApiModelProperty(value = "产品组编码")
    private String productGroupCode;

    @ExcelProperty("商品产品id")
    @ApiModelProperty(value = "商品产品id")
    private String commodityId;

}
