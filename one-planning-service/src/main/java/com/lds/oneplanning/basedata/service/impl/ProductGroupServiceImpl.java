package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.iot.common.exception.BusinessException;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.entity.ProductGroup;
import com.lds.oneplanning.basedata.entity.ProductGroupRel;
import com.lds.oneplanning.basedata.mapper.ProductGroupMapper;
import com.lds.oneplanning.basedata.model.ProductGroupDTO;
import com.lds.oneplanning.basedata.model.excel.ProductGroupExcel;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.basedata.service.IProductGroupRelService;
import com.lds.oneplanning.basedata.service.IProductGroupService;
import com.lds.oneplanning.mps.exception.MpsExceptionEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;



/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Service
public class ProductGroupServiceImpl extends ServiceImpl<ProductGroupMapper, ProductGroup> implements IProductGroupService {
    @Resource
    private IFactoryService factoryService;
    @Resource
    private IProductGroupRelService groupRelService;
    @Resource
    private IPlannerDataPermissionService plannerDataPermissionService;

    @Override
    public List<ProductGroupDTO> findList(Long userId,String keyword,String factoryCode) {
        LambdaQueryWrapper<ProductGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneBlank(factoryCode),ProductGroup::getFactoryCode,factoryCode);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(ProductGroup::getCode,keyword).or()
                    .like(ProductGroup::getName,keyword));
        }
        if (userId != null) {
            // 用户关联工厂，工厂关联产品组，用这个办法来过滤用户关联的产品组
             Set<String> factoryCodes =
                     plannerDataPermissionService.listByUserId(userId).stream().map(PlannerDataPermission::getFactoryCode).collect(Collectors.toSet());
            queryWrapper.in(CollectionUtils.isNotEmpty(factoryCodes),ProductGroup::getFactoryCode,factoryCodes);
        }
        List<ProductGroup> list = baseMapper.selectList(queryWrapper);
        List<ProductGroupDTO> resList = BeanUtil.mapList(list, ProductGroupDTO.class);
        this.decorate(resList);
        return resList;
    }

    private void decorate(List<ProductGroupDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> factoryCodes = sourceList.stream().map(ProductGroupDTO::getFactoryCode).collect(Collectors.toSet());
        List<Factory> factories = factoryService.listByFactoryCodes(factoryCodes);
        Map<String,String> factoryMap = factories.stream().collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));

        List<ProductGroupRel> relList= groupRelService.listByGroupCodes(sourceList.stream().map(ProductGroupDTO::getCode).collect(Collectors.toSet()));

        Map<String,List<ProductGroupRel>> mapGroup = relList.stream().collect(Collectors.groupingBy(ProductGroupRel::getProductGroupCode));
        sourceList.stream().forEach(dto -> {
            dto.setFactoryName(factoryMap.get(dto.getFactoryCode()));
            dto.setProductList(mapGroup.get(dto.getCode())!=null ?mapGroup.get(dto.getCode()) : Lists.newArrayList() );
        });
    }

    @Override
    public Page<ProductGroupDTO> page(String keyword, String factoryCode,String workshopCode,Collection<String> productIds, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ProductGroup> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ProductGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneBlank(factoryCode),ProductGroup::getFactoryCode,factoryCode);
        if (CollectionUtils.isNotEmpty(productIds)) {
            Set<String> productGroupCodes = groupRelService.listByProductIds(productIds).stream().map(ProductGroupRel::getProductGroupCode).collect(Collectors.toSet());
            if (productGroupCodes.isEmpty()) {
                // 一个编码都没有命中 则直接返回
                return  new Page<>(pageNum, pageSize);
            }
            queryWrapper.in(ProductGroup::getCode,productGroupCodes);
        }
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(ProductGroup::getCode,keyword).or()
                    .like(ProductGroup::getName,keyword));
        }
        queryWrapper.orderByDesc(ProductGroup::getUpdateTime).orderByAsc(ProductGroup::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<ProductGroupDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<ProductGroupDTO> results = BeanUtil.mapList(entityPage.getRecords(), ProductGroupDTO.class);
            this.decorate(results);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    @Override
    public List<ProductGroupDTO> listByKeyword(String keyword, String factoryCode, String workshopCode, Collection<String> productIds) {
        LambdaQueryWrapper<ProductGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneBlank(factoryCode),ProductGroup::getFactoryCode,factoryCode);
        if (CollectionUtils.isNotEmpty(productIds)) {
            Set<String> productGroupCodes = groupRelService.listByProductIds(productIds).stream().map(ProductGroupRel::getProductGroupCode).collect(Collectors.toSet());
            if (productGroupCodes.isEmpty()) {
                // 一个编码都没有命中 则直接返回
                return  Lists.newArrayList();
            }
            queryWrapper.in(ProductGroup::getCode,productGroupCodes);
        }
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(ProductGroup::getCode,keyword).or()
                    .like(ProductGroup::getName,keyword));
        }
        List<ProductGroup> productGroups = baseMapper.selectList(queryWrapper);
        List<ProductGroupDTO> results = BeanUtil.mapList(productGroups, ProductGroupDTO.class);
        this.decorate(results);
        return results;
    }

    @Override
    public ProductGroupDTO getDetail(Long id) {
        ProductGroup entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        ProductGroupDTO dto = BeanUtil.map(entity, ProductGroupDTO.class);
        this.decorate(Lists.newArrayList(dto));
        return dto;
    }

    @Override
    public Long add(ProductGroupDTO dto) {
        ProductGroup entity  = BeanUtil.map(dto,ProductGroup.class);
        ProductGroup byCode = this.getByCode(entity.getCode());
        if (byCode != null) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
        baseMapper.insert(entity);
        if (dto.getProductList()!=null && !dto.getProductList().isEmpty()) {
            dto.getProductList().stream().forEach(productGroupRel -> {
                productGroupRel.setProductGroupCode(entity.getCode());
            });
            List<String> productIds = dto.getProductList().stream().map(ProductGroupRel::getProductId).collect(Collectors.toList());
            groupRelService.deleteByGroupCode(dto.getCode());
            // 一个产品id只能属于一个产品组
            groupRelService.deleteByProductId(productIds);
            groupRelService.saveBatch(dto.getProductList());
        }
        return entity.getId();
    }

    private ProductGroup getByCode(String code){
        return  baseMapper.selectOne(Wrappers.<ProductGroup>lambdaQuery().eq(ProductGroup::getCode,code));
    }

    @Override
    public Integer edit(ProductGroupDTO dto) {
        ProductGroup entity  = BeanUtil.map(dto,ProductGroup.class);
        ProductGroup byCode = this.getByCode(entity.getCode());
        if (byCode != null && !byCode.getId().equals(dto.getId())) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
        Integer res =baseMapper.updateById(entity);
        if (dto.getProductList()!=null && !dto.getProductList().isEmpty()) {
            dto.getProductList().stream().forEach(productGroupRel -> {
                productGroupRel.setProductGroupCode(entity.getCode());
            });
            List<String> productIds = dto.getProductList().stream().map(ProductGroupRel::getProductId).collect(Collectors.toList());
            // 一个产品id只能属于一个产品组
            groupRelService.deleteByProductId(productIds);
            groupRelService.deleteByGroupCode(dto.getCode());
            groupRelService.saveBatch(dto.getProductList());
        }
        return res;
    }

    @Override
    public Integer delete(Long id) {
        ProductGroup productGroup = baseMapper.selectById(id);
        if (productGroup == null) {
            return 0;
        }
        groupRelService.deleteByGroupCode(productGroup.getCode());
        return baseMapper.deleteById(id);
    }

    @Override
    public Integer batchDelete(Collection<Long> ids) {
        ids.stream().forEach(id -> this.delete(id));
        return ids.size();
    }

    @Override
    public List<ProductGroupDTO> listByCodes(Collection<String> productCodes) {
        if (productCodes == null || productCodes.isEmpty()) {
            return Lists.newArrayList();
        }
         List<ProductGroup> entityList = baseMapper.selectList(Wrappers.<ProductGroup>lambdaQuery().in(ProductGroup::getCode,productCodes));
         List<ProductGroupDTO> resList = BeanUtil.mapList(entityList,ProductGroupDTO.class);
         this.decorate(resList);
         return resList;
    }

    @Override
    public Boolean importExcel(List<ProductGroupExcel> data) {
       Map<String,List<ProductGroupExcel>> groupMap = data.stream().collect(Collectors.groupingBy(ProductGroupExcel::getProductGroupCode));
        List<ProductGroup> groupInsertList = Lists.newArrayList();
        List<ProductGroupRel> relList = Lists.newArrayList();
       for (Map.Entry<String,List<ProductGroupExcel>> entry : groupMap.entrySet()){
           String groupCode = entry.getKey();
           List<ProductGroupExcel> productList =   entry.getValue();
           ProductGroupExcel template = productList.get(0);
           Set<String> productIds = productList.stream().map(ProductGroupExcel::getProductId).collect(Collectors.toSet());
           productIds.stream().filter(s -> StringUtils.isNotBlank(s)).forEach(productId -> {
               ProductGroupRel rel  = new ProductGroupRel();
               rel.setProductGroupCode(groupCode);
               rel.setProductId(productId);
               rel.setProductName(productId);
               rel.setCreateTime(new Date());
               rel.setUpdateTime(new Date());
               relList.add(rel);
           });
           ProductGroup  productGroup = new ProductGroup();
           productGroup.setCode(template.getProductGroupCode());
           productGroup.setName(template.getProductGroupName());
           productGroup.setFactoryCode(template.getFactoryCode());
           productGroup.setCreateTime(new Date());
           productGroup.setUpdateTime(new Date());
           groupInsertList.add(productGroup);
       }
       boolean resFlag  = true;
        if (!relList.isEmpty()) {
            Set<String> groupCodes = relList.stream().map(ProductGroupRel::getProductGroupCode).collect(Collectors.toSet());
            Set<String> productIds = relList.stream().map(ProductGroupRel::getProductId).collect(Collectors.toSet());
            groupRelService.deleteByGroupCodes(groupCodes);
            // 一个产品id只能属于一个产品组
            groupRelService.deleteByProductId(productIds);
            resFlag = resFlag && groupRelService.saveBatch(relList);
        }
        if (!groupInsertList.isEmpty()) {
            Set<String> groupCodes = groupInsertList.stream().map(ProductGroup::getCode).collect(Collectors.toSet());
            // 先根据编码清理，而后重新插入
            baseMapper.delete(Wrappers.<ProductGroup>lambdaQuery().in(ProductGroup::getCode,groupCodes));
            resFlag = resFlag && this.saveBatch(groupInsertList);
        }
        return resFlag;
    }

    @Override
    public List<ProductGroup> listByFactoryCodeAndCodes(String factoryCode, Collection<String> productCodes) {
        if (StringUtils.isBlank(factoryCode) || CollectionUtils.isEmpty(productCodes)) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<ProductGroup>lambdaQuery()
                .eq(ProductGroup::getFactoryCode, factoryCode)
                .in(ProductGroup::getCode, productCodes));
    }
}
