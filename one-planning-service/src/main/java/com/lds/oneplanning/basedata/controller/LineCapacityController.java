package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.model.LineCapacityBatchDTO;
import com.lds.oneplanning.basedata.model.LineCapacityDTO;
import com.lds.oneplanning.basedata.service.ILineCapacityService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Api(value = "MpsLineCategoryCapacityController", tags = "产线类别产线负载管理")
@RestController
@RequestMapping("/basedata/lineCapacity")
public class LineCapacityController {

    @Resource
    private ILineCapacityService lineCapacityService ;

    @ApiOperation(value = "批量新增", notes = "批量新增")
    @PostMapping("/batchCreate")
    @Loggable(businessName = "产线类别负载",operation = "批量新增")
    public void batchCreate(@RequestBody LineCapacityBatchDTO batchDTO ){
        lineCapacityService.batchCreate(batchDTO,UserContextUtils.getUserId());
    }

    @ApiOperation(value = "批量修改", notes = "批量修改")
    @PostMapping("/batchUpdate")
    @Loggable(businessName = "产线类别负载",operation = "批量修改")
    public void batchUpdate(@RequestBody LineCapacityBatchDTO batchDTO ){
        lineCapacityService.batchUpdate(batchDTO,UserContextUtils.getUserId());
    }


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<LineCapacityDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                      @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                      @RequestParam(value = "configType",defaultValue = "1")Integer configType,
                                      @RequestParam(value = "configCode",required = false)String configCode,
                                      @RequestParam(value = "workshopCode",required = false)String workshopCode,
                                      @RequestParam(value = "startDate",required = false) Date startDate,
                                      @RequestParam(value = "endDate",required = false)Date endDate,
                                      @RequestParam(value = "dayType",required = false,defaultValue = "1")Integer dayType,
                                      @RequestParam(value = "pageNum")Integer pageNum,
                                      @RequestParam(value = "pageSize")Integer pageSize
    ){
        return lineCapacityService.page(UserContextUtils.getUserId(),keyword,factoryCode,workshopCode,
                LocalDateTimeUtil.dateToLocalDate(startDate),LocalDateTimeUtil.dateToLocalDate(endDate),configType,configCode,dayType,pageNum,pageSize);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public LineCapacityDTO detail(@PathVariable("id")Long id){
        return  lineCapacityService.getDetail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "产线类别负载",operation = "新增")
    public Long add(@RequestBody LineCapacityDTO dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return lineCapacityService.add(dto);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "产线类别负载",operation = "编辑")
    public Integer edit(@RequestBody LineCapacityDTO dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  lineCapacityService.edit(dto);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "产线类别负载",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  lineCapacityService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "产线类别负载",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  lineCapacityService.removeByIds(ids) ? 1:0;
    }
}
