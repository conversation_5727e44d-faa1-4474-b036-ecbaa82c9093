package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.ProductGroup;
import com.lds.oneplanning.basedata.model.ProductGroupDTO;
import com.lds.oneplanning.basedata.model.excel.ProductGroupExcel;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
public interface IProductGroupService extends IService<ProductGroup> {
    List<ProductGroupDTO> findList(Long userId,String keyword,String factoryCode);
    Page<ProductGroupDTO> page(String keyword, String factoryCode,String workshopCode,Collection<String> productIds, Integer pageNum, Integer pageSize);
    List<ProductGroupDTO> listByKeyword(String keyword, String factoryCode,String workshopCode,Collection<String> productIds);

    ProductGroupDTO getDetail(Long id);

    Long add(ProductGroupDTO dto);

    Integer edit(ProductGroupDTO dto);

    Integer delete(Long id);

    Integer batchDelete(Collection<Long> ids);

    List<ProductGroupDTO> listByCodes(Collection<String> productCodes);

    Boolean importExcel(List<ProductGroupExcel> data);

    List<ProductGroup> listByFactoryCodeAndCodes(String factoryCode, Collection<String> productCodes);
}
