package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.UserInfo;
import com.lds.oneplanning.basedata.model.UserInfoDTO;
import com.lds.oneplanning.basedata.model.UserInfoQueryDTO;
import com.lds.oneplanning.wps.enums.ViewSource;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/5/16
 */
public interface IUserInfoService extends IService<UserInfo> {

    /**
     * 分页查询
     * @param queryDTO
     * @return
     */
    Page<UserInfoDTO> page(UserInfoQueryDTO queryDTO);

    /**
     * 条件查询
     * @param queryDTO
     * @return
     */
    List<UserInfoDTO> listByCondition(UserInfoQueryDTO queryDTO);

    /**
     * 获取当前用户类型
     * @param
     * @return
     */
    ViewSource getCurrentUserType();

    /**
     * 获取或默认用户类型
     *
     * @return {@link ViewSource }
     */
    ViewSource getOrDefaultUserType(ViewSource defaultType);

    /**
     *
     * @param id
     * @return
     */
    UserInfoDTO detail(Long id);

    /**
     * 新增
     * @param dto
     * @return
     */
    Long add(UserInfoDTO dto);

    /**
     * 编辑
     * @param dto
     * @return
     */
    void edit(UserInfoDTO dto);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    Integer batchDelete(Collection<Long> ids);

    /**
     * 根据工厂编码查询用户信息,key 为 工厂编码
     * @param factoryCodeList
     * @return
     */
    Map<String, List<UserInfoDTO>> listByFactoryCodes(Collection<String> factoryCodeList);
}
