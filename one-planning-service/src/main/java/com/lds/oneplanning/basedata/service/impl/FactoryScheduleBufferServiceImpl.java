package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.FactoryScheduleBuffer;
import com.lds.oneplanning.basedata.mapper.FactoryScheduleBufferMapper;
import com.lds.oneplanning.basedata.model.FactoryScheduleBufferDTO;
import com.lds.oneplanning.basedata.service.IFactoryScheduleBufferService;
import com.lds.oneplanning.basedata.service.IFactoryService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-14
 */
@Service
public class FactoryScheduleBufferServiceImpl extends ServiceImpl<FactoryScheduleBufferMapper, FactoryScheduleBuffer> implements IFactoryScheduleBufferService {

    @Resource
    private IFactoryService factoryService;

    @Override
    public Page<FactoryScheduleBufferDTO> page(String keyword, String factoryCode, Integer businessType, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<FactoryScheduleBuffer> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<FactoryScheduleBuffer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(factoryCode),FactoryScheduleBuffer::getFactoryCode,factoryCode);
        queryWrapper.eq(businessType !=null,FactoryScheduleBuffer::getBusinessType,businessType);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(FactoryScheduleBuffer::getFactoryCode,keyword).or()
                    .like(FactoryScheduleBuffer::getBusinessType,keyword));
        }
        queryWrapper.orderByDesc(FactoryScheduleBuffer::getUpdateTime).orderByAsc(FactoryScheduleBuffer::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<FactoryScheduleBufferDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<FactoryScheduleBufferDTO> results = BeanUtil.mapList(entityPage.getRecords(), FactoryScheduleBufferDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<FactoryScheduleBufferDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Map<String,String> factoryNameMap = factoryService.listByFactoryCodes(sourceList.stream().map(FactoryScheduleBufferDTO::getFactoryCode).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        sourceList.stream().forEach(bufferDTO -> bufferDTO.setFactoryName(factoryNameMap.get(bufferDTO.getFactoryCode())));
    }
    @Override
    public FactoryScheduleBufferDTO getDetail(Long id) {
        FactoryScheduleBuffer factoryScheduleBuffer = baseMapper.selectById(id);
        if (factoryScheduleBuffer == null) {
            return null;
        }
        FactoryScheduleBufferDTO bufferDTO = BeanUtil.map(factoryScheduleBuffer, FactoryScheduleBufferDTO.class);
        this.decorate(Lists.newArrayList(bufferDTO));
        return bufferDTO;
    }

    @Override
    public Map<Integer, Integer> getMapByFactoryCode(String factoryCode) {
        return Optional.ofNullable(factoryCode)
                .filter(StringUtils::isNotEmpty)
                .map(code -> this.baseMapper.selectList(
                        new LambdaQueryWrapper<FactoryScheduleBuffer>()
                                .eq(FactoryScheduleBuffer::getFactoryCode, code)
                ))
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .collect(Collectors.toMap(
                                FactoryScheduleBuffer::getBusinessType,
                                FactoryScheduleBuffer::getBufferDays,
                                (existing, replacement) -> existing // 处理重复键冲突
                        ))
                )
                .orElseGet(Maps::newHashMap);
    }
}
