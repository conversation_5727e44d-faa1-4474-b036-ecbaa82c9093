package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.ScheduleDateCfg;
import com.lds.oneplanning.basedata.model.ScheduleDateCfgDTO;
import com.lds.oneplanning.basedata.service.IScheduleDateCfgService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Api(value = "ScheduleDateCfgController", tags = "排产日期配置信息")
@RestController
@RequestMapping("/basedata/scheduleDateCfg")
public class ScheduleDateCfgController {

     @Resource
     private IScheduleDateCfgService  scheduleDateCfgService ;

    @ApiOperation(value = "获取排产日期配置列表", notes = "获取排产日期配置列表")
    @GetMapping("/list")
    public List<ScheduleDateCfg> listByFactoryProduct(@RequestParam(value = "factoryCode",required = false)String factoryCode,
                                      @RequestParam(value = "productGroupCode",required = false)String productGroupCode){
        return scheduleDateCfgService.findList(UserContextUtils.getUserId(),factoryCode,productGroupCode);
    }

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<ScheduleDateCfgDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                         @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                         @RequestParam(value = "productGroupCode",required = false)String productGroupCode,
                                         @RequestParam(value = "pageNum")Integer pageNum,
                                         @RequestParam(value = "pageSize")Integer pageSize
    ){
        return scheduleDateCfgService.page(UserContextUtils.getUserId(),keyword,factoryCode,productGroupCode,pageNum,pageSize);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public ScheduleDateCfgDTO detail(@PathVariable("id")Long id){
        return  scheduleDateCfgService.getDetail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "排产日期配置",operation = "新增")
    public Long add(@RequestBody ScheduleDateCfg dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        scheduleDateCfgService.save(dto);
        return dto.getId();
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "排产日期配置",operation = "编辑")
    public Integer edit(@RequestBody ScheduleDateCfg dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  scheduleDateCfgService.updateById(dto) ? 1 : 0;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "排产日期配置",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  scheduleDateCfgService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "排产日期配置",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  scheduleDateCfgService.removeByIds(ids) ? 1:0;
    }

}
