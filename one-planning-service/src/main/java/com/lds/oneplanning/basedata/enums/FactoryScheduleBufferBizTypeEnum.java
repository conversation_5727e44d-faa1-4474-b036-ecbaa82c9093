package com.lds.oneplanning.basedata.enums;

import lombok.Getter;

@Getter
public enum FactoryScheduleBufferBizTypeEnum {

    PACKAGE_PRINT(1, "包材无版面"),
    RISK_RESERVE(2, "风险备库"),
    INCOMPLETE_MATERIALS(3, "物料不齐套");

    private Integer type;
    private String description;

    FactoryScheduleBufferBizTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }
}