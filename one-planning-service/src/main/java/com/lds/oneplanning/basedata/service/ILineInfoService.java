package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.model.LineUphBatchDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-19
 */
public interface ILineInfoService extends IService<LineInfo> {

    Page<LineInfoDTO> page(String keyword, String factoryCode,String workshopCode, Integer pageNum, Integer pageSize);

   LineInfoDTO detail(Long id);

    Long add(LineInfoDTO dto);

    Integer edit(LineInfoDTO dto);

    Integer delete(Long id);
    Integer batchDelete(Collection<Long> ids);

    Map<String, List<LineInfo>> groupByFactoryCodes(Collection<String> factoryCodes);

    LineInfoDTO getByCode(String code,boolean decorateFlag);
    List<LineInfo> listByCodes(Collection<String> codes);
    List<LineInfoDTO> listDtoByCodes(Collection<String> codes);
    List<LineInfo> listByFactoryCode(String factoryCode);
    List<LineInfo> listByWorkshopCode(String workshopCode);

    List<LineInfo> listByWorkshopCodes(Collection<String> workshopCodes);

    Integer batchBind(LineUphBatchDTO batchDTO);

    Integer deleteByCodes(Collection<String> lineCodes);

    List<String> listAllLineCodes();

    Integer syncMesLineList();
    List<LineInfoDTO> listFilterByUser(Long userId, String factoryCode, String workshopCode);

    List<LineInfo> listByUuids(Collection<String> uuids);
}
