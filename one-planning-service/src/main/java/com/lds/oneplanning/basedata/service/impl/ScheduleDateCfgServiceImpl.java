package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.entity.ProductGroupRel;
import com.lds.oneplanning.basedata.entity.ScheduleDateCfg;
import com.lds.oneplanning.basedata.mapper.ScheduleDateCfgMapper;
import com.lds.oneplanning.basedata.model.ProductGroupDTO;
import com.lds.oneplanning.basedata.model.ScheduleDateCfgDTO;
import com.lds.oneplanning.basedata.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Service
public class ScheduleDateCfgServiceImpl extends ServiceImpl<ScheduleDateCfgMapper, ScheduleDateCfg> implements IScheduleDateCfgService {
    
     @Resource
     private IFactoryService factoryService;
     @Resource
     private IProductGroupService productGroupService;
     @Resource
     private IProductGroupRelService productGroupRelService;
     @Resource
     private IPlannerDataPermissionService plannerDataPermissionService;
    @Override
    public List<ScheduleDateCfg> findList(Long userId,String factoryCode, String productGroupCode) {
        Set<String> permissionFactoryCodes = plannerDataPermissionService.listByUserId(userId).stream().map(PlannerDataPermission::getFactoryCode).collect(Collectors.toSet());
        return baseMapper.selectList(Wrappers.<ScheduleDateCfg>lambdaQuery()
                .eq(StringUtils.isNoneBlank(factoryCode),ScheduleDateCfg::getFactoryCode, factoryCode)
                .eq(StringUtils.isNoneBlank(productGroupCode),ScheduleDateCfg::getProductGroupCode,productGroupCode).
                in(CollectionUtils.isNotEmpty(permissionFactoryCodes),ScheduleDateCfg::getFactoryCode,permissionFactoryCodes)
        );
    }
    @Override
    public List<ScheduleDateCfgDTO> listByFactoryCodes(Collection<String> factoryCodes) {
        if (factoryCodes == null || factoryCodes.isEmpty()) {
            return  Lists.newArrayList();
        }
        List<ScheduleDateCfg> entityList = baseMapper.selectList(Wrappers.<ScheduleDateCfg>lambdaQuery()
                .in(ScheduleDateCfg::getFactoryCode, factoryCodes)
        );
        List<ScheduleDateCfgDTO> res = BeanUtil.mapList(entityList,ScheduleDateCfgDTO.class);
        this.decorate(res);
        return res;
    }

    @Override
    public Page<ScheduleDateCfgDTO> page(Long userId,String keyword, String factoryCode, String productGroupCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ScheduleDateCfg> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ScheduleDateCfg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneBlank(factoryCode),ScheduleDateCfg::getFactoryCode,factoryCode);
        queryWrapper.eq(StringUtils.isNoneBlank(productGroupCode),ScheduleDateCfg::getProductGroupCode,productGroupCode);
        Set<String> permissionFactoryCodes = plannerDataPermissionService.listByUserId(userId).stream().map(PlannerDataPermission::getFactoryCode).collect(Collectors.toSet());
        queryWrapper.in(CollectionUtils.isNotEmpty(permissionFactoryCodes),ScheduleDateCfg::getFactoryCode,permissionFactoryCodes);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(ScheduleDateCfg::getFactoryCode,keyword).or()
                    .like(ScheduleDateCfg::getProductGroupCode,keyword));
        }
        queryWrapper.orderByDesc(ScheduleDateCfg::getUpdateTime).orderByAsc(ScheduleDateCfg::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<ScheduleDateCfgDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<ScheduleDateCfgDTO> results = BeanUtil.mapList(entityPage.getRecords(), ScheduleDateCfgDTO.class);
            this.decorate(results);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<ScheduleDateCfgDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> factoryCodes = sourceList.stream().map(ScheduleDateCfgDTO::getFactoryCode).collect(Collectors.toSet());
        List<Factory> factories = factoryService.listByFactoryCodes(factoryCodes);
        Map<String,String> factoryMap = factories.stream().collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        Set<String> productGroupCode =sourceList.stream().map(ScheduleDateCfgDTO::getProductGroupCode).collect(Collectors.toSet());

        Map<String,String> productMap = productGroupService.listByCodes(productGroupCode).stream().collect(Collectors.toMap(ProductGroupDTO::getCode, ProductGroupDTO::getName,(s, s2) -> s2));
        Map<String,List<ProductGroupRel>> relMap = productGroupRelService.listByGroupCodes(productGroupCode).stream().collect(Collectors.groupingBy(ProductGroupRel::getProductGroupCode));
        sourceList.stream().forEach(dto -> {
            dto.setFactoryName(factoryMap.get(dto.getFactoryCode()));
            dto.setProductGroupName(productMap.get(dto.getProductGroupCode()));
            if (relMap.containsKey(dto.getProductGroupCode())) {
                dto.getProductIds().addAll(relMap.get(dto.getProductGroupCode()).stream().map(ProductGroupRel::getProductId).collect(Collectors.toSet()));
            }
        });
    }
    @Override
    public ScheduleDateCfgDTO getDetail(Long id) {
        ScheduleDateCfg entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        ScheduleDateCfgDTO res = BeanUtil.map(entity, ScheduleDateCfgDTO.class);
        this.decorate(Lists.newArrayList(res));
        return res;
    }
}
