package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.model.StoreLocationDTO;
import com.lds.oneplanning.basedata.service.IStoreLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 库位配置
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Api(value = "wts库位配置", tags = "wts库位配置")
@RestController
@RequestMapping("/basedata/storeLocations")
public class StoreLocationController {

    @Resource
    private IStoreLocationService storeLocationService;

    @ApiOperation(value = "手动从sap同步", notes = "手动从sap同步库位数据")
    @GetMapping("/syncFromSap")
    public  void syncFromSap(){
          storeLocationService.syncFromSap();
    }

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<StoreLocationDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                       @RequestParam(value = "factoryBuildingCode",required = false)String factoryBuildingCode,
                                       @RequestParam(value = "factoryUnitCode",required = false)String factoryUnitCode,
                                       @RequestParam(value = "pageNum")Integer pageNum,
                                       @RequestParam(value = "pageSize")Integer pageSize
    ){
        return storeLocationService.page(keyword,factoryUnitCode,pageNum,pageSize);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public StoreLocationDTO detail(@PathVariable("id")Long id){
        return  storeLocationService.detail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    public Long add(@RequestBody StoreLocationDTO dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return  storeLocationService.add(dto);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    public Integer edit(@RequestBody StoreLocationDTO dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setId(id);
        return  storeLocationService.edit(dto);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    public Integer delete(@PathVariable("id")Long id ){
        return  storeLocationService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  storeLocationService.removeByIds(ids) ? 1:0;
    }

    @ApiOperation(value = "列表获取", notes = "列表获取")
    @GetMapping("/list")
    public List<StoreLocationDTO> list(
            @RequestParam(value = "factoryUnitCodes",required = false)String factoryUnitCodes
    ){
        return  storeLocationService.listByFactoryUnitCode(factoryUnitCodes);
    }

    @ApiOperation(value = "通过库位编码获取详情", notes = "通过库位编码获取详情")
    @GetMapping("/getByCode")
    public StoreLocationDTO getByCode(@RequestParam(value = "factoryCode",required = false)String factoryCode,@RequestParam(value = "storeCode",required = false)String storeCode){
        return  storeLocationService.getByCode(factoryCode,storeCode);
    }

}
