package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.iot.common.exception.BusinessException;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.MaterialGroup;
import com.lds.oneplanning.basedata.entity.MaterialGroupFactory;
import com.lds.oneplanning.basedata.entity.MaterialGroupRel;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.mapper.MaterialGroupMapper;
import com.lds.oneplanning.basedata.model.MaterialGroupDTO;
import com.lds.oneplanning.basedata.service.IMaterialGroupFactoryService;
import com.lds.oneplanning.basedata.service.IMaterialGroupRelService;
import com.lds.oneplanning.basedata.service.IMaterialGroupService;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.mps.exception.MpsExceptionEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
@Service
public class MaterialGroupServiceImpl extends ServiceImpl<MaterialGroupMapper, MaterialGroup> implements IMaterialGroupService {

    @Resource
    private IMaterialGroupRelService materialGroupRelService;
    @Resource
    private IMaterialGroupFactoryService materialGroupFactoryService;
    @Resource
    private IPlannerDataPermissionService plannerDataPermissionService;
    
    @Override
    public Page<MaterialGroupDTO> page(String keyword, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<MaterialGroup> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<MaterialGroup> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(MaterialGroup::getName,keyword).or()
                    .like(MaterialGroup::getCode,keyword).or()
                    .like(MaterialGroup::getName,keyword));
        }
        queryWrapper.orderByDesc(MaterialGroup::getUpdateTime).orderByAsc(MaterialGroup::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<MaterialGroupDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<MaterialGroupDTO> results = BeanUtil.mapList(entityPage.getRecords(), MaterialGroupDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<MaterialGroupDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> groupCodes = sourceList.stream().map(MaterialGroupDTO::getCode).collect(Collectors.toSet());
        List<MaterialGroupRel> materialGroups =  materialGroupRelService.listByGroupCodes(groupCodes);
        Map<String,List<MaterialGroupRel>> relMap = materialGroups.stream().collect(Collectors.groupingBy(MaterialGroupRel::getMaterialGroupCode));
        sourceList.stream().forEach(dto -> {
            // 设置编码
            if (CollectionUtils.isNotEmpty(relMap.get(dto.getCode()))) {
                dto.setMaterialCodes(relMap.get(dto.getCode()).stream().map(MaterialGroupRel::getMaterialCode).collect(Collectors.toList()));
            }
        });
    }
    @Override
    public MaterialGroupDTO getDetail(Long id) {
        MaterialGroup materialGroup = baseMapper.selectById(id);
        if (materialGroup == null) {
            return null;
        }
        MaterialGroupDTO res = BeanUtil.map(materialGroup, MaterialGroupDTO.class);
        this.decorate(Lists.newArrayList(res));
        return res;
    }

    @Override
    public Long add(MaterialGroupDTO dto) {
        MaterialGroup entity = BeanUtil.map(dto, MaterialGroup.class);
        MaterialGroup byCode = this.getByCode(dto.getCode());
        if (byCode != null) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
        // 主表记录插入
        baseMapper.insert(entity);
        materialGroupRelService.batchUpdateByGroupCode(dto.getCode(),dto.getMaterialCodes());
        return entity.getId() ;
    }

    @Override
    public Integer edit(MaterialGroupDTO dto) {
        MaterialGroup entity = BeanUtil.map(dto, MaterialGroup.class);
        MaterialGroup byCode = this.getByCode(dto.getCode());
        if (byCode != null && !byCode.getId().equals(dto.getId())) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
        // 主表记录插入
        Integer res = baseMapper.updateById(entity);
        materialGroupRelService.batchUpdateByGroupCode(dto.getCode(),dto.getMaterialCodes());
        return res;
    }
    private MaterialGroup getByCode(String code){
        return baseMapper.selectOne(Wrappers.<MaterialGroup>lambdaQuery().eq(MaterialGroup::getCode,code));
    }


    @Override
    public Integer deleteClean(Long id) {
        //
        MaterialGroup group = this.getById(id);
        materialGroupRelService.deleteByGroupCode(group.getCode());
        return baseMapper.deleteById(id);
    }

    @Override
    public Integer batchDelete(Collection<Long> ids) {
        ids.stream().forEach(this::deleteClean);
        return ids.size();
    }

    @Override
    public List<MaterialGroupDTO> listByFactoryCodes(Set<String> factoryCodes) {
        if (factoryCodes == null || factoryCodes.isEmpty()) {
            return  Lists.newArrayList();
        }
        List<MaterialGroupFactory> relList = materialGroupFactoryService.listByFactoryCodes(factoryCodes);
        Set<String> groupCodes = relList.stream().map(MaterialGroupFactory::getMaterialGroupCode).collect(Collectors.toSet());
        return this.listByCodes(groupCodes);
    }

    @Override
    public List<MaterialGroupDTO> listByCodes(Set<String> groupCodes) {
        if (groupCodes == null || groupCodes.isEmpty()) {
            return Lists.newArrayList();
        }
        List<MaterialGroup> entityList = baseMapper.selectList(Wrappers.<MaterialGroup>lambdaQuery().in(MaterialGroup::getCode,groupCodes));
        List<MaterialGroupDTO> resList = BeanUtil.mapList(entityList, MaterialGroupDTO.class);
        return resList;
    }

    @Override
    public List<MaterialGroupDTO> listByUserId(Long userId) {
       Set<String> factoryCodes  = plannerDataPermissionService.listByUserId(userId).stream().map(PlannerDataPermission::getFactoryCode).collect(Collectors.toSet());
       return this.listByFactoryCodes(factoryCodes);
    }
}
