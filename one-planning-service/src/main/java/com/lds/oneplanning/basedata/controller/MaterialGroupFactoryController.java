package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.model.FactoryDTO;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
@Slf4j
@Api(value = "MaterialGroupController", tags = "工厂物料组管理")
@RestController
@RequestMapping("/basedata/materialGroupFactory")
public class MaterialGroupFactoryController {

    @Resource
    private IFactoryFacadeService factoryFacadeService;

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<FactoryDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                 @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                 @RequestParam(value = "materialGroupCode",required = false)String materialGroupCode,
                                 @RequestParam(value = "pageNum")Integer pageNum,
                                 @RequestParam(value = "pageSize")Integer pageSize
    ){
        return factoryFacadeService.page(UserContextUtils.getUserId(),keyword,factoryCode,materialGroupCode,pageNum,pageSize,true);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public FactoryDTO detail(@PathVariable("id")Long id){
        return  factoryFacadeService.getDetail(id);
    }


    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "物料组",operation = "工厂物料组关联")
    public Integer edit(@RequestBody FactoryDTO dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  factoryFacadeService.edit(dto) ;
    }


}
