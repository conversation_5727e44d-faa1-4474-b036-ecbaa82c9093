package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.entity.PlannerFunPermission;
import com.lds.oneplanning.basedata.mapper.PlannerFunPermissionMapper;
import com.lds.oneplanning.basedata.service.IPlannerFunPermissionService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class PlannerFunPermissionServiceImpl extends ServiceImpl<PlannerFunPermissionMapper, PlannerFunPermission> implements IPlannerFunPermissionService {
    @Override
    public List<PlannerFunPermission> getByUserId(Long userId) {
         return baseMapper.selectList(Wrappers.<PlannerFunPermission>lambdaQuery()
                 .eq(PlannerFunPermission::getUserId,userId));
    }

    @Override
    public Map<Long, List<PlannerFunPermission>> groupByUserId(Collection<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Maps.newLinkedHashMap();
        }
        List<PlannerFunPermission> entityList = baseMapper.selectList(Wrappers.<PlannerFunPermission>lambdaQuery()
                .in(PlannerFunPermission::getUserId,userIds));
        return entityList.stream().collect(Collectors.groupingBy(PlannerFunPermission::getUserId));
    }

    @Override
    public void saveByUserId(List<PlannerFunPermission> funPermissions, Long userId) {
        // 先清空
         this.deleteByUserId(userId);
        if (CollectionUtils.isEmpty(funPermissions)) {
            return;
        }
         funPermissions.stream().forEach(PlannerFunPermission -> PlannerFunPermission.setUserId(userId));
         this.saveBatch(funPermissions);

    }

    @Override
    public void deleteByUserId(Long userId) {
        baseMapper.delete(Wrappers.<PlannerFunPermission>lambdaQuery().eq(PlannerFunPermission::getUserId,userId));
    }
}
