package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.basedata.entity.MaterialGroupRel;
import com.lds.oneplanning.basedata.mapper.MaterialGroupRelMapper;
import com.lds.oneplanning.basedata.service.IMaterialGroupRelService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
@Service
public class MaterialGroupRelServiceImpl extends ServiceImpl<MaterialGroupRelMapper, MaterialGroupRel> implements IMaterialGroupRelService {

    @Override
    public List<MaterialGroupRel> listByGroupCodes(Collection<String> groupCodes) {
        if (groupCodes == null || groupCodes.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<MaterialGroupRel>lambdaQuery().in(MaterialGroupRel::getMaterialGroupCode,groupCodes));
    }

    @Override
    public Integer batchUpdateByGroupCode(String groupCode, List<String> materialCodes) {
        if (StringUtils.isBlank(groupCode) || CollectionUtils.isEmpty(materialCodes)) {
            return 0;
        }
        // delete first by groupCode;
        this.deleteByGroupCode(groupCode);
        // batch insert new records
        List<MaterialGroupRel> targetList = Lists.newArrayList();
        materialCodes.stream().forEach(code ->{
            MaterialGroupRel rel = new MaterialGroupRel();
            rel.setMaterialGroupCode(groupCode);
            rel.setMaterialCode(code);
            rel.setCreateTime(new Date());
            rel.setUpdateTime(new Date());
            targetList.add(rel);
        } );
        return this.saveBatch(targetList) ? 1 : 0;
    }

    @Override
    public Integer deleteByGroupCode(String groupCode) {
       return this.baseMapper.delete(Wrappers.<MaterialGroupRel>lambdaQuery().eq(MaterialGroupRel::getMaterialGroupCode,groupCode));
    }
}
