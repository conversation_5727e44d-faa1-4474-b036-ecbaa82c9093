package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.basedata.entity.ProductGroupRel;
import com.lds.oneplanning.basedata.mapper.ProductGroupRelMapper;
import com.lds.oneplanning.basedata.service.IProductGroupRelService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Service
public class ProductGroupRelServiceImpl extends ServiceImpl<ProductGroupRelMapper, ProductGroupRel> implements IProductGroupRelService {

    @Override
    public List<ProductGroupRel> listByGroupCodes(Set<String> groupCodes) {
        return baseMapper.selectList(Wrappers.<ProductGroupRel>lambdaQuery().in(ProductGroupRel::getProductGroupCode,groupCodes));
    }

    @Override
    public Integer deleteByGroupCode(String groupCode) {
        return baseMapper.delete(Wrappers.<ProductGroupRel>lambdaQuery().eq(ProductGroupRel::getProductGroupCode,groupCode));
    }

    @Override
    public Integer deleteByGroupCodes(Collection<String> groupCodes) {
        if (groupCodes == null || groupCodes.isEmpty()) {
            return 0 ;
        }
        return baseMapper.delete(Wrappers.<ProductGroupRel>lambdaQuery().in(ProductGroupRel::getProductGroupCode,groupCodes));
    }

    @Override
    public List<ProductGroupRel> listByProductIds(Collection<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<ProductGroupRel>lambdaQuery().in(ProductGroupRel::getProductId, productIds));
    }

    @Override
    public Integer deleteByProductId(Collection<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return 0;
        }
        return baseMapper.delete(Wrappers.<ProductGroupRel>lambdaQuery().in(ProductGroupRel::getProductId, productIds));
    }
}
