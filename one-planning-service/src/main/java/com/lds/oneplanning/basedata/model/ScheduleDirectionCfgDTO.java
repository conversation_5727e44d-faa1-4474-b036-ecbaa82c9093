package com.lds.oneplanning.basedata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ScheduleDirectionCfg对象", description="")
public class ScheduleDirectionCfgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "1产品编码2产品id商品id 3工厂 4 客户组编码")
    private Integer configType;

    @ApiModelProperty(value = "对应具体的值")
    private String configValue;

    @ApiModelProperty(value = "排序方向 1 -1 -2")
    private Integer direction;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private String factoryName;
}
