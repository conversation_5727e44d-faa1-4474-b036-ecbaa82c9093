package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.model.PlannerBaseDTO;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Api(value = "PlannerBaseController", tags = "计划员信息")
@RestController
@RequestMapping("/basedata/planner")
public class PlannerBaseController {

    @Resource
    private IPlannerBaseService mpsPlannerBaseService;


    @ApiOperation(value = "列表查询", notes = "列表查询")
    @GetMapping("/list")
    public List<PlannerBaseDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                     @RequestParam(value = "factoryCode",required = false)String factoryCode
    ){
        return mpsPlannerBaseService.findList(keyword,factoryCode);
    }


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<PlannerBaseDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                     @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                     @RequestParam(value = "pageNum")Integer pageNum,
                                     @RequestParam(value = "pageSize")Integer pageSize
    ){
        return mpsPlannerBaseService.page(keyword,factoryCode,pageNum,pageSize);
    }

    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public PlannerBaseDTO detail(@PathVariable("id")Long id){
        return  mpsPlannerBaseService.detail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "计划员",operation = "新增")
    public Long add(@RequestBody PlannerBaseDTO dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return  mpsPlannerBaseService.add(dto);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "计划员",operation = "编辑")
    public Integer edit(@RequestBody PlannerBaseDTO dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setId(id);
        return  mpsPlannerBaseService.edit(dto);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "计划员",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  mpsPlannerBaseService.delete(id);
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "计划员",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  mpsPlannerBaseService.batchDelete(ids);
    }
    

}
