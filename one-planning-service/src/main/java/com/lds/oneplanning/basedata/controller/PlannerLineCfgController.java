package com.lds.oneplanning.basedata.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.entity.PlannerLineCfg;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.model.PlannerBaseDTO;
import com.lds.oneplanning.basedata.model.PlannerLineCfgDTO;
import com.lds.oneplanning.basedata.model.excel.PlannerLineCfgExcel;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.basedata.service.IPlannerLineCfgService;
import com.lds.oneplanning.basedata.service.impl.ImportExportService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;






/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Slf4j
@Api(value = "PlannerLineCfgController", tags = "计划员线体关联配置")
@RestController
@RequestMapping("/basedata/plannerLineCfg")
public class PlannerLineCfgController {


    @Resource
    private IPlannerLineCfgService plannerLineCfgService;
    @Resource
    private ImportExportService importExportService;
    @Resource
    private ILineInfoService lineInfoService;
    @Resource
    private IPlannerBaseService plannerBaseService;


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<PlannerLineCfgDTO> page(@RequestParam(value = "userId") Long  userId,
                                        @RequestParam(value = "configType",defaultValue = "2") Integer  configType,
                                        @RequestParam(value = "pageNum")Integer pageNum,
                                        @RequestParam(value = "pageSize")Integer pageSize
    ){
        return plannerLineCfgService.page(userId,configType,pageNum,pageSize);
    }

    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public PlannerLineCfgDTO detail(@PathVariable("id")Long id){
        return  plannerLineCfgService.getDetail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add/{userId}")
    @Loggable(businessName = "计划员线体配置",operation = "新增")
    public Long add(@RequestBody PlannerLineCfg dto,@PathVariable("userId")Long userId ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        if (dto.getConfigType() == null) {
            dto.setConfigType(BaseDataConstant.CONFIG_TYPE_LINE);
        }
        LineInfoDTO lineInfoDTO = lineInfoService.getByCode(dto.getConfigCode(),false);
        if (lineInfoDTO != null) {
            dto.setWorkshopCode(lineInfoDTO.getCode());
            dto.setFactoryCode(lineInfoDTO.getFactoryCode());
            dto.setLineUuid(lineInfoDTO.getLineUuid());
        }
       //  todo 线体类的有需要扩展
        dto.setUserId(userId);
        plannerLineCfgService.save(dto);
        return dto.getId();
    }

    @ApiOperation(value = "批量新增计划员线体配置", notes = "批量新增计划员线体配置")
    @PostMapping("/batchAdd/{userId}")
    @Loggable(businessName = "计划员线体配置",operation = "新增")
    public boolean batchAdd(@RequestBody PlannerLineCfgDTO dto,@PathVariable("userId")Long userId ){
        List<LineInfo> lineInfoDTOs = lineInfoService.listByCodes(dto.getConfigCodes());
        List<PlannerLineCfg> insertList = Lists.newArrayList();
        lineInfoDTOs.stream().forEach(lineInfo -> {
            PlannerLineCfg cfg = new PlannerLineCfg();
            cfg.setUserId(userId);
            cfg.setLineUuid(lineInfo.getLineUuid());
            cfg.setConfigType(BaseDataConstant.CONFIG_TYPE_LINE);
            cfg.setConfigCode(lineInfo.getCode());
            cfg.setWorkshopCode(lineInfo.getWorkshopCode());
            cfg.setFactoryCode(lineInfo.getFactoryCode());
            cfg.setSbu(lineInfo.getSbu());
            cfg.setCreateBy(UserContextUtils.getUserId());
            cfg.setCreateTime(new Date());
            cfg.setUpdateBy(UserContextUtils.getUserId());
            cfg.setUpdateTime(new Date());
            insertList.add(cfg);
        });
        if (CollectionUtils.isNotEmpty(dto.getConfigCodes())) {
            plannerLineCfgService.remove(Wrappers.<PlannerLineCfg>lambdaQuery().eq(PlannerLineCfg::getUserId,userId)
                    .in(PlannerLineCfg::getConfigCode,dto.getConfigCodes()));
        }
        return plannerLineCfgService.saveBatch(insertList);
    }


    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{userId}")
    @Loggable(businessName = "计划员线体配置",operation = "编辑")
    public Integer edit(@RequestBody PlannerLineCfg dto, @PathVariable("userId")Long userId ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        if (dto.getConfigType() == null) {
            dto.setConfigType(BaseDataConstant.CONFIG_TYPE_LINE);
        }
        LineInfoDTO lineInfoDTO = lineInfoService.getByCode(dto.getConfigCode(),false);
        if (lineInfoDTO != null) {
            dto.setWorkshopCode(lineInfoDTO.getCode());
            dto.setFactoryCode(lineInfoDTO.getFactoryCode());
            dto.setLineUuid(lineInfoDTO.getLineUuid());
        }
        //  todo 线体类的有需要扩展
        dto.setUserId(userId);
        return  plannerLineCfgService.updateById(dto) ? 1 : 0;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "计划员线体配置",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  plannerLineCfgService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "计划员线体配置",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  plannerLineCfgService.removeByIds(ids) ? 1:0;
    }


    @ApiOperation(value = "导入-线体导入", notes = "导入-线体导入")
    @PostMapping("/import")
    @Loggable(businessName = "计划员线体配置",operation = "导入")
    public Boolean  importExcel(@RequestParam("file") MultipartFile file) {
        List<PlannerLineCfgExcel> datas = importExportService.importData(file, PlannerLineCfgExcel.class);
        log.info("导入数据计划员线体关联：{}", datas);
        datas = datas.stream().filter(plannerLineCfgExcel -> StringUtils.isNotBlank(plannerLineCfgExcel.getCode()) &&
                StringUtils.isNotBlank(plannerLineCfgExcel.getEmpNo()))
                .collect(Collectors.toList());
        // 进行一次trim
        datas.stream().forEach(cfgExcel -> {
            cfgExcel.setCode(cfgExcel.getCode().trim());
            cfgExcel.setEmpNo(cfgExcel.getEmpNo().trim());
        });
        // STEP 1  计划员数据查询
        List<PlannerBaseDTO> userList = plannerBaseService.findList(null, null);
        Map<String,Long> plannerEmpNoIdMap = userList.stream().filter(plannerBaseDTO -> plannerBaseDTO.getEmpNo()!=null)
                .collect(Collectors.toMap(PlannerBaseDTO::getEmpNo,PlannerBaseDTO::getUserId,(aLong, aLong2) -> aLong2));
        // STEP 2 数据组装
        List<PlannerLineCfg> targetList = Lists.newArrayList();
        Set<String> lineCodes = datas.stream().map(PlannerLineCfgExcel::getCode).collect(Collectors.toSet());
       List<LineInfo> lineInfos= lineInfoService.listByCodes(lineCodes);
        // 防止有些数线体没有设置工厂
        lineInfos.stream().forEach(lineInfo -> lineInfo.setFactoryCode(lineInfo.getFactoryCode()==null?"":lineInfo.getFactoryCode()));
        Map<String,String> factoryMap =lineInfos.stream().collect(Collectors.toMap(LineInfo::getCode,LineInfo::getFactoryCode,(s, s2) -> s2));
        Map<String,String> workshopMap =lineInfos.stream().collect(Collectors.toMap(LineInfo::getCode,LineInfo::getWorkshopCode,(s, s2) -> s2));
        Map<String,String> uuidMap =lineInfos.stream().collect(Collectors.toMap(LineInfo::getCode,LineInfo::getLineUuid,(s, s2) -> s2));
        Set<Long> userIds = Sets.newHashSet();
        for (PlannerLineCfgExcel excel : datas){
            // 正常取线体关联的工厂车间即可
            String factoryCode= Optional.ofNullable(factoryMap.get(excel.getCode())).orElse(excel.getFactoryCode());
            String workshopCode= Optional.ofNullable(workshopMap.get(excel.getCode())).orElse(excel.getWorkshopCode());

            PlannerLineCfg cfg = new PlannerLineCfg();
            cfg.setUserId(plannerEmpNoIdMap.get(excel.getEmpNo()));
            if (cfg.getUserId() == null) {
                // 不存在的工号跳过
                continue;
            }
            cfg.setLineUuid(uuidMap.get(excel.getCode()));
            cfg.setConfigType(BaseDataConstant.CONFIG_TYPE_LINE);
            cfg.setConfigCode(excel.getCode());
            cfg.setFactoryCode(factoryCode);
            cfg.setWorkshopCode(workshopCode);
            cfg.setCreateBy(UserContextUtils.getUserId());
            cfg.setCreateTime(new Date());
            cfg.setUpdateBy(UserContextUtils.getExternalUserId());
            cfg.setUpdateTime(new Date());
            targetList.add(cfg);
            userIds.add(cfg.getUserId());
        }
        // STEP 3 数据删除和处理
        if (!userIds.isEmpty()) {
            plannerLineCfgService.deleteByUserIds(userIds,BaseDataConstant.CONFIG_TYPE_LINE);
        }
        if (targetList.isEmpty()) {
            return false;
        }
        return plannerLineCfgService.saveBatch(targetList);
    }

    @ApiOperation(value = "导出-线体导出", notes = "导出-线体导出")
    @GetMapping("/export")
    @Loggable(businessName = "计划员线体配置",operation = "导出")
    public ResponseEntity exportExcel(@RequestParam(value = "keyword",required = false)String keyword,
                                      @RequestParam(value = "factoryCode",required = false)String factoryCode){
        //请求参数
        log.info("计划员关联线体导出请求参数：keyword={},factoryCode={}", keyword,factoryCode);
        // STEP 1  计划员数据
        List<PlannerBaseDTO> userList = plannerBaseService.findList(keyword, factoryCode);
        Set<Long> userIds = userList.stream().map(PlannerBaseDTO::getUserId).collect(Collectors.toSet());
        Map<Long,String> plannerIdEmpNoMap = userList.stream().filter(plannerBaseDTO -> plannerBaseDTO.getEmpNo()!=null)
                .collect(Collectors.toMap(PlannerBaseDTO::getUserId,PlannerBaseDTO::getEmpNo,(s, s2) -> s2));
        Map<Long,String> plannerIdEmpNameMap = userList.stream().filter(plannerBaseDTO -> plannerBaseDTO.getEmpNo()!=null)
                .collect(Collectors.toMap(PlannerBaseDTO::getUserId,PlannerBaseDTO::getUserName,(s, s2) -> s2));


        // STEP 2 计划员关联线体信息
        LambdaQueryWrapper<PlannerLineCfg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(factoryCode),PlannerLineCfg::getFactoryCode,factoryCode);
        queryWrapper.eq(PlannerLineCfg::getConfigType,BaseDataConstant.CONFIG_TYPE_LINE);
        queryWrapper.in(CollectionUtils.isNotEmpty(userIds),PlannerLineCfg::getUserId,userIds);
        queryWrapper.orderByDesc(PlannerLineCfg::getUserId,PlannerLineCfg::getId);
        List<PlannerLineCfg> cfgList  = plannerLineCfgService.list(queryWrapper);

        List<LineInfoDTO> lineInfos= lineInfoService.listDtoByCodes(cfgList.stream().map(PlannerLineCfg::getConfigCode).collect(Collectors.toSet()));
        Map<String,LineInfoDTO> lineInfoDTOMap = lineInfos.stream().collect(Collectors.toMap(LineInfoDTO::getCode,lineInfoDTO -> lineInfoDTO,(t, t2) -> t2));
        //STEP 3 对象转换
        List<PlannerLineCfgExcel> data = Lists.newArrayList();
        cfgList.forEach(item -> {
            PlannerLineCfgExcel excel = new PlannerLineCfgExcel();
            excel.setEmpNo(plannerIdEmpNoMap.get(item.getUserId()));
            excel.setCode(item.getConfigCode());
            excel.setEmpName(plannerIdEmpNameMap.get(item.getUserId()));
            LineInfoDTO lineInfoDTO = lineInfoDTOMap.get(item.getConfigCode());
            if (lineInfoDTO != null) {
                excel.setFactoryCode(lineInfoDTO.getFactoryCode());
                excel.setWorkshopCode(lineInfoDTO.getWorkshopCode());
                excel.setName(lineInfoDTO.getName());
            }
            data.add(excel);
        });
        return importExportService.exportData(data, PlannerLineCfgExcel.class,"计划员关联线体");
    }
}
