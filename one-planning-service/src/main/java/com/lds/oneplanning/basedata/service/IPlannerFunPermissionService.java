package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.basedata.entity.PlannerFunPermission;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface IPlannerFunPermissionService extends IService<PlannerFunPermission> {

    List<PlannerFunPermission> getByUserId(Long userId);
    Map<Long,List<PlannerFunPermission>> groupByUserId(Collection<Long> userIds);
    void saveByUserId(List<PlannerFunPermission> funPermissions, Long userId);

    void deleteByUserId(Long userId);
}
