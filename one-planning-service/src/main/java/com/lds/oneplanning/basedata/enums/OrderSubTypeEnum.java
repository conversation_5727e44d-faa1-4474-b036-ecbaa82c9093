package com.lds.oneplanning.basedata.enums;

/**
 * 订单子类型枚举
 */
public enum OrderSubTypeEnum {
    STD("ZP01", "标准生产订单"),
    REWORK("ZP02", "返工订单"),
    IMPROVE("ZP03", "改制订单"),
    EP_SAMPLE("ZP04", "EP试制订单"),
    PP_SAMPLE("ZP05", "PP试产订单"),
    REPAIR("ZP06", "模具/设备维修订单"),
    DISASSEMBLY("ZP07", "拆解订单"),
    ENTRUSTED("ZP12", "受托加工生产"),
    OEM("ZP13", "成品外发（联合制造）");

    private String code;
    private String name;

    OrderSubTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code){
        for (OrderSubTypeEnum typeEnum : OrderSubTypeEnum.values()){
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getName();
            }
        }
        return null;
    }

}