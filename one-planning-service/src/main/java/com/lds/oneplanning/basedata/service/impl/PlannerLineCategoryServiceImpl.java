package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.PlannerLineCategory;
import com.lds.oneplanning.basedata.mapper.PlannerLineCategoryMapper;
import com.lds.oneplanning.basedata.model.PlannerLineCategoryDTO;
import com.lds.oneplanning.basedata.service.IPlannerLineCategoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class PlannerLineCategoryServiceImpl extends ServiceImpl<PlannerLineCategoryMapper, PlannerLineCategory> implements IPlannerLineCategoryService {
    @Override
    public List<PlannerLineCategoryDTO> listByUserId(Long userId) {
        List<PlannerLineCategory> entityList = baseMapper.selectList(Wrappers.<PlannerLineCategory>lambdaQuery().eq(PlannerLineCategory::getUserId,userId));
        return BeanUtil.mapList(entityList, PlannerLineCategoryDTO.class);
    }

    @Override
    public void saveByUserId(List<PlannerLineCategoryDTO> lineCategoryList, Long userId) {
        // 先清空
        this.deleteByUserId(userId);
        if (CollectionUtils.isEmpty(lineCategoryList)) {
            return;
        }
        //再保存
        lineCategoryList.stream().forEach(entity -> entity.setUserId(userId));
        List<PlannerLineCategory> entityList = BeanUtil.mapList(lineCategoryList,PlannerLineCategory.class);
        this.saveBatch(entityList);
    }

    @Override
    public void deleteByUserId(Long userId) {
        baseMapper.delete(Wrappers.<PlannerLineCategory>lambdaQuery().eq(PlannerLineCategory::getUserId,userId));
    }
}
