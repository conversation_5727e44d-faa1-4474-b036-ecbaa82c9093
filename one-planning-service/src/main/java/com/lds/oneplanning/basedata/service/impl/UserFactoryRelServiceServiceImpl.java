package com.lds.oneplanning.basedata.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.UserFactoryRel;
import com.lds.oneplanning.basedata.mapper.UserFactoryRelMapper;
import com.lds.oneplanning.basedata.model.UserFactoryRelDTO;
import com.lds.oneplanning.basedata.model.UserFactoryStoreLocationDTO;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.IUserFactoryRelService;
import com.lds.oneplanning.wps.enums.ViewSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/17
 */
@Service
public class UserFactoryRelServiceServiceImpl extends ServiceImpl<UserFactoryRelMapper, UserFactoryRel> implements IUserFactoryRelService {

    @Resource
    private IFactoryService factoryService;

    @Override
    public void save(List<UserFactoryRelDTO> userFactoryList, Long userId) {
        baseMapper.delete(Wrappers.<UserFactoryRel>lambdaQuery().eq(UserFactoryRel::getUserId, userId));
        if (CollUtil.isEmpty(userFactoryList)) {
            return;
        }
        List<UserFactoryRel> saveEntityList = BeanUtil.copyToList(userFactoryList, UserFactoryRel.class);
        saveEntityList.forEach(item -> {
            item.setUserId(userId);
            item.setUpdateBy(UserContextUtils.getUserId());
            item.setCreateBy(UserContextUtils.getUserId());
        });
        saveBatch(saveEntityList);
    }

    @Override
    public Map<Long, List<UserFactoryRelDTO>> findMapping(List<Long> userIdList) {
        List<UserFactoryRel> list = baseMapper.selectList(Wrappers.<UserFactoryRel>lambdaQuery()
                .in(UserFactoryRel::getUserId, userIdList));
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        List<String> factoryCodeList = list.stream().map(UserFactoryRel::getFactoryCode).distinct().collect(Collectors.toList());
        Map<String, String> factoryMap = factoryService.listByFactoryCodes(factoryCodeList).stream().collect(
                Collectors.toMap(Factory::getCode, Factory::getName, (o, o2) -> o2)
        );
        List<UserFactoryRelDTO> resultList = BeanUtil.copyToList(list, UserFactoryRelDTO.class);
        return resultList.stream().peek(item -> {
            item.setFactoryName(factoryMap.get(item.getFactoryCode()));
        }).collect(Collectors.groupingBy(UserFactoryRelDTO::getUserId));

    }

    @Override
    public void deleteByUserId(Collection<Long> userIdList) {
        baseMapper.delete(Wrappers.<UserFactoryRel>lambdaQuery().in(UserFactoryRel::getUserId, userIdList));
    }

    @Override
    public void batchSaveUserFactoryDtos(String userType, List<UserFactoryStoreLocationDTO> dtos) {
        if (StrUtil.isEmpty(userType) || CollUtil.isEmpty(dtos)) {
            return;
        }
        if (ViewSource.IQC.name().equals(userType)) {
            processIqcUserFactoryRelations(dtos);
        } else {
            processStandardUserFactoryRelations(dtos);
        }
    }

    /**
     * 处理IQC用户工厂仓库关系
     *
     * @param dtos
     */
    private void processIqcUserFactoryRelations(List<UserFactoryStoreLocationDTO> dtos) {
        // 按工厂+仓库维度处理
        Map<String, Map<String, List<UserFactoryStoreLocationDTO>>> factoryStoreMap = dtos.stream()
                .collect(Collectors.groupingBy(
                        UserFactoryStoreLocationDTO::getFactoryCode,
                        Collectors.groupingBy(
                                UserFactoryStoreLocationDTO::getStoreLocationCode,
                                Collectors.toList()
                        )
                ));
        factoryStoreMap.forEach((factoryCode, storeLocationMap) ->
                storeLocationMap.forEach((storeLocationCode, dtoList) ->
                        processFactoryStoreLocation(factoryCode, storeLocationCode, dtoList)));
    }

    /**
     * 处理非IQC用户工厂仓库关系
     * @param dtos
     */
    private void processStandardUserFactoryRelations(List<UserFactoryStoreLocationDTO> dtos) {
        Map<String, List<UserFactoryStoreLocationDTO>> factoryMap = dtos.stream()
                .collect(Collectors.groupingBy(UserFactoryStoreLocationDTO::getFactoryCode));
        factoryMap.forEach(this::processFactory);
    }

    private void processFactoryStoreLocation(String factoryCode, String storeLocationCode,
                                             List<UserFactoryStoreLocationDTO> dtos) {
        if (CollUtil.isEmpty(dtos)) {
            return;
        }
        List<Long> userIds = dtos.stream()
                .map(UserFactoryStoreLocationDTO::getUserId)
                .distinct().collect(Collectors.toList());
        List<UserFactoryRel> existingRelations = list(createFactoryQuery(factoryCode));
        List<UserFactoryRel> toUpdate = Lists.newArrayList();
        Long currentUserId = UserContextUtils.getUserId();
        Date currentTime = new Date();
        existingRelations.forEach(rel -> {
            Long userId = rel.getUserId();
            if (!userIds.remove(userId)) {
                return;
            }
            updateStoreLocationCodes(rel, storeLocationCode, currentUserId, currentTime);
            toUpdate.add(rel);
        });
        if (!toUpdate.isEmpty()) {
            updateBatchById(toUpdate);
        }
        if (!userIds.isEmpty()) {
            saveBatch(createNewRelations(userIds, factoryCode, storeLocationCode, currentUserId, currentTime));
        }
    }

    private void processFactory(String factoryCode, List<UserFactoryStoreLocationDTO> dtos) {
        if (CollUtil.isEmpty(dtos)) {
            return;
        }
        List<Long> userIds = dtos.stream()
                .map(UserFactoryStoreLocationDTO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        List<Long> existingUserIds = list(createFactoryQuery(factoryCode)
                .select(UserFactoryRel::getUserId))
                .stream()
                .map(UserFactoryRel::getUserId)
                .distinct()
                .collect(Collectors.toList());
        userIds.removeAll(existingUserIds);
        if (!userIds.isEmpty()) {
            Long currentUserId = UserContextUtils.getUserId();
            Date currentTime = new Date();
            saveBatch(createNewRelations(userIds, factoryCode, null, currentUserId, currentTime));
        }
    }

    private LambdaQueryWrapper<UserFactoryRel> createFactoryQuery(String factoryCode) {
        return Wrappers.<UserFactoryRel>lambdaQuery()
                .eq(UserFactoryRel::getFactoryCode, factoryCode);
    }

    private void updateStoreLocationCodes(UserFactoryRel rel, String storeLocationCode,
                                          Long currentUserId, Date currentTime) {
        String existingCodes = rel.getStoreLocationCodes();
        String newCodes = StrUtil.isEmpty(existingCodes)
                ? storeLocationCode
                : existingCodes + "," + storeLocationCode;
        rel.setStoreLocationCodes(newCodes);
        rel.setUpdateBy(currentUserId);
        rel.setUpdateTime(currentTime);
    }

    private List<UserFactoryRel> createNewRelations(List<Long> userIds, String factoryCode,
                                                    String storeLocationCode, Long currentUserId,
                                                    Date currentTime) {
        return userIds.stream()
                .map(userId -> {
                    UserFactoryRel rel = new UserFactoryRel();
                    rel.setUserId(userId);
                    rel.setFactoryCode(factoryCode);
                    rel.setStoreLocationCodes(storeLocationCode);
                    rel.setCreateBy(currentUserId);
                    rel.setCreateTime(currentTime);
                    return rel;
                })
                .collect(Collectors.toList());
    }
}
