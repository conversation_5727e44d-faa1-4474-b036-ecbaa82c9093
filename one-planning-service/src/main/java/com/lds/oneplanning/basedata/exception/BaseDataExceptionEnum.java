package com.lds.oneplanning.basedata.exception;

import com.iot.common.exception.IBusinessException;

/**
 * <AUTHOR>
 */
public enum BaseDataExceptionEnum implements IBusinessException {
    USER_EXIST(5100001, "用户已存在！"),;
    /**
     * 异常代码
     */
    private Integer code;

    /**
     * 异常描述
     */
    private String messageKey;

    /**
     * 描述：构建异常
     *
     * @param code       错误代码
     * @param messageKey 错误描述
     * @return
     * <AUTHOR>
     * @created 2017年3月21日 上午10:50:58
     * @since
     */
    BaseDataExceptionEnum(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    public static BaseDataExceptionEnum getByCode(Integer code) {
        for (BaseDataExceptionEnum businessExceptionEnum : BaseDataExceptionEnum.values()) {
            if (businessExceptionEnum.code.equals(code)) {
                return businessExceptionEnum;
            }
        }
        return null;
    }
}
