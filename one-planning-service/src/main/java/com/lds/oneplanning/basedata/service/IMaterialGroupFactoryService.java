package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.basedata.entity.MaterialGroupFactory;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
public interface IMaterialGroupFactoryService extends IService<MaterialGroupFactory> {

    List<MaterialGroupFactory> listByFactoryCodes(Set<String> factoryCodes);

    Integer batchUpdateByFactory(String factoryCode, Collection<String> groupCodes);

    List<MaterialGroupFactory> listByMaterialGroupCode(String materialGroupCode);
}
