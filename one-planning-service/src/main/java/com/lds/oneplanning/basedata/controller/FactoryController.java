package com.lds.oneplanning.basedata.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.model.FactoryDTO;
import com.lds.oneplanning.basedata.model.excel.FactoryExcel;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import com.lds.oneplanning.basedata.service.impl.ImportExportService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Slf4j
@Api(value = "FactoryController", tags = "工厂信息")
@RestController
@RequestMapping("/basedata/factory")
public class FactoryController {

    @Resource
    private IFactoryService factoryService;
    @Resource
    private IFactoryFacadeService factoryFacadeService;
    @Resource
    private ImportExportService importExportService;

    @ApiOperation(value = "同步工厂数据", notes = "同步工厂数据")
    @Loggable(businessName = "工厂数据",operation = "手动同步")
    @GetMapping("/syncData")
    public void syncData(){
        factoryService.syncFromSap();
    }

    @ApiOperation(value = "获取工厂列表", notes = "获取工厂列表")
    @GetMapping("/list")
    public List<Factory> list(){
        return factoryService.list();
    }

    @ApiOperation(value = "获取用户工厂列表", notes = "获取用户工厂列表")
    @GetMapping("/listByUser")
    public List<Factory> listByUser(){
        return factoryFacadeService.listByUser(UserContextUtils.getUserId());
    }

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<FactoryDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                 @RequestParam(value = "pageNum")Integer pageNum,
                                 @RequestParam(value = "pageSize")Integer pageSize
    ){
        // userId 不传 则查询全部
        return factoryFacadeService.page(null,keyword,null,null,pageNum,pageSize,false);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public Factory detail(@PathVariable("id")Long id){
        return  factoryService.getById(id);
    }


    @Loggable(businessName = "工厂数据",operation = "新增")
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    public Long add(@RequestBody Factory dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        factoryService.save(dto);
        return dto.getId();
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "工厂数据",operation = "编辑")
    public Integer edit(@RequestBody Factory dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  factoryService.updateById(dto) ? 1 : 0;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "工厂数据",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  factoryService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "工厂数据",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  factoryService.removeByIds(ids) ? 1:0;
    }

    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping("/import")
    @Loggable(businessName = "工厂数据",operation = "导入")
    public Boolean importExcel(@RequestParam("file") MultipartFile file) {
        List<FactoryExcel> data = importExportService.importData(file, FactoryExcel.class);
        log.info("导入数据：{}", data);
        factoryService.batchSaveByCode(BeanUtil.mapList(data,Factory.class));
        return true;
    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping("/export")
    @Loggable(businessName = "工厂数据",operation = "导出")
    public ResponseEntity exportExcel(@RequestParam(value = "keyword",required = false)String keyword){
        //请求参数
        log.info("导出请求参数：{}", keyword);
        //根据入参获取数据
        LambdaQueryWrapper<Factory> wrapper = Wrappers.<Factory>lambdaQuery();
        if (StringUtils.isNotBlank(keyword)) {
            wrapper.and(o2Wrapper -> o2Wrapper.like(Factory::getCode,keyword).or()
                    .like(Factory::getName,keyword));
        }
        List<Factory> mpsFactoryList  = factoryService.list(wrapper);
        //对象转换
        List<FactoryExcel> data = Lists.newArrayList();
        mpsFactoryList.forEach(item -> {
            FactoryExcel excel = new FactoryExcel();
            BeanUtils.copyProperties(item, excel);
            data.add(excel);
        });
        return importExportService.exportData(data, FactoryExcel.class,"工厂数据");
    }

    @ApiOperation(value = "获取泰国工厂代码", notes = "获取泰国工厂代码")
    @GetMapping("/getThaiFactoryCodes")
    public List<String> getThaiFactoryCodes(){
        return factoryService.getThaiFactoryCodes();
    }

}

