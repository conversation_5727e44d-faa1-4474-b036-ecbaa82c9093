package com.lds.oneplanning.basedata.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.entity.SchedulePriority;
import com.lds.oneplanning.basedata.helper.SchedulePriorityHelper;
import com.lds.oneplanning.basedata.model.SchedulePriorityDTO;
import com.lds.oneplanning.basedata.model.excel.SchedulePriorityExcel;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.basedata.service.ISchedulePriorityService;
import com.lds.oneplanning.basedata.service.impl.ImportExportService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Slf4j
@Api(value = "SchedulePriorityController", tags = "产线优先级配置")
@RestController
@RequestMapping("/basedata/schedulePriority")
public class SchedulePriorityController {

    @Resource
    private ISchedulePriorityService schedulePriorityService;
    @Resource
    private ImportExportService importExportService;
    @Resource
    SchedulePriorityHelper schedulePriorityHelper;
    @Resource
    private ILineInfoService lineInfoService;
    @ApiOperation(value = "列表获取", notes = "列表获取")
    @GetMapping("/list")
    public List<SchedulePriorityDTO> list(@RequestParam(value = "lineCode",required = false)String lineCode,
                                          @RequestParam(value = "productGroupCode",required = false)String productGroupCode,
                                          @RequestParam(value = "customerCode",required = false)String customerCode){
        return schedulePriorityService.list(UserContextUtils.getUserId(),lineCode,productGroupCode,customerCode);
    }

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<SchedulePriorityDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                          @RequestParam(value = "lineCode",required = false)String lineCode,
                                          @RequestParam(value = "productGroupCode",required = false)String productGroupCode,
                                          @RequestParam(value = "customerCode",required = false)String customerCode,
                               @RequestParam(value = "pageNum")Integer pageNum,
                               @RequestParam(value = "pageSize")Integer pageSize
    ){
        return schedulePriorityService.page(UserContextUtils.getUserId(),keyword,lineCode,productGroupCode,customerCode,pageNum,pageSize);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public SchedulePriorityDTO detail(@PathVariable("id")Long id){
        return  schedulePriorityService.getDetail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "排产优先级配置",operation = "新增")
    public Long add(@RequestBody SchedulePriority dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        schedulePriorityHelper.setSchedulePriorityLineId(dto);
        schedulePriorityService.save(dto);
        return dto.getId();
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "排产优先级配置",operation = "编辑")
    public Integer edit(@RequestBody SchedulePriority dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        schedulePriorityHelper.setSchedulePriorityLineId(dto);
        return  schedulePriorityService.updateById(dto) ? 1 : 0;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @Loggable(businessName = "排产优先级配置",operation = "删除")
    @DeleteMapping("/delete/{id}")
    public Integer delete(@PathVariable("id")Long id ){
        return  schedulePriorityService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "排产优先级配置",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  schedulePriorityService.removeByIds(ids) ? 1:0;
    }

    @ApiOperation(value = "导入-线体优先级配置", notes = "导入-线体优先级配置")
    @PostMapping("/import")
    @Loggable(businessName = "排产优先级配置",operation = "导入")
    public Boolean importExcel(@RequestParam("file") MultipartFile file) {
        List<SchedulePriorityExcel> datas = importExportService.importData(file, SchedulePriorityExcel.class);
        log.info("导入数据：{}", datas);
        List<SchedulePriority> targetList = Lists.newArrayList();
        Set<String> lineCodes = datas.stream().map(SchedulePriorityExcel::getLineCode).collect(Collectors.toSet());
        List<LineInfo> lineInfos = lineInfoService.listByCodes(lineCodes);
        Map<String,String> codeUuidMap = lineInfos.stream().collect(Collectors.toMap(LineInfo::getCode, LineInfo::getLineUuid,(s, s2) -> s2));
        datas.stream().filter(excel-> StringUtils.isNotBlank(excel.getPrioritySeqStr()) &&
                StringUtils.isNotBlank(excel.getLineCode()) && StringUtils.isNotBlank(excel.getProductGroupCode())).forEach(excel->{
            SchedulePriority entity = new SchedulePriority();
            entity.setLineUuid(codeUuidMap.get(excel.getLineCode()));
            entity.setLineCode(excel.getLineCode());
            entity.setPrioritySeq(Integer.valueOf(excel.getPrioritySeqStr()));
            entity.setProductGroupCode(excel.getProductGroupCode());
            entity.setCustomerCode(excel.getCustomerCode());
            entity.setCreateBy(UserContextUtils.getUserId());
            entity.setCreateTime(new Date());
            entity.setUpdateBy(UserContextUtils.getUserId());
            entity.setUpdateTime(new Date());
            targetList.add(entity);
            // 根据线体编号和产品编码进行删除
            schedulePriorityService.remove(Wrappers.<SchedulePriority>lambdaQuery()
                    .eq(SchedulePriority::getLineCode,excel.getLineCode())
                    .eq(SchedulePriority::getProductGroupCode,excel.getProductGroupCode())
                    .eq(StringUtils.isNoneBlank(excel.getCustomerCode()),SchedulePriority::getCustomerCode,excel.getCustomerCode())
            );
        });
        if (targetList.isEmpty()) {
            return false;
        }
        return  schedulePriorityService.saveBatch(targetList);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping("/export")
    @Loggable(businessName = "排产优先级配置",operation = "导出")
    public ResponseEntity exportExcel(@RequestParam(value = "keyword",required = false)String keyword,
                                      @RequestParam(value = "lineCode",required = false)String lineCode,
                                      @RequestParam(value = "productGroupCode",required = false)String productGroupCode,
                                      @RequestParam(value = "customerCode",required = false)String customerCode){
        //请求参数
        log.info("导出请求参数：{}", keyword);
        //根据入参获取数据
        LambdaQueryWrapper<SchedulePriority> queryWrapper = Wrappers.<SchedulePriority>lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(lineCode),SchedulePriority::getLineCode,lineCode);
        queryWrapper.eq(StringUtils.isNotBlank(productGroupCode),SchedulePriority::getProductGroupCode,productGroupCode);
        queryWrapper.eq(StringUtils.isNotBlank(customerCode),SchedulePriority::getCustomerCode,customerCode);
        if (org.apache.commons.lang.StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(SchedulePriority::getLineCode,keyword).or()
                    .like(SchedulePriority::getProductGroupCode,keyword).or().like(SchedulePriority::getCustomerCode,keyword));
        }
        queryWrapper.orderByDesc(SchedulePriority::getLineCode).orderByAsc(SchedulePriority::getId);
        List<SchedulePriority>  schedulePriorityList  = schedulePriorityService.list(queryWrapper);
        //对象转换
        List<SchedulePriorityExcel> data = Lists.newArrayList();
        schedulePriorityList.forEach(item -> {
            SchedulePriorityExcel excel = new SchedulePriorityExcel();
            BeanUtils.copyProperties(item, excel);
            excel.setPrioritySeqStr(item.getPrioritySeq().toString());
            data.add(excel);
        });
        return importExportService.exportData(data, SchedulePriorityExcel.class,"线体排产优先级");
    }



}
