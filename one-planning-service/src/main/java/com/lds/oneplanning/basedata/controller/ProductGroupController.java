package com.lds.oneplanning.basedata.controller;


import com.google.common.collect.Lists;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.model.ProductGroupDTO;
import com.lds.oneplanning.basedata.model.excel.ProductGroupExcel;
import com.lds.oneplanning.basedata.model.req.ProductGroupReq;
import com.lds.oneplanning.basedata.service.IProductGroupService;
import com.lds.oneplanning.basedata.service.impl.ImportExportService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Slf4j
@Api(value = "ProductGroupController", tags = "产品组信息")
@RestController
@RequestMapping("/basedata/productGroup")
public class ProductGroupController {

    @Resource
    private IProductGroupService productGroupService;
    @Resource
    private ImportExportService importExportService;

    @ApiOperation(value = "根据用户获取产品组列表", notes = "根据用户获取产品组列表")
    @GetMapping("/listFilterByUser")
    public List<ProductGroupDTO> listFilterByUser(@RequestParam(value = "keyword",required = false) String keyword,
                                            @RequestParam(value = "factoryCode",required = false)String factoryCode
                                                   ){
        return productGroupService.findList(UserContextUtils.getUserId(),keyword,factoryCode);
    }

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @PostMapping("/page")
    public Page<ProductGroupDTO> page(@RequestBody ProductGroupReq req){
        return productGroupService.page(req.getKeyword(),req.getFactoryCode(),req.getWorkshopCode(),req.getProductIds(),req.getPageNum(),req.getPageSize());
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public ProductGroupDTO detail(@PathVariable("id")Long id){
        return  productGroupService.getDetail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "产品组信息",operation = "新增")
    public Long add(@RequestBody ProductGroupDTO dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return productGroupService.add(dto);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "产品组信息",operation = "编辑")
    public Integer edit(@RequestBody ProductGroupDTO dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  productGroupService.edit(dto);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "产品组信息",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  productGroupService.delete(id) ;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "产品组信息",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  productGroupService.batchDelete(ids);
    }
    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping("/export")
    @Loggable(businessName = "产品组信息",operation = "导出")
    public ResponseEntity exportExcel(@RequestParam(value = "keyword",required = false)String keyword,
                                      @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                      @RequestParam(value = "workshopCode",required = false)String workshopCode
                                      ){
        //请求参数
        log.info("导出请求参数：{}", keyword);
        List<ProductGroupDTO> productGroupList  = productGroupService.listByKeyword(keyword,factoryCode,workshopCode,null);
        //对象转换
        List<ProductGroupExcel> data = Lists.newArrayList();
        productGroupList.forEach(item -> {
             item.getProductIds().stream().forEach(productId -> {
                 ProductGroupExcel excel = new ProductGroupExcel();
                 excel.setProductGroupCode(item.getCode());
                 excel.setProductGroupName(item.getName());
                 excel.setProductId(productId);
                 excel.setFactoryCode(item.getFactoryCode());
                 data.add(excel);
             });
        });
        return importExportService.exportData(data, ProductGroupExcel.class,"产品组信息");
    }


    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping("/import")
    @Loggable(businessName = "产品组信息",operation = "导入")
    public Boolean importExcel(@RequestParam("file") MultipartFile file) {
        List<ProductGroupExcel> data = importExportService.importData(file, ProductGroupExcel.class);
        log.info("导入产品组数据：{}", data);
        return productGroupService.importExcel(data);
    }

}
