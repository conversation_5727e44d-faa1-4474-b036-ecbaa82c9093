package com.lds.oneplanning.basedata.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述：产线优先级配置
 * 创建人： LaiGuiMing
 * 创建时间： 2025/2/21
 */
@Data
public class SchedulePriorityExcel {

    @ExcelProperty("线体编码")
    @ApiModelProperty(value = "线体编码")
    private String lineCode;

    @ExcelProperty("产品组编码")
    @ApiModelProperty(value = "产品组编码")
    private String productGroupCode;

    @ExcelProperty("客户编号")
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    @ExcelProperty("优先级")
    @ApiModelProperty(value = "优先级：1、2/3")
    private String  prioritySeqStr;





}
