package com.lds.oneplanning.basedata.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iot.common.exception.BusinessException;
import com.lds.basic.account.user.api.UserApi2;
import com.lds.basic.account.user.dto.UserDto;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.UserInfo;
import com.lds.oneplanning.basedata.exception.BaseDataExceptionEnum;
import com.lds.oneplanning.basedata.mapper.UserInfoMapper;
import com.lds.oneplanning.basedata.model.UserFactoryRelDTO;
import com.lds.oneplanning.basedata.model.UserInfoDTO;
import com.lds.oneplanning.basedata.model.UserInfoQueryDTO;
import com.lds.oneplanning.basedata.service.IUserFactoryRelService;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.wps.enums.ViewSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/16
 */
@Service
@Slf4j
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements IUserInfoService {

    @Resource
    private IUserFactoryRelService userFactoryRelService;
    @Resource
    private UserApi2 userApi2;

    @Override
    public Page<UserInfoDTO> page(UserInfoQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        PageInfo<UserInfoDTO> pageInfo = new PageInfo<>(baseMapper.list(queryDTO));
        Page<UserInfoDTO> resultPage = new Page<>();
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            List<UserInfoDTO> results = pageInfo.getList();
            List<Long> userIdList = results.stream().map(UserInfoDTO::getUserId).collect(Collectors.toList());
            Map<Long, List<UserFactoryRelDTO>> mapping = userFactoryRelService.findMapping(userIdList);
            results.forEach(dto -> dto.setUserFactoryList(mapping.get(dto.getUserId())));
            resultPage.setTotal(pageInfo.getTotal());
            resultPage.setPageNum(queryDTO.getPageNum());
            resultPage.setPageSize(queryDTO.getPageSize());
            resultPage.setResult(results);
        }
        return resultPage;
    }

    @Override
    public List<UserInfoDTO> listByCondition(UserInfoQueryDTO queryDTO) {
        return baseMapper.list(queryDTO);
    }

    @Override
    public ViewSource getCurrentUserType() {
        Long userId = UserContextUtils.getUserId();
        if (userId != null) {
            UserInfoDTO userInfoDTO = baseMapper.getOneByUserId(userId);
            if (userInfoDTO != null) {
                return ViewSource.valueOf(userInfoDTO.getUserType());
            }
        }
        return ViewSource.DEFAULT;
    }

    @Override
    public ViewSource getOrDefaultUserType(ViewSource defaultType) {
        ViewSource type = getCurrentUserType();
        if (ViewSource.DEFAULT.equals(type) && defaultType != null) {
            type = defaultType;
        }
        log.info("当前用户类型：{}", type.name());
        return type;
    }

    @Override
    public UserInfoDTO detail(Long id) {
        UserInfo entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        UserInfoDTO res = BeanUtil.map(entity, UserInfoDTO.class);
        List<UserFactoryRelDTO> userFactoryList = userFactoryRelService.findMapping(CollUtil.newArrayList(res.getUserId()))
                .get(res.getUserId());
        res.setUserFactoryList(userFactoryList);
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(UserInfoDTO dto) {
        validateUserIdExist(dto.getUserId());
        UserInfo entity = BeanUtil.map(dto, UserInfo.class);
        Long userId = dto.getUserId();
        UserDto userParam = new UserDto();
        userParam.setId(userId);
        UserDto user = userApi2.getDto(userParam);
        entity.setEmpNo(user.getJobNo());
        entity.setUserName(user.getName());
        baseMapper.insert(entity);
        userFactoryRelService.save(dto.getUserFactoryList(), userId);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(UserInfoDTO dto) {
        UserInfo entity = BeanUtil.map(dto, UserInfo.class);
        Long userId = dto.getUserId();
        UserDto userParam = new UserDto();
        userParam.setId(userId);
        UserDto user = userApi2.getDto(userParam);
        entity.setEmpNo(user.getJobNo());
        entity.setUserName(user.getName());
        baseMapper.updateById(entity);
        userFactoryRelService.save(dto.getUserFactoryList(), userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchDelete(Collection<Long> ids) {
        List<UserInfo> userInfos = baseMapper.selectBatchIds(ids);
        if (CollUtil.isEmpty(userInfos)) {
            return 0;
        }
        List<Long> userIdList = userInfos.stream().map(UserInfo::getUserId).distinct().collect(Collectors.toList());
        userFactoryRelService.deleteByUserId(userIdList);
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    public Map<String, List<UserInfoDTO>> listByFactoryCodes(Collection<String> factoryCodeList) {
        if (CollectionUtils.isEmpty(factoryCodeList)){
            return Collections.emptyMap();
        }
        UserInfoQueryDTO queryDTO = new UserInfoQueryDTO();
        queryDTO.setFactoryCodeList(factoryCodeList);
        return baseMapper.list(queryDTO).stream()
                .collect(Collectors.groupingBy(UserInfoDTO::getFactoryCodes));
    }

    /**
     * 校验用户id是否存在
     *
     * @param userId
     */
    private void validateUserIdExist(Long userId) {
        UserInfo userInfo = baseMapper.selectOne(Wrappers.lambdaQuery(UserInfo.class)
                .eq(UserInfo::getUserId, userId)
                .last("limit 1"));
        if (userInfo != null) {
            throw new BusinessException(BaseDataExceptionEnum.USER_EXIST);
        }
    }
}
