package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.mapper.PlannerDataPermissionMapper;
import com.lds.oneplanning.basedata.model.PlannerDataPermissionDTO;
import com.lds.oneplanning.basedata.model.ProductGroupDTO;
import com.lds.oneplanning.basedata.model.WorkshopDTO;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.basedata.service.IProductGroupService;
import com.lds.oneplanning.basedata.service.IWorkshopService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class PlannerDataPermissionServiceImpl extends ServiceImpl<PlannerDataPermissionMapper, PlannerDataPermission> implements IPlannerDataPermissionService {

    @Resource
    private IWorkshopService  workshopService;
    @Resource
    private IFactoryService factoryService;
    @Resource
    private IProductGroupService productGroupService;

    @Override
    public Page<PlannerDataPermissionDTO> page(Long userId, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<PlannerDataPermission> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<PlannerDataPermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlannerDataPermission::getUserId,userId);
        queryWrapper.orderByDesc(PlannerDataPermission::getUpdateTime).orderByAsc(PlannerDataPermission::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<PlannerDataPermissionDTO> resultPage = new Page<>(pageNum, pageSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<PlannerDataPermissionDTO> results = BeanUtil.mapList(entityPage.getRecords(), PlannerDataPermissionDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<PlannerDataPermissionDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Map<String,String> factoryNameMap = factoryService.
                listByFactoryCodes(sourceList.stream().map(PlannerDataPermissionDTO::getFactoryCode).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        Map<String,String> workshopNameMap = workshopService.
                listByCodes(sourceList.stream().map(PlannerDataPermissionDTO::getWorkshopCode).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(WorkshopDTO::getCode,WorkshopDTO::getName,(s, s2) -> s2));
        Map<String,String> groupNameMap = productGroupService.
                listByCodes(sourceList.stream().map(PlannerDataPermissionDTO::getProductGroupCode).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(ProductGroupDTO::getCode,ProductGroupDTO::getName,(s, s2) -> s2));
        sourceList.stream().forEach(plannerDataPermissionDTO -> {
            plannerDataPermissionDTO.setFactoryName(factoryNameMap.get(plannerDataPermissionDTO.getFactoryCode()));
            plannerDataPermissionDTO.setWorkshopName(workshopNameMap.get(plannerDataPermissionDTO.getWorkshopCode()));
            plannerDataPermissionDTO.setProductGroupName(groupNameMap.get(plannerDataPermissionDTO.getProductGroupCode()));
        });

    }

    @Override
    public List<PlannerDataPermission> listByUserId(Long userId) {
        if (userId == null) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<PlannerDataPermission>lambdaQuery().eq(PlannerDataPermission::getUserId,userId));
    }

    @Override
    public void saveByUserId(List<PlannerDataPermission> dataPermissionList,Long userId) {
        // 先清空
        this.deleteByUserId(userId);
        if (CollectionUtils.isEmpty(dataPermissionList)) {
            return;
        }
        Set<String> workshopCodes = dataPermissionList.stream().map(PlannerDataPermission::getWorkshopCode).collect(Collectors.toSet());
        Map<String,String> workshopFactoryMap = workshopService.listByCodes(workshopCodes).stream().collect(Collectors.toMap(WorkshopDTO::getCode,WorkshopDTO::getFactoryCode,(s, s2) -> s2));
        //再保存
        dataPermissionList.stream().forEach(dataPermission -> {
            dataPermission .setUserId(userId);
            if (StringUtils.isBlank(dataPermission.getFactoryCode())) {
                dataPermission.setFactoryCode(workshopFactoryMap.get(dataPermission.getWorkshopCode()));
            }
        });
        this.saveBatch(dataPermissionList);
    }

    @Override
    public void deleteByUserId(Long userId) {
        baseMapper.delete(Wrappers.<PlannerDataPermission>lambdaQuery().eq(PlannerDataPermission::getUserId,userId));
    }

    @Override
    public void deleteByUserIds(Collection<Long> userIds) {
        if (userIds == null  || userIds.isEmpty()) {
            return ;
        }
        baseMapper.delete(Wrappers.<PlannerDataPermission>lambdaQuery().in(PlannerDataPermission::getUserId,userIds));
    }

    @Override
    public List<String> listAllFactoryCode() {
        return baseMapper.selectList(Wrappers.<PlannerDataPermission>lambdaQuery()
                        .select(PlannerDataPermission::getFactoryCode))
                .stream().map(PlannerDataPermission::getFactoryCode)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, Set<String>> getUserFactoryCodeMap() {
        List<PlannerDataPermission> plannerDataPermissionList = this.list();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(plannerDataPermissionList)) {
            return Maps.newHashMap();
        }
        return plannerDataPermissionList.stream().collect(
                Collectors.groupingBy(PlannerDataPermission::getUserId,
                        Collectors.mapping(PlannerDataPermission::getFactoryCode,
                                Collectors.toSet())));
    }
    @Override
    public Map<String,Set<Long>> getFactoryUserMap(){
        List<PlannerDataPermission> plannerDataPermissionList = this.list();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(plannerDataPermissionList)) {
            return Maps.newHashMap();
        }
        return plannerDataPermissionList.stream().collect(
                Collectors.groupingBy(PlannerDataPermission::getFactoryCode,
                        Collectors.mapping(PlannerDataPermission::getUserId,
                                Collectors.toSet())));
    }



    @Override
    public Set<String> getFactoryCodeByUserId(Long userId) {
        if (null == userId) {
            return Collections.emptySet();
        }
        List<PlannerDataPermission> plannerDataPermissions = this.listByUserId(userId);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(plannerDataPermissions)) {
            return Collections.emptySet();
        }
        return plannerDataPermissions.stream().map(PlannerDataPermission::getFactoryCode)
                .collect(Collectors.toSet());
    }
}
