package com.lds.oneplanning.basedata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MpsLineCategoryRuleDTO", description="")
public class LineCategoryRuleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "产线类别编码")
    private String lineCategoryCode;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "产品id商品id")
    private String productId;


    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    // 以下非库表所有
    private String lineCategoryName;

    @ApiModelProperty(value = "客户名称")
    private String customerName;
}
