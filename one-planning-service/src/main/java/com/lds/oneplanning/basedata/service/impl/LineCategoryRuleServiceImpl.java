package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Customer;
import com.lds.oneplanning.basedata.entity.LineCategory;
import com.lds.oneplanning.basedata.entity.LineCategoryRule;
import com.lds.oneplanning.basedata.mapper.LineCategoryRuleMapper;
import com.lds.oneplanning.basedata.model.LineCategoryRuleDTO;
import com.lds.oneplanning.basedata.service.ICustomerService;
import com.lds.oneplanning.basedata.service.ILineCategoryRuleService;
import com.lds.oneplanning.basedata.service.ILineCategoryService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class LineCategoryRuleServiceImpl extends ServiceImpl<LineCategoryRuleMapper, LineCategoryRule> implements ILineCategoryRuleService {
    @Resource
    private ILineCategoryService lineCategoryService;
    @Resource
    private ICustomerService customerService;
    @Override
    public Page<LineCategoryRuleDTO> page(String keyword, String factoryCode, String lineCategoryCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<LineCategoryRule> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<LineCategoryRule> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(factoryCode)) {
            Set<String> lineCategoryCodes =  lineCategoryService.listByFactoryCode(factoryCode).stream().map(LineCategory::getCode).collect(Collectors.toSet());
            queryWrapper.in(CollectionUtils.isNotEmpty(lineCategoryCodes), LineCategoryRule::getLineCategoryCode,lineCategoryCodes);
        }
        if (StringUtils.isNotBlank(lineCategoryCode)) {
            queryWrapper.eq(LineCategoryRule::getLineCategoryCode,lineCategoryCode);
        }
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(LineCategoryRule::getLineCategoryCode,keyword).or()
                    .like(LineCategoryRule::getCustomerCode,keyword));
        }
        queryWrapper.orderByDesc(LineCategoryRule::getUpdateTime).orderByAsc(LineCategoryRule::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<LineCategoryRuleDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<LineCategoryRuleDTO> results = BeanUtil.mapList(entityPage.getRecords(), LineCategoryRuleDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<LineCategoryRuleDTO> results){
        if (results == null || results.isEmpty()) {
            return;
        }
        Set<String> categoryCodes = results.stream().map(LineCategoryRuleDTO::getLineCategoryCode).collect(Collectors.toSet());
        Set<String> customerCodes = results.stream().map(LineCategoryRuleDTO::getCustomerCode).collect(Collectors.toSet());
        List<LineCategory> LineCategoryList = lineCategoryService.listByCodes(categoryCodes);
        Map<String,String> categoryMap  = LineCategoryList.stream().collect(Collectors.toMap(LineCategory::getCode,LineCategory::getName,(s, s2) -> s2));
        Map<String,String> customerMap = customerService.listByCodes(customerCodes).stream().collect(Collectors.toMap(Customer::getCode,Customer::getName,(s, s2) -> s2));
        results.stream().forEach(dto ->{
            dto.setLineCategoryName(categoryMap.get(dto.getLineCategoryCode()));
            dto.setCustomerName(customerMap.get(dto.getCustomerCode()));
        } );
    }
    @Override
    public LineCategoryRuleDTO getDetail(Long id) {
        LineCategoryRule entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        LineCategoryRuleDTO res = BeanUtil.map(entity, LineCategoryRuleDTO.class);
        this.decorate(Lists.newArrayList(res));
        return  res;
    }

    @Override
    public List<LineCategoryRule> listByCategoryCode(String code) {
        return baseMapper.selectList(Wrappers.<LineCategoryRule>lambdaQuery().eq(LineCategoryRule::getLineCategoryCode,code));
    }

    @Override
    public Map<String, List<LineCategoryRule>> groupByCategoryCodes(Collection<String> codes) {
        if (codes == null || codes.isEmpty()) {
            return Maps.newLinkedHashMap();
        }
        return baseMapper.selectList(Wrappers.<LineCategoryRule>lambdaQuery().in(LineCategoryRule::getLineCategoryCode,codes)).stream().collect(Collectors.groupingBy(LineCategoryRule::getCustomerCode));
    }

    @Override
    public void deleteByCategoryCode(String code) {
        baseMapper.delete(Wrappers.<LineCategoryRule>lambdaQuery().eq(LineCategoryRule::getLineCategoryCode,code));
    }

    @Override
    public LineCategoryRule getByCustomerCodeAndProductId(String customerCode, String productId) {
        if (StringUtils.isEmpty(customerCode) || StringUtils.isEmpty(productId)) {
            return null;
        }
        List<LineCategoryRule> mpsLineCategoryRules = baseMapper.selectList(Wrappers.<LineCategoryRule>lambdaQuery()
                .eq(LineCategoryRule::getCustomerCode, customerCode)
                .eq(LineCategoryRule::getProductId, productId));
        if (CollectionUtils.isEmpty(mpsLineCategoryRules)) {
            return null;
        }
        return mpsLineCategoryRules.get(0);
    }

    @Override
    public void saveByCategoryCode(List<LineCategoryRule> lineCategoryRuleList, String code) {
         this.deleteByCategoryCode(code);
         if (CollectionUtils.isEmpty(lineCategoryRuleList)) {
            return;
         }
         lineCategoryRuleList.stream().forEach(LineCategoryRule -> LineCategoryRule.setLineCategoryCode(code));
         this.saveBatch(lineCategoryRuleList);
    }
}
