package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.model.LineChangeCfgDTO;
import com.lds.oneplanning.basedata.service.ILineChangeCfgService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-19
 */
@Slf4j
@Api(value = "LineInfoController", tags = "换线时长管理")
@RestController
@RequestMapping("/basedata/lineChangeCfg")
public class LineChangeCfgController {


    @Resource
    private ILineChangeCfgService lineChangeCfgService;


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<LineChangeCfgDTO> page(
            @RequestParam(value = "factoryCode", required = false) String factoryCode,
            @RequestParam(value = "pageNum") Integer pageNum,
            @RequestParam(value = "pageSize") Integer pageSize) {
        return lineChangeCfgService.page(factoryCode, pageNum, pageSize);
    }

    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public LineChangeCfgDTO detail(@PathVariable("id") Long id) {
        return lineChangeCfgService.findById(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "线体信息", operation = "新增")
    public Long add(@RequestBody @Valid LineChangeCfgDTO dto) {
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return lineChangeCfgService.add(dto);
    }

    @ApiOperation(value = "修改换线时长", notes = "修改换线时长")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "修改换线时长", operation = "编辑")
    public Long edit(@RequestBody @Valid LineChangeCfgDTO dto, @PathVariable("id") Long id) {
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setId(id);
        return lineChangeCfgService.edit(dto);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "删除换线时长", operation = "删除")
    public void delete(@PathVariable("id") Long id) {
        lineChangeCfgService.delete(id);
    }

}
