package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.LineCategoryRule;
import com.lds.oneplanning.basedata.model.LineCategoryRuleDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface ILineCategoryRuleService extends IService<LineCategoryRule> {
    Page<LineCategoryRuleDTO> page(String keyword, String factoryCode, String lineCategoryCode, Integer pageNum, Integer pageSize);
    LineCategoryRuleDTO getDetail(Long id);

    List<LineCategoryRule> listByCategoryCode(String code);

   Map<String,List<LineCategoryRule>> groupByCategoryCodes(Collection<String> code);

    void deleteByCategoryCode(String code);

    void saveByCategoryCode(List<LineCategoryRule> lineCategoryRuleList, String code);

    LineCategoryRule getByCustomerCodeAndProductId(String customerCode, String productId);
}