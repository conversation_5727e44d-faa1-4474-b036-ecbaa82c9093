package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.ScheduleDirectionCfg;
import com.lds.oneplanning.basedata.model.ScheduleDirectionCfgDTO;
import com.lds.oneplanning.basedata.service.IScheduleDirectionCfgService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-14
 */
@Slf4j
@Api(value = "ScheduleDirectionCfgController", tags = "排产顺序配置")
@RestController
@RequestMapping("/basedata/scheduleDirectionCfg")
public class ScheduleDirectionCfgController {


    @Resource
    private IScheduleDirectionCfgService scheduleDirectionCfgService;


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<ScheduleDirectionCfgDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                              @RequestParam(value = "pageNum")Integer pageNum,
                                              @RequestParam(value = "pageSize")Integer pageSize
    ){
        return scheduleDirectionCfgService.page(keyword,pageNum,pageSize);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public ScheduleDirectionCfgDTO detail(@PathVariable("id")Long id){
        return  scheduleDirectionCfgService.detail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "排产顺序",operation = "新增")
    public Long add(@RequestBody ScheduleDirectionCfg dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        scheduleDirectionCfgService.save(dto);
        return dto.getId();
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "排产顺序",operation = "编辑")
    public Integer edit(@RequestBody ScheduleDirectionCfg dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  scheduleDirectionCfgService.updateById(dto) ? 1 : 0;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "排产顺序",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  scheduleDirectionCfgService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "排产顺序",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  scheduleDirectionCfgService.removeByIds(ids) ? 1:0;
    }
}
