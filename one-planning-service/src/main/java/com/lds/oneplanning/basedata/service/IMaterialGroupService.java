package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.MaterialGroup;
import com.lds.oneplanning.basedata.model.MaterialGroupDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
public interface IMaterialGroupService extends IService<MaterialGroup> {

    Page<MaterialGroupDTO> page(String keyword, Integer pageNum, Integer pageSize);

    MaterialGroupDTO getDetail(Long id);

    Long add(MaterialGroupDTO dto);

    Integer edit(MaterialGroupDTO dto);

    Integer deleteClean(Long id);

    Integer batchDelete(Collection<Long> ids);

    List<MaterialGroupDTO> listByFactoryCodes(Set<String> factoryCodes);

    List<MaterialGroupDTO> listByCodes(Set<String> groupCodes);

    List<MaterialGroupDTO> listByUserId(Long userId);
}
