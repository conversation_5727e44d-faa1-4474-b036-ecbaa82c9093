package com.lds.oneplanning.basedata.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/13 17:48
 */
@Data
public class LineUphBatchDTO {

    @ApiModelProperty(value = "线体id")
    private Collection<Long> ids ;
    @ApiModelProperty(value = "产线类别编码")
    private String lineCategoryCode;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

}
