package com.lds.oneplanning.basedata.enums;

/**
 * 订单子类型枚举
 */
public enum OrderTypeEnum {
    SELL("SELL", "销售订单"),
    PLAN("PLAN", "计划订单"),
    PROD("PROD", "生产订单"),
    PURCHASE("PURCHASE", "采购订单"),
    PURCHASE_APPLY("PURCHASE_APPLY", "采购申请"),
    BACKUP_PLAN("BACKUP_PLAN", "备库计划订单");

    private String code;
    private String name;

    OrderTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code){
        for (OrderTypeEnum typeEnum : OrderTypeEnum.values()){
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getName();
            }
        }
        return null;
    }

}