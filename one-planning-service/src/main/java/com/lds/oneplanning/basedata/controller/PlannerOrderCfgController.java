package com.lds.oneplanning.basedata.controller;


import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.model.PlannerOrderCfgDTO;
import com.lds.oneplanning.basedata.service.IPlannerOrderCfgService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-27
 */
@Slf4j
@Api(value = "PlannerOrderCfgController", tags = "特殊订单配置-计划员订单配置")
@RestController
@RequestMapping("/basedata/plannerOrderCfg")
public class PlannerOrderCfgController {
    @Resource
    private IPlannerOrderCfgService plannerOrderCfgService;


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<PlannerOrderCfgDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                         @RequestParam(value = "empNo",required = false)String empNo,
                                         @RequestParam(value = "pageNum")Integer pageNum,
                                         @RequestParam(value = "pageSize")Integer pageSize
    ){
        return plannerOrderCfgService.page(keyword,empNo,pageNum,pageSize);
    }

    @ApiOperation(value = "批量新增修改", notes = "批量新增修改")
    @PostMapping("/batchSaveUpdate")
    @Loggable(businessName = "特殊订单配置-计划员",operation = "批量新增修改")
    public Integer batchSaveUpdate(@RequestBody PlannerOrderCfgDTO dto ){

        return  plannerOrderCfgService.batchSaveUpdate(dto);
    }

    @ApiOperation(value = "根据计划员工号查询记录", notes = "根据计划员工号查询记录")
    @GetMapping("/getByempNo")
    public PlannerOrderCfgDTO getByempNo(@RequestParam("empNo")String empNo){

        return  plannerOrderCfgService.getByEmpNo(empNo);
    }

    @ApiOperation(value = "根据计划员工号查询删除", notes = "根据计划员工号查询删除")
    @DeleteMapping("/deleteByempNo")
    @Loggable(businessName = "特殊订单配置-计划员",operation = "删除")
    public Integer deleteByEmpNo(@RequestParam("empNo")String empNo){

        return  plannerOrderCfgService.deleteByEmpNo(empNo);
    }

    @ApiOperation(value = "根据计划员工号查询删除-批量删除", notes = "根据计划员工号查询删除-批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "特殊订单配置-计划员",operation = "批量删除")
    public Integer batchDeleteByEmpNos(@RequestBody Collection<String> empNos){

        return  plannerOrderCfgService.batchDeleteByEmpNos(empNos);
    }
}
