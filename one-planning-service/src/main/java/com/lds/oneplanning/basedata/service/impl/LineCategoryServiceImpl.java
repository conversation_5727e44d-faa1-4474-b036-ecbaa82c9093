package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.iot.common.exception.BusinessException;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.*;
import com.lds.oneplanning.basedata.mapper.LineCategoryMapper;
import com.lds.oneplanning.basedata.model.LineCategoryDTO;
import com.lds.oneplanning.basedata.service.*;
import com.lds.oneplanning.mps.exception.MpsExceptionEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class LineCategoryServiceImpl extends ServiceImpl<LineCategoryMapper, LineCategory> implements ILineCategoryService {
    @Resource
    private ILineCategoryRuleService lineCategoryRuleService;
    @Resource
    private ILineCapacityService lineCapacityService;
    @Resource
    private ILineUphService lineUphService;
    @Resource
    private IFactoryService factoryService;
    
    @Override
    public Page<LineCategoryDTO> page(String keyword, String factoryCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<LineCategory> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<LineCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneBlank(factoryCode),LineCategory::getFactoryCode,factoryCode);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(LineCategory::getCode,keyword).or()
                    .like(LineCategory::getName,keyword));
        }
        queryWrapper.orderByDesc(LineCategory::getUpdateTime).orderByAsc(LineCategory::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<LineCategoryDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<LineCategoryDTO> results = BeanUtil.mapList(entityPage.getRecords(), LineCategoryDTO.class);
            this.decorate(results);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            resultPage.setResult(results);
        }
        return resultPage;
    }
    
    private void decorate(List<LineCategoryDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> configCodes = sourceList.stream().map(LineCategoryDTO::getCode).collect(Collectors.toSet());
        Set<String> factoryCodes = sourceList.stream().map(LineCategoryDTO::getFactoryCode).collect(Collectors.toSet());
        List<Factory> factories = factoryService.listByFactoryCodes(factoryCodes);
        Map<String,String> factoryMap = factories.stream().collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        Map<String, List<LineCapacity>> capicityMap = lineCapacityService.groupByTypeAndCodes(BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY, configCodes);
        Map<String, List<LineUph>> uphMap = lineUphService.groupByTypeAndCodes(BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY, configCodes);
        Map<String, List<LineCategoryRule>> ruleMap = lineCategoryRuleService.groupByCategoryCodes(configCodes);

        sourceList.stream().forEach(dto -> {
            dto.setLineCategoryRuleList(ruleMap.get(dto.getCode()));
            dto.setCapacityList(capicityMap.get(dto.getCode()));
            dto.setUphList(uphMap.get(dto.getCode()));
            dto.setFactoryName(factoryMap.get(dto.getFactoryCode()));
        });
    }

    @Override
    public LineCategoryDTO detail(Long id) {
        LineCategory entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        LineCategoryDTO res = BeanUtil.map(entity, LineCategoryDTO.class);
        decorate(Lists.newArrayList(res));
        return res;
    }

    @Override
    public Long add(LineCategoryDTO dto) {
        LineCategory byCode = this.getByCode(dto.getCode());
        if (byCode != null) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
        LineCategory entity = BeanUtil.map(dto,LineCategory.class);
        baseMapper.insert(entity);
        // 子表单独保存
//        lineCategoryRuleService.saveByCategoryCode(dto.getLineCategoryRuleList(),entity.getCode());
//        lineCategoryCapacityService.saveByCategoryCode(dto.getLineCategoryCapacityList(),entity.getCode());
//        LineCategoryUphService.saveByCategoryCode(dto.getLineCategoryUphList(),entity.getCode());
        return entity.getId();
    }

    @Override
    public Integer edit(LineCategoryDTO dto) {
        LineCategory byCode = this.getByCode(dto.getCode());
        if (byCode != null && !dto.getId().equals(byCode.getId())) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
        LineCategory entity = BeanUtil.map(dto,LineCategory.class);
        baseMapper.updateById(entity);
//        lineCategoryRuleService.saveByCategoryCode(dto.getLineCategoryRuleList(),entity.getCode());
//        lineCategoryCapacityService.saveByCategoryCode(dto.getLineCategoryCapacityList(),entity.getCode());
//        LineCategoryUphService.saveByCategoryCode(dto.getLineCategoryUphList(),entity.getCode());
        return  1;
    }

    @Override
    public Integer delete(Long id) {
        LineCategory entity = baseMapper.selectById(id);
        String code = entity.getCode();
//        lineCategoryRuleService.deleteByCategoryCode(code);
//        lineCategoryCapacityService.deleteByCategoryCode(code);
//        LineCategoryUphService.deleteByCategoryCode(code);
        // TODO 判断是否存在关联配置，存在不能直接删除
        baseMapper.deleteById(id);
        return 1;
    }

    @Override
    public Integer batchDelete(Collection<Long> ids) {
        ids.stream().forEach(this::delete);
        return ids.size();
    }

    @Override
    public Map<String, List<LineCategory>> groupByFactoryCodes(Collection<String> factoryCodes) {
        List<LineCategory> LineCategoryList = baseMapper.selectList(Wrappers.<LineCategory>lambdaQuery().in(LineCategory::getFactoryCode, factoryCodes));
        return LineCategoryList.stream().collect(Collectors.groupingBy(LineCategory::getFactoryCode));
    }

    @Override
    public LineCategory getByCode(String code) {
        //编码上有唯一索引
        return baseMapper.selectOne(Wrappers.<LineCategory>lambdaQuery().eq(LineCategory::getCode,code));
    }

    @Override
    public List<LineCategory> listByCodes(Collection<String> codes) {
        return baseMapper.selectList(Wrappers.<LineCategory>lambdaQuery().in(LineCategory::getCode,codes));
    }

    @Override
    public List<LineCategory> listByFactoryCode(String factoryCode) {
        return baseMapper.selectList(Wrappers.<LineCategory>lambdaQuery().eq(LineCategory::getFactoryCode,factoryCode));
    }

    @Override
    public List<LineCategory> listByWorkshopCode(String workshopCode) {
        return baseMapper.selectList(Wrappers.<LineCategory>lambdaQuery().eq(LineCategory::getWorkshopCode,workshopCode));
    }

    @Override
    public List<LineCategory> listByFactoryCodes(Collection<String> factoryCodes) {
        if (CollectionUtils.isEmpty(factoryCodes)) {
            return Lists.newArrayList();
        }
        if (factoryCodes.size() == 1) {
            return this.listByFactoryCode(factoryCodes.iterator().next());
        }
        return baseMapper.selectList(Wrappers.<LineCategory>lambdaQuery().in(LineCategory::getFactoryCode, factoryCodes));
    }
}
