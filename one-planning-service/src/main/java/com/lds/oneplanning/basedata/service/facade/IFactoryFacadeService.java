package com.lds.oneplanning.basedata.service.facade;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.model.FactoryDTO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface IFactoryFacadeService  {
    Page<FactoryDTO> page(Long userId,String keyword,String factoryCode,String materialGroupCode, Integer pageNum, Integer pageSize,boolean decorate);
    FactoryDTO getDetail(Long id);

    Integer edit(FactoryDTO dto);
    List<Factory> listByUser(Long userId);

    /**
     * 获取用户工厂代码
     * @param userId
     * @return
     */
    Set<String> getUserFactoryCodes(Long userId);

}
