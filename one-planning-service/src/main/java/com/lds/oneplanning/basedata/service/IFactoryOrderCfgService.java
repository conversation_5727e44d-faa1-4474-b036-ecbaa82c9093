package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.FactoryOrderCfg;
import com.lds.oneplanning.basedata.model.FactoryOrderCfgDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-13
 */
public interface IFactoryOrderCfgService extends IService<FactoryOrderCfg> {

    Page<FactoryOrderCfgDTO> page(String keyword, String factoryCode, Integer pageNum, Integer pageSize);
    Set<String> listOrderSubTypeByFactoryCode(String factoryCode,Integer businessType);
    List<FactoryOrderCfg> listByFactoryCodes(Collection<String> factoryCodes,Integer businessType);

    Integer batchSaveUpdate(FactoryOrderCfgDTO dto);

    FactoryOrderCfgDTO getByFactoryCode(String factoryCode);

    Integer deleteByFactoryCode(String factoryCode);

    Integer batchDeleteByFactoryCodes(Collection<String> factoryCodes);
}
