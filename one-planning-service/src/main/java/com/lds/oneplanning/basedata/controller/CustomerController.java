package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.Customer;
import com.lds.oneplanning.basedata.service.ICustomerService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Api(value = "CustomerController", tags = "客户信息")
@RestController
@RequestMapping("/basedata/customer")
public class CustomerController {


    @Resource
    private ICustomerService customerService;

    @ApiOperation(value = "同步客户数据", notes = "同步客户数据")
    @GetMapping("/syncData")
    @Loggable(businessName = "客户数据",operation = "同步")
    public void syncData(){
        customerService.syncFromSap();
    }

    @ApiOperation(value = "获取客户列表", notes = "获取客户列表")
    @GetMapping("/list")
    public List<Customer> list(){
        return customerService.list();
    }

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<Customer> page(@RequestParam(value = "keyword",required = false)String keyword,
                                     @RequestParam(value = "pageNum")Integer pageNum,
                                     @RequestParam(value = "pageSize")Integer pageSize
    ){
        return customerService.page(keyword,pageNum,pageSize);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public Customer detail(@PathVariable("id")Long id){
        return  customerService.getById(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "客户数据",operation = "新增")
    public Long add(@RequestBody Customer dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        customerService.save(dto);
        return dto.getId();
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "客户数据",operation = "编辑")
    public Integer edit(@RequestBody Customer dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  customerService.updateById(dto) ? 1 : 0;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "客户数据",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  customerService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "客户数据",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  customerService.removeByIds(ids) ? 1:0;
    }
    
    
}
