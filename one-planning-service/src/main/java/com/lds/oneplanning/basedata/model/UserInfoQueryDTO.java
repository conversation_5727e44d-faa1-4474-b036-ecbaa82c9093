package com.lds.oneplanning.basedata.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotNull
    private Integer pageNum;
    @NotNull
    private Integer pageSize;
    /**
     * 关键字：
     */
    private String keyword;
    /**
     * 工厂编码：
     */
    private String factoryCode;
    /**
     * 工厂编码列表
     */
    private Collection<String> factoryCodeList;

    /**
     * 用户类型：
     */
    private String userType;

    /**
     * 工号：
     */
    private String empNo;

    /**
     * 工号：
     */
    private Long userId;

}
