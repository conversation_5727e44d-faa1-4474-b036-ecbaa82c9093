package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.LineCategory;
import com.lds.oneplanning.basedata.model.LineCategoryDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface ILineCategoryService extends IService<LineCategory> {

    Page<LineCategoryDTO> page(String keyword, String factoryCode, Integer pageNum, Integer pageSize);

    LineCategoryDTO detail(Long id);

    Long add(LineCategoryDTO dto);

    Integer edit(LineCategoryDTO dto);

    Integer delete(Long id);
    Integer batchDelete(Collection<Long> ids);

    Map<String,List<LineCategory>> groupByFactoryCodes(Collection<String> factoryCodes);

    LineCategory getByCode(String code);
    List<LineCategory> listByCodes(Collection<String> codes);
    List<LineCategory> listByFactoryCode(String factoryCode);

    List<LineCategory> listByWorkshopCode(String workshopCode);

    List<LineCategory> listByFactoryCodes(Collection<String> factoryCodes);
}
