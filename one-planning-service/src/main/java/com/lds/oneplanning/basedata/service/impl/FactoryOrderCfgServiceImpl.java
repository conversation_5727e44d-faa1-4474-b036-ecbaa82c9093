package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.FactoryOrderCfg;
import com.lds.oneplanning.basedata.mapper.FactoryOrderCfgMapper;
import com.lds.oneplanning.basedata.model.FactoryOrderCfgDTO;
import com.lds.oneplanning.basedata.service.IFactoryOrderCfgService;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.utils.BizOrderCfgUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;




/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-13
 */
@Service
public class FactoryOrderCfgServiceImpl extends ServiceImpl<FactoryOrderCfgMapper, FactoryOrderCfg> implements IFactoryOrderCfgService {

    @Resource
    private IFactoryService factoryService;
    @Override
    public Page<FactoryOrderCfgDTO> page(String keyword, String factoryCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<FactoryOrderCfg> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<FactoryOrderCfg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(FactoryOrderCfg::getFactoryCode);

        queryWrapper.eq(StringUtils.isNotBlank(factoryCode),FactoryOrderCfg::getFactoryCode,factoryCode);
        if (StringUtils.isNotBlank(keyword)) {
           Set<String> keywordFactoryCodes = factoryService.findList(keyword).stream().map(Factory::getCode).collect(Collectors.toSet());
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(FactoryOrderCfg::getFactoryCode,keyword).or()
                    .in(CollectionUtils.isNotEmpty(keywordFactoryCodes),FactoryOrderCfg::getFactoryCode,keywordFactoryCodes));
        }
        queryWrapper.groupBy(FactoryOrderCfg::getFactoryCode);
        queryWrapper.orderByDesc(FactoryOrderCfg::getFactoryCode).orderByAsc(FactoryOrderCfg::getId);
        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<FactoryOrderCfgDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<FactoryOrderCfgDTO> results = BeanUtil.mapList(entityPage.getRecords(), FactoryOrderCfgDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    @Override
    public Set<String> listOrderSubTypeByFactoryCode(String factoryCode,Integer businessType) {
         List<FactoryOrderCfg> list = this.listByFactoryCodes(Lists.newArrayList(factoryCode),businessType);
         return list.stream().map(FactoryOrderCfg::getOrderTypeValue).collect(Collectors.toSet());
    }

    private void decorate(List<FactoryOrderCfgDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> factoryCodes = sourceList.stream().map(FactoryOrderCfgDTO::getFactoryCode).collect(Collectors.toSet());
        Map<String,String> factoryNameMap = factoryService.listByFactoryCodes(factoryCodes).stream().collect(Collectors.toMap(Factory::getCode, Factory::getName,(s, s2) -> s2));
        Map<String,List<FactoryOrderCfg>> orderTypeList =this.listByFactoryCodes(factoryCodes,BaseDataConstant.BUSINESS_TYPE_ORDER).stream().collect(Collectors.groupingBy(FactoryOrderCfg::getFactoryCode));
        Map<String,List<FactoryOrderCfg>> subOrderTypeList =this.listByFactoryCodes(factoryCodes,BaseDataConstant.BUSINESS_TYPE_SUB_ORDER).stream().collect(Collectors.groupingBy(FactoryOrderCfg::getFactoryCode));
        sourceList.forEach(orderCfgDTO -> {
            orderCfgDTO.setFactoryName(factoryNameMap.get(orderCfgDTO.getFactoryCode()));
            orderCfgDTO.setProductOrderSubTypeList(BizOrderCfgUtil.buildProductOrderSubTypeList());
            List<FactoryOrderCfg> cfgList = Optional.ofNullable(subOrderTypeList.get(orderCfgDTO.getFactoryCode())).orElse(Lists.newArrayList());
            List<String> orderSubTypes = cfgList.stream().map(FactoryOrderCfg::getOrderTypeValue).collect(Collectors.toList());
            orderCfgDTO.getProductOrderSubTypeList().forEach(configDTO -> {
                configDTO.setIsChecked(orderSubTypes.contains(configDTO.getConfigCode()) ? 1 : 0);
            });

            orderCfgDTO.setOrderTypeList(BizOrderCfgUtil.buildOrderTypeList());
            List<FactoryOrderCfg> orderTypes = Optional.ofNullable(orderTypeList.get(orderCfgDTO.getFactoryCode())).orElse(Lists.newArrayList());
            List<String> orderTypeSets = orderTypes.stream().map(FactoryOrderCfg::getOrderTypeValue).collect(Collectors.toList());
            orderCfgDTO.getOrderTypeList().forEach(configDTO -> {
                configDTO.setIsChecked(orderTypeSets.contains(configDTO.getConfigCode()) ? 1 : 0);
            });

        });

    }

    @Override
    public List<FactoryOrderCfg> listByFactoryCodes(Collection<String> factoryCodes,Integer businessType) {
        if (factoryCodes == null || factoryCodes.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<FactoryOrderCfg>lambdaQuery()
                        .eq(businessType!=null,FactoryOrderCfg::getBusinessType,businessType)
                .in(factoryCodes!=null,FactoryOrderCfg::getFactoryCode,factoryCodes));
    }

    @Override
    public Integer batchSaveUpdate(FactoryOrderCfgDTO dto) {
        String factoryCode= dto.getFactoryCode();
        this.baseMapper.delete(Wrappers.<FactoryOrderCfg>lambdaQuery().eq(FactoryOrderCfg::getFactoryCode,factoryCode));
        if (CollectionUtils.isEmpty(dto.getOrderTypeList()) || CollectionUtils.isEmpty(dto.getProductOrderSubTypeList())) {
            return 0;
        }
        List<FactoryOrderCfg> targetList = Lists.newArrayList();
        dto.getProductOrderSubTypeList().stream().filter(configDTO -> 1 == configDTO.getIsChecked()).forEach(configDTO -> {
            FactoryOrderCfg cfg = new FactoryOrderCfg();
            cfg.setBusinessType(BaseDataConstant.BUSINESS_TYPE_SUB_ORDER);
            cfg.setFactoryCode(factoryCode);
            cfg.setOrderTypeValue(configDTO.getConfigCode());
            cfg.setCreateTime(new Date());
            cfg.setUpdateTime(new Date());
            targetList.add(cfg);
        });
        dto.getOrderTypeList().stream().filter(configDTO -> 1 == configDTO.getIsChecked()).forEach(configDTO -> {
            FactoryOrderCfg cfg = new FactoryOrderCfg();
            cfg.setBusinessType(BaseDataConstant.BUSINESS_TYPE_ORDER);
            cfg.setFactoryCode(factoryCode);
            cfg.setOrderTypeValue(configDTO.getConfigCode());
            cfg.setCreateTime(new Date());
            cfg.setUpdateTime(new Date());
            targetList.add(cfg);
        });
        if (!targetList.isEmpty()) {
            this.saveBatch(targetList);
            return 1;
        }
        return  0;
    }

    @Override
    public FactoryOrderCfgDTO getByFactoryCode(String factoryCode) {
        List<FactoryOrderCfg> cfgList = this.listByFactoryCodes(Lists.newArrayList(factoryCode),null);
        if (cfgList.isEmpty()) {
            return null;
        }
        FactoryOrderCfg template = cfgList.get(0);
        FactoryOrderCfgDTO res = new FactoryOrderCfgDTO();
        res.setFactoryCode(template.getFactoryCode());
        res.setFactoryName(template.getFactoryCode());
        this.decorate(Lists.newArrayList(res));
        return res;
    }

    @Override
    public Integer deleteByFactoryCode(String factoryCode) {
        return this.baseMapper.delete(Wrappers.<FactoryOrderCfg>lambdaQuery().eq(FactoryOrderCfg::getFactoryCode,factoryCode));
    }

    @Override
    public Integer batchDeleteByFactoryCodes(Collection<String> factoryCodes) {
        if (factoryCodes == null || factoryCodes.isEmpty()) {
            return 0;
        }
        return this.baseMapper.delete(Wrappers.<FactoryOrderCfg>lambdaQuery().in(FactoryOrderCfg::getFactoryCode,factoryCodes));
    }
}
