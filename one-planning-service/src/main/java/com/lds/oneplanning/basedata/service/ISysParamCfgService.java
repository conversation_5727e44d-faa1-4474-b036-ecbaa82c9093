package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.basedata.entity.SysParamCfg;
import com.lds.oneplanning.basedata.model.SysParamCfgDTO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface ISysParamCfgService extends IService<SysParamCfg> {
    Long saveSysParamCfg(SysParamCfgDTO sysParamCfgDto);

    Long updateSysParamCfg(SysParamCfgDTO sysParamCfgDto);

    void deleteById(Long id);

    String getValueByCode(String code);
}
