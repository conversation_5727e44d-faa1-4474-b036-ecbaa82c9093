package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Customer;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.entity.SchedulePriority;
import com.lds.oneplanning.basedata.mapper.SchedulePriorityMapper;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.model.ProductGroupDTO;
import com.lds.oneplanning.basedata.model.SchedulePriorityDTO;
import com.lds.oneplanning.basedata.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Service
public class SchedulePriorityServiceImpl extends ServiceImpl<SchedulePriorityMapper, SchedulePriority> implements ISchedulePriorityService {
    @Resource
    private ILineInfoService lineInfoService;
    @Resource
    private IProductGroupService productGroupService;
    @Resource
    private ICustomerService customerService;
    @Resource
    private IPlannerLineCfgService plannerLineCfgService;


    @Override
    public List<SchedulePriorityDTO> list(Long userId,String lineCode,String productGroupCode,String customerCode) {
        LambdaQueryWrapper<SchedulePriority> queryWrapper = new LambdaQueryWrapper<>();
        if (userId != null) {
            Set<String> lineCodes = plannerLineCfgService.listLineInfoByUserId(userId).stream().map(LineInfoDTO::getCode).collect(Collectors.toSet());
            queryWrapper.in(CollectionUtils.isNotEmpty(lineCodes), SchedulePriority::getLineCode,lineCodes);
        }
        queryWrapper.eq(StringUtils.isNotBlank(lineCode),SchedulePriority::getLineCode,lineCode);
        queryWrapper.eq(StringUtils.isNotBlank(productGroupCode),SchedulePriority::getProductGroupCode,productGroupCode);
        queryWrapper.eq(StringUtils.isNotBlank(customerCode),SchedulePriority::getCustomerCode,customerCode);

         List<SchedulePriority> endityList = baseMapper.selectList(queryWrapper);
         List<SchedulePriorityDTO> res = BeanUtil.mapList(endityList,SchedulePriorityDTO.class);
         this.decorate(res);
         return res;
    }

    @Override
    public Page<SchedulePriorityDTO> page(Long userId,String keyword,String lineCode,String productGroupCode,String customerCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SchedulePriority> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SchedulePriority> queryWrapper = new LambdaQueryWrapper<>();
        if (userId != null) {
            Set<String> lineCodes = plannerLineCfgService.listLineInfoByUserId(userId).stream().map(LineInfoDTO::getCode).collect(Collectors.toSet());
            queryWrapper.in(CollectionUtils.isNotEmpty(lineCodes), SchedulePriority::getLineCode,lineCodes);
        }
        queryWrapper.eq(StringUtils.isNotBlank(lineCode),SchedulePriority::getLineCode,lineCode);
        queryWrapper.eq(StringUtils.isNotBlank(productGroupCode),SchedulePriority::getProductGroupCode,productGroupCode);
        queryWrapper.eq(StringUtils.isNotBlank(customerCode),SchedulePriority::getCustomerCode,customerCode);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(SchedulePriority::getLineCode,keyword).or()
                    .like(SchedulePriority::getProductGroupCode,keyword).or().like(SchedulePriority::getCustomerCode,keyword));
        }
        queryWrapper.orderByDesc(SchedulePriority::getUpdateTime).orderByAsc(SchedulePriority::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<SchedulePriorityDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<SchedulePriorityDTO> results = BeanUtil.mapList(entityPage.getRecords(), SchedulePriorityDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<SchedulePriorityDTO> souceList){
        if (souceList == null || souceList.isEmpty()) {
            return;
        }
        Set<String> lineCodes = souceList.stream().map(SchedulePriorityDTO::getLineCode).collect(Collectors.toSet());
        Set<String> productGroupCodes = souceList.stream().map(SchedulePriorityDTO::getProductGroupCode).collect(Collectors.toSet());
        Set<String> customerCodes = souceList.stream().map(SchedulePriorityDTO::getCustomerCode).collect(Collectors.toSet());
        Map<String,String> lineMap = lineInfoService.listByCodes(lineCodes).stream().collect(Collectors
                .toMap(LineInfo::getCode,LineInfo::getName,(s, s2) -> s2));
        Map<String,String> productGroupMap = productGroupService.listByCodes(productGroupCodes).stream().collect(Collectors
                .toMap(ProductGroupDTO::getCode,ProductGroupDTO::getName,(s, s2) -> s2));
        Map<String,String> customerMap = customerService.listByCodes(customerCodes).stream()
                .collect(Collectors.toMap(Customer::getCode,Customer::getName,(s, s2) -> s2));
        souceList.stream().forEach(schedulePriorityDTO -> {
            schedulePriorityDTO.setLineName(lineMap.get(schedulePriorityDTO.getLineCode()));
            schedulePriorityDTO.setProductGroupName(productGroupMap.get(schedulePriorityDTO.getProductGroupCode()));
            schedulePriorityDTO.setCustomerName(customerMap.get(schedulePriorityDTO.getCustomerCode()));
        });
    }

    @Override
    public SchedulePriorityDTO getDetail(Long id) {
        SchedulePriority  entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        SchedulePriorityDTO res =  BeanUtil.map(entity, SchedulePriorityDTO.class);
        this.decorate(Lists.newArrayList(res));
        return res;
    }

    @Override
    public List<SchedulePriority> listByProductGroupCodes(List<String> lineUuids, Collection<String> productGroupCodes) {
        if (CollectionUtils.isEmpty(lineUuids) || CollectionUtils.isEmpty(productGroupCodes)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.selectList(Wrappers.<SchedulePriority>lambdaQuery()
                .in(SchedulePriority::getLineUuid, lineUuids)
                .in(SchedulePriority::getProductGroupCode, productGroupCodes));
    }
}
