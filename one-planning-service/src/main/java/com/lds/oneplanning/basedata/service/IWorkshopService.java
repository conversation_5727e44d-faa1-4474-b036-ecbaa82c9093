package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.Workshop;
import com.lds.oneplanning.basedata.model.WorkshopDTO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-20
 */
public interface IWorkshopService extends IService<Workshop> {
    List<WorkshopDTO> listByCodes(Collection<String> codes);

    List<WorkshopDTO> listByFactoryCode(String factoryCode);
    Page<WorkshopDTO> page(String keyword,String factoryCode, Integer pageNum, Integer pageSize);


    WorkshopDTO getDetail(Long id);
    WorkshopDTO getByCode(String code);
}
