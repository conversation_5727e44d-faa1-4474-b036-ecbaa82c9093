package com.lds.oneplanning.basedata.model.req;

import com.lds.oneplanning.basedata.model.UserFactoryRelDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/17
 */
@Data
public class UserInfoAddReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @NotBlank
    @ApiModelProperty(value = "用户类型")
    private String userType;

    @ApiModelProperty(value = "工号")
    private String empNo;

    @NotEmpty
    @ApiModelProperty(value = "用户关联工厂列表")
    private List<UserFactoryRelDTO> userFactoryList;
}
