package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iot.common.exception.BusinessException;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.LineCapacity;
import com.lds.oneplanning.basedata.entity.LineCategory;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.mapper.LineCapacityMapper;
import com.lds.oneplanning.basedata.model.LineCapacityBatchDTO;
import com.lds.oneplanning.basedata.model.LineCapacityDTO;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.service.*;
import com.lds.oneplanning.mps.exception.MpsExceptionEnum;
import com.lds.oneplanning.mps.utils.ScheduleCalculateUtil;
import com.lds.oneplanning.wps.schedule.model.WpsProductionLine;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class LineCapacityServiceImpl extends ServiceImpl<LineCapacityMapper, LineCapacity> implements ILineCapacityService {
    @Resource
    private ILineCategoryService lineCategoryService;
    @Resource
    private ILineInfoService lineInfoService;
    @Resource
    private IFactoryService factoryService;
    @Resource
    private IPlannerLineCfgService plannerLineCfgService;

    @Override
    public void batchCreate(LineCapacityBatchDTO createDTO, Long userId) {
        if (CollectionUtils.isEmpty(createDTO.getConfigCodes())) {
            return;
        }
       List<LineInfo> lineInfos = lineInfoService.listByCodes(createDTO.getConfigCodes());
       Map<String,String> lineUuidMap = lineInfos.stream().collect(Collectors.toMap(LineInfo::getCode,LineInfo::getLineUuid,(s, s2) -> s2));
        List<LineCapacity> targetList = Lists.newArrayList();
        createDTO.getConfigCodes().stream().forEach(configCode -> {
            LocalDate indexDate = createDTO.getStartDate();
            while (indexDate.compareTo(createDTO.getEndDate()) <= 0) {
                if (createDTO.getRestDayList() != null && createDTO.getRestDayList().contains(indexDate)) {
                    indexDate = indexDate.plusDays(1);
                    continue;
                }
                LineCapacity capacity = new LineCapacity();
                capacity.setLineUuid(lineUuidMap.get(configCode));
                capacity.setConfigType(createDTO.getConfigType());
                capacity.setConfigCode(configCode);
                capacity.setScheduleDate(indexDate);
                capacity.setProductHours(createDTO.getProductHours());
                capacity.setLoadPercent(createDTO.getLoadPercent());
                capacity.setCreateBy(userId);
                capacity.setCreateTime(new Date());
                capacity.setUpdateBy(userId);
                capacity.setUpdateTime(new Date());
                capacity.setDayType(createDTO.getDayType());
                // 暂无更好的批量清理办法
                this.deleteByCodeAndDate(createDTO.getConfigType(),configCode,createDTO.getDayType(),indexDate);
                targetList.add(capacity);
                indexDate = indexDate.plusDays(1);
            }
        });
        this.saveBatch(targetList);
    }

    @Override
    public void batchUpdate(LineCapacityBatchDTO batchDTO, Long userId) {
        if (batchDTO.getIds() == null || batchDTO.getIds().isEmpty()) {
            return;
        }
        List<LineCapacity> updateList = Lists.newArrayList();
        batchDTO.getIds().stream().forEach(id -> {
            LineCapacity capacity = new LineCapacity();
            capacity.setId(id);
            capacity.setProductHours(batchDTO.getProductHours());
            capacity.setLoadPercent(batchDTO.getLoadPercent());
            capacity.setUpdateTime(new Date());
            updateList.add(capacity);
        });
        this.updateBatchById(updateList);
    }

    @Override
    public Page<LineCapacityDTO> page(Long userId, String keyword, String factoryCode, String workshopCode, LocalDate startDate, LocalDate endDate,
                                      Integer configType, String configCode,Integer dayType, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<LineCapacity> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<LineCapacity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LineCapacity::getConfigType, configType);
        if (userId != null) {
            Set<String> lineCodes = plannerLineCfgService.listLineInfoByUserId(userId).stream().map(LineInfoDTO::getCode).collect(Collectors.toSet());
            queryWrapper.in(CollectionUtils.isNotEmpty(lineCodes), LineCapacity::getConfigCode, lineCodes);
        }
        if (StringUtils.isNotBlank(factoryCode)) {
            if (BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY.equals(configType)) {
                Set<String> lineCategoryCodes = lineCategoryService.listByFactoryCode(factoryCode).stream().map(LineCategory::getCode).collect(Collectors.toSet());
                queryWrapper.in(CollectionUtils.isNotEmpty(lineCategoryCodes), LineCapacity::getConfigCode, lineCategoryCodes);
            } else {
                Set<String> lineCodes = lineInfoService.listByFactoryCode(factoryCode).stream().map(LineInfo::getCode).collect(Collectors.toSet());
                queryWrapper.in(CollectionUtils.isNotEmpty(lineCodes), LineCapacity::getConfigCode, lineCodes);
            }
        }
        if (StringUtils.isNotBlank(workshopCode)) {
            if (BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY.equals(configType)) {
                Set<String> lineCategoryCodes = lineCategoryService.listByWorkshopCode(workshopCode).stream().map(LineCategory::getCode).collect(Collectors.toSet());
                queryWrapper.in(CollectionUtils.isNotEmpty(lineCategoryCodes), LineCapacity::getConfigCode, lineCategoryCodes);
            } else {
                Set<String> lineCodes = lineInfoService.listByWorkshopCode(workshopCode).stream().map(LineInfo::getCode).collect(Collectors.toSet());
                queryWrapper.in(CollectionUtils.isNotEmpty(lineCodes), LineCapacity::getConfigCode, lineCodes);
            }
        }
        if (StringUtils.isNotBlank(configCode)) {
            queryWrapper.eq(LineCapacity::getConfigCode, configCode);
        }
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(LineCapacity::getConfigCode, keyword).or()
                    .like(LineCapacity::getConfigCode, keyword));
        }
        queryWrapper.ge(startDate != null, LineCapacity::getScheduleDate, startDate);
        queryWrapper.le(endDate != null, LineCapacity::getScheduleDate, endDate);
        queryWrapper.eq(dayType!=null,LineCapacity::getDayType,dayType);
        queryWrapper.orderByAsc(LineCapacity::getScheduleDate).orderByAsc(LineCapacity::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<LineCapacityDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<LineCapacityDTO> results = BeanUtil.mapList(entityPage.getRecords(), LineCapacityDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results, configType);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<LineCapacityDTO> results, Integer configType) {
        if (results == null || results.isEmpty()) {
            return;
        }
        Set<String> lineUuids = results.stream().map(LineCapacityDTO::getLineUuid).collect(Collectors.toSet());
        List<LineInfo> lineInfos = lineInfoService.listByUuids(lineUuids);
        Map<String, String> configNameMap = lineInfos.stream().collect(Collectors.toMap(LineInfo::getLineUuid, LineInfo::getName,(s, s2) -> s2));
        Map<String, String> factoryCodeMap = lineInfos.stream().filter(lineInfo -> StringUtils.isNotBlank(lineInfo.getFactoryCode()))
                .collect(Collectors.toMap(LineInfo::getLineUuid, LineInfo::getFactoryCode,(s, s2) -> s2));
        Map<String, String> factoryNameMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(factoryCodeMap)) {
            List<Factory> factories = factoryService.listByFactoryCodes(factoryCodeMap.values());
            factoryNameMap  = factories.stream().collect(Collectors.toMap(Factory::getCode, Factory::getName,(s, s2) -> s2));
        }
        for (LineCapacityDTO dto : results) {
            dto.setConfigName(configNameMap.get(dto.getLineUuid()));
            dto.setFactoryCode(factoryCodeMap.get(dto.getLineUuid()));
            if (dto.getFactoryCode() != null && MapUtils.isNotEmpty(factoryNameMap)) {
                dto.setFactoryName(factoryNameMap.get(dto.getFactoryCode()));
            }
        }
    }

    @Override
    public LineCapacityDTO getDetail(Long id) {
        LineCapacity entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        LineCapacityDTO res = BeanUtil.map(entity, LineCapacityDTO.class);
        this.decorate(Lists.newArrayList(res), entity.getConfigType());
        return res;
    }

    @Override
    public Long add(LineCapacityDTO dto) {
        LineCapacity entity = BeanUtil.map(dto, LineCapacity.class);
        LineCapacity codeAndDate = this.getByCodeAndDate(dto.getConfigType(), dto.getConfigCode(),dto.getDayType(), dto.getScheduleDate());
        if (codeAndDate != null) {
            throw new BusinessException(MpsExceptionEnum.RECORD_EXIST);
        }
        LineInfoDTO lineInfoDTO = lineInfoService.getByCode(dto.getConfigCode(), false);
        if (lineInfoDTO != null) {
            entity.setLineUuid(lineInfoDTO.getLineUuid());
        }
        baseMapper.insert(entity);
        return entity.getId();
    }

    @Override
    public Integer edit(LineCapacityDTO dto) {
        LineCapacity entity = BeanUtil.map(dto, LineCapacity.class);
        LineCapacity codeAndDate = this.getByCodeAndDate(dto.getConfigType(), dto.getConfigCode(),dto.getDayType(), dto.getScheduleDate());
        if (codeAndDate != null && !dto.getId().equals(codeAndDate.getId())) {
            throw new BusinessException(MpsExceptionEnum.RECORD_EXIST);
        }
        LineInfoDTO lineInfoDTO = lineInfoService.getByCode(dto.getConfigCode(), false);
        if (lineInfoDTO != null) {
            entity.setLineUuid(lineInfoDTO.getLineUuid());
        }
        return baseMapper.updateById(entity);
    }

    @Override
    public LineCapacity getByCodeAndDate(Integer configType, String configCode,Integer dayType, LocalDate scheduleDate) {
        return baseMapper.selectOne(Wrappers.<LineCapacity>lambdaQuery()
                .eq(LineCapacity::getConfigType, configType)
                .eq(LineCapacity::getConfigCode, configCode)
                .eq(dayType!=null,LineCapacity::getDayType, dayType)
                .eq(LineCapacity::getScheduleDate, scheduleDate)
                .last(" limit 1")
        );
    }

    private void deleteByCodeAndDate(Integer configType, String configCode,Integer dayType, LocalDate scheduleDate){
         baseMapper.delete(Wrappers.<LineCapacity>lambdaQuery()
                .eq(LineCapacity::getConfigType, configType)
                .eq(LineCapacity::getConfigCode, configCode)
                .eq(dayType!=null,LineCapacity::getDayType, dayType)
                .eq(LineCapacity::getScheduleDate, scheduleDate)
        );
    }

    @Override
    public Map<LocalDate, LineCapacity> getByCodeAndDates(Integer configType, String configCode,Integer dayType, List<LocalDate> scheduleDateList) {
        Map<LocalDate, LineCapacity> resultMap = Maps.newLinkedHashMap();
        if (StringUtils.isEmpty(configCode) || CollectionUtils.isEmpty(scheduleDateList)) {
            return resultMap;
        }
        List<LineCapacity> capacityList = baseMapper.selectList(Wrappers.<LineCapacity>lambdaQuery()
                .eq(LineCapacity::getConfigType, configType)
                .eq(LineCapacity::getConfigCode, configCode)
                .eq(dayType!=null,LineCapacity::getDayType,dayType)
                .in(LineCapacity::getScheduleDate, scheduleDateList)
                .orderByAsc(LineCapacity::getScheduleDate)
        );
        if (CollectionUtils.isEmpty(capacityList)) {
            return resultMap;
        }
        capacityList.forEach(capacity -> resultMap.put(capacity.getScheduleDate(), capacity));
        return resultMap;
    }


    @Override
    public Map<String, List<LineCapacity>> groupByTypeAndCodes(Integer configType, Collection<String> configCodes) {
        if (configType == null || CollectionUtils.isEmpty(configCodes) ) {
            return Maps.newHashMap();
        }
        return baseMapper.selectList(Wrappers.<LineCapacity>lambdaQuery()
                .eq(LineCapacity::getConfigType, configType)
                .in(LineCapacity::getConfigCode, configCodes))
                .stream().collect(Collectors.groupingBy(LineCapacity::getConfigCode));
    }

    @Override
    public Map<String, Map<LocalDate, WpsProductionLine>> getDailyProductionLineMap(Integer configType, List<String> lineUuids,Integer dayType, List<LocalDate> localDates) {
        if (null == configType || CollectionUtils.isEmpty(lineUuids) || CollectionUtils.isEmpty(localDates)) {
            return Maps.newHashMap();
        }
        List<LineCapacity> lineCapacities = this.baseMapper.selectList(Wrappers.<LineCapacity>lambdaQuery()
                .eq(LineCapacity::getConfigType, configType)
                .eq(dayType!=null,LineCapacity::getDayType,dayType)
                .in(LineCapacity::getLineUuid, lineUuids)
                .in(LineCapacity::getScheduleDate, localDates)
                .orderByAsc(LineCapacity::getScheduleDate));
        if (CollectionUtils.isEmpty(lineCapacities)) {
            return Maps.newHashMap();
        }
        Map<String, Map<LocalDate, WpsProductionLine>> resultMap = Maps.newHashMap();
        for (LineCapacity lineCapacity : lineCapacities) {
            String lineUuid = lineCapacity.getLineUuid();
            LocalDate scheduleDate = lineCapacity.getScheduleDate();
            WpsProductionLine productionLine = resultMap
                    .computeIfAbsent(lineUuid, k -> Maps.newLinkedHashMap())
                    .computeIfAbsent(scheduleDate, k -> new WpsProductionLine());
            float waitingLineHour = ScheduleCalculateUtil.calculateWaitingOrderHour(lineCapacity.getScheduleDate(), lineCapacity);
            productionLine.setLineUuid(lineCapacity.getLineUuid());
            productionLine.setLineCode(lineCapacity.getConfigCode());
            productionLine.setPlanScheduleHours(waitingLineHour);
            productionLine.setWaitingScheduleHours(waitingLineHour);
        }
        return resultMap;
    }

    /**
     * 获取分组后每个线体每天的排产时长（白班、夜班合并）
     * @param configType
     * @param lineUuids
     * @param dayType
     * @param localDates
     * @return
     */
    @Override
    public Map<String, Map<LocalDate, WpsProductionLine>> getGroupDailyProductionLineMap(Integer configType, List<String> lineUuids,Integer dayType, List<LocalDate> localDates) {
        if (null == configType || CollectionUtils.isEmpty(lineUuids) || CollectionUtils.isEmpty(localDates)) {
            return Maps.newHashMap();
        }
        List<LineCapacity> lineCapacities = this.baseMapper.selectList(Wrappers.<LineCapacity>lambdaQuery()
                .eq(LineCapacity::getConfigType, configType)
                .eq(dayType!=null,LineCapacity::getDayType,dayType)
                .in(LineCapacity::getLineUuid, lineUuids)
                .in(LineCapacity::getScheduleDate, localDates)
                .orderByAsc(LineCapacity::getScheduleDate)
        );
        if (CollectionUtils.isEmpty(lineCapacities)) {
            return Maps.newHashMap();
        }
        Map<String, Map<LocalDate, WpsProductionLine>> resultMap = Maps.newHashMap();
        Map<String,List<LineCapacity>> linCapacityMap = lineCapacities.stream().collect(Collectors.groupingBy(data->data.getLineUuid()+data.getScheduleDate(),LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<String,List<LineCapacity>> entry : linCapacityMap.entrySet()){
            List<LineCapacity> lineCapacityList = entry.getValue();
            float waitingLineHour = 0f;
            for (LineCapacity lineCapacity : lineCapacityList) {
                waitingLineHour =waitingLineHour +lineCapacity.getProductHours() * (lineCapacity.getLoadPercent()/100f);
            }
            LineCapacity lineCapacity = lineCapacityList.get(0);
            String lineUuid = lineCapacity.getLineUuid();
            LocalDate scheduleDate = lineCapacity.getScheduleDate();
            WpsProductionLine productionLine = resultMap
                    .computeIfAbsent(lineUuid, k -> Maps.newLinkedHashMap())
                    .computeIfAbsent(scheduleDate, k -> new WpsProductionLine());
            productionLine.setLineUuid(lineCapacity.getLineUuid());
            productionLine.setLineCode(lineCapacity.getConfigCode());
            productionLine.setPlanScheduleHours(waitingLineHour);
            productionLine.setWaitingScheduleHours(waitingLineHour);
        }
        return resultMap;
    }

    @Override
    public List<LineCapacity> listBylineUuid(String lineUUid,LocalDate startDate, LocalDate endDate) {
        return baseMapper.selectList(Wrappers.<LineCapacity>lambdaQuery()
                .eq(LineCapacity::getLineUuid,lineUUid)
                .ge(LineCapacity::getScheduleDate,startDate)
                .le(LineCapacity::getScheduleDate,endDate)
        );
    }

    @Override
    public Map<String, List<LineCapacity>> groupByLineUuid(Collection<String> lineUUids, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(lineUUids)) {
            return Maps.newHashMap();
        }
        return baseMapper.selectList(Wrappers.<LineCapacity>lambdaQuery()
                .in(LineCapacity::getLineUuid,lineUUids)
                .ge(startDate!=null,LineCapacity::getScheduleDate,startDate)
                .le(endDate!=null,LineCapacity::getScheduleDate,endDate)).stream().collect(Collectors.groupingBy(LineCapacity::getLineUuid));
    }
}
