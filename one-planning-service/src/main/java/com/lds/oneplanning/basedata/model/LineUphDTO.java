package com.lds.oneplanning.basedata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LineUphDTO", description="")
public class LineUphDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "线体uuid")
    private String lineUuid;

    @ApiModelProperty(value = "配置项：1产线类型 2产线")
    private Integer configType;
    @ApiModelProperty(value = "配置编码")
    private String configCode;


    @ApiModelProperty(value = "产品id商品id")
    private String productId;

    @ApiModelProperty(value = "每小时产能")
    private Integer uph;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // 以下非库表所有
    private String configName;

}
