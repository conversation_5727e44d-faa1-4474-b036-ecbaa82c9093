package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.LineCategoryRule;
import com.lds.oneplanning.basedata.model.LineCategoryRuleDTO;
import com.lds.oneplanning.basedata.service.ILineCategoryRuleService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Api(value = "LineCategoryRuleController", tags = "产线规则（专线）")
@RestController
@RequestMapping("/basedata/lineCategoryRule")
public class LineCategoryRuleController {
    
    @Resource
    private ILineCategoryRuleService lineCategoryRuleService;

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<LineCategoryRuleDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                          @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                          @RequestParam(value = "lineCategoryCode",required = false)String lineCategoryCode,
                                          @RequestParam(value = "pageNum")Integer pageNum,
                                          @RequestParam(value = "pageSize")Integer pageSize
    ){
        return lineCategoryRuleService.page(keyword,factoryCode,lineCategoryCode,pageNum,pageSize);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public LineCategoryRuleDTO detail(@PathVariable("id")Long id){
        return  lineCategoryRuleService.getDetail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "专线规则",operation = "新增")
    public Long add(@RequestBody LineCategoryRule dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        lineCategoryRuleService.save(dto);
        return dto.getId();
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "专线规则",operation = "编辑")
    public Integer edit(@RequestBody LineCategoryRule dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  lineCategoryRuleService.updateById(dto) ? 1 : 0;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "专线规则",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  lineCategoryRuleService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "专线规则",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  lineCategoryRuleService.removeByIds(ids) ? 1:0;
    }
}
