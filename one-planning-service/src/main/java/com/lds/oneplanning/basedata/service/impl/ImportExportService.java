package com.lds.oneplanning.basedata.service.impl;

/**
 * 描述：导入导出服务
 * 创建人： LaiGuiMing
 * 创建时间： 2025/2/20
 */
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.FileUtils;
import com.alibaba.excel.util.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;

@Service
public class ImportExportService<T>  {

    public List<T> importData(MultipartFile file, Class clazz) {
        File tempFile = convertMultipartFileToFile(file);
        List<T> list = EasyExcel.read(tempFile)
                .head(clazz)
                .sheet()
                .doReadSync();
        // 删除临时文件
        tempFile.delete();
        return list;
    }

    public ResponseEntity exportData(List<T> data, Class clazz, String excelName) {
        try {
            if(StringUtils.isEmpty(excelName)){
                excelName = "export.xlsx";
            }else {
                excelName += ".xlsx";
            }
            // 创建临时文件
            File tempFile = File.createTempFile("export-", ".xlsx");
            // 调用服务层进行导出
            EasyExcel.write(tempFile, clazz)
                    .sheet("Data")
                    .doWrite(data);
            // 读取文件内容
            byte[] fileContent = FileUtils.readFileToByteArray(tempFile);
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFileName = URLEncoder.encode(excelName, StandardCharsets.UTF_8.toString());
            headers.setContentDispositionFormData("attachment", encodedFileName);
            // 删除临时文件
            tempFile.delete();
            return new ResponseEntity<>(fileContent, headers, HttpStatus.OK);
        } catch (IOException e) {
            return new ResponseEntity<>(null, null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private File convertMultipartFileToFile(MultipartFile multipartFile){
        File tempFile = null;
        try{
            tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
            multipartFile.transferTo(tempFile);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
        return tempFile;
    }
}
