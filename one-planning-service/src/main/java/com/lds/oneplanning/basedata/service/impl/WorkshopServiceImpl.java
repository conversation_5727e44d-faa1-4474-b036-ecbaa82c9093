package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.iot.common.util.SpringUtil;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.Workshop;
import com.lds.oneplanning.basedata.mapper.WorkshopMapper;
import com.lds.oneplanning.basedata.model.WorkshopDTO;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.IWorkshopService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-20
 */
@Service
public class WorkshopServiceImpl extends ServiceImpl<WorkshopMapper, Workshop> implements IWorkshopService {

    @Override
    public List<WorkshopDTO> listByCodes(Collection<String> codes) {
        if (codes == null || codes.isEmpty()) {
            return Lists.newArrayList();
        }
        List<Workshop> list = baseMapper.selectList(Wrappers.<Workshop>lambdaQuery()
                .in(Workshop::getCode,codes));
        List<WorkshopDTO> resList = BeanUtil.mapList(list, WorkshopDTO.class);
        this.decorate(resList);
        return resList;
    }

    @Override
    public WorkshopDTO getByCode(String code) {
        List<WorkshopDTO> workshopDTOS = this.listByCodes(Lists.newArrayList(code));
        return workshopDTOS.isEmpty() ? null : workshopDTOS.get(0);
    }

    @Override
    public List<WorkshopDTO> listByFactoryCode(String factoryCode) {
        List<Workshop> list = baseMapper.selectList(Wrappers.<Workshop>lambdaQuery()
                .eq(StringUtils.isNoneBlank(factoryCode),Workshop::getFactoryCode, factoryCode));
        List<WorkshopDTO> resList = BeanUtil.mapList(list, WorkshopDTO.class);
        this.decorate(resList);
        return resList;
    }

    private void decorate(List<WorkshopDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> factoryCodes = sourceList.stream().map(WorkshopDTO::getFactoryCode).collect(Collectors.toSet());
        List<Factory> factories = SpringUtil.getBean(IFactoryService.class).listByFactoryCodes(factoryCodes);
        Map<String,String> factoryMap = factories.stream().collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        sourceList.stream().forEach(dto -> {
            dto.setFactoryName(factoryMap.get(dto.getFactoryCode()));
        });
    }
    @Override
    public Page<WorkshopDTO> page(String keyword,String factoryCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<Workshop> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Workshop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneBlank(factoryCode),Workshop::getFactoryCode,factoryCode);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(Workshop::getCode,keyword).or()
                    .like(Workshop::getName,keyword));
        }
        queryWrapper.orderByDesc(Workshop::getUpdateTime).orderByAsc(Workshop::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<WorkshopDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<WorkshopDTO> results = BeanUtil.mapList(entityPage.getRecords(), WorkshopDTO.class);
            this.decorate(results);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    @Override
    public WorkshopDTO getDetail(Long id) {
        Workshop workshop = baseMapper.selectById(id);
        if (workshop == null) {
            return null;
        }
        WorkshopDTO dto = BeanUtil.map(workshop, WorkshopDTO.class);
        this.decorate(Lists.newArrayList(dto));
        return dto;
    }
}
