package com.lds.oneplanning.basedata.service;

import com.lds.oneplanning.basedata.entity.PlannerLineCategory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.basedata.model.PlannerLineCategoryDTO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface IPlannerLineCategoryService extends IService<PlannerLineCategory> {

    List<PlannerLineCategoryDTO> listByUserId(Long userId);

    void saveByUserId(List<PlannerLineCategoryDTO> lineCategoryList, Long userId);

    void deleteByUserId(Long userId);
}
