package com.lds.oneplanning.basedata.model;

import com.lds.oneplanning.basedata.entity.ProductGroupRel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ProductGroup对象", description="")
public class ProductGroupDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    private String workshopName;
    private String factoryName;

    private List<ProductGroupRel>  productList = Lists.newArrayList();
    /**
     *  产品id或者商品id
     */
    private Set<String> productIds;

    public Set<String> getProductIds() {
        if (CollectionUtils.isNotEmpty(productIds)) {
            return productIds;
        }
        return productList.stream().map(ProductGroupRel::getProductId).collect(Collectors.toSet());
    }
}
