package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.FactoryScheduleBuffer;
import com.lds.oneplanning.basedata.model.FactoryScheduleBufferDTO;

import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-14
 */
public interface IFactoryScheduleBufferService extends IService<FactoryScheduleBuffer> {

    Page<FactoryScheduleBufferDTO> page(String keyword, String factoryCode, Integer businessType, Integer pageNum, Integer pageSize);

    FactoryScheduleBufferDTO getDetail(Long id);

    Map<Integer, Integer> getMapByFactoryCode(String factoryCode);
}
