package com.lds.oneplanning.basedata.service;

import com.lds.oneplanning.basedata.entity.MaterialGroupRel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
public interface IMaterialGroupRelService extends IService<MaterialGroupRel> {

    List<MaterialGroupRel> listByGroupCodes(Collection<String> groupCodes);

    Integer batchUpdateByGroupCode(String groupCode, List<String> materialCodes);

    Integer deleteByGroupCode(String code);
}
