package com.lds.oneplanning.basedata.model;

import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.entity.PlannerFunPermission;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MpsPlannerBase对象", description="")
public class PlannerBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "工号")
    private String empNo;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "sbu")
    private String sbu;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // 以下字段非库表所有


    @ApiModelProperty(value = "工厂名称")
    private String factoryName;


//    private List<PlannerDataPermission> dataPermissionList;

    private List<PlannerFunPermission>  funPermissionList;

//    private List<LineCategoryDTO> lineCategoryList;
//
//    private List<LineInfoDTO> lineList;

}
