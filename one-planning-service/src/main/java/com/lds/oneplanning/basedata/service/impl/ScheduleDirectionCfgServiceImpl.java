package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.constants.ScheduleConstant;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.ScheduleDirectionCfg;
import com.lds.oneplanning.basedata.mapper.ScheduleDirectionCfgMapper;
import com.lds.oneplanning.basedata.model.ScheduleDirectionCfgDTO;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.IScheduleDirectionCfgService;
import com.lds.oneplanning.wps.model.WpsRowData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-14
 */
@Slf4j
@Service
public class ScheduleDirectionCfgServiceImpl extends ServiceImpl<ScheduleDirectionCfgMapper, ScheduleDirectionCfg> implements IScheduleDirectionCfgService {
    @Resource
    private IFactoryService  factoryService;

    @Override
    public Page<ScheduleDirectionCfgDTO> page(String keyword, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ScheduleDirectionCfg> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ScheduleDirectionCfg> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(ScheduleDirectionCfg::getFactoryCode,keyword).or()
                    .like(ScheduleDirectionCfg::getConfigValue,keyword));
        }
        queryWrapper.orderByDesc(ScheduleDirectionCfg::getUpdateTime).orderByAsc(ScheduleDirectionCfg::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<ScheduleDirectionCfgDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<ScheduleDirectionCfgDTO> results = BeanUtil.mapList(entityPage.getRecords(), ScheduleDirectionCfgDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<ScheduleDirectionCfgDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()){
            return;
        }
        Map<String,String> factoryMap = factoryService.listByFactoryCodes(sourceList.stream().map(ScheduleDirectionCfgDTO::getFactoryCode)
                .collect(Collectors.toSet())).stream().collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        sourceList.stream().forEach(scheduleDirectionCfgDTO -> scheduleDirectionCfgDTO.setFactoryName(factoryMap.get(scheduleDirectionCfgDTO.getFactoryCode())));
    }

    @Override
    public ScheduleDirectionCfgDTO detail(Long id) {
        ScheduleDirectionCfg scheduleDirectionCfg = baseMapper.selectById(id);
        if (scheduleDirectionCfg == null) {
            return null;
        }
        ScheduleDirectionCfgDTO dto = BeanUtil.map(scheduleDirectionCfg, ScheduleDirectionCfgDTO.class);
        this.decorate(Lists.newArrayList(dto));
        return dto;
    }

    @Override
    public Map<String, Integer> getScheduleMap(List<WpsRowData> wpsRowDatas) {
        Map<String,Integer> resMap = Maps.newLinkedHashMap();
        if (CollectionUtils.isEmpty(wpsRowDatas)) {
            return resMap;
        }
        List<ScheduleDirectionCfg> scheduleDirectionCfgs = baseMapper.selectList(Wrappers.emptyWrapper());
        Map<String ,Integer> productIdCommodityMap = scheduleDirectionCfgs.stream()
                .filter(cfg ->{ return ScheduleConstant.CONFIG_TYPE_PRODUCT_ID.equals(cfg.getConfigType());})
                .collect(Collectors.toMap(ScheduleDirectionCfg::getConfigValue,ScheduleDirectionCfg::getDirection,(integer, integer2) -> integer2));
        Map<String ,Integer> productGroupMap = scheduleDirectionCfgs.stream()
                .filter(cfg ->{ return ScheduleConstant.CONFIG_TYPE_PRODUCT_GROUP_CODE.equals(cfg.getConfigType());})
                .collect(Collectors.toMap(ScheduleDirectionCfg::getConfigValue,ScheduleDirectionCfg::getDirection,(integer, integer2) -> integer2));
        Map<String ,Integer> factoryMap = scheduleDirectionCfgs.stream()
                .filter(cfg ->{ return ScheduleConstant.CONFIG_TYPE_FACTORY.equals(cfg.getConfigType());})
                .collect(Collectors.toMap(ScheduleDirectionCfg::getConfigValue,ScheduleDirectionCfg::getDirection,(integer, integer2) -> integer2));
        Map<String ,Integer> customerGroupMap = scheduleDirectionCfgs.stream()
                .filter(cfg ->{ return ScheduleConstant.CONFIG_TYPE_CUSTOMER_GROUP.equals(cfg.getConfigType());})
                .collect(Collectors.toMap(ScheduleDirectionCfg::getConfigValue,ScheduleDirectionCfg::getDirection,(integer, integer2) -> integer2));

        wpsRowDatas.forEach(wpsRowData -> {
            String customerGroupCode = wpsRowData.getCustomerGroup();
            String factoryCode = wpsRowData.getFactory();
            String productGroupCode = wpsRowData.getProductGroupCode();
            String productId = wpsRowData.getProductId();
            String commodityId = wpsRowData.getCommodityId();
            resMap.put(wpsRowData.getOrderNo(),this.getDirection(customerGroupCode,factoryCode,productGroupCode, productId, commodityId,productIdCommodityMap,productGroupMap,factoryMap,customerGroupMap));
        });
        return resMap;
    }

    /**
     * 根据订单产品组编码 产品id 商品id获取订单排产方向 这个无法批量操作
     * @param productGroupCode
     * @param productId
     * @param commodityId
     * @return
     */
    private Integer getDirection(String customerGroupCode,String factoryCode,
                                 String productGroupCode,String productId,String commodityId,
                                 Map<String ,Integer> productIdCommodityMap,
                                 Map<String ,Integer> productGroupMap,
                                 Map<String ,Integer> factoryMap,
                                 Map<String ,Integer> customerGroupMap){
        Set<String> productCommodityIds = Sets.newHashSet();
        Integer  direction = ScheduleConstant.DEFAULT_DIRECTION;
        if (StringUtils.isNotBlank(productId)) {
            productCommodityIds.addAll(Arrays.asList(productId.split(";")));
        }
        if (StringUtils.isNotBlank(commodityId)) {
            productCommodityIds.addAll(Arrays.asList(commodityId.split(";")));
        }
        for (Map.Entry<String,Integer> entry : productIdCommodityMap.entrySet()){
            if (productCommodityIds.contains(entry.getKey())) {
                //根据产品id过滤
                return entry.getValue();
            }
        }
        for (Map.Entry<String,Integer> entry : productGroupMap.entrySet()){
            if (StringUtils.isNotBlank(productGroupCode) && productGroupCode.equals(entry.getKey())) {
                //根据产品组编码过滤
                return entry.getValue();
            }
        }
        for (Map.Entry<String,Integer> entry : factoryMap.entrySet()){
            if (factoryMap.containsKey(factoryCode)) {
                //根据工厂配置
                return entry.getValue();
            }
        }
        for (Map.Entry<String,Integer> entry : customerGroupMap.entrySet()){
            if (customerGroupMap.containsKey(customerGroupCode)) {
                //根据客户组
                return entry.getValue();
            }
        }

        return direction;
    }
}
