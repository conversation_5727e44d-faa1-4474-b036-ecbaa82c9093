package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.Workshop;
import com.lds.oneplanning.basedata.model.WorkshopDTO;
import com.lds.oneplanning.basedata.service.IWorkshopService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-20
 */
@Api(value = "WorkshopController", tags = "车间信息")
@RestController
@RequestMapping("/basedata/workshop")
public class WorkshopController {

    @Resource
    private IWorkshopService workshopService;

    @ApiOperation(value = "获取客户列表", notes = "获取客户列表")
    @GetMapping("/list")
    public List<WorkshopDTO> list(@RequestParam(value = "factoryCode",required = false)String factoryCode){
        return workshopService.listByFactoryCode(factoryCode);
    }

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<WorkshopDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                  @RequestParam(value = "factoryCode",required = false)String factoryCode,
                               @RequestParam(value = "pageNum")Integer pageNum,
                               @RequestParam(value = "pageSize")Integer pageSize
    ){
        return workshopService.page(keyword,factoryCode,pageNum,pageSize);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public WorkshopDTO detail(@PathVariable("id")Long id){
        return  workshopService.getDetail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "车间",operation = "新增")
    public Long add(@RequestBody Workshop dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        workshopService.save(dto);
        return dto.getId();
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "车间",operation = "编辑")
    public Integer edit(@RequestBody Workshop dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  workshopService.updateById(dto) ? 1 : 0;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @Loggable(businessName = "车间",operation = "删除")
    @DeleteMapping("/delete/{id}")
    public Integer delete(@PathVariable("id")Long id ){
        return  workshopService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @Loggable(businessName = "车间",operation = "批量删除")
    @DeleteMapping("/batchDelete")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  workshopService.removeByIds(ids) ? 1:0;
    }
}
