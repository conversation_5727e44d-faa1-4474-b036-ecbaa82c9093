package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.basedata.entity.UserFactoryRel;
import com.lds.oneplanning.basedata.model.UserFactoryRelDTO;
import com.lds.oneplanning.basedata.model.UserFactoryStoreLocationDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/5/17
 */
public interface IUserFactoryRelService extends IService<UserFactoryRel> {
    /**
     * 新增用户工厂关联
     *
     * @param userFactoryList
     * @param userId
     */
    void save(List<UserFactoryRelDTO> userFactoryList, Long userId);

    /**
     * 根据用户id列表查询用户工厂关联
     *
     * @param userIdList
     * @return
     */
    Map<Long, List<UserFactoryRelDTO>> findMapping(List<Long> userIdList);

    /**
     * 根据用户id列表删除用户工厂关联
     * @param userIdList
     */
    void deleteByUserId(Collection<Long> userIdList);

    /**
     * 批量保存用户工厂关联
     *
     * @param userType
     * @param userFactoryStoreLocationDTOList
     */
    void batchSaveUserFactoryDtos(String userType,
                                  List<UserFactoryStoreLocationDTO> userFactoryStoreLocationDTOList);
}
