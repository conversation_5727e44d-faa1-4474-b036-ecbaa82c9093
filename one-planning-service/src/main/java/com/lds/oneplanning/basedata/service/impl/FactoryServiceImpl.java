package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.mapper.FactoryMapper;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.esb.datafetch.model.EsbData;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class FactoryServiceImpl extends ServiceImpl<FactoryMapper, Factory> implements IFactoryService {

    @Resource
    private IEsbDataFetchService esbDataFetchService;
    private static final Integer BATCH_SIZE = 1000 ;
    @Override
    public void syncFromSap() {
        List<EsbData> sourceDatas = esbDataFetchService.fetchFactoryList();
        Set<String> sourceCodes = sourceDatas.stream().map(EsbData::getCode).collect(Collectors.toSet());
        List<Factory> insertList = Lists.newArrayList();
        List<Factory> updateList = Lists.newArrayList();
        List<Long>  deleteIds = Lists.newArrayList();
        List<Factory> existList = baseMapper.selectList(Wrappers.<Factory>lambdaQuery());
        Map<String, Factory> mapTarget = existList.stream().collect(Collectors.toMap(Factory::getCode, Factory -> Factory,(t, t2) -> t2));
        // 新增编辑收集
        sourceDatas.forEach(esbData -> {
            String code = esbData.getCode();
            if (mapTarget.containsKey(code)) {
                Factory updateTarget = mapTarget.get(code);
//                updateTarget.setSapParentCode(esbData.getParentCode());
                updateTarget.setName(esbData.getName());
                updateTarget.setUpdateTime(new Date());
                updateList.add(updateTarget);
            }else {
                Factory insert = new Factory();
                insert.setName(esbData.getName());
                insert.setCode(esbData.getCode());
//                insert.setSapParentCode(esbData.getParentCode());
                insertList.add(insert);
            }
        });
        // 修改收集
        existList.forEach(Factory -> {
            if (!sourceCodes.contains(Factory.getCode())) {
                deleteIds.add(Factory.getId());
            }
        });
        // 删除进行
        if (!deleteIds.isEmpty()) {
            baseMapper.deleteBatchIds(deleteIds);
        }
        // 新增
        if (!insertList.isEmpty()) {
            this.saveBatch(insertList,BATCH_SIZE);
        }
        //编辑
        if (!updateList.isEmpty()) {
            this.updateBatchById(updateList,BATCH_SIZE);
        }
    }



    @Override
    public List<Factory> listByFactoryCodes(Collection<String> factoryCodes) {
        if (factoryCodes == null || factoryCodes.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<Factory>lambdaQuery().in(Factory::getCode,factoryCodes));
    }

    @Override
    public List<Factory> findList(String keyword) {
        LambdaQueryWrapper<Factory> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(Factory::getCode,keyword).or()
                    .like(Factory::getName,keyword));
        }
        queryWrapper.orderByDesc(Factory::getUpdateTime).orderByAsc(Factory::getId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void batchSaveByCode(List<Factory> sourceList) {
        Set<String> sourceCodes = sourceList.stream().map(Factory::getCode).collect(Collectors.toSet());
        List<Factory> existList = baseMapper.selectList(Wrappers.<Factory>lambdaQuery().in(Factory::getCode, sourceCodes));
        Map<String,Long> existCodeMap = existList.stream().collect(Collectors.toMap(Factory::getCode,Factory::getId,(aLong, aLong2) -> aLong2));
        List<Factory> insertLis = Lists.newArrayList();
        List<Factory> updateList = Lists.newArrayList();
        sourceList.stream().forEach(factory -> {
            if (existCodeMap.containsKey(factory.getCode())) {
                factory.setId(existCodeMap.get(factory.getCode()));
                updateList.add(factory);
            }else{
                insertLis.add(factory);
            }
        });
        if (!updateList.isEmpty()) {
            this.updateBatchById(updateList);
        }
        if (!insertLis.isEmpty()) {
            this.saveBatch(insertLis);
        }
    }

    @Override
    public List<String> getThaiFactoryCodes() {
        List<Factory> list = baseMapper.selectList(Wrappers.<Factory>lambdaQuery()
                .likeRight(Factory::getCode,"26").or(o2Wrapper -> o2Wrapper.likeRight(Factory::getCode,"27")));
        return list.stream().map(Factory::getCode).collect(Collectors.toList());
    }


}
