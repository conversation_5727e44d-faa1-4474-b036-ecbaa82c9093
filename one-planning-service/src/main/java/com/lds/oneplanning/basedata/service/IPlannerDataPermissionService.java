package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.model.PlannerDataPermissionDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface IPlannerDataPermissionService extends IService<PlannerDataPermission> {

    Page<PlannerDataPermissionDTO> page(Long userId, Integer pageNum, Integer pageSize);
    List<PlannerDataPermission> listByUserId(Long userId);

    void saveByUserId(List<PlannerDataPermission> dataPermissionList,Long userId);

    void deleteByUserId(Long userId);
    void deleteByUserIds(Collection<Long> userIds);

    List<String> listAllFactoryCode();

    Map<Long, Set<String>> getUserFactoryCodeMap();

    Set<String> getFactoryCodeByUserId(Long userId);

    /**
     * 获取工厂下所有用户
     * @return
     */
    Map<String,Set<Long>> getFactoryUserMap();
}
