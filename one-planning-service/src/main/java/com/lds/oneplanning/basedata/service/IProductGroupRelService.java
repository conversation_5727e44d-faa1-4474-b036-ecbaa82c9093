package com.lds.oneplanning.basedata.service;

import com.lds.oneplanning.basedata.entity.ProductGroupRel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
public interface IProductGroupRelService extends IService<ProductGroupRel> {

    List<ProductGroupRel> listByGroupCodes(Set<String> groupCodes);

    Integer deleteByGroupCode(String groupCode);
    Integer deleteByGroupCodes(Collection<String> groupCodes);

    List<ProductGroupRel> listByProductIds(Collection<String> productIds);

    Integer deleteByProductId(Collection<String> productIds);
}
