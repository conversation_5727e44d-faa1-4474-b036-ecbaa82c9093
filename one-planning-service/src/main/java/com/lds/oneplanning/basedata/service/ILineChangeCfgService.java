package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.LineChangeCfg;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.model.LineChangeCfgDTO;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.model.LineUphBatchDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-19
 */
public interface ILineChangeCfgService extends IService<LineChangeCfg> {
      Long add(LineChangeCfgDTO dto);

      Long edit(LineChangeCfgDTO dto);

      Page<LineChangeCfgDTO> page(String factoryCode,Integer pageNum, Integer pageSize);

      LineChangeCfgDTO findById(Long id);

      /**
       * 根据工厂编号获取缓存换线时长
       * @param factoryCode
       * @return
       */
      LineChangeCfgDTO getCacheLineChangeCfg(String factoryCode);

      void delete(Long id);

}
