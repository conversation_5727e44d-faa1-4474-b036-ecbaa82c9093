package com.lds.oneplanning.basedata.controller;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.LineCategory;
import com.lds.oneplanning.basedata.model.LineCategoryDTO;
import com.lds.oneplanning.basedata.service.ILineCategoryService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Api(value = "LineCategoryController", tags = "产线类别信息")
@RestController
@RequestMapping("/basedata/lineCategory")
public class LineCategoryController {

    @Resource
    private ILineCategoryService lineCategoryService;

    @ApiOperation(value = "获取产线列表-可以指定工厂", notes = "获取产线列表-可以指定工厂编码")
    @GetMapping("/list")
    public List<LineCategory> list(@RequestParam(value = "facotryCode",required = false)String factoryCode){
        return lineCategoryService.list(Wrappers.<LineCategory>lambdaQuery()
                .eq(StringUtils.isNotBlank(factoryCode),LineCategory::getFactoryCode,factoryCode));
    }


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<LineCategoryDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                      @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                      @RequestParam(value = "pageNum")Integer pageNum,
                                      @RequestParam(value = "pageSize")Integer pageSize
    ){
        return lineCategoryService.page(keyword,factoryCode,pageNum,pageSize);
    }

    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public LineCategoryDTO detail(@PathVariable("id")Long id){
        return  lineCategoryService.detail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "产线类别",operation = "新增")
    public Long add(@RequestBody LineCategoryDTO dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return  lineCategoryService.add(dto);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "产线类别",operation = "编辑")
    public Integer edit(@RequestBody LineCategoryDTO dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setId(id);
        return  lineCategoryService.edit(dto);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "产线类别",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  lineCategoryService.delete(id);
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "产线类别",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  lineCategoryService.batchDelete(ids);
    }
}
