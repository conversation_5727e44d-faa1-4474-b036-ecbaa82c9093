package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iot.common.exception.BusinessException;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.coral.website.common.util.RedisCacheUtil;
import com.lds.oneplanning.basedata.entity.SysParamCfg;
import com.lds.oneplanning.basedata.enums.SysParamCfgEnum;
import com.lds.oneplanning.basedata.mapper.SysParamCfgMapper;
import com.lds.oneplanning.basedata.model.SysParamCfgDTO;
import com.lds.oneplanning.basedata.service.ISysParamCfgService;
import com.lds.oneplanning.wps.exception.WpsExceptionEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class SysParamCfgServiceImpl extends ServiceImpl<SysParamCfgMapper, SysParamCfg> implements ISysParamCfgService {
    public static final String SYS_PARAM_CFG_CACHE_KEY = "sysParamCfg:%s";
    public static final Long CACHE_TIME_OUT = 24*60*60L;

    @Override
    public Long saveSysParamCfg(SysParamCfgDTO sysParamCfgDto) {
        checkCodeExist(sysParamCfgDto);
        SysParamCfg sysParamCfg = BeanUtil.map(sysParamCfgDto,SysParamCfg.class);
        this.save(sysParamCfg);
        RedisCacheUtil.delete(getCacheKey(sysParamCfg.getCode()));
        return sysParamCfg.getId();
    }

    @Override
    public Long updateSysParamCfg(SysParamCfgDTO sysParamCfgDto) {
        SysParamCfg sysParamCfg = this.getById(sysParamCfgDto.getId());
        if(Objects.isNull(sysParamCfg)){
            throw new BusinessException(WpsExceptionEnum.RECORD_NOT_EXIST);
        }
        checkCodeExist(sysParamCfgDto);
        sysParamCfg.setName(sysParamCfgDto.getName());
        sysParamCfg.setCode(sysParamCfgDto.getCode());
        sysParamCfg.setValue(sysParamCfgDto.getValue());
        sysParamCfg.setUpdateBy(sysParamCfgDto.getUpdateBy());
        this.updateById(sysParamCfg);
        RedisCacheUtil.delete(getCacheKey(sysParamCfg.getCode()));
        return sysParamCfg.getId();
    }

    private void checkCodeExist(SysParamCfgDTO sysParamCfgDto) {
        SysParamCfg sysParamCfg = baseMapper.selectOne(Wrappers.<SysParamCfg>lambdaQuery(SysParamCfg.class)
                .eq(SysParamCfg::getCode, sysParamCfgDto.getCode())
                .ne(SysParamCfg::getId, sysParamCfgDto.getId())
                .last("limit 1"));
        if (sysParamCfg != null) {
            throw new BusinessException(WpsExceptionEnum.CODE_EXIST);
        }
    }
    @Override
    public void deleteById(Long id) {
        SysParamCfg sysParamCfg = this.getById(id);
        if(Objects.isNull(sysParamCfg)){
            throw new BusinessException(WpsExceptionEnum.RECORD_NOT_EXIST);
        }
        this.removeById(id);
        RedisCacheUtil.delete(getCacheKey(sysParamCfg.getCode()));
    }

    @Override
    public String getValueByCode(String code) {
        SysParamCfg sysParamCfg = RedisCacheUtil.valueObjGet(getCacheKey(code),SysParamCfg.class);
        if(Objects.nonNull(sysParamCfg)){
            return sysParamCfg.getValue();
        }
        List<SysParamCfg> sysParamCfgs = this.list(Wrappers.<SysParamCfg>lambdaQuery().eq(SysParamCfg::getCode,code));
        if(CollectionUtils.isEmpty(sysParamCfgs)){
            sysParamCfg = createSysParamCfg(code);
            if(Objects.isNull(sysParamCfg)){
                return null;
            }
            sysParamCfgs.add(sysParamCfg);
        }
        RedisCacheUtil.valueObjSet(getCacheKey(code),sysParamCfgs.get(0),CACHE_TIME_OUT);
        return sysParamCfgs.get(0).getValue();
    }
    private String getCacheKey(String code){
        return String.format(SYS_PARAM_CFG_CACHE_KEY,code);
    }

    private SysParamCfg createSysParamCfg(String code) {
        SysParamCfgEnum sysParamCfgEnum =  SysParamCfgEnum.getValueByCode(code);
        if(Objects.isNull(sysParamCfgEnum)){
            return null;
        }
        SysParamCfg sysParamCfg = new SysParamCfg();
        sysParamCfg.setValue(sysParamCfgEnum.getDefaultValue());
        sysParamCfg.setCode(sysParamCfgEnum.getCode());
        return sysParamCfg;
    }
}
