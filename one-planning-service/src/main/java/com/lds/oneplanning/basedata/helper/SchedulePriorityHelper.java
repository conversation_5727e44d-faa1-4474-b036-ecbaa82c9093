package com.lds.oneplanning.basedata.helper;

import com.iot.common.exception.BusinessException;
import com.lds.oneplanning.basedata.entity.SchedulePriority;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.wps.exception.WpsExceptionEnum;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Objects;

@Component
public class SchedulePriorityHelper {
    @Resource
    ILineInfoService lineInfoService;

    /**
     * 设置线体ID
     * @param schedulePriority
     */
    public void setSchedulePriorityLineId(SchedulePriority schedulePriority){
        LineInfoDTO lineInfoDTO = lineInfoService.getByCode(schedulePriority.getLineCode(),false);
        if(Objects.isNull(lineInfoDTO)){
            throw new BusinessException(WpsExceptionEnum.RECORD_NOT_EXIST);
        }
        schedulePriority.setLineUuid(lineInfoDTO.getLineUuid());
    }
}
