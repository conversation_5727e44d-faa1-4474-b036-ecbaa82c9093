package com.lds.oneplanning.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lds.oneplanning.basedata.entity.UserInfo;
import com.lds.oneplanning.basedata.model.UserInfoDTO;
import com.lds.oneplanning.basedata.model.UserInfoQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/16
 */
public interface UserInfoMapper extends BaseMapper<UserInfo> {
    /**
     * 分页查询
     *
     * @return
     */
    List<UserInfoDTO> list(UserInfoQueryDTO queryDTO);


    /**
     * 详情获取
     *
     * @param userId
     * @return
     */
    UserInfoDTO getOneByUserId(Long userId);
}
