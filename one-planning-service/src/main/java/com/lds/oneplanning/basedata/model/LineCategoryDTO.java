package com.lds.oneplanning.basedata.model;

import com.lds.oneplanning.basedata.entity.LineCapacity;
import com.lds.oneplanning.basedata.entity.LineCategory;
import com.lds.oneplanning.basedata.entity.LineCategoryRule;
import com.lds.oneplanning.basedata.entity.LineUph;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MpsLineCategory对象", description="")
public class LineCategoryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "车间")
    private String workshopCode;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "sbu")
    private String sbu;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // 以下字段非库表所有
    private String factoryName;

    private List<LineCategoryRule> lineCategoryRuleList;

    private List<LineCapacity> capacityList;

    private List<LineUph> uphList;

//    private List<LineCategory> lineCategoryOptions;
}
