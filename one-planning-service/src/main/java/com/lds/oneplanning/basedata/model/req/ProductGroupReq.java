package com.lds.oneplanning.basedata.model.req;

import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;

/**
 * @Description:
 * @Author: zhuang<PERSON>ayin
 * @Email: <EMAIL>
 * @Date: 2025/2/26 13:59
 */
@Data
public class ProductGroupReq {
    private String keyword ;
    private String factoryCode;
    private String workshopCode;
    private Integer pageNum = 1 ;
    private Integer pageSize =15;
    private Collection<String> productIds;
}
