package com.lds.oneplanning.basedata.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/16
 */
@Data
public class UserFactoryRelDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 工厂编号
     */
    private String factoryCode;

    /**
     * 工厂名称
     */
    private String factoryName;

    /**
     * 库位编号，多个用逗号分隔
     */
    private String storeLocationCodes;

    /**
     * 库位编号，多个用逗号分隔
     */
    private List<StoreLocationVo> storeLocationList;

    static class StoreLocationVo{
        /**
         * 库位编号
         */
        private String storeLocationCode;
        /**
         * 库位名称
         */
        private String storeLocationName;
    }
}
