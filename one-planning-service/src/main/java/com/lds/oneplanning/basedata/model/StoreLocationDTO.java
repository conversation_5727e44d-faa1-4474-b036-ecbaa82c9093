package com.lds.oneplanning.basedata.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2024-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StoreLocationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 仓位名称
     */
    private String name;

    /**
     * 仓位编码
     */
    private String code;

    /**
     * 工厂编码
     */
    private String factoryUnitCode;

    /**
     * 厂房编码
     */
    private String factoryBuildingCode;

    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者id
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    // 非库表字段
    private String createUserName;
    private String updateUserName;
    private String factoryUnitName;
    private String factoryBuildingName;
    private String uniqueCode;

    private Collection<String> codes;

    public String getUniqueCode() {
        return this.getFactoryUnitCode()+"_"+this.getCode();
    }
}
