package com.lds.oneplanning.basedata.service;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.PlannerBase;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.basedata.model.PlannerBaseDTO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface IPlannerBaseService extends IService<PlannerBase> {

    List<PlannerBaseDTO> findList(String keyword, String factoryCode);

    Page<PlannerBaseDTO> page(String keyword, String factoryCode, Integer pageNum, Integer pageSize);

    PlannerBaseDTO detail(Long id);

    Long add(PlannerBaseDTO dto);

    Integer edit(PlannerBaseDTO dto);

    Integer delete(Long id);

    Integer batchDelete(Collection<Long> ids);

    String getEmpNoByUserId(Long userId);
    String getNameNoByUserId(Long userId);
    String getNameNoByEmpNo(String empNo);

}
