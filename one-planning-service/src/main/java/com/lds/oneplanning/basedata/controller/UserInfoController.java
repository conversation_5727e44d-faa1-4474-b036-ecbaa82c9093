package com.lds.oneplanning.basedata.controller;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.model.UserInfoDTO;
import com.lds.oneplanning.basedata.model.UserInfoQueryDTO;
import com.lds.oneplanning.basedata.model.req.UserInfoAddReq;
import com.lds.oneplanning.basedata.service.IUserInfoExcelService;
import com.lds.oneplanning.basedata.service.IUserInfoService;
import com.lds.oneplanning.log.anotation.Loggable;
import com.lds.oneplanning.wps.enums.ViewSource;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * 人员管理
 *
 * <AUTHOR>
 * @since 2025/5/16
 */
@RestController
@RequestMapping("/basedata/userInfo")
public class UserInfoController {

    @Resource
    private IUserInfoService userInfoService;

    @Autowired
    private IUserInfoExcelService userInfoExcelService;

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<UserInfoDTO> page(UserInfoQueryDTO queryDTO) {
        return userInfoService.page(queryDTO);
    }

    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public UserInfoDTO detail(@PathVariable("id") Long id) {
        return userInfoService.detail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "用户", operation = "新增")
    public Long add(@Validated @RequestBody UserInfoAddReq req) {
        UserInfoDTO dto = BeanUtil.copyProperties(req, UserInfoDTO.class);
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        return userInfoService.add(dto);
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "用户", operation = "编辑")
    public void edit(@RequestBody UserInfoDTO dto, @PathVariable("id") Long id) {
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setId(id);
        userInfoService.edit(dto);
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "用户", operation = "删除")
    public Integer delete(@PathVariable("id") Long id) {
        return userInfoService.batchDelete(Lists.newArrayList(id));
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "用户", operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids) {
        return userInfoService.batchDelete(ids);
    }

    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping("/import/{userType}")
    @Loggable(businessName = "人员身份管理", operation = "导入")
    public boolean importExcel(@PathVariable("userType") String userType, @RequestParam("file") MultipartFile file) {
        return userInfoExcelService.importExcel(userType, file);
    }

    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping("/export")
    @Loggable(businessName = "人员身份管理", operation = "导出")
    public ResponseEntity<byte[]> exportExcel(@RequestParam(value = "keyword", required = false) String keyword,
                                              @RequestParam(value = "userType", required = false) String userType,
                                              @RequestParam(value = "factoryCode", required = false) String factoryCode) {
        return userInfoExcelService.exportExcel(keyword, userType, factoryCode);
    }

    @ApiOperation(value = "当前用户类型", notes = "当前用户类型")
    @GetMapping("/currentUserType")
    public ViewSource currentUserType() {
        return userInfoService.getCurrentUserType();
    }
}