package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.LineUph;
import com.lds.oneplanning.basedata.model.LineUphDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-12
 */
public interface ILineUphService extends IService<LineUph> {
    Page<LineUphDTO> page(Long userId,String keyword, String factoryCode,String productId,Integer configType, String configCode, Integer pageNum, Integer pageSize);
    LineUphDTO getDetail(Long id);

    Map<String,List<LineUph>> groupByTypeAndCodes(Integer configType, Collection<String> configCodes);

    Map<String,Float> getUphByTypeAndUuid(Integer configType, String lineUuid, List<String> productIds);

    void batchSaveByUuid(Integer configType, String lineUuid, List<LineUph> lineUphList);

    void batchDeleteByUuids(Integer configType, Collection<String> needDeleteLineUuids);

    boolean deleteByIds(Collection<Long> ids);

    boolean update(LineUph lineUph);

    boolean saveOne(LineUph lineUph);

    Map<String,Float> findMap(List<String> lineUUid, List<String> productIds);

    Map<String, List<LineUph>> groupByUuid(Set<String> lineUUids);
}