package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.LineCategory;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.entity.PlannerLineCfg;
import com.lds.oneplanning.basedata.mapper.PlannerLineCfgMapper;
import com.lds.oneplanning.basedata.model.LineCategoryDTO;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.model.PlannerLineCfgDTO;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.ILineCategoryService;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.basedata.service.IPlannerLineCfgService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Service
public class PlannerLineCfgServiceImpl extends ServiceImpl<PlannerLineCfgMapper, PlannerLineCfg> implements IPlannerLineCfgService {
    @Resource
    private ILineInfoService lineInfoService;
    @Resource
    private ILineCategoryService lineCategoryService;
    @Resource
    private IFactoryService factoryService;

    @Override
    public Page<PlannerLineCfgDTO> page(Long userId, Integer configType, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<PlannerLineCfg> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<PlannerLineCfg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlannerLineCfg::getUserId,userId);
        queryWrapper.eq(PlannerLineCfg::getConfigType,configType);
        queryWrapper.orderByDesc(PlannerLineCfg::getUpdateTime).orderByAsc(PlannerLineCfg::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<PlannerLineCfgDTO> resultPage = new Page<>(pageNum, pageSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<PlannerLineCfgDTO> results = BeanUtil.mapList(entityPage.getRecords(), PlannerLineCfgDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results,configType);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<PlannerLineCfgDTO> sourceList,Integer configType){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Map<String,String> factoryNameMap = factoryService.
                listByFactoryCodes(sourceList.stream().map(PlannerLineCfgDTO::getFactoryCode).collect(Collectors.toSet())).stream()
                .collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        Map<String,LineInfoDTO> lineInfoDTOMap = lineInfoService.
                listDtoByCodes(sourceList.stream().map(PlannerLineCfgDTO::getConfigCode).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(LineInfoDTO::getCode,lineInfoDTO -> lineInfoDTO,(t, t2) -> t2));
        if (BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY.equals(configType)) {
           // todo 产线类型先不处理
        }
        sourceList.stream().forEach(cfgDTO -> {
            LineInfoDTO lineInfoDTO = lineInfoDTOMap.get(cfgDTO.getConfigCode());
            if (lineInfoDTO != null) {
                cfgDTO.setConfigName(lineInfoDTO.getName());
                cfgDTO.setFactoryName(lineInfoDTO.getFactoryName());
                cfgDTO.setWorkshopCode(lineInfoDTO.getWorkshopCode());
                cfgDTO.setWorkshopName(lineInfoDTO.getWorkshopName());
                cfgDTO.setLineUuid(lineInfoDTO.getLineUuid());
            }
            if (cfgDTO.getFactoryName() == null) {
                cfgDTO.setFactoryName(factoryNameMap.get(cfgDTO.getFactoryCode()));
            }
        });

    }

    @Override
    public PlannerLineCfgDTO getDetail(Long id) {
        PlannerLineCfg entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        PlannerLineCfgDTO res = BeanUtil.map(entity, PlannerLineCfgDTO.class);
        this.decorate(Lists.newArrayList(res),res.getConfigType());
        return res;
    }

    @Override
    public List<LineCategoryDTO> listLineCategoryByUserId(Long userId) {
        List<PlannerLineCfgDTO> plannerLineCfgDTOS = this.listByUserId(userId, BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY);
        if (plannerLineCfgDTOS.isEmpty()) {
            return Lists.newArrayList();
        }
        Set<String> lineCodes = plannerLineCfgDTOS.stream().map(PlannerLineCfgDTO::getConfigCode).collect(Collectors.toSet());
        List<LineCategory> entityList = lineCategoryService.listByCodes(lineCodes);
        return BeanUtil.mapList(entityList, LineCategoryDTO.class);
    }

    @Override
    public List<LineInfoDTO> listLineInfoByUserId(Long userId) {
        if (userId == null) {
            return Lists.newArrayList();
        }
        List<PlannerLineCfgDTO> plannerLineCfgDTOS = this.listByUserId(userId, BaseDataConstant.CONFIG_TYPE_LINE);
        if (plannerLineCfgDTOS.isEmpty()) {
            return Lists.newArrayList();
        }
        Set<String> lineCodes = plannerLineCfgDTOS.stream().map(PlannerLineCfgDTO::getConfigCode).collect(Collectors.toSet());
        List<LineInfo> entityList = lineInfoService.listByCodes(lineCodes);
        return BeanUtil.mapList(entityList, LineInfoDTO.class);
    }

    @Override
    public List<PlannerLineCfgDTO> listByUserId(Long userId, Integer configType) {
        List<PlannerLineCfg> entityList = baseMapper.selectList(Wrappers.<PlannerLineCfg>lambdaQuery().eq(PlannerLineCfg::getUserId,userId).eq(PlannerLineCfg::getConfigType,configType));
        return BeanUtil.mapList(entityList, PlannerLineCfgDTO.class);
    }

    @Override
    public void saveByUserId(List<PlannerLineCfgDTO> lineCategoryList, Long userId, Integer configType) {
        // 先清空
        this.deleteByUserId(userId,configType);
        if (CollectionUtils.isEmpty(lineCategoryList)) {
            return;
        }
        //再保存
        lineCategoryList.stream().forEach(entity -> {
            entity.setUserId(userId);
            entity.setConfigType(configType);
        });
        List<PlannerLineCfg> entityList = BeanUtil.mapList(lineCategoryList,PlannerLineCfg.class);
        this.saveBatch(entityList);
    }

    @Override
    public void deleteByUserId(Long userId, Integer configType) {
        baseMapper.delete(Wrappers.<PlannerLineCfg>lambdaQuery().eq(PlannerLineCfg::getUserId,userId).eq(PlannerLineCfg::getConfigType,configType));
    }

    @Override
    public void saveCategoryByUserId(List<LineCategoryDTO> lineCategoryList, Long userId) {
        Set<String> lineCategoryCodes = lineCategoryList.stream().map(LineCategoryDTO::getCode).collect(Collectors.toSet());
        if (lineCategoryCodes == null || lineCategoryCodes.isEmpty()) {
            return;
        }
        List<LineCategory> lineCategories = lineCategoryService.listByCodes(lineCategoryCodes);

        this.deleteByUserIdAndConfigType(userId, BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY);
           List<PlannerLineCfg> targetList = Lists.newArrayList();
           for (LineCategory dto : lineCategories){
               PlannerLineCfg cfg = new PlannerLineCfg();
               cfg.setUserId(userId);
               cfg.setConfigType(BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY);
               cfg.setConfigCode(dto.getCode());
               cfg.setFactoryCode(dto.getFactoryCode());
               cfg.setSbu(dto.getSbu());
               cfg.setCreateTime(new Date());
               cfg.setUpdateTime(new Date());
               targetList.add(cfg);
           }
           this.saveBatch(targetList);
    }

    @Override
    public void saveLineByUserId(List<LineInfoDTO> lineList, Long userId) {
        Set<String> lineCodes = lineList.stream().map(LineInfoDTO::getCode).collect(Collectors.toSet());
        if (lineCodes == null || lineCodes.isEmpty()) {
            return;
        }
        List<LineInfo> lineInfos = lineInfoService.listByCodes(lineCodes);
        this.deleteByUserIdAndConfigType(userId, BaseDataConstant.CONFIG_TYPE_LINE);
        List<PlannerLineCfg> targetList = Lists.newArrayList();
        for (LineInfo dto : lineInfos){
            PlannerLineCfg cfg = new PlannerLineCfg();
            cfg.setUserId(userId);
            cfg.setConfigType(BaseDataConstant.CONFIG_TYPE_LINE);
            cfg.setConfigCode(dto.getCode());
            cfg.setFactoryCode(dto.getFactoryCode());
            cfg.setSbu(dto.getSbu());
            cfg.setCreateTime(new Date());
            cfg.setUpdateTime(new Date());
            targetList.add(cfg);
        }
        this.saveBatch(targetList);
    }

    @Override
    public void deleteByUserIdAndConfigType(Long userId, Integer configType) {
        baseMapper.delete(Wrappers.<PlannerLineCfg>lambdaQuery().eq(PlannerLineCfg::getUserId,userId)
                .eq(PlannerLineCfg::getConfigType,configType));
    }

    @Override
    public void deleteByUserIds(Collection<Long> userIds, Integer configType) {
        if (CollectionUtils.isEmpty(userIds) || configType == null) {
            return;
        }
        baseMapper.delete(Wrappers.<PlannerLineCfg>lambdaQuery().in(PlannerLineCfg::getUserId,userIds)
                .eq(PlannerLineCfg::getConfigType,configType));
    }
}
