package com.lds.oneplanning.basedata.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iot.common.exception.BusinessException;
import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.*;
import com.lds.oneplanning.basedata.mapper.LineInfoMapper;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.model.LineUphBatchDTO;
import com.lds.oneplanning.basedata.model.WorkshopDTO;
import com.lds.oneplanning.basedata.service.*;
import com.lds.oneplanning.esb.biz.req.GetMesLineRequest;
import com.lds.oneplanning.esb.biz.resp.GetMesLineResponse;
import com.lds.oneplanning.esb.datafetch.service.IEsbMesDataFetchService;
import com.lds.oneplanning.mps.exception.MpsExceptionEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-19
 */
@Service
public class LineInfoServiceImpl extends ServiceImpl<LineInfoMapper, LineInfo> implements ILineInfoService {

    @Resource
    private ILineCapacityService lineCapacityService;
    @Resource
    private ILineUphService lineUphService;
    @Resource
    private IFactoryService factoryService;
    @Resource
    private ILineCategoryService lineCategoryService;
    @Resource
    private IWorkshopService workshopService;

    @Autowired
    private IEsbMesDataFetchService esbMesDataFetchService;

    @Resource
    private IPlannerLineCfgService plannerLineCfgService;
    @Resource
    private IPlannerDataPermissionService plannerDataPermissionService;
    @Override
    public Page<LineInfoDTO> page(String keyword, String factoryCode, String workshopCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<LineInfo> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<LineInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneBlank(factoryCode),LineInfo::getFactoryCode,factoryCode);
        queryWrapper.eq(StringUtils.isNoneBlank(workshopCode),LineInfo::getWorkshopCode,workshopCode);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(LineInfo::getCode,keyword).or()
                    .like(LineInfo::getName,keyword));
        }
        queryWrapper.orderByDesc(LineInfo::getUpdateTime).orderByAsc(LineInfo::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<LineInfoDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<LineInfoDTO> results = BeanUtil.mapList(entityPage.getRecords(), LineInfoDTO.class);
            this.decorate(results);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<LineInfoDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> configCodes  = sourceList.stream().map(LineInfoDTO::getCode).collect(Collectors.toSet());
        Set<String> factoryCodes = sourceList.stream().map(LineInfoDTO::getFactoryCode).collect(Collectors.toSet());
        List<Factory> factories = factoryService.listByFactoryCodes(factoryCodes);
        Map<String,String> factoryMap = factories.stream().collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        Set<String> categoryList =sourceList.stream().map(LineInfoDTO::getLineCategoryCode).collect(Collectors.toSet());
        Map<String,String> categoryMap;
        if (categoryList != null && !categoryList.isEmpty()) {
            categoryMap= lineCategoryService.listByCodes(categoryList).stream().collect(Collectors.toMap(LineCategory::getCode, LineCategory::getName,(s, s2) -> s2));
        } else {
            categoryMap = Maps.newHashMap();
        }
        List<WorkshopDTO> workshopDTOS = workshopService.listByCodes(sourceList.stream().map(LineInfoDTO::getWorkshopCode).collect(Collectors.toSet()));
        Map<String,String> workMap = workshopDTOS.stream().collect(Collectors.toMap(WorkshopDTO::getCode,WorkshopDTO::getName,(s, s2) -> s2));
        Map<String, List<LineCapacity>> capacityMap = lineCapacityService.groupByTypeAndCodes(BaseDataConstant.CONFIG_TYPE_LINE, configCodes);
        Map<String, List<LineUph>> uphMap = lineUphService.groupByTypeAndCodes(BaseDataConstant.CONFIG_TYPE_LINE, configCodes);
        sourceList.stream().forEach(dto -> {
            dto.setLineCategoryName(categoryMap.get(dto.getLineCategoryCode()));
            dto.setCapacityList(capacityMap.get(dto.getCode()));
            dto.setUphList(uphMap.get(dto.getCode()));
            dto.setFactoryName(factoryMap.get(dto.getFactoryCode()));
            dto.setWorkshopName(workMap.get(dto.getWorkshopCode()));
        });
    }

    @Override
    public LineInfoDTO detail(Long id) {
        LineInfo entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        LineInfoDTO res = BeanUtil.map(entity, LineInfoDTO.class);
        decorate(Lists.newArrayList(res));
        return res;
    }

    @Override
    public Long add(LineInfoDTO dto) {
        LineInfoDTO byCode = this.getByCode(dto.getCode(),false);
        if (byCode != null) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
        LineInfo entity = BeanUtil.map(dto,LineInfo.class);
        baseMapper.insert(entity);
        // 子表单独保存
        return entity.getId();
    }

    @Override
    public Integer edit(LineInfoDTO dto) {
        LineInfoDTO byCode = this.getByCode(dto.getCode(),false);
        if (byCode != null && !dto.getId().equals(byCode.getId())) {
            throw new BusinessException(MpsExceptionEnum.CODE_EXIST);
        }
        LineInfo entity = BeanUtil.map(dto,LineInfo.class);
        baseMapper.updateById(entity);
        return  1;
    }

    @Override
    public Integer delete(Long id) {
        LineInfo entity = baseMapper.selectById(id);
        String code = entity.getCode();
        baseMapper.deleteById(id);
        return 1;
    }

    @Override
    public Integer batchDelete(Collection<Long> ids) {
        ids.stream().forEach(this::delete);
        return ids.size();
    }

    @Override
    public Map<String, List<LineInfo>> groupByFactoryCodes(Collection<String> factoryCodes) {
        List<LineInfo> LineInfoList = baseMapper.selectList(Wrappers.<LineInfo>lambdaQuery().in(LineInfo::getFactoryCode, factoryCodes));
        return LineInfoList.stream().collect(Collectors.groupingBy(LineInfo::getFactoryCode));
    }

    @Override
    public LineInfoDTO getByCode(String code,boolean decorateFlag) {
        //编码上有唯一索引
        LineInfo entity = baseMapper.selectOne(Wrappers.<LineInfo>lambdaQuery().eq(LineInfo::getCode,code));
        if (entity == null) {
            return null;
        }
        LineInfoDTO res = BeanUtil.map(entity, LineInfoDTO.class);
        if (decorateFlag) {
            this.decorate(Lists.newArrayList(res));
        }
        return res;
    }

    @Override
    public List<LineInfo> listByCodes(Collection<String> codes) {
        if (codes == null || codes.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<LineInfo>lambdaQuery().in(LineInfo::getCode,codes));
    }

    @Override
    public List<LineInfoDTO> listDtoByCodes(Collection<String> codes) {
         List<LineInfoDTO> resList = BeanUtil.mapList(this.listByCodes(codes),LineInfoDTO.class);
         this.decorate(resList);
         return resList;
    }

    @Override
    public List<LineInfo> listByFactoryCode(String factoryCode) {
        return baseMapper.selectList(Wrappers.<LineInfo>lambdaQuery().eq(LineInfo::getFactoryCode,factoryCode));
    }

    @Override
    public List<LineInfo> listByWorkshopCode(String workshopCode) {
        return baseMapper.selectList(Wrappers.<LineInfo>lambdaQuery().eq(LineInfo::getWorkshopCode,workshopCode));
    }

    @Override
    public List<LineInfo> listByWorkshopCodes(Collection<String> workshopCodes) {
        if (CollectionUtils.isEmpty(workshopCodes)) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<LineInfo>lambdaQuery().in(LineInfo::getWorkshopCode, workshopCodes));
    }

    @Override
    public Integer batchBind(LineUphBatchDTO batchDTO) {
        if (CollectionUtils.isEmpty(batchDTO.getIds())) {
            return 0;
        }
        List<LineInfo> targetList = Lists.newArrayList();
        batchDTO.getIds().stream().forEach(id -> {
            LineInfo update = new LineInfo();
            update.setId(id);
            update.setFactoryCode(batchDTO.getFactoryCode());
            update.setWorkshopCode(batchDTO.getWorkshopCode());
            update.setLineCategoryCode(batchDTO.getLineCategoryCode());
            targetList.add(update);
        });
        return this.updateBatchById(targetList) ? 1 :0;
    }

    @Override
    public Integer deleteByCodes(Collection<String> lineCodes) {
        if (lineCodes == null || lineCodes.isEmpty()) {
            return 0;
        }
        return baseMapper.delete(Wrappers.<LineInfo>lambdaQuery().in(LineInfo::getCode,lineCodes));
    }

    @Override
    public List<String> listAllLineCodes() {
        return baseMapper.selectList(Wrappers.<LineInfo>lambdaQuery().select(LineInfo::getCode)).stream()
                .map(LineInfo::getCode)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<LineInfoDTO> listFilterByUser(Long userId, String factoryCode, String workshopCode) {
        Set<String> lineCodes = plannerLineCfgService.listLineInfoByUserId(userId)
                .stream().map(LineInfoDTO::getCode).collect(Collectors.toSet());
        List<LineInfo> entityList ;
        if (CollectionUtils.isNotEmpty(lineCodes)) {
            // 有配置可排产产线
            entityList = baseMapper.selectList(Wrappers.<LineInfo>lambdaQuery()
                    .in(LineInfo::getCode,lineCodes)
                    .eq(StringUtils.isNotBlank(factoryCode),LineInfo::getFactoryCode,factoryCode)
                    .eq(StringUtils.isNotBlank(workshopCode),LineInfo::getWorkshopCode,workshopCode)
            );
        }else{
            // 没有配置可排产的，则根据车间查询所有产线
            List<PlannerDataPermission> dataPermissionList = plannerDataPermissionService.listByUserId(userId);
            Set<String> workshopCodes  = dataPermissionList.stream().map(PlannerDataPermission::getWorkshopCode).collect(Collectors.toSet());
            entityList = baseMapper.selectList(Wrappers.<LineInfo>lambdaQuery()
                            .in(CollectionUtils.isNotEmpty(workshopCodes),LineInfo::getWorkshopCode,workshopCodes)
                    .eq(StringUtils.isNotBlank(factoryCode),LineInfo::getFactoryCode,factoryCode)
                    .eq(StringUtils.isNotBlank(workshopCode),LineInfo::getWorkshopCode,workshopCode)
            );
        }
        List<LineInfoDTO> resList = BeanUtil.mapList(entityList, LineInfoDTO.class);
        this.decorate(resList);
        return resList;
    }

    @Override
    public List<LineInfo> listByUuids(Collection<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<LineInfo>lambdaQuery().in(LineInfo::getLineUuid, uuids));
    }

    @Override
    public Integer syncMesLineList() {

        //从esb获取到mes的线体信息列表
        Set<GetMesLineResponse> resultList = esbMesDataFetchService.syncMesLineList(GetMesLineRequest.builder().build());

        if (CollectionUtils.isNotEmpty(resultList)){

            List<String > lineUuids = resultList.stream().filter(item -> StrUtil.isNotBlank(item.getLineUuid())).map(GetMesLineResponse::getLineUuid).distinct().collect(Collectors.toList());

            //批量查询库里面现有的线体信息
            List<LineInfo> lineInfoList = this.listByUuids(lineUuids);

            Map<String, LineInfo> uuidLineInfoMap = lineInfoList.stream().filter(lineInfo -> StrUtil.isNotBlank(lineInfo.getLineUuid())).collect(Collectors.toMap(LineInfo::getLineUuid,Function.identity(), (k1, k2) -> k1));

            List<LineInfo> addLineInfoList = new ArrayList<>();

            for (GetMesLineResponse resp: resultList){
                if (null == resp) {
                    continue;
                }
                String lineUuid = resp.getLineUuid();
                if (StrUtil.isNotBlank(lineUuid) && uuidLineInfoMap.containsKey(lineUuid)){
                    LineInfo oldLineInfo = uuidLineInfoMap.get(lineUuid);
                    oldLineInfo.setCode(resp.getLineCode());
                    oldLineInfo.setEnableStatus(resp.getEnableStatus());
                    oldLineInfo.setUpdateTime(new Date());
                    oldLineInfo.setUpdateBy(UserContextUtils.getUserId());

                    //更新操作
                    baseMapper.updateById(oldLineInfo);
                }else{
                    LineInfo lineInfo = new LineInfo();
                    lineInfo.setLineUuid(resp.getLineUuid());
                    lineInfo.setCode(resp.getLineCode());
                    lineInfo.setName(resp.getLineName());
                    lineInfo.setEnableStatus(resp.getEnableStatus());
                    lineInfo.setWorkshopCode(resp.getWorkshopCode());
                    lineInfo.setCreateBy(UserContextUtils.getUserId());
                    lineInfo.setUpdateBy(UserContextUtils.getUserId());
                    addLineInfoList.add(lineInfo);
                }
            }
            if (addLineInfoList.size() > 0){
                //批量保存
                this.saveBatch(addLineInfoList);
            }
        }
        return 1;
    }

}
