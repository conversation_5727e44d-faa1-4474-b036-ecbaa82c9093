package com.lds.oneplanning.basedata.service;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.ScheduleDateCfg;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.basedata.model.ScheduleDateCfgDTO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
public interface IScheduleDateCfgService extends IService<ScheduleDateCfg> {

    List<ScheduleDateCfg> findList(Long userId,String factoryCode, String productGroupCode);

    Page<ScheduleDateCfgDTO> page(Long userId,String keyword, String factoryCode, String productGroupCode, Integer pageNum, Integer pageSize);

    ScheduleDateCfgDTO getDetail(Long id);

    List<ScheduleDateCfgDTO> listByFactoryCodes(Collection<String> factoryCodes);
}
