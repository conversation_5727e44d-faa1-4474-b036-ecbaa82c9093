package com.lds.oneplanning.basedata.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述：线体信息excel
 * 创建人： LaiGuiMing
 * 创建时间： 2025/2/21
 */
@Data
public class LineInfoExcel {


    @ExcelProperty("线体uuid")
    private String lineUuid;

    @ExcelProperty("线体编码")
    private String code;

    @ExcelProperty("线体名称")
    private String name;

    @ExcelProperty("虚拟线体1是0否")
    @ApiModelProperty(value = "虚拟线体1是0否")
    private Integer virtualStatus;


    @ApiModelProperty(value = "产线类编码")
    @ExcelProperty("产线类编码")
    private String lineCategoryCode;

    @ApiModelProperty(value = "车间编码")
    @ExcelProperty("车间编码")
    private String workshopCode;

    @ApiModelProperty(value = "工厂编码")
    @ExcelProperty("工厂编码")
    private String factoryCode;




}
