package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.PlannerOrderCfg;
import com.lds.oneplanning.basedata.mapper.PlannerOrderCfgMapper;
import com.lds.oneplanning.basedata.model.PlannerBaseDTO;
import com.lds.oneplanning.basedata.model.PlannerOrderCfgDTO;
import com.lds.oneplanning.basedata.service.IPlannerBaseService;
import com.lds.oneplanning.basedata.service.IPlannerOrderCfgService;
import com.lds.oneplanning.basedata.utils.BizOrderCfgUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-27
 */
@Service
public class PlannerOrderCfgServiceImpl extends ServiceImpl<PlannerOrderCfgMapper, PlannerOrderCfg> implements IPlannerOrderCfgService {
    @Resource
    private IPlannerBaseService plannerBaseService;
    @Override
    public Page<PlannerOrderCfgDTO> page(String keyword, String empNo, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<PlannerOrderCfg> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<PlannerOrderCfg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(PlannerOrderCfg::getEmpNo);

        queryWrapper.eq(StringUtils.isNotBlank(empNo),PlannerOrderCfg::getEmpNo,empNo);
        if (StringUtils.isNotBlank(keyword)) {
            List<PlannerBaseDTO> userList = plannerBaseService.findList(keyword, null);
            Set<String> keywordEmpNos = userList.stream().map(PlannerBaseDTO::getEmpNo).collect(Collectors.toSet());
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(PlannerOrderCfg::getEmpNo,keyword).or()
                    .in(CollectionUtils.isNotEmpty(keywordEmpNos),PlannerOrderCfg::getEmpNo,keywordEmpNos));
        }
        queryWrapper.groupBy(PlannerOrderCfg::getEmpNo);
        queryWrapper.orderByDesc(PlannerOrderCfg::getEmpNo).orderByAsc(PlannerOrderCfg::getId);
        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<PlannerOrderCfgDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<PlannerOrderCfgDTO> results = BeanUtil.mapList(entityPage.getRecords(), PlannerOrderCfgDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);

            List<PlannerBaseDTO> plannerBaseList = plannerBaseService.findList(null, null);
            Map<String,String> plannerNamMap = plannerBaseList.stream().collect(Collectors.toMap(PlannerBaseDTO::getEmpNo, PlannerBaseDTO::getUserName,(s, s2) -> s2));
            this.decorate(results,plannerNamMap);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    @Override
    public Set<String> listOrderSubTypeByEmpNo(String empNo, Integer businessType) {
        List<PlannerOrderCfg> list = this.listByEmpNos(Lists.newArrayList(empNo),businessType);
        return list.stream().map(PlannerOrderCfg::getOrderTypeValue).collect(Collectors.toSet());
    }

    @Override
    public List<PlannerOrderCfg> listByEmpNos(Collection<String> empNos, Integer businessType) {
        if (empNos == null || empNos.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<PlannerOrderCfg>lambdaQuery()
                .eq(businessType!=null,PlannerOrderCfg::getBusinessType,businessType)
                .in(PlannerOrderCfg::getEmpNo,empNos));
    }

    private void decorate(List<PlannerOrderCfgDTO> sourceList,Map<String,String> plannerNameMap){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> empNos = sourceList.stream().map(PlannerOrderCfgDTO::getEmpNo).collect(Collectors.toSet());
        Map<String,List<PlannerOrderCfg>> orderTypeList =this.listByEmpNos(empNos, BaseDataConstant.BUSINESS_TYPE_ORDER).stream().collect(Collectors.groupingBy(PlannerOrderCfg::getEmpNo));
        Map<String,List<PlannerOrderCfg>> subOrderTypeList =this.listByEmpNos(empNos,BaseDataConstant.BUSINESS_TYPE_SUB_ORDER).stream().collect(Collectors.groupingBy(PlannerOrderCfg::getEmpNo));

        sourceList.forEach(orderCfgDTO -> {
            orderCfgDTO.setUserName(plannerNameMap.get(orderCfgDTO.getEmpNo()));
            orderCfgDTO.setProductOrderSubTypeList(BizOrderCfgUtil.buildProductOrderSubTypeList());
            List<PlannerOrderCfg> cfgList = Optional.ofNullable(subOrderTypeList.get(orderCfgDTO.getEmpNo())).orElse(Lists.newArrayList());
            List<String> orderSubTypes = cfgList.stream().map(PlannerOrderCfg::getOrderTypeValue).collect(Collectors.toList());
            orderCfgDTO.getProductOrderSubTypeList().forEach(configDTO -> {
                configDTO.setIsChecked(orderSubTypes.contains(configDTO.getConfigCode()) ? 1 : 0);
            });
            orderCfgDTO.setOrderTypeList(BizOrderCfgUtil.buildOrderTypeList());
            List<PlannerOrderCfg> orderTypes = Optional.ofNullable(orderTypeList.get(orderCfgDTO.getEmpNo())).orElse(Lists.newArrayList());
            List<String> orderTypeSets = orderTypes.stream().map(PlannerOrderCfg::getOrderTypeValue).collect(Collectors.toList());
            orderCfgDTO.getOrderTypeList().forEach(configDTO -> {
                configDTO.setIsChecked(orderTypeSets.contains(configDTO.getConfigCode()) ? 1 : 0);
            });

        });

    }


    @Override
    public Integer batchSaveUpdate(PlannerOrderCfgDTO dto) {
        String empNo= dto.getEmpNo();
        this.baseMapper.delete(Wrappers.<PlannerOrderCfg>lambdaQuery().eq(PlannerOrderCfg::getEmpNo,empNo));
        if (CollectionUtils.isEmpty(dto.getOrderTypeList()) || CollectionUtils.isEmpty(dto.getProductOrderSubTypeList())) {
            return 0;
        }
        List<PlannerOrderCfg> targetList = Lists.newArrayList();
        dto.getProductOrderSubTypeList().stream().filter(configDTO -> 1 == configDTO.getIsChecked()).forEach(configDTO -> {
            PlannerOrderCfg cfg = new PlannerOrderCfg();
            cfg.setBusinessType(BaseDataConstant.BUSINESS_TYPE_SUB_ORDER);
            cfg.setEmpNo(empNo);
            cfg.setOrderTypeValue(configDTO.getConfigCode());
            cfg.setCreateTime(new Date());
            cfg.setUpdateTime(new Date());
            targetList.add(cfg);
        });
        dto.getOrderTypeList().stream().filter(configDTO -> 1 == configDTO.getIsChecked()).forEach(configDTO -> {
            PlannerOrderCfg cfg = new PlannerOrderCfg();
            cfg.setBusinessType(BaseDataConstant.BUSINESS_TYPE_ORDER);
            cfg.setEmpNo(empNo);
            cfg.setOrderTypeValue(configDTO.getConfigCode());
            cfg.setCreateTime(new Date());
            cfg.setUpdateTime(new Date());
            targetList.add(cfg);
        });
        if (!targetList.isEmpty()) {
            this.saveBatch(targetList);
            return 1;
        }
        return  0;
    }

    @Override
    public PlannerOrderCfgDTO getByEmpNo(String empNo) {
        List<PlannerOrderCfg> cfgList = this.listByEmpNos(Lists.newArrayList(empNo),null);
        if (cfgList.isEmpty()) {
            return null;
        }
        PlannerOrderCfg template = cfgList.get(0);
        PlannerOrderCfgDTO res = new PlannerOrderCfgDTO();
        res.setEmpNo(template.getEmpNo());
        Map<String,String> plannerNameMap = Maps.newLinkedHashMap();
        plannerNameMap.put(empNo,plannerBaseService.getNameNoByEmpNo(empNo));
        this.decorate(Lists.newArrayList(res),plannerNameMap);
        return res;
    }

    @Override
    public Integer deleteByEmpNo(String empNo) {
        return this.baseMapper.delete(Wrappers.<PlannerOrderCfg>lambdaQuery().eq(PlannerOrderCfg::getEmpNo,empNo));
    }

    @Override
    public Integer batchDeleteByEmpNos(Collection<String> empNos) {
        if (empNos == null || empNos.isEmpty()) {
            return 0;
        }
        return this.baseMapper.delete(Wrappers.<PlannerOrderCfg>lambdaQuery().in(PlannerOrderCfg::getEmpNo,empNos));
    }
}
