package com.lds.oneplanning.basedata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="PlannerLineCfgDTO", description="")
public class PlannerLineCfgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "线体uuid")
    private String lineUuid;

    @ApiModelProperty(value = "配置类型:1产线类型 2产线")
    private Integer configType;

    @ApiModelProperty(value = "配置编码")
    private String configCode;

    @ApiModelProperty(value = "车间编码")
    private String workshopCode;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "sbu")
    private String sbu;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    private String configName;

    private String factoryName;

    private String workshopName;


    private Collection<String> configCodes;

}
