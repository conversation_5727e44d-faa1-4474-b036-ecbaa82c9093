package com.lds.oneplanning.basedata.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.FactoryScheduleBuffer;
import com.lds.oneplanning.basedata.model.FactoryScheduleBufferDTO;
import com.lds.oneplanning.basedata.service.IFactoryScheduleBufferService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-14
 */
@Slf4j
@Api(value = "FactoryScheduleBufferController", tags = "风险备库管理")
@RestController
@RequestMapping("/basedata/factoryScheduleBuffer")
public class FactoryScheduleBufferController {

    @Resource
    private IFactoryScheduleBufferService factoryScheduleBufferService;


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<FactoryScheduleBufferDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                            @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                            @RequestParam(value = "businessType",required = false)Integer businessType,
                               @RequestParam(value = "pageNum")Integer pageNum,
                               @RequestParam(value = "pageSize")Integer pageSize
    ){
        return factoryScheduleBufferService.page(keyword,factoryCode,businessType,pageNum,pageSize);
    }
    @ApiOperation(value = "详情获取", notes = "详情获取")
    @GetMapping("/detail/{id}")
    public FactoryScheduleBufferDTO detail(@PathVariable("id")Long id){
        return  factoryScheduleBufferService.getDetail(id);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    @Loggable(businessName = "风险备库管理",operation = "新增")
    public Long add(@RequestBody FactoryScheduleBuffer dto ){
        dto.setCreateBy(UserContextUtils.getUserId());
        dto.setUpdateBy(UserContextUtils.getUserId());
        factoryScheduleBufferService.save(dto);
        return dto.getId();
    }

    @ApiOperation(value = "编辑", notes = "编辑")
    @PatchMapping("/edit/{id}")
    @Loggable(businessName = "风险备库管理",operation = "编辑")
    public Integer edit(@RequestBody FactoryScheduleBuffer dto, @PathVariable("id")Long id ){
        dto.setUpdateBy(UserContextUtils.getUserId());
        dto.setUpdateTime(new Date());
        dto.setId(id);
        return  factoryScheduleBufferService.updateById(dto) ? 1 : 0;
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/delete/{id}")
    @Loggable(businessName = "风险备库管理",operation = "删除")
    public Integer delete(@PathVariable("id")Long id ){
        return  factoryScheduleBufferService.removeById(id) ? 1 :0;
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "风险备库管理",operation = "批量删除")
    public Integer batchDelete(@RequestBody Collection<Long> ids){
        return  factoryScheduleBufferService.removeByIds(ids) ? 1:0;
    }

}
