package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.basic.account.user.api.UserApi2;
import com.lds.basic.account.user.dto.UserDto;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.PlannerBase;
import com.lds.oneplanning.basedata.entity.PlannerFunPermission;
import com.lds.oneplanning.basedata.mapper.PlannerBaseMapper;
import com.lds.oneplanning.basedata.model.PlannerBaseDTO;
import com.lds.oneplanning.basedata.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class PlannerBaseServiceImpl extends ServiceImpl<PlannerBaseMapper, PlannerBase> implements IPlannerBaseService {

    @Resource
    private IPlannerDataPermissionService dataPermissionService;
    @Resource
    private IPlannerFunPermissionService funPermissionService;
    @Resource
    private IPlannerLineCfgService plannerLineCfgService;
    @Resource
    private IFactoryService factoryService;

    @Resource
    private UserApi2 userApi2;

    @Override
    public List<PlannerBaseDTO> findList(String keyword, String factoryCode) {
        LambdaQueryWrapper<PlannerBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneBlank(factoryCode),PlannerBase::getFactoryCode,factoryCode);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(PlannerBase::getUserName,keyword).or()
                    .like(PlannerBase::getEmpNo,keyword));
        }
        queryWrapper.orderByDesc(PlannerBase::getUpdateTime).orderByAsc(PlannerBase::getId);
        List<PlannerBase> entityList = baseMapper.selectList(queryWrapper);
        List<PlannerBaseDTO> resList = BeanUtil.mapList(entityList, PlannerBaseDTO.class);
        this.decorate(resList);
        return  resList;
    }

    @Override
    public Page<PlannerBaseDTO> page(String keyword, String factoryCode, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<PlannerBase> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<PlannerBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNoneBlank(factoryCode),PlannerBase::getFactoryCode,factoryCode);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(PlannerBase::getUserName,keyword).or()
                    .like(PlannerBase::getEmpNo,keyword));
        }
        queryWrapper.orderByDesc(PlannerBase::getUpdateTime).orderByAsc(PlannerBase::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<PlannerBaseDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<PlannerBaseDTO> results = BeanUtil.mapList(entityPage.getRecords(), PlannerBaseDTO.class);
            this.decorate(results);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<PlannerBaseDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> factoryCodes = sourceList.stream().map(PlannerBaseDTO::getFactoryCode).collect(Collectors.toSet());
        List<Factory> factories = factoryService.listByFactoryCodes(factoryCodes);
        Map<String,String> factoryMap = factories.stream().collect(Collectors.toMap(Factory::getCode,Factory::getName,(s, s2) -> s2));
        Map<Long, List<PlannerFunPermission>> userPermissionMap = funPermissionService.groupByUserId(sourceList.stream().map(PlannerBaseDTO::getUserId).collect(Collectors.toSet()));
        sourceList.stream().forEach(dto -> {
            dto.setFunPermissionList(userPermissionMap.get(dto.getUserId()));
            dto.setFactoryName(factoryMap.get(dto.getFactoryCode()));
        });
    }

    @Override
    public PlannerBaseDTO detail(Long id) {
        PlannerBase entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        PlannerBaseDTO res = BeanUtil.map(entity, PlannerBaseDTO.class);
        decorate(Lists.newArrayList(res));
        return res;
    }

    @Override
    public Long add(PlannerBaseDTO dto) {
        PlannerBase entity = BeanUtil.map(dto,PlannerBase.class);
        Long userId = dto.getUserId();
        UserDto userParam = new UserDto();
        userParam.setId(userId);
        UserDto user = userApi2.getDto(userParam);
        entity.setEmpNo(user.getJobNo());
        entity.setUserName(user.getName());
        baseMapper.insert(entity);
        funPermissionService.saveByUserId(dto.getFunPermissionList(), userId);

        return entity.getId();
    }

    @Override
    public Integer edit(PlannerBaseDTO dto) {
        PlannerBase entity = BeanUtil.map(dto,PlannerBase.class);
        Long userId = dto.getUserId();
        UserDto userParam = new UserDto();
        userParam.setId(userId);
        UserDto user = userApi2.getDto(userParam);
        entity.setEmpNo(user.getJobNo());
        entity.setUserName(user.getName());
        baseMapper.updateById(entity);
        funPermissionService.saveByUserId(dto.getFunPermissionList(),userId);
        return 1;
    }

    @Override
    public Integer delete(Long id) {
        PlannerBase entity = baseMapper.selectById(id);
        Long userId = entity.getUserId();
        dataPermissionService.deleteByUserId(userId);
        funPermissionService.deleteByUserId(userId);
        plannerLineCfgService.deleteByUserIdAndConfigType(userId, BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY);
        plannerLineCfgService.deleteByUserIdAndConfigType(userId, BaseDataConstant.CONFIG_TYPE_LINE);
        baseMapper.deleteById(id);
        return 1;
    }

    @Override
    public Integer batchDelete(Collection<Long> ids) {
        ids.stream().forEach(aLong -> this.delete(aLong));
        return ids.size();
    }

    @Override
    public String getEmpNoByUserId(Long userId) {
        PlannerBase plannerBase = baseMapper.selectOne(Wrappers.<PlannerBase>lambdaQuery().eq(PlannerBase::getUserId, userId).last(" limit 1"));
        return plannerBase ==  null ? null : plannerBase.getEmpNo();
    }

    @Override
    public String getNameNoByUserId(Long userId) {
        PlannerBase plannerBase = baseMapper.selectOne(Wrappers.<PlannerBase>lambdaQuery().eq(PlannerBase::getUserId, userId).last(" limit 1"));
        return plannerBase ==  null ? null : plannerBase.getUserName();
    }

    @Override
    public String getNameNoByEmpNo(String empNo) {
        PlannerBase plannerBase = baseMapper.selectOne(Wrappers.<PlannerBase>lambdaQuery().eq(PlannerBase::getEmpNo, empNo).last(" limit 1"));
        return plannerBase ==  null ? null : plannerBase.getUserName();
    }
}
