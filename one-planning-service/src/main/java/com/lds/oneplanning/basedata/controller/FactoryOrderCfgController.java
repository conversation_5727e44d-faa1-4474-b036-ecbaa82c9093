package com.lds.oneplanning.basedata.controller;


import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.model.FactoryOrderCfgDTO;
import com.lds.oneplanning.basedata.service.IFactoryOrderCfgService;
import com.lds.oneplanning.log.anotation.Loggable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-13
 */
@Slf4j
@Api(value = "FactoryOrderCfgController", tags = "特殊订单排产-工厂订单配置")
@RestController
@RequestMapping("/basedata/factoryOrderCfg")
public class FactoryOrderCfgController {

    @Resource
    private IFactoryOrderCfgService factoryOrderCfgService;


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public Page<FactoryOrderCfgDTO> page(@RequestParam(value = "keyword",required = false)String keyword,
                                         @RequestParam(value = "factoryCode",required = false)String factoryCode,
                                         @RequestParam(value = "pageNum")Integer pageNum,
                                         @RequestParam(value = "pageSize")Integer pageSize
    ){
        return factoryOrderCfgService.page(keyword,factoryCode,pageNum,pageSize);
    }

    @ApiOperation(value = "批量新增修改", notes = "批量新增修改")
    @PostMapping("/batchSaveUpdate")
    @Loggable(businessName = "特殊订单排产工厂",operation = "批量新增修改")
    public Integer batchSaveUpdate(@RequestBody FactoryOrderCfgDTO dto ){

        return  factoryOrderCfgService.batchSaveUpdate(dto);
    }

    @ApiOperation(value = "根据工厂编码查询记录", notes = "根据工厂编码查询记录")
    @GetMapping("/getByFactoryCode")
    public FactoryOrderCfgDTO getByFactoryCode(@RequestParam("factoryCode")String factoryCode){

        return  factoryOrderCfgService.getByFactoryCode(factoryCode);
    }

    @ApiOperation(value = "根据工厂编码删除", notes = "根据工厂编码删除")
    @DeleteMapping("/deleteByFactoryCode")
    @Loggable(businessName = "特殊订单排产工厂",operation = "根据工厂编码删除")
    public Integer deleteByFactoryCode(@RequestParam("factoryCode")String factoryCode){

        return  factoryOrderCfgService.deleteByFactoryCode(factoryCode);
    }

    @ApiOperation(value = "根据工厂编码删除-批量删除", notes = "根据工厂编码删除-批量删除")
    @DeleteMapping("/batchDelete")
    @Loggable(businessName = "特殊订单排产工厂",operation = "批量删除")
    public Integer batchDeleteByFactoryCodes(@RequestBody Collection<String> factoryCodes){

        return  factoryOrderCfgService.batchDeleteByFactoryCodes(factoryCodes);
    }
}
