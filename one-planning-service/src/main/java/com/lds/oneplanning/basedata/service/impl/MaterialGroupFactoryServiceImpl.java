package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.basedata.entity.MaterialGroupFactory;
import com.lds.oneplanning.basedata.mapper.MaterialGroupFactoryMapper;
import com.lds.oneplanning.basedata.service.IMaterialGroupFactoryService;
import io.jsonwebtoken.lang.Collections;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-03
 */
@Service
public class MaterialGroupFactoryServiceImpl extends ServiceImpl<MaterialGroupFactoryMapper, MaterialGroupFactory> implements IMaterialGroupFactoryService {
    @Override
    public List<MaterialGroupFactory> listByFactoryCodes(Set<String> factoryCodes) {
        if (factoryCodes == null || factoryCodes.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<MaterialGroupFactory>lambdaQuery().in(MaterialGroupFactory::getFactoryCode,factoryCodes));
    }

    @Override
    public Integer batchUpdateByFactory(String factoryCode, Collection<String> groupCodes) {
        if (StringUtils.isBlank(factoryCode) || Collections.isEmpty(groupCodes)) {
            return 0;
        }
        this.baseMapper.delete(Wrappers.<MaterialGroupFactory>lambdaQuery().eq(MaterialGroupFactory::getFactoryCode,factoryCode));
        List<MaterialGroupFactory> targetList = Lists.newArrayList();
        groupCodes.stream().forEach(groupCode -> {
            MaterialGroupFactory groupFactory = new MaterialGroupFactory();
            groupFactory.setFactoryCode(factoryCode);
            groupFactory.setMaterialGroupCode(groupCode);
            groupFactory.setCreateTime(new Date());
            groupFactory.setUpdateTime(new Date());
            targetList.add(groupFactory);
        });
        return this.saveBatch(targetList) ? 1: 0;
    }

    @Override
    public List<MaterialGroupFactory> listByMaterialGroupCode(String materialGroupCode) {
        if (StringUtils.isBlank(materialGroupCode)) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<MaterialGroupFactory>lambdaQuery().eq(MaterialGroupFactory::getMaterialGroupCode,materialGroupCode));
    }
}
