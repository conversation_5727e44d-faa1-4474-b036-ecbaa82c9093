package com.lds.oneplanning.basedata.model;

import com.lds.oneplanning.basedata.entity.LineCapacity;
import com.lds.oneplanning.basedata.entity.LineUph;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="LineChangeCfgDTO 对象", description="")
public class LineChangeCfgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @NotBlank
    @ApiModelProperty(value = "工厂编号")
    private String factoryCode;
    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    @NotNull
    @ApiModelProperty(value = "小时")
    private Integer hour;
    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
