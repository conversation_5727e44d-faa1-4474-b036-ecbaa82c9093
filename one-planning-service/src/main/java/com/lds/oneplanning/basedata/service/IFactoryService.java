package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.basedata.entity.Factory;
import org.springframework.scheduling.annotation.Async;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface IFactoryService extends IService<Factory> {
    @Async
    void syncFromSap();

    List<Factory> listByFactoryCodes(Collection<String> factoryCodes);

    List<Factory> findList(String keyword);

    void batchSaveByCode(List<Factory> souceList);

    /**
     * 获取泰国工厂列表
     * @return
     */
    List<String> getThaiFactoryCodes();
}
