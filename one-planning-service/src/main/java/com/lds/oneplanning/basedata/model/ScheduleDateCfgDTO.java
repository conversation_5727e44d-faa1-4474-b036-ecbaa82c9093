package com.lds.oneplanning.basedata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ScheduleDateCfg对象", description="")
public class ScheduleDateCfgDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "产品组")
    private String productGroupCode;

    @ApiModelProperty(value = "销售订单号")
    private String sellOrderNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "上线开始日期提前天数")
    private Integer beforeStartDays;

    @ApiModelProperty(value = "上线截止日期提前天数")
    private Integer beforeEndDays;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // 以下非库表字段
    private String factoryName;
    private String productGroupName;

    private List<String> productIds = Lists.newArrayList();

}
