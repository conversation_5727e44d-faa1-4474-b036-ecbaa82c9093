package com.lds.oneplanning.basedata.utils;

import com.google.common.collect.Lists;
import com.lds.oneplanning.basedata.enums.OrderSubTypeEnum;
import com.lds.oneplanning.basedata.enums.OrderTypeEnum;
import com.lds.oneplanning.basedata.model.base.ConfigDTO;

import java.util.List;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/27 9:45
 */
public class BizOrderCfgUtil {

    private BizOrderCfgUtil() {
    }

    public static List<ConfigDTO> buildProductOrderSubTypeList(){
        List<ConfigDTO> configDTOS   = Lists.newArrayList();
        for (OrderSubTypeEnum typeEnum : OrderSubTypeEnum.values()){
            ConfigDTO configDTO = new ConfigDTO();
            configDTO.setConfigCode(typeEnum.getCode());
            configDTO.setConfigName(typeEnum.getName());
            configDTOS.add(configDTO);
        }
        return configDTOS;
    }

    public static  List<ConfigDTO> buildOrderTypeList(){
        List<ConfigDTO> configDTOS   = Lists.newArrayList();
        for (OrderTypeEnum typeEnum : OrderTypeEnum.values()){
            ConfigDTO configDTO = new ConfigDTO();
            configDTO.setConfigCode(typeEnum.getCode());
            configDTO.setConfigName(typeEnum.getName());
            configDTOS.add(configDTO);
        }
        return configDTOS;
    }
}
