package com.lds.oneplanning.basedata.service;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.Customer;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.scheduling.annotation.Async;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
public interface ICustomerService extends IService<Customer> {
    @Async
    void syncFromSap();
    Page<Customer> page(String keyword, Integer pageNum, Integer pageSize);


    List<Customer> listByCodes(Collection<String> codes);
}
