package com.lds.oneplanning.basedata.enums;

import java.util.Arrays;

public enum SysParamCfgEnum {
    ORDER_EXPIRE_DAYS("ORDER_EXPIRE_DAYS","订单过期天数","0"),
    SCHEDULE_DAYS("SCHEDULE_DAYS","排产天数","90"),
    ;

    private SysParamCfgEnum(String code, String name,String defaultValue) {
        this.code = code;
        this.name = name;
        this.defaultValue = defaultValue;
    }
    private String code;
    private String name;

    private String defaultValue;

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public static SysParamCfgEnum getValueByCode(String code){
        return Arrays.stream(SysParamCfgEnum.values()).filter(data->data.getCode().equals(code)).findFirst().orElse(null);
    }
}
