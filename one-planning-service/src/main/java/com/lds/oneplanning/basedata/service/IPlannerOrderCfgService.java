package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.PlannerOrderCfg;
import com.lds.oneplanning.basedata.model.PlannerOrderCfgDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-03-27
 */
public interface IPlannerOrderCfgService extends IService<PlannerOrderCfg> {

    Page<PlannerOrderCfgDTO> page(String keyword, String empNo, Integer pageNum, Integer pageSize);

    Set<String> listOrderSubTypeByEmpNo(String empNo, Integer businessType);
    List<PlannerOrderCfg> listByEmpNos(Collection<String> empNos, Integer businessType);

    Integer batchSaveUpdate(PlannerOrderCfgDTO dto);

    PlannerOrderCfgDTO getByEmpNo(String empNo);

    Integer deleteByEmpNo(String empNo);

    Integer batchDeleteByEmpNos(Collection<String> empNos);
}
