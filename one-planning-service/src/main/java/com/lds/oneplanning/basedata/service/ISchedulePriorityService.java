package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.SchedulePriority;
import com.lds.oneplanning.basedata.model.SchedulePriorityDTO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
public interface ISchedulePriorityService extends IService<SchedulePriority> {
    List<SchedulePriorityDTO> list(Long userId,String lineCode,String productGroupCode,String customerCode);

    Page<SchedulePriorityDTO> page(Long userId,String keyword,String lineCode,String productGroupCode,String customerCode, Integer pageNum, Integer pageSize);

    SchedulePriorityDTO getDetail(Long id);


    List<SchedulePriority> listByProductGroupCodes(List<String> lineUuids, Collection<String> productGroupCodes);


}
