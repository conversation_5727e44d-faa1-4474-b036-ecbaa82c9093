package com.lds.oneplanning.basedata.service.impl;



import com.lds.oneplanning.basedata.enums.UserTypeEnum;
import com.lds.oneplanning.basedata.service.IEnumService;
import com.lds.oneplanning.basedata.utils.EnumRegistry;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/5/17
 */
@Service
public class EnumServiceImpl implements IEnumService {

    public EnumServiceImpl() {
        EnumRegistry.registerEnum(UserTypeEnum.class);
    }

    @Override
    public List<Map<String, Object>> getEnumValues(String enumName) {
        return EnumRegistry.getEnumMap().get(enumName);
    }
}
