package com.lds.oneplanning.basedata.service;

import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.ScheduleDirectionCfg;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.basedata.model.ScheduleDirectionCfgDTO;
import com.lds.oneplanning.wps.model.WpsRowData;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-05-14
 */
public interface IScheduleDirectionCfgService extends IService<ScheduleDirectionCfg> {

    Page<ScheduleDirectionCfgDTO> page(String keyword, Integer pageNum, Integer pageSize);

    /**
     * key是orderNo value是方向
     * @param wpsRowDatas
     * @return
     */
    Map<String, Integer> getScheduleMap(List<WpsRowData> wpsRowDatas);

    ScheduleDirectionCfgDTO detail(Long id);
}
