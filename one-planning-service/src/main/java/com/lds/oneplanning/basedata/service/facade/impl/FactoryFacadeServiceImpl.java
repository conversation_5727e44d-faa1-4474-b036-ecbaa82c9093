package com.lds.oneplanning.basedata.service.facade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.entity.Factory;
import com.lds.oneplanning.basedata.entity.MaterialGroupFactory;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.model.FactoryDTO;
import com.lds.oneplanning.basedata.service.IFactoryService;
import com.lds.oneplanning.basedata.service.IMaterialGroupFactoryService;
import com.lds.oneplanning.basedata.service.IMaterialGroupService;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.basedata.service.facade.IFactoryFacadeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/3/11 15:56
 */
@Service
public class FactoryFacadeServiceImpl implements IFactoryFacadeService {
    @Resource
    private IFactoryService factoryService;
    @Resource
    private IMaterialGroupFactoryService materialGroupFactoryService;
    @Resource
    private IMaterialGroupService materialGroupService;
    @Resource
    private IPlannerDataPermissionService plannerDataPermissionService;

    @Override
    public Page<FactoryDTO> page(Long userId,String keyword, String factoryCode, String materialGroupCode, Integer pageNum, Integer pageSize, boolean decorate) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<Factory> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Factory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(factoryCode),Factory::getCode, factoryCode);
        Set<String> permissionFactoryCodes = plannerDataPermissionService.listByUserId(userId).stream().map(PlannerDataPermission::getFactoryCode).collect(Collectors.toSet());
        queryWrapper.in(CollectionUtils.isNotEmpty(permissionFactoryCodes),Factory::getCode, permissionFactoryCodes);
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(Factory::getName,keyword).or()
                    .like(Factory::getCode,keyword));
        }
        if (StringUtils.isNotBlank(materialGroupCode)) {
            List<MaterialGroupFactory> materialGroupFactoryList = materialGroupFactoryService.listByMaterialGroupCode(materialGroupCode);
            Set<String> factoryCodes = materialGroupFactoryList.stream().map(MaterialGroupFactory::getFactoryCode).collect(Collectors.toSet());
            queryWrapper.in(!factoryCodes.isEmpty(),Factory::getCode,factoryCodes);
        }
        queryWrapper.orderByDesc(Factory::getUpdateTime).orderByAsc(Factory::getId);

        entityPage = factoryService.getBaseMapper().selectPage(entityPage, queryWrapper);
        Page<FactoryDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<FactoryDTO> results = BeanUtil.mapList(entityPage.getRecords(), FactoryDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            if (decorate) {
                this.decorate(results);
            }
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<FactoryDTO> sourceList){
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> factoryCodes = sourceList.stream().map(FactoryDTO::getCode).collect(Collectors.toSet());
        List<MaterialGroupFactory> relList = materialGroupFactoryService.listByFactoryCodes(factoryCodes);
        Map<String,List<MaterialGroupFactory>> relMap = relList.stream().collect(Collectors.groupingBy(MaterialGroupFactory::getFactoryCode));
        sourceList.stream().forEach(factoryDTO -> {
            if (CollectionUtils.isNotEmpty(relMap.get(factoryDTO.getCode()))) {
                List<MaterialGroupFactory> factoryRels = relMap.get(factoryDTO.getCode());
                factoryDTO.setMaterialGroupList(materialGroupService.listByCodes(factoryRels.stream().map(MaterialGroupFactory::getMaterialGroupCode).collect(Collectors.toSet())));
            }
        });

    }
    @Override
    public FactoryDTO getDetail(Long id) {
        Factory factory = factoryService.getBaseMapper().selectById(id);
        if (factory == null) {
            return null;
        }
        FactoryDTO res = BeanUtil.map(factory, FactoryDTO.class);
        this.decorate(Lists.newArrayList(res));
        return res;
    }
    @Override
    public Integer edit(FactoryDTO dto) {
        Factory entity = new Factory();
        if (StringUtils.isNotBlank(dto.getName())) {
            entity.setId(dto.getId());
            entity.setName(dto.getName());
            // main data only update name if necessary
            factoryService.getBaseMapper().updateById(entity);
        }
        materialGroupFactoryService.batchUpdateByFactory(dto.getCode(),dto.getMaterialGroupCodes());
        return 1;
    }
    @Override
    public List<Factory> listByUser(Long userId) {
        Set<String> factoryCodes = getUserFactoryCodes(userId);
        return factoryService.listByFactoryCodes(factoryCodes);
    }

    @NotNull
    public Set<String> getUserFactoryCodes(Long userId) {
        List<PlannerDataPermission> dataPermissionList = plannerDataPermissionService.listByUserId(userId);
        return dataPermissionList.stream().map(PlannerDataPermission::getFactoryCode).collect(Collectors.toSet());
    }
}
