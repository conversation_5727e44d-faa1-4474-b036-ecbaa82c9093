package com.lds.oneplanning.basedata.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.LineCategory;
import com.lds.oneplanning.basedata.entity.LineInfo;
import com.lds.oneplanning.basedata.entity.LineUph;
import com.lds.oneplanning.basedata.mapper.LineUphMapper;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.model.LineUphDTO;
import com.lds.oneplanning.basedata.service.ILineCategoryService;
import com.lds.oneplanning.basedata.service.ILineInfoService;
import com.lds.oneplanning.basedata.service.ILineUphService;
import com.lds.oneplanning.basedata.service.IPlannerLineCfgService;
import com.lds.oneplanning.common.utils.CacheKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-12
 */
@Slf4j
@Service
public class LineUphServiceImpl extends ServiceImpl<LineUphMapper, LineUph> implements ILineUphService {

    @Resource
    private ILineCategoryService lineCategoryService;
    @Resource
    private ILineInfoService lineInfoService;
    @Resource
    private IPlannerLineCfgService plannerLineCfgService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Page<LineUphDTO> page(Long userId,String keyword, String factoryCode, String productId, Integer configType, String configCOde, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<LineUph> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<LineUph> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LineUph::getConfigType, configType);
        queryWrapper.like(StringUtils.isNotBlank(productId), LineUph::getProductId, productId);
        if (userId != null) {
            Set<String> lineCodes = plannerLineCfgService.listLineInfoByUserId(userId).stream().map(LineInfoDTO::getCode).collect(Collectors.toSet());
            queryWrapper.in(CollectionUtils.isNotEmpty(lineCodes), LineUph::getConfigCode,lineCodes);
        }
        if (StringUtils.isNotBlank(factoryCode)) {
            Set<String> lineCategoryCodes = lineCategoryService.listByFactoryCode(factoryCode).stream().map(LineCategory::getCode).collect(Collectors.toSet());
            queryWrapper.in(CollectionUtils.isNotEmpty(lineCategoryCodes), LineUph::getConfigCode, lineCategoryCodes);
        }
        if (StringUtils.isNotBlank(configCOde)) {
            queryWrapper.eq(LineUph::getConfigCode, configCOde);
        }
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(LineUph::getConfigCode, keyword).or()
                    .like(LineUph::getProductId, keyword));
        }
        queryWrapper.orderByDesc(LineUph::getUpdateTime).orderByAsc(LineUph::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<LineUphDTO> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<LineUphDTO> results = BeanUtil.mapList(entityPage.getRecords(), LineUphDTO.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            this.decorate(results, configType);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    private void decorate(List<LineUphDTO> results, Integer configType) {
        if (results == null || results.isEmpty()) {
            return;
        }
        Set<String> configCodes = results.stream().map(LineUphDTO::getConfigCode).collect(Collectors.toSet());
        Map<String, String> configNameMap;
        if (BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY.equals(configType)) {
            List<LineCategory> categoryList = lineCategoryService.listByCodes(configCodes);
            configNameMap = categoryList.stream().collect(Collectors.toMap(LineCategory::getCode, LineCategory::getName, (s, s2) -> s2));
        } else {
            List<LineInfo> lineInfos = lineInfoService.listByCodes(configCodes);
            configNameMap = lineInfos.stream().collect(Collectors.toMap(LineInfo::getCode, LineInfo::getName, (s, s2) -> s2));
        }
        for (LineUphDTO dto : results) {
            dto.setConfigName(configNameMap.get(dto.getConfigCode()));
        }

    }

    @Override
    public LineUphDTO getDetail(Long id) {
        LineUph entity = baseMapper.selectById(id);
        if (entity == null) {
            return null;
        }
        LineUphDTO res = BeanUtil.map(entity, LineUphDTO.class);
        this.decorate(Lists.newArrayList(res), entity.getConfigType());
        return res;
    }

    @Override
    public Map<String, List<LineUph>> groupByTypeAndCodes(Integer configType, Collection<String> configCodes) {
        if (configType == null || CollectionUtils.isEmpty(configCodes) ) {
            return Maps.newHashMap();
        }
        return baseMapper.selectList(Wrappers.<LineUph>lambdaQuery()
                .eq(LineUph::getConfigType, configType)
                .in(LineUph::getConfigCode, configCodes))
                .stream().collect(Collectors.groupingBy(LineUph::getConfigCode));
    }

    @Override
    public Map<String, Float> getUphByTypeAndUuid(Integer configType, String lineUuid, List<String> productIds) {
        Map<String, Float> resultMap = Maps.newHashMap();
        if (null == configType || CollectionUtils.isEmpty(productIds) || StringUtils.isBlank(lineUuid)) {
            return resultMap;
        }
        String wpsLineUphCacheKey = CacheKeyUtil.getWpsLineUphCacheKey(configType, lineUuid);
        Map<Object, Object> productUphMap = stringRedisTemplate.opsForHash().entries(wpsLineUphCacheKey);
        if (MapUtils.isEmpty(productUphMap)) {
            reloadCacheFromDb(configType, lineUuid);
        }
        // 从缓存中获取
        fetchFromCache(productIds, resultMap, productUphMap);
        return resultMap;
    }

    @Override
    public void batchSaveByUuid(Integer configType, String lineUuid, List<LineUph> lineUphList) {
        if (null == configType || CollectionUtils.isEmpty(lineUphList) || StringUtils.isBlank(lineUuid)) {
            return;
        }
        baseMapper.delete(Wrappers.<LineUph>lambdaQuery()
                .eq(LineUph::getConfigType, configType)
                .eq(LineUph::getLineUuid, lineUuid));
        this.saveBatch(lineUphList);
        // 清除缓存
        stringRedisTemplate.delete(CacheKeyUtil.getWpsLineUphCacheKey(configType, lineUuid));
    }

    @Override
    public void batchDeleteByUuids(Integer configType, Collection<String> needDeleteLineUuids) {
        if (null == configType || CollectionUtils.isEmpty(needDeleteLineUuids)) {
            return;
        }
        this.remove(Wrappers.<LineUph>lambdaQuery()
                .in(LineUph::getLineUuid, needDeleteLineUuids));
        // 清除缓存
        List<String> needDeleteCacheKeys = needDeleteLineUuids.stream()
                .map(lineUuid -> CacheKeyUtil.getWpsLineUphCacheKey(configType, lineUuid))
                .distinct()
                .collect(Collectors.toList());
        stringRedisTemplate.delete(needDeleteCacheKeys);
    }

    @Override
    public boolean deleteByIds(Collection<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return true;
        }
        List<LineUph> lineUphList = listByIds(ids);
        if(CollectionUtils.isEmpty(lineUphList)){
            return true;
        }
        boolean result = removeByIds(ids);
        Map<String, List<String>> lineUphMap = lineUphList.stream()
                .collect(Collectors.groupingBy(LineUph::getLineUuid,
                        Collectors.mapping(LineUph::getProductId, Collectors.toList())));
        batchDeleteCache(lineUphMap);
        return result;
    }

    @Override
    public boolean update(LineUph lineUph) {
        boolean result = this.updateById(lineUph);
        String cacheKey = CacheKeyUtil.getWpsLineUphCacheKey(lineUph.getConfigType(), lineUph.getLineUuid());
        stringRedisTemplate.opsForHash().put(cacheKey, lineUph.getProductId(), lineUph.getUph().toString());
        return result;
    }

    @Override
    public boolean saveOne(LineUph lineUph) {
        boolean result = this.save(lineUph);
        // 清除缓存
        String cacheKey = CacheKeyUtil.getWpsLineUphCacheKey(lineUph.getConfigType(), lineUph.getLineUuid());
        stringRedisTemplate.opsForHash().put(cacheKey, lineUph.getProductId(), lineUph.getUph().toString());
        return result;
    }

    @Override
    public Map<String,Float> findMap(List<String> lineUUids, List<String> productIds) {
        if (CollectionUtils.isEmpty(lineUUids) || CollectionUtils.isEmpty(productIds)){
            return Maps.newHashMap();
        }
        return baseMapper.selectList(Wrappers.<LineUph>lambdaQuery()
                        .in(LineUph::getLineUuid, lineUUids)
                        .in(LineUph::getProductId, productIds))
                .stream().collect(Collectors.toMap(lineUph -> lineUph.getLineUuid() + ":" + lineUph.getProductId(), LineUph::getUph, (a1, a2) -> a1));
    }

    @Override
    public Map<String, List<LineUph>> groupByUuid(Set<String> lineUUids) {
        if (CollectionUtils.isEmpty(lineUUids)) {
            return Maps.newHashMap();
        }
        return baseMapper.selectList(Wrappers.<LineUph>lambdaQuery().in(LineUph::getLineUuid,lineUUids)).stream().collect(Collectors.groupingBy(LineUph::getLineUuid));
    }

    private void reloadCacheFromDb(Integer configType, String lineUuid) {
        if (configType == null || StringUtils.isBlank(lineUuid)) {
            log.warn("Invalid input: configType={} or lineUuid={} is null/blank", configType, lineUuid);
            return;
        }
        Map<String, Float> uphMap = Maps.newHashMap();
        String cacheKey = CacheKeyUtil.getWpsLineUphCacheKey(configType, lineUuid);
        try {
            List<LineUph> lineUphList = baseMapper.selectList(Wrappers.<LineUph>lambdaQuery()
                    .eq(LineUph::getConfigType, configType)
                    .eq(LineUph::getLineUuid, lineUuid));
            log.info("Retrieved {} LineUph records for configType={} and lineUuid={}",
                    CollectionUtils.size(lineUphList), configType, lineUuid);
            if (CollectionUtils.isNotEmpty(lineUphList)) {
                uphMap = lineUphList.stream()
                        .filter(uph -> uph.getProductId() != null && uph.getUph() != null)
                        .collect(Collectors.toMap(
                                LineUph::getProductId,
                                LineUph::getUph,
                                (k1, k2) -> {
                                    log.debug("Duplicate productId found, keeping first uph: {} over {}", k1, k2);
                                    return k1;
                                }
                        ));
            } else {
                uphMap.put("nil", 0F);
            }
            cacheToRedis(cacheKey, uphMap);
        } catch (Exception e) {
            log.error("Failed to reload LineUph cache for configType={} and lineUuid={}", configType, lineUuid, e);
        }
    }

    private void cacheToRedis(String cacheKey, Map<String, Float> uphMap) {
        if (CollUtil.isEmpty(uphMap) || stringRedisTemplate == null) {
            log.debug("Skipping Redis cache: empty uphMap or null stringRedisTemplate for key={}", cacheKey);
            return;
        }
        try {
            Map<String, String> uphStrMap = uphMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().toString()
                    ));
            stringRedisTemplate.opsForHash().putAll(cacheKey, uphStrMap);
            stringRedisTemplate.expire(cacheKey, 1L, TimeUnit.DAYS);
            log.info("Cached {} entries in Redis with key={}, TTL=1 day", uphStrMap.size(), cacheKey);
        } catch (Exception e) {
            log.error("Failed to cache uphMap in Redis for key={}", cacheKey, e);
        }
    }

    private void fetchFromCache(List<String> productIds, Map<String, Float> resultMap, Map<Object, Object> productUphMap) {
        productIds.forEach(productId -> {
            if (productUphMap.containsKey(productId)) {
                resultMap.put(productId, Float.parseFloat(productUphMap.get(productId).toString()));
            }
        });
    }

    private void batchDeleteCache(Map<String, List<String>> lineUphMap) {
        if (MapUtils.isEmpty(lineUphMap)) {
            return;
        }
        // 批量删除缓存
        lineUphMap.forEach((lineUuid, productIds) -> {
            String deleteCacheKey = CacheKeyUtil.getWpsLineUphCacheKey(BaseDataConstant.CONFIG_TYPE_LINE, lineUuid);
            // 按productIds批量删除缓存
            if (CollectionUtils.isNotEmpty(productIds)) {
                stringRedisTemplate.opsForHash().delete(deleteCacheKey, productIds.toArray());
            }
        });
    }
}