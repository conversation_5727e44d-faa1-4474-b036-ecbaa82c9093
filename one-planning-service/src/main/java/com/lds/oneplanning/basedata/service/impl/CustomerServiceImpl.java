package com.lds.oneplanning.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lds.coral.base.model.Page;
import com.lds.coral.common.util.bean.BeanUtil;
import com.lds.oneplanning.esb.datafetch.model.EsbData;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.basedata.entity.Customer;
import com.lds.oneplanning.basedata.mapper.CustomerMapper;
import com.lds.oneplanning.basedata.service.ICustomerService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-11
 */
@Service
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer> implements ICustomerService {
    @Resource
    private IEsbDataFetchService esbDataFetchService;
    private static final Integer BATCH_SIZE = 1000 ;
    @Override
    public void syncFromSap() {
        List<EsbData> sourceDatas = esbDataFetchService.fetchCustomerList();
        Set<String> sourceCodes = sourceDatas.stream().map(EsbData::getCode).collect(Collectors.toSet());
        List<Customer> insertList = Lists.newArrayList();
        List<Customer> updateList = Lists.newArrayList();
        List<Long>  deleteIds = Lists.newArrayList();
        List<Customer> existList = baseMapper.selectList(Wrappers.<Customer>lambdaQuery());
        Map<String, Customer> mapTarget = existList.stream().collect(Collectors.toMap(Customer::getCode, Customer -> Customer,(t, t2) -> t2));
        // 新增编辑收集
        sourceDatas.forEach(esbData -> {
            String code = esbData.getCode();
            if (mapTarget.containsKey(code)) {
                Customer updateTarget = mapTarget.get(code);
                updateTarget.setName(esbData.getName());
                updateTarget.setAbbreviation(esbData.getSubName());
                updateTarget.setUpdateTime(new Date());
                updateList.add(updateTarget);
            }else {
                Customer insert = new Customer();
                insert.setName(esbData.getName());
                insert.setAbbreviation(esbData.getSubName());
                insert.setCode(esbData.getCode());
                insertList.add(insert);
            }
        });
        // 修改收集
        existList.forEach(Customer -> {
            if (!sourceCodes.contains(Customer.getCode())) {
                deleteIds.add(Customer.getId());
            }
        });
        // 删除进行
        if (!deleteIds.isEmpty()) {
            baseMapper.deleteBatchIds(deleteIds);
        }
        // 新增
        if (!insertList.isEmpty()) {
            this.saveBatch(insertList,BATCH_SIZE);
        }
        //编辑
        if (!updateList.isEmpty()) {
            this.updateBatchById(updateList,BATCH_SIZE);
        }
    }

    @Override
    public Page<Customer> page(String keyword, Integer pageNum, Integer pageSize) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<Customer> entityPage =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.and(o2Wrapper -> o2Wrapper.like(Customer::getName,keyword).or()
                    .like(Customer::getCode,keyword).or()
                    .like(Customer::getAbbreviation,keyword));
        }
        queryWrapper.orderByDesc(Customer::getUpdateTime).orderByAsc(Customer::getId);

        entityPage = baseMapper.selectPage(entityPage, queryWrapper);
        Page<Customer> resultPage = new Page<>(pageNum, pageSize);
        if (CollectionUtils.isNotEmpty(entityPage.getRecords())) {
            resultPage = BeanUtil.map(entityPage, Page.class);
            List<Customer> results = BeanUtil.mapList(entityPage.getRecords(), Customer.class);
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            resultPage.setResult(results);
        }
        return resultPage;
    }

    @Override
    public List<Customer> listByCodes(Collection<String> codes) {
        return baseMapper.selectList(Wrappers.<Customer>lambdaQuery().in(Customer::getCode,codes));
    }
}
