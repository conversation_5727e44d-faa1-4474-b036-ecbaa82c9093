package com.lds.oneplanning.basedata.model.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
public class BasePageEntity {
    private Integer page;
    private Integer pageNum;
    private Integer pageSize;
    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();

    public Integer getPage() {
        if (Objects.isNull(page)) {
            return pageNum;
        }
        return page;
    }

    public Integer getPageNum() {
        if (Objects.isNull(pageNum)) {
            return page;
        }
        return pageNum;
    }
}
