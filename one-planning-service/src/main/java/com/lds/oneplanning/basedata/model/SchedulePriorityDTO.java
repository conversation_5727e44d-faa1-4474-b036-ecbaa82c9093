package com.lds.oneplanning.basedata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="SchedulePriority对象", description="")
public class SchedulePriorityDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "线体uuid")
    private String lineUuid;

    @ApiModelProperty(value = "线体编码")
    private String lineCode;

    @ApiModelProperty(value = "优先级：1、2/3")
    private Integer prioritySeq;

    @ApiModelProperty(value = "产品组")
    private String productGroupCode;

    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    private String lineName;
    private String productGroupName;
    private String customerName;


}
