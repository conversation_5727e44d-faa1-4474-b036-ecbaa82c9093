package com.lds.oneplanning.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.coral.base.model.Page;
import com.lds.oneplanning.basedata.entity.PlannerLineCfg;
import com.lds.oneplanning.basedata.model.LineCategoryDTO;
import com.lds.oneplanning.basedata.model.LineInfoDTO;
import com.lds.oneplanning.basedata.model.PlannerLineCfgDTO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-21
 */
public interface IPlannerLineCfgService extends IService<PlannerLineCfg> {

    Page<PlannerLineCfgDTO> page(Long userId, Integer configType, Integer pageNum, Integer pageSize);

    PlannerLineCfgDTO getDetail(Long id);

    List<LineCategoryDTO> listLineCategoryByUserId(Long userId);
    List<LineInfoDTO> listLineInfoByUserId(Long userId);

    List<PlannerLineCfgDTO> listByUserId(Long userId,Integer configType);

    void saveByUserId(List<PlannerLineCfgDTO> lineCategoryList, Long userId,Integer configType);

    void deleteByUserId(Long userId,Integer configType);


    void saveCategoryByUserId(List<LineCategoryDTO> lineCategoryList, Long userId);

    void saveLineByUserId(List<LineInfoDTO> lineList, Long userId);


    void deleteByUserIdAndConfigType(Long userId,Integer configType);
    void deleteByUserIds(Collection<Long> userIds, Integer configType);
}
