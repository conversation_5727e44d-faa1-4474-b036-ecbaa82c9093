package com.lds.oneplanning.basedata.controller;


import com.lds.oneplanning.basedata.service.IEnumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 枚举值控制层
 *
 * <AUTHOR>
 * @since 2024/6/4
 */
@RestController
public class EnumController {

    @Autowired
    private IEnumService enumService;

    @GetMapping("/enum/values")
    public List<Map<String, Object>> getEnumValues(@RequestParam String enumName) {
        return enumService.getEnumValues(enumName);
    }
}
