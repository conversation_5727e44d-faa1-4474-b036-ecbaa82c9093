package com.lds.oneplanning.mps.schedule.handlers;

import com.google.common.collect.Lists;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.basedata.entity.LineCategory;
import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.model.PlannerLineCfgDTO;
import com.lds.oneplanning.basedata.service.ILineCategoryService;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.basedata.service.IPlannerLineCfgService;
import com.lds.oneplanning.mps.schedule.MpsAutoScheduleContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询计划员的排产产线类
 *
 * <AUTHOR>
 */
@Component
public class MpsPlannerProductionLineHandler implements IMpsAutoScheduleHandler {

    @Autowired
    private ILineCategoryService lineCategoryService;

    @Resource
    private IPlannerLineCfgService plannerLineCfgService;

    @Autowired
    private IPlannerDataPermissionService plannerDataPermissionService;

    @Override
    public void execute(MpsAutoScheduleContext context) {
        Long userId = context.getUserId();
        List<LineCategory> lineCategories = listLineCategoryByUserId(userId);
        if (CollectionUtils.isEmpty(lineCategories)) {
            return;
        }
        List<String> lineCategoryCodes = lineCategories.stream().map(LineCategory::getCode).distinct().collect(Collectors.toList());
        context.setLineCategoryCodes(lineCategoryCodes);
    }

    @Override
    public int getOrder() {
        return 0;
    }

    private List<LineCategory> listLineCategoryByUserId(Long userId) {
        // 查询计划员是否有配置可排产产线类
        List<PlannerLineCfgDTO> mpsPlannerLineCategoryDTOS = plannerLineCfgService.listByUserId(userId, BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY);
        if (CollectionUtils.isNotEmpty(mpsPlannerLineCategoryDTOS)) {
            // 计划员有配置可排产产线类
            List<String> lineCategoryCodes = mpsPlannerLineCategoryDTOS.stream()
                    .map(PlannerLineCfgDTO::getConfigCode).
                    distinct().collect(Collectors.toList());
            return lineCategoryService.listByCodes(lineCategoryCodes);
        }
        // 计划员未配置可排产产线类，查询所有产线类
        List<PlannerDataPermission> mpsPlannerDataPermissions = plannerDataPermissionService.listByUserId(userId);
        if (CollectionUtils.isNotEmpty(mpsPlannerDataPermissions)) {
            List<String> factoryCodes = mpsPlannerDataPermissions.stream().map(PlannerDataPermission::getFactoryCode)
                    .distinct().collect(Collectors.toList());
            return lineCategoryService.listByFactoryCodes(factoryCodes);
        }
        return Lists.newArrayList();
    }
}