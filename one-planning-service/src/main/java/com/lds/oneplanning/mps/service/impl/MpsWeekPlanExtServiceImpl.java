package com.lds.oneplanning.mps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.mps.entity.MpsWeekPlanExt;
import com.lds.oneplanning.mps.mapper.MpsWeekPlanExtMapper;
import com.lds.oneplanning.mps.service.IMpsWeekPlanExtService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-13
 */
@Service
public class MpsWeekPlanExtServiceImpl extends ServiceImpl<MpsWeekPlanExtMapper, MpsWeekPlanExt> implements IMpsWeekPlanExtService {
    @Override
    public List<MpsWeekPlanExt> listByBizIds(List<String> bizIds) {
        if (bizIds == null || bizIds.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.selectList(Wrappers.<MpsWeekPlanExt>lambdaQuery().in(MpsWeekPlanExt::getBizId,bizIds));
    }

    @Override
    public void batchSaveByBizId(List<MpsWeekPlanExt> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> bizIds =sourceList.stream().map(MpsWeekPlanExt::getBizId).collect(Collectors.toSet());
        LambdaQueryWrapper<MpsWeekPlanExt> deleteQuery = Wrappers.<MpsWeekPlanExt>lambdaQuery().in(MpsWeekPlanExt::getBizId,bizIds);
        baseMapper.delete(deleteQuery);
        // 重新新增
        this.saveBatch(sourceList);
    }
}
