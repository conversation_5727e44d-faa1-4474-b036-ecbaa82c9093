package com.lds.oneplanning.mps.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mps_form_info")
@ApiModel(value="FormInfo对象", description="")
public class MpsFormInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "业务id")
    private String bizId;

    @ApiModelProperty(value = "原回复完工日期")
    private Date oldReplyCompletionDate;

    @ApiModelProperty(value = "新回复完工日期")
    private Date newReplyCompletionDate;

    @ApiModelProperty(value = "备注1")
    private String remarkOne;

    @ApiModelProperty(value = "备注2")
    private String remarkTwo;

    @ApiModelProperty(value = "包装/总装")
    private String packagingOrFinalAssembly;

    @ApiModelProperty(value = "风险物料信息备注")
    private String remarkHazardousInfo;

    @ApiModelProperty(value = "客人顺序")
    private String guestSeq;

    @ApiModelProperty(value = "产线类别编码")
    private String lineCategoryCode;

    @ApiModelProperty(value = "线体编号")
    private String lineCode;

    @ApiModelProperty(value = "订单数量")
    private Integer orderPcsQty;

    @ApiModelProperty(value = "报工数量")
    private Integer reportQty;

    @ApiModelProperty(value = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
