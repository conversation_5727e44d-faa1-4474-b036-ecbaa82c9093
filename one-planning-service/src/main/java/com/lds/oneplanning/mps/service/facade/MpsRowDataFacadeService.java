package com.lds.oneplanning.mps.service.facade;

import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.model.RowSaveData;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: zhuang<PERSON><PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025/2/8 16:53
 */
public interface MpsRowDataFacadeService {
    /**
     * api行数据，没有填充排产数据
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    List<MpsRowData> listUserApiRowData(Long userId,Date startTime,Date endTime);
    void fullFillWeekPlanQty(List<MpsRowData> mpsRowDatas);
    void sortMpsRowData(List<MpsRowData> mpsRowDatas);

    List<MpsRowData> listCompleteRowData(Long userId, Date startTime, Date endTime);

    void saveRowData(List<RowSaveData> mpsRowDatas, Date date);
}
