package com.lds.oneplanning.mps.constans;

import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuang<PERSON><PERSON>in
 * @Email: zhuang<PERSON><PERSON><PERSON>@leedarson.com
 * @Date: 2025/2/7 10:54
 */
public class MpsExcelConstant {

    public static final Integer HEAD_ROW_START_INDEX = 0;
    public static final Integer HEAD_ROW_END_INDEX = 1;

    public static final Integer BODY_ROW_OFFSET = 18;
    public static final Integer COLUMN_OFFSET = 0;


    public static final List<String> TITLES_GROUP1 = Lists.newArrayList();
    public static final List<String> TITLES_GROUP2 = Lists.newArrayList();
    public static final List<String> TITLES_GROUP3 = Lists.newArrayList();

    public static final Map<String,String> nameValueMap =  Maps.newLinkedHashMap();

    static {
        TITLES_GROUP1.add("主计划");
        TITLES_GROUP1.add("部门（车间）");
        TITLES_GROUP1.add("客户组");
        TITLES_GROUP1.add("客户代码");
        TITLES_GROUP1.add("销售订单号");
        TITLES_GROUP1.add("行项目");
        TITLES_GROUP1.add("OM负责人");
        TITLES_GROUP1.add("生产工厂");
        TITLES_GROUP1.add("订单类型");
        TITLES_GROUP1.add("订单号");
        TITLES_GROUP1.add("外向交货单");
        TITLES_GROUP1.add("外向行项目");
        TITLES_GROUP1.add("商品ID");
        TITLES_GROUP1.add("商品描述");
        TITLES_GROUP1.add("跨工厂物料状态");
        TITLES_GROUP1.add("三级产品线");
        TITLES_GROUP1.add("五级产品线");
        TITLES_GROUP1.add("品类（大）");
        TITLES_GROUP1.add("机型（中）");
        TITLES_GROUP1.add("规格（小）");
        TITLES_GROUP1.add("产品ID");
        TITLES_GROUP1.add("产品描述");
        TITLES_GROUP1.add("订单数量（套数）");
        TITLES_GROUP1.add("订单数量（只数）");
        TITLES_GROUP1.add("转换数量");
        TITLES_GROUP1.add("已报工数量（只数）");
        TITLES_GROUP1.add("已入库数量（只数）");
        TITLES_GROUP1.add("需排产数量（只数）");
        TITLES_GROUP1.add("业务留样数量");
        TITLES_GROUP1.add("品保留样数量");
        TITLES_GROUP1.add("光源组件ID");
        TITLES_GROUP1.add("光源交期");
        TITLES_GROUP1.add("驱动组件ID");
        TITLES_GROUP1.add("驱动交期");
        TITLES_GROUP1.add("结构件交期");
        TITLES_GROUP1.add("包装交期");
        TITLES_GROUP1.add("标准工时（总装+老化+包装）");
        TITLES_GROUP1.add("包装方式");
        TITLES_GROUP1.add("产能结构");
        TITLES_GROUP1.add("上线日期");
        TITLES_GROUP1.add("原始完工日期");
        TITLES_GROUP1.add("是否验货");
        TITLES_GROUP1.add("原始计划验货日期");
        TITLES_GROUP1.add("最新计划验货日期");
        TITLES_GROUP1.add("原始计划装柜日期");
        TITLES_GROUP1.add("最新计划装柜日期");
        TITLES_GROUP1.add("原始船期");
        TITLES_GROUP1.add("最终船期");
        TITLES_GROUP1.add("推估需求完工日期");
        TITLES_GROUP1.add("生产开始日期（组装/上线）");
        TITLES_GROUP1.add("生产结束日期（包装）");
        TITLES_GROUP1.add("原回复完工日期");
        TITLES_GROUP1.add("新回复完工日期");
        TITLES_GROUP1.add("备注1");
        TITLES_GROUP1.add("备注2");
    }
    static {
        for (int i = 1; i <14 ; i++) {
            TITLES_GROUP2.add("WK"+i);
        }
    }
    static {
        TITLES_GROUP3.add("产品单价");
        TITLES_GROUP3.add("订单签发完成日期");
        TITLES_GROUP3.add("包装/总装");
        TITLES_GROUP3.add("风险物料信息备注");
        TITLES_GROUP3.add("虚拟订单号");
        TITLES_GROUP3.add("虚拟订单项次");
        TITLES_GROUP3.add("首次评审船期");
        TITLES_GROUP3.add("PONO");
        TITLES_GROUP3.add("客户物料编号");
        TITLES_GROUP3.add("客人顺序");
        TITLES_GROUP3.add("机型");
        TITLES_GROUP3.add("瓦数");
        TITLES_GROUP3.add("尺寸（方形/原型/规）");
    }





    static {
        nameValueMap.put("主计划","mainPlan");
        nameValueMap.put("部门（车间）","dept");
        nameValueMap.put("客户组","customerGroup");
        nameValueMap.put("客户代码","customerCode");
        nameValueMap.put("销售订单号","sellOrderNo");
        nameValueMap.put("行项目","rowItem");
        nameValueMap.put("OM负责人","omRespons");
        nameValueMap.put("生产工厂","factory");
        nameValueMap.put("订单类型","orderType");
        nameValueMap.put("订单号","orderNo");
        nameValueMap.put("外向交货单","outDeliveryNo");
        nameValueMap.put("外向行项目","outRowItem");
        nameValueMap.put("商品ID","commodityId");
        nameValueMap.put("商品描述","commodityDesc");
        nameValueMap.put("跨工厂物料状态","crossPlantMaterialStatus");
        nameValueMap.put("三级产品线","productLineLevel3");
        nameValueMap.put("五级产品线","productLineLevel5");
        nameValueMap.put("品类（大）","category");
        nameValueMap.put("机型（中）","machineModel");
        nameValueMap.put("规格（小）","specification");
        nameValueMap.put("产品ID","productId");
        nameValueMap.put("产品描述","productDesc");
        nameValueMap.put("订单数量（套数）","orderUnitQty");
        nameValueMap.put("订单数量（只数）","orderPcsQty");
        nameValueMap.put("转换数量","transQty");
        nameValueMap.put("已报工数量（只数）","reportedPcsQty");
        nameValueMap.put("已入库数量（只数）","stockedPcsQty");
        nameValueMap.put("需排产数量（只数）","schedulePcsQty");
        nameValueMap.put("业务留样数量","bizSampleQty");
        nameValueMap.put("品保留样数量","qcSampleQty");
        nameValueMap.put("光源组件ID","lightComponentId");
        nameValueMap.put("光源交期","lightDeliveryTime");
        nameValueMap.put("驱动组件ID","driverComponentId");
        nameValueMap.put("驱动交期","driverDeliveryTime");
        nameValueMap.put("结构件交期","structDeliveryTime");
        nameValueMap.put("包装交期","packageDeliveryTime");
        nameValueMap.put("标准工时（总装+老化+包装）","stdWorkHours");
        nameValueMap.put("包装方式","packageType");
        nameValueMap.put("产能结构","capacityStruct");
        nameValueMap.put("上线日期","onlineTime");
        nameValueMap.put("原始完工日期","originalOnlineTime");
        nameValueMap.put("是否验货","isInspect");
        nameValueMap.put("原始计划验货日期","originalInspectTime");
        nameValueMap.put("最新计划验货日期","latestInspectTime");
        nameValueMap.put("原始计划装柜日期","originalLoadTime");
        nameValueMap.put("最新计划装柜日期","latestLoadTime");
        nameValueMap.put("原始船期","originalShipTime");
        nameValueMap.put("最终船期","finalShipTime");
        nameValueMap.put("推估需求完工日期","estFinishTime");
        nameValueMap.put("生产开始日期（组装/上线）","productStartTime");
        nameValueMap.put("生产结束日期（包装）","productEndTime");
        nameValueMap.put("原回复完工日期","originalReplyTime");
        nameValueMap.put("新回复完工日期","latestReplyTime");
        nameValueMap.put("备注1","remark1");
        nameValueMap.put("备注2","remark2");

        nameValueMap.put("产品单价","productUnitPrice");
        nameValueMap.put("订单签发完成日期","orderSignFinishTime");
        nameValueMap.put("包装/总装","finalAssembly");
        nameValueMap.put("风险物料信息备注","riskMaterialRemark");
        nameValueMap.put("虚拟订单号","virtualOrderNo");
        nameValueMap.put("虚拟订单项次","virtualRowItem");
        nameValueMap.put("首次评审船期","firstReviewShipTime");
        nameValueMap.put("PONO","pono");
        nameValueMap.put("客户物料编号","customerMaterialNo");
        nameValueMap.put("客人顺序","customerSeq");
        nameValueMap.put("机型","machineModel2");
        nameValueMap.put("瓦数","powerNum");
        nameValueMap.put("尺寸（方形/原型/规格）","size");

        nameValueMap.put("WK1","WK1");
        nameValueMap.put("WK2","WK2");
        nameValueMap.put("WK3","WK3");
        nameValueMap.put("WK4","WK4");
        nameValueMap.put("WK5","WK5");
        nameValueMap.put("WK6","WK6");
        nameValueMap.put("WK7","WK7");
        nameValueMap.put("WK8","WK8");
        nameValueMap.put("WK9","WK9");
        nameValueMap.put("WK10","WK10");
        nameValueMap.put("WK11","WK11");
        nameValueMap.put("WK12","WK12");
        nameValueMap.put("WK13","WK13");

        nameValueMap.put("W1上半周","upperHalfWeek1");
        nameValueMap.put("W1下半周","lowerHalfWeek1");
        nameValueMap.put("W2上半周","upperHalfWeek2");
        nameValueMap.put("W2下半周","lowerHalfWeek2");
        nameValueMap.put("W3上半周","upperHalfWeek3");
        nameValueMap.put("W3下半周","lowerHalfWeek3");
        nameValueMap.put("W4上半周","upperHalfWeek4");
        nameValueMap.put("W4下半周","lowerHalfWeek4");
        nameValueMap.put("W5上半周","upperHalfWeek5");
        nameValueMap.put("W5下半周","lowerHalfWeek5");
        nameValueMap.put("W6上半周","upperHalfWeek6");
        nameValueMap.put("W6下半周","lowerHalfWeek6");
        nameValueMap.put("W7上半周","upperHalfWeek7");
        nameValueMap.put("W7下半周","lowerHalfWeek7");
        nameValueMap.put("W8上半周","upperHalfWeek8");
        nameValueMap.put("W8下半周","lowerHalfWeek8");
        nameValueMap.put("W9上半周","upperHalfWeek9");
        nameValueMap.put("W9下半周","lowerHalfWeek9");
        nameValueMap.put("W10上半周","upperHalfWeek10");
        nameValueMap.put("W10下半周","lowerHalfWeek10");
        nameValueMap.put("W11上半周","upperHalfWeek11");
        nameValueMap.put("W11下半周","lowerHalfWeek11");
        nameValueMap.put("W12上半周","upperHalfWeek12");
        nameValueMap.put("W12下半周","lowerHalfWeek12");
        nameValueMap.put("W13上半周","upperHalfWeek13");
        nameValueMap.put("W13下半周","lowerHalfWeek13");
    }

    public static void main(String[] args) {
        String temp =  "%s(\"%s\",\"%s\"),";
        for(Map.Entry<String,String> entry : nameValueMap.entrySet()){
            System.out.println(String.format(temp,entry.getValue(),entry.getValue(),entry.getKey()));
        }
    }
}
