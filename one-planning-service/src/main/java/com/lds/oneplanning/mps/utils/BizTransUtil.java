package com.lds.oneplanning.mps.utils;

import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.mps.date.YearWeekMap;
import com.lds.oneplanning.mps.entity.MpsFormInfo;
import com.lds.oneplanning.mps.entity.MpsWeekPlan;
import com.lds.oneplanning.mps.entity.MpsWeekPlanExt;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.model.RowSaveData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;






/**
 * @Description:
 * @Author: zhuang<PERSON>ayin
 * @Email: <EMAIL>
 * @Date: 2025/2/10 11:18
 */
@Slf4j
public class BizTransUtil {

    private BizTransUtil() {

    }

    public static MpsFormInfo mpsRowDataToForm(RowSaveData saveData){
        MpsFormInfo formInfo = new MpsFormInfo();
        formInfo.setBizId(saveData.getOrderNo());
        formInfo.setOldReplyCompletionDate(saveData.getOriginalReplyTime());
        formInfo.setNewReplyCompletionDate(saveData.getLatestReplyTime());
        formInfo.setRemarkOne(saveData.getRemark1());
        formInfo.setRemarkTwo(saveData.getRemark2());
        formInfo.setPackagingOrFinalAssembly(saveData.getFinalAssembly());
        formInfo.setRemarkHazardousInfo(saveData.getRiskMaterialRemark());
        formInfo.setGuestSeq(saveData.getCustomerSeq());
        formInfo.setLineCategoryCode(saveData.getLineCategoryCode());
        formInfo.setReportQty(saveData.getReportedPcsQty());
        formInfo.setOrderType(saveData.getOrderType());
        formInfo.setOrderPcsQty(saveData.getOrderPcsQty());
        formInfo.setCreateTime(new Date());
        formInfo.setUpdateTime(new Date());
        return formInfo;
    }


    public static List<MpsFormInfo> mpsRowDatasToFormList(List<RowSaveData> sourceList){
        if (sourceList == null ||  sourceList.isEmpty()) {
            return Lists.newArrayList();
        }
        List<MpsFormInfo> resList = Lists.newArrayList();
        sourceList.forEach(saveData -> resList.add(mpsRowDataToForm(saveData)) );
        return resList;
    }



    public static List<MpsWeekPlan> mpsRowDataToMpsWeekPlanList(RowSaveData mpsRow, Date queryTime) {
        List<MpsWeekPlan> mwps = Lists.newArrayList();
        Integer startWeek = LocalDateTimeUtil.getWeekSeqOfYear(LocalDateTimeUtil.dateToLocalDate(queryTime));
        YearWeekMap yearWeekMap = MpsDateUtil.getAllDatesFromCurrentDate(queryTime, 12);
        LocalDate localDate = LocalDateTimeUtil.dateToLocalDate(queryTime);
        // todo 跨年的先不处理
        int methodIndex = 1 ;
        for ( int wekSeqNo = startWeek; wekSeqNo < startWeek+13 ; wekSeqNo++) {
           // 一个周次 两个半周
            for (int weekType = 0; weekType < 2; weekType++) {
                MpsWeekPlan mpsWeekPlan = new MpsWeekPlan();
                mpsWeekPlan.setBizId(mpsRow.getOrderNo());
                mpsWeekPlan.setCurrentYear(localDate.getYear());
                mpsWeekPlan.setDayNum(3);
                mpsWeekPlan.setNatureWeek(wekSeqNo);
                mpsWeekPlan.setCreateTime(new Date());
                mpsWeekPlan.setUpdateTime(new Date());
                mpsWeekPlan.setStartDate(yearWeekMap.getYearMap().get(localDate.getYear()).getWeekMap().get(wekSeqNo).getHalfWeekMap().get(weekType).get(0));
                mpsWeekPlan.setEndDate(yearWeekMap.getYearMap().get(localDate.getYear()).getWeekMap().get(wekSeqNo).getHalfWeekMap().get(weekType).get(2));
                mpsWeekPlan.setWeekType(weekType);
                mpsWeekPlan.setPrePlanQuantity(getPrePlanQuantity(mpsRow, methodIndex, weekType));
                mwps.add(mpsWeekPlan);
            }
            methodIndex++;

        }
        return mwps;
    }
    public static MpsWeekPlanExt mpsRowDataToMpsWeekPlanExt(RowSaveData mpsRow, Date queryTime) {
        MpsWeekPlanExt  res = new MpsWeekPlanExt();
        res.setBizId(mpsRow.getOrderNo());
        res.setScheduleDate(getScheduleDate(mpsRow, queryTime));
        res.setScheduleYear(LocalDate.now().getYear());
        res.setScheduleWeek(getScheduleWeek(mpsRow, queryTime));
        res.setWeekType(getWeekType(mpsRow,queryTime));
        // 排序增加
        res.setScheduleSeq(res.getScheduleYear()*1000000000L+res.getScheduleDate().getMonthValue()*100000000L+res.getScheduleDate().getDayOfMonth()*100000L+mpsRow.getScheduleSeq());
        res.setLineCategoryCode(mpsRow.getLineCategoryCode());
        res.setFrozenStatus(mpsRow.get_frozenStatus());
        res.setCreateTime(new Date());
        res.setUpdateTime(new Date());
        return res;
    }

    private static LocalDate getScheduleDate(RowSaveData mpsRow, Date queryTime){
        if (LocalDateTimeUtil.getWeekSeqOfYear(LocalDateTimeUtil.dateToLocalDate(queryTime))< (52-13)) {
            return LocalDate.now();
        }else{
            // todo 要结合具体的周次判断 先不处理
            return LocalDate.now();
        }
    }
    private static Integer getScheduleWeek(RowSaveData mpsRow, Date queryTime){
        Integer startWeek = LocalDateTimeUtil.getWeekSeqOfYear(LocalDateTimeUtil.dateToLocalDate(queryTime));
        YearWeekMap yearWeekMap = MpsDateUtil.getAllDatesFromCurrentDate(queryTime, 12);
        LocalDate localDate = LocalDateTimeUtil.dateToLocalDate(queryTime);
        // 区区13行 懒得再写反射。
        if (mpsRow.getUpperHalfWeek1() !=null  || mpsRow.getLowerHalfWeek1()!= null) {
            return startWeek;
        }
        if (mpsRow.getUpperHalfWeek2() !=null  || mpsRow.getLowerHalfWeek2()!= null) {
            return startWeek+1;
        }
        if (mpsRow.getUpperHalfWeek3() !=null  || mpsRow.getLowerHalfWeek3()!= null) {
            return startWeek+2;
        }
        if (mpsRow.getUpperHalfWeek4() !=null  || mpsRow.getLowerHalfWeek4()!= null) {
            return startWeek+3;
        }
        if (mpsRow.getUpperHalfWeek5() !=null  || mpsRow.getLowerHalfWeek5()!= null) {
            return startWeek+4;
        }
        if (mpsRow.getUpperHalfWeek6() !=null  || mpsRow.getLowerHalfWeek6()!= null) {
            return startWeek+5;
        }
        if (mpsRow.getUpperHalfWeek7() !=null  || mpsRow.getLowerHalfWeek7()!= null) {
            return startWeek+6;
        }
        if (mpsRow.getUpperHalfWeek8() !=null  || mpsRow.getLowerHalfWeek8()!= null) {
            return startWeek+7;
        }
        if (mpsRow.getUpperHalfWeek9() !=null  || mpsRow.getLowerHalfWeek9()!= null) {
            return startWeek+8;
        }
        if (mpsRow.getUpperHalfWeek10() !=null  || mpsRow.getLowerHalfWeek10()!= null) {
            return startWeek+9;
        }
        if (mpsRow.getUpperHalfWeek11() !=null  || mpsRow.getLowerHalfWeek11()!= null) {
            return startWeek+10;
        }
        if (mpsRow.getUpperHalfWeek12() !=null  || mpsRow.getLowerHalfWeek12()!= null) {
            return startWeek+11;
        }
        if (mpsRow.getUpperHalfWeek13() !=null  || mpsRow.getLowerHalfWeek13()!= null) {
            return startWeek+12;
        }
        return null;
    }

    private static Integer getWeekType(RowSaveData mpsRow, Date queryTime){
        String methodNameUpper = "getUpperHalfWeek";
        String methodNameLower = "getLowerHalfWeek";
        try {
            for (int i = 1; i <=13 ; i++) {
                String upperMethod= methodNameUpper+i;
                Method method1 =     RowSaveData.class.getMethod(upperMethod);
                Object value1 = method1.invoke(mpsRow);
                if (value1 == null) {
                    return 0;
                }
                String lowerMethod= methodNameLower+i;
                Method method2 =     RowSaveData.class.getMethod(lowerMethod);
                Object value2 = method2.invoke(mpsRow);
                if (value2 == null) {
                    return 1;
                }
            }
        } catch (Exception e) {
            log.error("反射获取周类型失败 msg={}",e.getMessage(),e);
        }
        return null;
    }

    private static Integer getPrePlanQuantity(RowSaveData mpsRow, int methodIndex, int weekType){
        try {
            String methodName = weekType == 0 ? "getUpperHalfWeek"+methodIndex : "getLowerHalfWeek"+methodIndex;
            Method method =     RowSaveData.class.getMethod(methodName);
            Object res = method.invoke(mpsRow);
            return  res == null  ? null : (Integer) res;
        } catch (Exception e) {
           log.error("反射获取预排产失败 msg={}",e.getMessage(),e);
        }
        return 0;
    }


    public static MpsRowData getRowData(Map<String, String> objMap){
        String customerGroup = objMap.get("LOCCO"); // 客户组
        String customerCode = objMap.get("SORTL"); // 客户代码
        String sellOrderNo = objMap.get("VBELN"); //销售订单号
        String rowItem = objMap.get("POSNR"); // 行项目
        String factory = objMap.get("WERKS"); // 工厂(自有或外部)
        String orderType = objMap.get("ZTYPE"); // 订单类型
        String orderNo = objMap.get("ZDDH"); //订单号
        String customerNo = objMap.get("KUNNR"); // 客户编号
        String outDeliveryNo = objMap.get("WXJHD"); // 外向交货单
        String outRowItem = objMap.get("WXJHH"); // 外向行项目
        String commodityId = objMap.get("ZSPID"); // 商品id
        String commodityDesc = objMap.get("ARKTX"); // 商品描述
        String crossPlantMaterialStatus = objMap.get("MSTAE"); // 跨工厂物料状态
        String productLineLevel3 = objMap.get("Z10859"); // 三级产品线
        String productLineLevel5 = objMap.get("Z10861"); // 五级产品线
        String category = objMap.get("Z10302"); // 产品大类
        String productId = objMap.get("CPIDZ"); // 产品I
        String productDesc = objMap.get("ZCPMS"); // 产品描述
        Object orderUnitQty = objMap.get("ZMENG"); // 套数
        Object orderPcsQty = objMap.get("DDLTS"); // 只数
        Object transQty = objMap.get("Z11165"); // 转化数量
        Object stockedPcsQty = objMap.get("GSMNG"); // 已入库数量
        Object productStartTime = objMap.get("PSTTR"); //  生产开始日期
        Object productEndTime = objMap.get("PEDTR"); //  生产结束日期
        Object productUnitPrice = objMap.get("ZSPDJ"); // 产品单价
        Object orderSignFinishTime = objMap.get("ZPSDAT3"); // 订单签发完成日期



        Object originalOnlineTime = objMap.get("YSWGRQ"); // 原始完工日期
        Object onlineTime = objMap.get("ZZWGRQ"); // 最终完工日期
        Object originalLoadTime = objMap.get("YSGZRQ"); // 原始装柜日期
        Object latestLoadTime = objMap.get("ZZZGRQ"); // 最终装柜日期
        Object originalInspectTime = objMap.get("YSYHRQ"); // 原始验货日期
        Object latestInspectTime = objMap.get("YZYHRQ"); // 最终验货日期


        String packageType = objMap.get("Z10227"); // 包装方式
        String powerNum = objMap.get("Z10078"); // 高岛雄平瓦数
        String size = objMap.get("Z10294"); // 尺寸(方形/原型/规格)
        String virtualOrderNo = objMap.get("VBELN_V"); // 虚拟订单号
        String virtualRowItem = objMap.get("POSNR_V"); // 虚拟订单项目号
        Object firstReviewShipTime = objMap.get("ZBPDATE1"); // 首次评审船期
        String customerMaterialNo = objMap.get("KDMAT"); // 客户物料
        String lightComponentId = objMap.get("ZGYID"); // 光源ID
        String pono = objMap.get("BSTKD_E"); // 收货方的客户参考




//        Object orderUnitQty = objMap.get("DDLTS"); // 订单套数


        String MATNR = objMap.get("MATNR"); // 物料编号
        String ARKTX = objMap.get("ARKTX"); // 销售订单项目短文本

        Object NETWR = objMap.get("NETWR"); // 凭证货币计量的净价值
        Object KPEIN = objMap.get("KPEIN"); // 条件定价单位
        String WAERK = objMap.get("WAERK"); // SD 凭证货币
        String VRKME = objMap.get("VRKME"); // 销售单位
        Object ERDAT = objMap.get("ERDAT"); // 记录创建日期
        Object ERZET = objMap.get("ERZET"); // 输入时间
        String ETTYP = objMap.get("ETTYP"); // 计划行类别

        String ZWCBZ = objMap.get("ZWCBZ"); // 出货完成标识
        String IDNRK = objMap.get("IDNRK"); // 组件
        String ZTYPE = objMap.get("ZTYPE"); // 订单类型
        String LOCCO = objMap.get("LOCCO"); // 城市协调
        String WXJHD = objMap.get("WXJHD"); // 交货
        String WXJHH = objMap.get("WXJHH"); // 交货项目


        String MAKTX = objMap.get("MAKTX"); // 物料描述
        String LFDAT = objMap.get("LFDAT"); // 交货日期
        String ZCHZJ = objMap.get("ZCHZJ"); // 制程组件

        MpsRowData rowData = new MpsRowData();
        rowData.setMainPlan("");
        rowData.setDept("");
        rowData.setLineCategoryCode("");
        rowData.setCustomerGroup(customerGroup);
        rowData.setCustomerCode(customerCode);
        rowData.setSellOrderNo(sellOrderNo);
        rowData.setRowItem(rowItem);
        rowData.setOmRespons("");
        rowData.setFactory(factory);
        rowData.setOrderType(orderType);
        rowData.setOrderNo(orderNo);
        rowData.setOutDeliveryNo(outDeliveryNo);
        rowData.setOutRowItem(outRowItem);
        rowData.setCommodityId(commodityId);
        rowData.setCommodityDesc(commodityDesc);
        rowData.setCrossPlantMaterialStatus(crossPlantMaterialStatus);
        rowData.setProductLineLevel3(productLineLevel3);
        rowData.setProductLineLevel5(productLineLevel5);
        rowData.setCategory(category);
        rowData.setMachineModel("");
        rowData.setSpecification("");
        rowData.setProductId(productId);
        rowData.setProductDesc(productDesc);
        rowData.setOrderUnitQty(strToNumber(orderUnitQty));
        rowData.setOrderPcsQty(strToNumber(orderPcsQty));
        rowData.setTransQty(strToNumber(transQty));
        rowData.setReportedPcsQty(0);
        rowData.setStockedPcsQty(strToNumber(stockedPcsQty));
        rowData.setSchedulePcsQty(0);
        rowData.setBizSampleQty(0);
        rowData.setQcSampleQty(0);
        rowData.setLightComponentId(lightComponentId);
        rowData.setLightDeliveryTime(new Date());
        rowData.setDriverComponentId("");
        rowData.setDriverDeliveryTime(new Date());
        rowData.setStructDeliveryTime(new Date());
        rowData.setPackageDeliveryTime(new Date());
        rowData.setStdWorkHours("");
        rowData.setPackageType(packageType);
        rowData.setCapacityStruct("");
        rowData.setOnlineTime(strToDate(onlineTime));
        rowData.setOriginalOnlineTime(strToDate(originalOnlineTime));
        rowData.setIsInspect("");
        rowData.setOriginalInspectTime(strToDate(originalInspectTime));
        rowData.setLatestInspectTime(strToDate(latestInspectTime));
        rowData.setOriginalLoadTime(strToDate(originalLoadTime));
        rowData.setLatestLoadTime(strToDate(latestLoadTime));
        rowData.setOriginalShipTime(strToDate(originalLoadTime));
        rowData.setFinalShipTime(null);
        rowData.setEstFinishTime(new Date());
        rowData.setProductStartTime(strToDate(productStartTime));
        rowData.setProductEndTime(strToDate(productEndTime));
        rowData.setOriginalReplyTime(new Date());
        rowData.setLatestReplyTime(new Date());
        rowData.setRemark1("");
        rowData.setRemark2("");

        rowData.setProductUnitPrice(strToNumber(productUnitPrice));
        rowData.setOrderSignFinishTime(strToDate(orderSignFinishTime));
        rowData.setFinalAssembly("");
        rowData.setRiskMaterialRemark("");
        rowData.setVirtualOrderNo(virtualOrderNo);
        rowData.setVirtualRowItem(virtualRowItem);
        rowData.setFirstReviewShipTime(strToDate(firstReviewShipTime));
        rowData.setPono(pono);
        rowData.setCustomerMaterialNo(customerMaterialNo);
        rowData.setCustomerSeq("");
        rowData.setPowerNum(powerNum);
        rowData.setSize(size);

        return  rowData ;

    }


    public static Date strToDate(Object source){

        try {
            if (source == null || StringUtils.isBlank(source.toString())) {
                return null;
            }
            if (source instanceof Long) {
                return  new Date(((Long) source).longValue());
            }
            if (source instanceof String && StringUtils.isNotBlank(source.toString())) {
                String formatter = source.toString().contains("-")? "yyyy-MM-dd" : "yyyyMMdd";
                formatter = source.toString().contains("/") ? "yyyy/MM/dd" : formatter;
                return  DateUtils.parseDate(source.toString(),formatter);
            }
        }catch (Exception e){
            log.error("日期转化异常 msg={}",e.getMessage(),e);
        }
        return null;
    }
    public static Number strToNumber(Object source){
        if (source == null || StringUtils.isBlank(source.toString())) {
            return null;
        }
        try {
            return new BigDecimal(source.toString());
        }catch (Exception e){
            log.error(" 转Integer异常 source={},msg={}",source,e.getMessage(),e);
        }
        return null;
    }


}
