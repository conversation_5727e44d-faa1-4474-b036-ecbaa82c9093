/**/package com.lds.oneplanning.mps.service.facade.impl;

import com.lds.oneplanning.basedata.entity.PlannerDataPermission;
import com.lds.oneplanning.basedata.model.ProductGroupDTO;
import com.lds.oneplanning.basedata.service.IPlannerDataPermissionService;
import com.lds.oneplanning.basedata.service.IProductGroupService;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import com.lds.oneplanning.mps.entity.MpsFormInfo;
import com.lds.oneplanning.mps.entity.MpsWeekPlan;
import com.lds.oneplanning.mps.entity.MpsWeekPlanExt;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.model.RowSaveData;
import com.lds.oneplanning.mps.service.IMpsFormInfoService;
import com.lds.oneplanning.mps.service.IMpsWeekPlanExtService;
import com.lds.oneplanning.mps.service.IMpsWeekPlanService;
import com.lds.oneplanning.mps.service.facade.MpsRowDataFacadeService;
import com.lds.oneplanning.mps.service.impl.MpsWeekPlanManager;
import com.lds.oneplanning.mps.utils.BizTransUtil;
import com.lds.oneplanning.mps.vo.MpsPrePlanQuantityVo;
import com.lds.oneplanning.mps.vo.MpsWeekPlanVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/8 16:53
 */
@Slf4j
@Service
public class MpsRowDataFacadeServiceImpl implements MpsRowDataFacadeService {

    @Resource
    private IMpsFormInfoService mpsFormInfoService;
    @Resource
    private IMpsWeekPlanService weekPlanService;
    @Resource
    private MpsWeekPlanManager weekPlanManager;
    @Resource
    private IEsbDataFetchService dataFetchService;
    @Resource
    private IMpsWeekPlanExtService mpsWeekPlanExtService;
    @Resource
    private IPlannerDataPermissionService plannerDataPermissionService;
    @Resource
    private IProductGroupService productGroupService;

    private static final Integer OFFSET_DAYS = 6;


    @Override
    public List<MpsRowData> listUserApiRowData(Long userId, Date startTime, Date endTime) {
        List<PlannerDataPermission> PlannerDataPermissions = plannerDataPermissionService.listByUserId(userId);
        Set<String> factoryCodes = PlannerDataPermissions.stream().map(PlannerDataPermission::getFactoryCode).collect(Collectors.toSet());
        Set<String> productGroupCodes = PlannerDataPermissions.stream().map(PlannerDataPermission::getProductGroupCode).collect(Collectors.toSet());
        List<ProductGroupDTO> productGroupDTOS = productGroupService.listByCodes(productGroupCodes);
        Map<String, ProductGroupDTO> productGroupDTOMap = productGroupDTOS.stream().collect(Collectors.toMap(ProductGroupDTO::getCode, productGroupDTO -> productGroupDTO, (t, t2) -> t2));

        List<MpsRowData> dirtyList = dataFetchService.fetchOrderList(LocalDateTimeUtil.dateToLocalDate(startTime), LocalDateTimeUtil.dateToLocalDate(endTime), factoryCodes);
        List<MpsRowData> resList = Lists.newArrayList();
        // 内存过滤
        List<Predicate<MpsRowData>> predicateList = null;
        for (PlannerDataPermission dataPermission : PlannerDataPermissions) {
            String factoryCode = dataPermission.getFactoryCode();
            //   增加产品组过滤
            String productGroupCode = dataPermission.getProductGroupCode();
            // 商品id或者产品id
            String commodityId = dataPermission.getCommodityId();
            predicateList = Lists.newArrayList();
            // 默认过滤器
            predicateList.add(mpsRowData -> factoryCode.equals(mpsRowData.getFactory()));
            if (StringUtils.isNotBlank(productGroupCode) ) {
                // 产品组配置  商品id没有配置  10
                ProductGroupDTO groupDTO = productGroupDTOMap.get(productGroupCode);
                predicateList.add(mpsRowData -> groupDTO.getProductList().contains(mpsRowData.getProductId()));
            }
            if (StringUtils.isNotBlank(commodityId)) {
                // 产品组没有配置，商品产品id有配置 01  注意是过滤商品id和产品id
                predicateList.add(mpsRowData -> commodityId.equals(mpsRowData.getCommodityId()) || commodityId.equals(mpsRowData.getProductId()));
            }
            resList.addAll(this.filterRowData(dirtyList,predicateList));
        }
        return resList;
    }
    private List<MpsRowData> filterRowData( List<MpsRowData> dirtyList,List<Predicate<MpsRowData>> predicateList){
        Stream<MpsRowData> stream = dirtyList.stream();
        for (Predicate<MpsRowData> predicate: predicateList){
            stream = stream.filter(predicate);
        }
        return stream.collect(Collectors.toList());
    }
    @Override
    public void fullFillWeekPlanQty(List<MpsRowData> mpsRowDatas) {
        List<String> bizIds = mpsRowDatas.stream().map(MpsRowData::getOrderNo).collect(Collectors.toList());
        if (bizIds.isEmpty()) {
            return;
        }
        Map<String, List<MpsWeekPlanVo>> weekDataMap = weekPlanManager.listByBizIds(bizIds,new Date(), 12);
        Map<String,MpsFormInfo> fromMaps = mpsFormInfoService.listByBizIds(bizIds).stream().collect(Collectors.toMap(MpsFormInfo::getBizId,mpsFormInfo -> mpsFormInfo,(t, t2) -> t2));
        List<MpsWeekPlanExt> planExts = mpsWeekPlanExtService.listByBizIds(bizIds);
        Map<String, Integer> frozenMap = planExts.stream().collect(Collectors.toMap(MpsWeekPlanExt::getBizId,MpsWeekPlanExt::getFrozenStatus,(bizId, bizId2) -> bizId2));
        Map<String, LocalDate> dateMap = planExts.stream().collect(Collectors.toMap(MpsWeekPlanExt::getBizId,MpsWeekPlanExt::getScheduleDate,(bizId, bizId2) -> bizId2));
        for (MpsRowData rowData :mpsRowDatas){
            String bizId   = rowData.getOrderNo();
            Integer frozenStatus = frozenMap.get(bizId);
            frozenStatus = frozenStatus!=null && frozenStatus ==1 ? 1 : 0;
            LocalDate _startProductPeriod = dateMap.get(bizId);
            // 冻结状态处理
            rowData.set_frozenStatus(frozenStatus);
            rowData.set_startProductPeriod(_startProductPeriod);
            // 自定义的字段进行覆盖
            MpsFormInfo formInfo = fromMaps.get(bizId);
            if (formInfo !=null) {
                rowData.setReportedPcsQty(formInfo.getReportQty() != null ? formInfo.getReportQty() : 0);
                rowData.setSchedulePcsQty(((BigDecimal) rowData.getOrderPcsQty()).subtract(new BigDecimal(rowData.getReportedPcsQty().intValue())));
                rowData.setOriginalReplyTime(formInfo.getOldReplyCompletionDate());
                rowData.setLatestReplyTime(formInfo.getNewReplyCompletionDate());
                rowData.setRemark1(formInfo.getRemarkOne());
                rowData.setRemark2(formInfo.getRemarkTwo());
                rowData.setFinalAssembly(formInfo.getPackagingOrFinalAssembly());
                rowData.setRiskMaterialRemark(formInfo.getRemarkHazardousInfo());
                rowData.setCustomerSeq(formInfo.getGuestSeq());
            }

            // 排产数量进行覆盖
            if (weekDataMap.containsKey(bizId)) {
                List<MpsWeekPlanVo> mwp = weekDataMap.get(bizId);
                if (CollectionUtils.isNotEmpty(mwp)) {
                    // 小的排前面 一个接一个
                    mwp.sort(Comparator.comparing(MpsWeekPlanVo::getNatureWeek));
                    rowData.setUpperHalfWeek1(mwp.get(0).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek1(mwp.get(0).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek2(mwp.get(1).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek2(mwp.get(1).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek3(mwp.get(2).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek3(mwp.get(2).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek4(mwp.get(3).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek4(mwp.get(3).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek5(mwp.get(4).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek5(mwp.get(4).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek6(mwp.get(5).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek6(mwp.get(5).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek7(mwp.get(6).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek7(mwp.get(6).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek8(mwp.get(7).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek8(mwp.get(7).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek9(mwp.get(8).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek9(mwp.get(8).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek10(mwp.get(9).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek10(mwp.get(9).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek11(mwp.get(10).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek11(mwp.get(10).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setUpperHalfWeek12(mwp.get(11).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    rowData.setLowerHalfWeek12(mwp.get(11).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    if (mwp.size()>12) {
                        // 目前接口有bug，待修复完之后去除这个判断
                        rowData.setUpperHalfWeek13(mwp.get(12).getPrePlanQuantityVos().stream().filter(vo -> 0== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                        rowData.setLowerHalfWeek13(mwp.get(12).getPrePlanQuantityVos().stream().filter(vo -> 1== vo.getWeekType()).findAny().orElse(new MpsPrePlanQuantityVo()).getPrePlanQuantity());
                    }
                }
            }
        }
    }

    @Override
    public void sortMpsRowData(List<MpsRowData> mpsRowDatas) {
        List<String> bizIds = mpsRowDatas.stream().map(MpsRowData::getOrderNo).collect(Collectors.toList());
        if (bizIds.isEmpty()) {
            return;
        }
        List<MpsWeekPlanExt> planExts = mpsWeekPlanExtService.listByBizIds(bizIds);
        Map<String, LocalDate> dateSortMap = planExts.stream().collect(Collectors.toMap(MpsWeekPlanExt::getBizId,MpsWeekPlanExt::getScheduleDate,(bizId, bizId2) -> bizId2));
        Map<String, Long> seqSortMap = planExts.stream().collect(Collectors.toMap(MpsWeekPlanExt::getBizId,MpsWeekPlanExt::getScheduleSeq,(bizId, bizId2) -> bizId2));
        mpsRowDatas.stream().sorted((o1, o2) -> {
            // 排序规则 https://confluence.leedarson.com/pages/viewpage.action?pageId=178296687
            // 计算完工日期
            Date finishTime1 = o1.getEstFinishTime() !=null ? o1.getEstFinishTime() : o1.getOriginalOnlineTime();
            Date finishTime2 = o2.getEstFinishTime() !=null ? o2.getEstFinishTime() : o2.getOriginalOnlineTime();
            // 上线日期 = 完工日期-6天
            LocalDate onlineTime1 =  LocalDateTimeUtil.dateToLocalDate(finishTime1).minusDays(OFFSET_DAYS);
            LocalDate onlineTime2 = LocalDateTimeUtil.dateToLocalDate(finishTime2).minusDays(OFFSET_DAYS);
            if ( dateSortMap.get(o1.getOrderNo())!=null) {
                // 库表有保存 则替换
                onlineTime1 = dateSortMap.get(o1.getOrderNo());
            }
            if (dateSortMap.get(o2.getOrderNo())!=null) {
                // 库表有保存 则替换
                onlineTime2 = dateSortMap.get(o2.getOrderNo());
            }
            if (onlineTime1.isEqual(onlineTime2)) {
                // 日期相同 则比较顺序
                Long seq1 = seqSortMap.get(o1.getOrderNo());
                Long seq2 = seqSortMap.get(o2.getOrderNo());
                if (seq1!=null && seq2!=null) {
                    return  seq1.compareTo(seq2);
                }else if(seq2 ==null){
                    // 没有序号排最后
                    return -1;
                } else if (seq1 == null) {
                    return 1;
                }else {
                    // 都没有 用上线日期比较
                    return finishTime1.compareTo(finishTime2);
                }
            }else {
                return onlineTime1.compareTo(onlineTime2);
            }
        }) ;

        // 顺便设置截止时间
        for (MpsRowData mpsRowData : mpsRowDatas){
            Date finishTime1 = mpsRowData.getEstFinishTime() !=null ? mpsRowData.getEstFinishTime() : mpsRowData.getOriginalOnlineTime();
            LocalDate onlineTime1 =  LocalDateTimeUtil.dateToLocalDate(finishTime1).minusDays(OFFSET_DAYS);
            if ( dateSortMap.get(mpsRowData.getOrderNo())!=null) {
                // 库表有保存 则替换
                onlineTime1 = dateSortMap.get(mpsRowData.getOrderNo());
            }
            mpsRowData.set_endProductPeriod(onlineTime1);
        }
    }

    @Override
    public List<MpsRowData> listCompleteRowData(Long userId, Date startTime, Date endTime) {
        List<MpsRowData> mpsRowDatas = this.listUserApiRowData(userId, startTime, endTime);
        this.fullFillWeekPlanQty(mpsRowDatas);
        this.sortMpsRowData(mpsRowDatas);
        return mpsRowDatas;
    }

    @Override
    public void saveRowData(List<RowSaveData> mpsRowDatas, Date date) {
        // save frominfo
        List<MpsFormInfo> mpsFormInfos = BizTransUtil.mpsRowDatasToFormList(mpsRowDatas);
        mpsFormInfoService.saveOrUpdateBatchByBizId(mpsFormInfos);
        // save plandata
        List<MpsWeekPlan> totalMpsWeekPlans = Lists.newArrayList();
        for (RowSaveData mpsRow  : mpsRowDatas){
            // 一行就变成一个list数组
            List<MpsWeekPlan> rowList = BizTransUtil.mpsRowDataToMpsWeekPlanList(mpsRow,date);
            totalMpsWeekPlans.addAll(rowList);
        }
        weekPlanService.batchSaveByBizId(totalMpsWeekPlans);

        List<MpsWeekPlanExt> totalMpsWeekPlanExts = Lists.newArrayList();
        for (RowSaveData mpsRow  : mpsRowDatas){
            // 一行就变成一个list数组
            MpsWeekPlanExt planExt = BizTransUtil.mpsRowDataToMpsWeekPlanExt(mpsRow,date);
            totalMpsWeekPlanExts.add(planExt);
        }
        mpsWeekPlanExtService.batchSaveByBizId(totalMpsWeekPlanExts);
    }
}
