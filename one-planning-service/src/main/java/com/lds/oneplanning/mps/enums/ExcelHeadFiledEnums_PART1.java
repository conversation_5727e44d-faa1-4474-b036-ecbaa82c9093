package com.lds.oneplanning.mps.enums;

/**
 * 表头枚举
 *
 */
public enum ExcelHeadFiledEnums_PART1 {
    mainPlan("mainPlan",ExcelDataTypeEnum.STRING, "主计划"),
    dept("dept",ExcelDataTypeEnum.STRING, "部门（车间）"),
    lineCategoryCode("lineCategoryCode",ExcelDataTypeEnum.STRING, "产线类型"),
    customerGroup("customerGroup",ExcelDataTypeEnum.STRING,"客户组"),
    customerCode("customerCode",ExcelDataTypeEnum.STRING,"客户代码"),
    sellOrderNo("sellOrderNo",ExcelDataTypeEnum.STRING,"销售订单号"),
    rowItem("rowItem",ExcelDataTypeEnum.STRING,"行项目"),
    omRespons("omRespons",ExcelDataTypeEnum.STRING,"OM负责人"),
    factory("factory",ExcelDataTypeEnum.STRING,"生产工厂"),
    orderType("orderType",ExcelDataTypeEnum.STRING,"订单类型"),
    orderNo("orderNo",ExcelDataTypeEnum.STRING,"订单号"),
    outDeliveryNo("outDeliveryNo",ExcelDataTypeEnum.STRING,"外向交货单"),
    outRowItem("outRowItem",ExcelDataTypeEnum.STRING,"外向行项目"),
    commodityId("commodityId",ExcelDataTypeEnum.STRING,"商品ID"),
    commodityDesc("commodityDesc",ExcelDataTypeEnum.STRING,"商品描述"),
    crossPlantMaterialStatus("crossPlantMaterialStatus",ExcelDataTypeEnum.STRING,"跨工厂物料状态"),
    productLineLevel3("productLineLevel3",ExcelDataTypeEnum.STRING,"三级产品线"),
    productLineLevel5("productLineLevel5",ExcelDataTypeEnum.STRING,"五级产品线"),
    category("category",ExcelDataTypeEnum.STRING,"品类（大）"),
    machineModel("machineModel",ExcelDataTypeEnum.STRING,"机型（中）"),
    specification("specification",ExcelDataTypeEnum.STRING,"规格（小）"),
    productId("productId",ExcelDataTypeEnum.STRING,"产品ID"),
    productDesc("productDesc",ExcelDataTypeEnum.STRING,"产品描述"),
    orderUnitQty("orderUnitQty",ExcelDataTypeEnum.NUMBER,"订单数量（套数）"),
    orderPcsQty("orderPcsQty",ExcelDataTypeEnum.NUMBER,"订单数量（只数）"),
    transQty("transQty",ExcelDataTypeEnum.NUMBER,"转换数量"),
    reportedPcsQty("reportedPcsQty",ExcelDataTypeEnum.NUMBER,"已报工数量（只数）"),
    stockedPcsQty("stockedPcsQty",ExcelDataTypeEnum.NUMBER,"已入库数量（只数）"),
    schedulePcsQty("schedulePcsQty",ExcelDataTypeEnum.NUMBER,"需排产数量（只数）"),
    bizSampleQty("bizSampleQty",ExcelDataTypeEnum.NUMBER,"业务留样数量"),
    qcSampleQty("qcSampleQty",ExcelDataTypeEnum.NUMBER,"品保留样数量"),
    lightComponentId("lightComponentId",ExcelDataTypeEnum.STRING,"光源组件ID"),
    lightDeliveryTime("lightDeliveryTime",ExcelDataTypeEnum.DATE,"光源交期"),
    driverComponentId("driverComponentId",ExcelDataTypeEnum.STRING,"驱动组件ID"),
    driverDeliveryTime("driverDeliveryTime",ExcelDataTypeEnum.DATE,"驱动交期"),
    structDeliveryTime("structDeliveryTime",ExcelDataTypeEnum.DATE,"结构件交期"),
    packageDeliveryTime("packageDeliveryTime",ExcelDataTypeEnum.DATE,"包装交期"),
    stdWorkHours("stdWorkHours",ExcelDataTypeEnum.NUMBER,"标准工时（总装+老化+包装）"),
    packageType("packageType",ExcelDataTypeEnum.STRING,"包装方式"),
    capacityStruct("capacityStruct",ExcelDataTypeEnum.STRING,"产能结构"),
    onlineTime("onlineTime",ExcelDataTypeEnum.DATE,"上线日期"),
    originalOnlineTime("originalOnlineTime",ExcelDataTypeEnum.DATE,"原始完工日期"),
    isInspect("isInspect",ExcelDataTypeEnum.STRING,"是否验货"),
    originalInspectTime("originalInspectTime",ExcelDataTypeEnum.DATE,"原始计划验货日期"),
    latestInspectTime("latestInspectTime",ExcelDataTypeEnum.DATE,"最新计划验货日期"),
    originalLoadTime("originalLoadTime",ExcelDataTypeEnum.DATE,"原始计划装柜日期"),
    latestLoadTime("latestLoadTime",ExcelDataTypeEnum.DATE,"最新计划装柜日期"),
    originalShipTime("originalShipTime",ExcelDataTypeEnum.DATE,"原始船期"),
    finalShipTime("finalShipTime",ExcelDataTypeEnum.DATE,"最终船期"),
    estFinishTime("estFinishTime",ExcelDataTypeEnum.DATE,"推估需求完工日期"),
    productStartTime("productStartTime",ExcelDataTypeEnum.DATE,"生产开始日期（组装/上线）"),
    productEndTime("productEndTime",ExcelDataTypeEnum.DATE,"生产结束日期（包装）"),
    originalReplyTime("originalReplyTime",ExcelDataTypeEnum.DATE,"原回复完工日期",true),
    latestReplyTime("latestReplyTime",ExcelDataTypeEnum.DATE,"新回复完工日期",true),
    remark1("remark1",ExcelDataTypeEnum.STRING,"备注1",true),
    remark2("remark2",ExcelDataTypeEnum.STRING,"备注2",true);


    private String code;
    private String dataType;
    private String desc;
    private boolean editAble;

    private ExcelHeadFiledEnums_PART1(String code, ExcelDataTypeEnum dataTypeEnum,String desc) {
        this.code = code;
        this.dataType = dataTypeEnum.getCode();
        this.desc = desc;
        this.editAble = false;
    }

    private ExcelHeadFiledEnums_PART1(String code,ExcelDataTypeEnum dataTypeEnum, String desc, boolean editAble) {
        this.code = code;
        this.dataType = dataTypeEnum.getCode();
        this.desc = desc;
        this.editAble = editAble;
    }


    public String getCode() {
        return code;
    }

    public String getDataType() {
        return dataType;
    }
    public String getDesc() {
        return desc;
    }

    public boolean isEditAble() {
        return editAble;
    }

    public static ExcelHeadFiledEnums_PART1 getByCode(String code){
        for (ExcelHeadFiledEnums_PART1 enums : ExcelHeadFiledEnums_PART1.values()){
            if (enums.code.equals(code)) {
              return enums;
            }
        }
        return null;
    }
    public static String getDescByCode(String code){
        ExcelHeadFiledEnums_PART1 enumsPart1 = ExcelHeadFiledEnums_PART1.getByCode(code);
        return enumsPart1==null ? null : enumsPart1.getCode();
    }
}
