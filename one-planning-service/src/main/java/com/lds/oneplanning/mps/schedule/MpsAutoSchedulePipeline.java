package com.lds.oneplanning.mps.schedule;

import com.google.common.collect.Lists;
import com.lds.oneplanning.mps.schedule.handlers.IMpsAutoScheduleHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

@Component
public class MpsAutoSchedulePipeline {

    private final List<IMpsAutoScheduleHandler> handlers = Lists.newArrayList();

    @Autowired
    public MpsAutoSchedulePipeline(List<IMpsAutoScheduleHandler> pipelineHandlers) {
        handlers.addAll(pipelineHandlers);
        handlers.sort(Comparator.comparingInt(IMpsAutoScheduleHandler::getOrder));
    }

    public void execute(MpsAutoScheduleContext context) {
        for (IMpsAutoScheduleHandler handler : handlers) {
            handler.execute(context);
        }
    }
}
