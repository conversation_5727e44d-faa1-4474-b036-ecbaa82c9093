package com.lds.oneplanning.mps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.esb.datafetch.service.IEsbMesDataFetchService;
import com.lds.oneplanning.mps.entity.MpsFormInfo;
import com.lds.oneplanning.mps.mapper.MpsFormInfoMapper;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.service.IMpsFormInfoService;
import com.lds.oneplanning.mps.service.facade.MpsRowDataFacadeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Slf4j
@Service
public class MpsFormInfoServiceImpl extends ServiceImpl<MpsFormInfoMapper, MpsFormInfo> implements IMpsFormInfoService {

    @Resource
    private IEsbMesDataFetchService esbMesDataFetchService;
    @Resource
    private MpsRowDataFacadeService mpsRowDataFacadeService;

    @Override
    public MpsFormInfo getByBizId(String bizId) {
        LambdaQueryWrapper<MpsFormInfo> queryWrapper = Wrappers.<MpsFormInfo>lambdaQuery()
                .eq(MpsFormInfo::getBizId,bizId
                ).last(" limit 1");
           return baseMapper.selectOne(queryWrapper);
    }
    @Override
    public List<MpsFormInfo> listByBizIds(List<String> bizIds) {
        if (bizIds == null || bizIds.isEmpty()) {
            return Lists.newArrayList();
        }
        return this.list(new QueryWrapper<MpsFormInfo>().lambda().in(MpsFormInfo::getBizId, bizIds));
    }

    @Override
    public Long saveOrUpdateByBizId(MpsFormInfo entity) {
        LambdaQueryWrapper<MpsFormInfo> queryWrapper = Wrappers.<MpsFormInfo>lambdaQuery()
                .eq(MpsFormInfo::getBizId,entity.getBizId()
                ).last(" limit 1");
        MpsFormInfo mpsFormInfo = baseMapper.selectOne(queryWrapper);
        Long id =null;
        if (mpsFormInfo == null) {
             baseMapper.insert(entity);
             id=  entity.getId();
        }else{
            entity.setId(mpsFormInfo.getId());
            baseMapper.updateById(entity);
        }
        return id;
    }

    @Override
    public void saveOrUpdateBatchByBizId(List<MpsFormInfo> mpsFormInfos) {
        List<String> bizIds = mpsFormInfos.stream().map(MpsFormInfo::getBizId).distinct().collect(Collectors.toList());
        List<MpsFormInfo> existList = baseMapper.selectList(Wrappers.<MpsFormInfo>lambdaQuery().in(MpsFormInfo::getBizId, bizIds));
        Map<String,Long> updateBizMap = existList.stream().collect(Collectors.toMap(MpsFormInfo::getBizId, MpsFormInfo::getId,(aLong, aLong2) -> aLong2));
        List<MpsFormInfo> insertList = Lists.newArrayList();
        List<MpsFormInfo> updateList = Lists.newArrayList();
        for (MpsFormInfo formInfo : mpsFormInfos){
            if (updateBizMap.containsKey(formInfo.getBizId())) {
                formInfo.setId(updateBizMap.get(formInfo.getBizId()));
                formInfo.setUpdateTime(new Date());
                updateList.add(formInfo);
            }else{
                insertList.add(formInfo);
            }
        }

        if (!insertList.isEmpty()) {
            this.saveBatch(insertList);
        }
        if (!updateList.isEmpty()) {
            this.updateBatchById(updateList);
        }

    }

    @Override
    public void updateReportedByUserId(Long userId) {
        try{
            //取出用户有权限的订单数据
            Date startTime = LocalDateTimeUtil.localDateToDate(LocalDate.now().minusDays(30));
            Date endTime =  new Date();
            List<MpsRowData> rowDataList = mpsRowDataFacadeService.listUserApiRowData(userId, startTime, endTime);
            if(CollectionUtils.isEmpty(rowDataList)){
                return;
            }
            //取出订单ID列表
            List<String> bizIds = rowDataList.stream().map(MpsRowData::getOrderNo).distinct().collect(Collectors.toList());
            List<MpsFormInfo> mpsFormInfos = baseMapper.selectList(Wrappers.<MpsFormInfo>lambdaQuery().in(MpsFormInfo::getBizId, bizIds));
            updateReportedByBizData(mpsFormInfos);
        }catch (Exception e){
            log.error("更新报工数量失败",e);
        }
    }

    /**
     * 根据订单信息更新报工数量
     * @param mpsFormInfos
     */
    public void updateReportedByBizData(List<MpsFormInfo> mpsFormInfos) {
            //过滤出计划订单数据
            mpsFormInfos = mpsFormInfos.stream().filter(vo -> "生产订单".equals(vo.getOrderType())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(mpsFormInfos)){
                return;
            }
            //取出未完工的订单（需排产数量>0）
            List<String> unfinishedBizIds = Lists.newArrayList();
            for (MpsFormInfo vo : mpsFormInfos) {
                //订单数量
                Integer orderQty = vo.getOrderPcsQty();
                if(orderQty == null){
                    orderQty = 0;
                }
                //报工数量
                Integer reportedQty = vo.getReportQty();
                if(reportedQty == null){
                    reportedQty = 0;
                }
                if (orderQty - reportedQty > 0) {
                    unfinishedBizIds.add(vo.getBizId());
                }
            }
            if(CollectionUtils.isEmpty(unfinishedBizIds)){
                log.info("没有未完工的订单");
                return;
            }
            // 获取订单报工数量
            Map<String, Integer> finishMap = esbMesDataFetchService.fetchOrderCompletList(unfinishedBizIds);
            log.info("未完工订单：{},订单报工数量：{}", unfinishedBizIds, finishMap);
            //更新订单报工数量
            List<MpsFormInfo> updateList = Lists.newArrayList();
            for (MpsFormInfo vo : mpsFormInfos) {
                if(unfinishedBizIds.contains(vo.getBizId()) && finishMap.containsKey(vo.getBizId())){
                    vo.setReportQty(finishMap.get(vo.getBizId()));
                    updateList.add(vo);
                }
            }
            //批量更新
            if(!CollectionUtils.isEmpty(updateList)){
                updateBatchById(updateList);
            }
    }

    @Override
    public void synReportedQtyData() {
        try{
            //取出全部订单
            List<MpsFormInfo> mpsFormInfos = this.list();
            if(CollectionUtils.isNotEmpty(mpsFormInfos)){
                updateReportedByBizData(mpsFormInfos);
            }
        }catch (Exception e){
            log.error("定时更新报工数量失败",e);
        }
    }
}
