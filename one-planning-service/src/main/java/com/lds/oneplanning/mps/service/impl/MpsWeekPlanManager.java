package com.lds.oneplanning.mps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iot.common.beans.BeanUtil;
import com.lds.oneplanning.mps.entity.MpsWeekPlan;
import com.lds.oneplanning.mps.service.IMpsWeekPlanService;
import com.lds.oneplanning.mps.utils.MpsDateUtil;
import com.lds.oneplanning.mps.vo.MpsPrePlanQuantityVo;
import com.lds.oneplanning.mps.vo.MpsWeekPlanVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 排产管理服务实现类
 *
 * <AUTHOR>
 */
@Component
public class MpsWeekPlanManager {

    @Autowired
    private IMpsWeekPlanService mpsWeekPlanService;


    /**
     * 批量查询排产计划
     *
     * @param bizIds      业务id列表
     * @param currentDate 当前日期
     * @param weeksToPush 往后推的周数
     * @return
     */
    public Map<String, List<MpsWeekPlanVo>> listByBizIds(List<String> bizIds, Date currentDate, int weeksToPush) {
        Map<String, List<MpsWeekPlanVo>> mpsWeekPlanVoMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(bizIds)) {
            return mpsWeekPlanVoMap;
        }
        Map<Integer, List<Integer>> yearNatureWeekMap = MpsDateUtil.getAllNatureWeeksFromCurrentDate(currentDate, weeksToPush);
        if (MapUtils.isEmpty(yearNatureWeekMap)) {
            return mpsWeekPlanVoMap;
        }
        LambdaQueryWrapper<MpsWeekPlan> queryWrapper = Wrappers.<MpsWeekPlan>lambdaQuery().in(MpsWeekPlan::getBizId, bizIds);
        queryWrapper.and(wrapper -> yearNatureWeekMap.forEach((year, natureWeeks) ->
                wrapper.or(subWrapper -> subWrapper.eq(MpsWeekPlan::getCurrentYear, year)
                        .in(MpsWeekPlan::getNatureWeek, natureWeeks))));
        // 按年份和自然周数查询排产计划
        List<MpsWeekPlan> mpsWeekPlans = this.mpsWeekPlanService.list(queryWrapper);
        if (CollectionUtils.isEmpty(mpsWeekPlans)) {
            return mpsWeekPlanVoMap;
        }
        // 按bizId分组
        Map<String, List<MpsWeekPlan>> mpsWeekPlanMap = mpsWeekPlans.stream().collect(Collectors.groupingBy(MpsWeekPlan::getBizId));
        // 转换为vo
        for (Map.Entry<String, List<MpsWeekPlan>> entry : mpsWeekPlanMap.entrySet()) {
            String bizId = entry.getKey();
            List<MpsWeekPlan> subMpsWeekPlans = entry.getValue();
            // 处理单个bizId下的mpsWeekPlan
            mpsWeekPlanVoMap.put(bizId, buildMpsWeekPlanVos(subMpsWeekPlans));
        }
        return mpsWeekPlanVoMap;
    }

    private List<MpsWeekPlanVo> buildMpsWeekPlanVos(List<MpsWeekPlan> subMpsWeekPlans) {
        // 按自然周分组
        Map<Integer, List<MpsWeekPlan>> natureWeekMap = subMpsWeekPlans.stream().collect(Collectors.groupingBy(MpsWeekPlan::getNatureWeek));
        List<MpsWeekPlanVo> mpsWeekPlanVos = Lists.newArrayList();
        for (Map.Entry<Integer, List<MpsWeekPlan>> weekEntry : natureWeekMap.entrySet()) {
            List<MpsWeekPlan> subSubMpsWeekPlans = weekEntry.getValue();
            if (CollectionUtils.isEmpty(subSubMpsWeekPlans)) {
                continue;
            }
            mpsWeekPlanVos.add(buidMpsWeekPlanVo(weekEntry.getKey(), subSubMpsWeekPlans));
        }
        return mpsWeekPlanVos;
    }

    private MpsWeekPlanVo buidMpsWeekPlanVo(Integer natureWeek, List<MpsWeekPlan> subSubMpsWeekPlans) {
        MpsWeekPlanVo mpsWeekPlanVo = new MpsWeekPlanVo();
        mpsWeekPlanVo.setNatureWeek(natureWeek);
        // 按上/下半周排序
        subSubMpsWeekPlans.sort(Comparator.comparing(MpsWeekPlan::getWeekType));
        List<MpsPrePlanQuantityVo> mpsPrePlanQuantityVos = subSubMpsWeekPlans.stream().map(mpsWeekPlan -> {
            MpsPrePlanQuantityVo mpsPrePlanQuantityVo = new MpsPrePlanQuantityVo();
            BeanUtil.copyProperties(mpsWeekPlan, mpsPrePlanQuantityVo);
            return mpsPrePlanQuantityVo;
        }).collect(Collectors.toList());
        mpsWeekPlanVo.setPrePlanQuantityVos(mpsPrePlanQuantityVos);
        return mpsWeekPlanVo;
    }
}
