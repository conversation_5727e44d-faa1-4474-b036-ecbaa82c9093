package com.lds.oneplanning.mps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MpsWeekPlanExt对象", description="")
public class MpsWeekPlanExt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "业务编码")
    private String bizId;

    @ApiModelProperty(value = "具体排产日期取第一天：2025-02-01")
    private LocalDate scheduleDate;

    @ApiModelProperty(value = "排产年份如：2025")
    private Integer scheduleYear;

    @ApiModelProperty(value = "开始排产自然周：如6,7")
    private Integer scheduleWeek;

    @ApiModelProperty(value = "0上半周 1下班周")
    private Integer weekType;

    @ApiModelProperty(value = "2025021200001 年月日+5位数")
    private Long scheduleSeq;

    @ApiModelProperty(value = "产线列表编码")
    private String lineCategoryCode;

    @ApiModelProperty(value = "产线编码")
    private String lineCode;

    @ApiModelProperty(value = "是否冻结产能：0否1是，默认是0")
    private Integer frozenStatus;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
