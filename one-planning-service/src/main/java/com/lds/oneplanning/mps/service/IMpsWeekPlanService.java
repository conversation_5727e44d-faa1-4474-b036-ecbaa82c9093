package com.lds.oneplanning.mps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.mps.entity.MpsWeekPlan;
import com.lds.oneplanning.mps.vo.MpsWeekPlanVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
public interface IMpsWeekPlanService extends IService<MpsWeekPlan> {
    void batchSaveByBizId(List<MpsWeekPlan> sourceList);
    Map<String, List<MpsWeekPlanVo>> listByBizIds(List<String> bizIds);
    Map<String, List<MpsWeekPlan>> groupByBizId(List<String> bizIds);



}