package com.lds.oneplanning.mps.schedule;

import com.google.common.collect.Maps;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.schedule.model.MpsProductionLine;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class MpsAutoScheduleContext implements Serializable {

    private static final long serialVersionUID = 4209152096432085847L;

    private Long userId;

    private Date currentDate;

    private int weeksToPush;

    /**
     * 订单数据
     */
    private Map<String, MpsRowData> orderMap;

    /**
     * 客户拥有的生产线类code列表
     */
    private List<String> lineCategoryCodes;

    /**
     * 生产线类数据
     * key: 生产线类code
     * value: <排产周期Key，排产数据>
     */
    private Map<String, Map<String, MpsProductionLine>> halfWeekProductionLineMap;

    /**
     * 订单->生产线类code->uph映射
     */
    private Map<String, Map<String, Float>> orderLineCategoryUphMap;


    public MpsAutoScheduleContext(Map<String, MpsRowData> orderMap) {
        this.orderMap = orderMap;
        this.halfWeekProductionLineMap = Maps.newHashMap();
    }
}