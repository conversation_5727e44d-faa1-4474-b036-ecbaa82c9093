package com.lds.oneplanning.mps.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuang<PERSON><PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025/2/7 17:00
 */
@ApiModel(value="excel行对象", description="excel行对象")
@Data
public class MpsRowData {
    @ApiModelProperty(value = "主计划")
    private String mainPlan;
    @ApiModelProperty(value = "部门")
    private String dept;
    @ApiModelProperty(value = "产线类别")
    private String lineCategoryCode;
    @ApiModelProperty(value = "客户组-订单接口-LOCCO")
    private String customerGroup;
    @ApiModelProperty(value = "客户代码-订单接口SORTL")
    private String customerCode;
    @ApiModelProperty(value = "销售订单号-订单接口-VBELN")
    private String sellOrderNo;
    @ApiModelProperty(value = "行项目-订单接口-订单接口-POSNR")
    private String rowItem;
    @ApiModelProperty(value = "om负责人-评审接口")
    private String omRespons;
    @ApiModelProperty(value = "生产工厂（编码）-订单接口-WERKS")
    private String factory;
    @ApiModelProperty(value = "订单类型-订单接口-ZTYPE")
    private String orderType;
    @ApiModelProperty(value = "订单号-订单接口-ZDDH")
    private String orderNo;
    @ApiModelProperty(value = "外向交货单-订单接口-未对应")
    private String outDeliveryNo;
    @ApiModelProperty(value = "外向行项目-订单接口-未对应")
    private String outRowItem;
    @ApiModelProperty(value = "商品id-订单接口-ZSPID")
    private String commodityId;
    @ApiModelProperty(value = "商品描述-订单接口-未对应")
    private String commodityDesc;
    @ApiModelProperty(value = "跨工厂物料状态")
    private String crossPlantMaterialStatus;
    @ApiModelProperty(value = "三级产品线")
    private String productLineLevel3;
    @ApiModelProperty(value = "五级产品线")
    private String productLineLevel5;
    @ApiModelProperty(value = "品类（大）")
    private String category;
    @ApiModelProperty(value = "机型（中）")
    private String machineModel;
    @ApiModelProperty(value = "规格（小）")
    private String specification;
    @ApiModelProperty(value = "产品id")
    private String productId;
    @ApiModelProperty(value = "产品描述")
    private String productDesc;
    @ApiModelProperty(value = "订单数量（套数）")
    private Number orderUnitQty;
    @ApiModelProperty(value = "订单数量（只数）")
    private Number orderPcsQty;
    @ApiModelProperty(value = "转化数量")
    private Number transQty;
    @ApiModelProperty(value = "已报工数量（只数）")
    private Number reportedPcsQty;
    @ApiModelProperty(value = "已入库数量（只数）")
    private Number stockedPcsQty;
    @ApiModelProperty(value = "需排产数量（只数）")
    private Number schedulePcsQty;
    @ApiModelProperty(value = "业务留样数量")
    private Number bizSampleQty;
    @ApiModelProperty(value = "品保留样数量")
    private Integer qcSampleQty;
    @ApiModelProperty(value = "光源组件id")
    private String lightComponentId;
    @ApiModelProperty(value = "光源交期")
    private Date lightDeliveryTime;
    @ApiModelProperty(value = "驱动组件id")
    private String driverComponentId;
    @ApiModelProperty(value = "驱动交期")
    private Date driverDeliveryTime;
    @ApiModelProperty(value = "结构件交期")
    private Date structDeliveryTime;
    @ApiModelProperty(value = "包装交期")
    private Date packageDeliveryTime;
    @ApiModelProperty(value = "标准工时（总装+老化+包装）")
    private String stdWorkHours;
    @ApiModelProperty(value = "包装方式")
    private String packageType;
    @ApiModelProperty(value = "产能结构")
    private String capacityStruct;
    @ApiModelProperty(value = "上线日期")
    private Date onlineTime;
    @ApiModelProperty(value = "原始完工日期")
    private Date originalOnlineTime;
    @ApiModelProperty(value = "是否验货")
    private String isInspect;
    @ApiModelProperty(value = "原始计划验货日期")
    private Date originalInspectTime;
    @ApiModelProperty(value = "最新计划验货日期")
    private Date latestInspectTime;
    @ApiModelProperty(value = "原始计划装柜日期")
    private Date originalLoadTime;
    @ApiModelProperty(value = "最新计划装柜日期")
    private Date latestLoadTime;
    @ApiModelProperty(value = "原始船期")
    private Date originalShipTime;
    @ApiModelProperty(value = "最终船期")
    private Date finalShipTime;
    @ApiModelProperty(value = "推估需求完工日期")
    private Date estFinishTime;
    @ApiModelProperty(value = "生产开始日期（组装/上线）")
    private Date productStartTime;
    @ApiModelProperty(value = "生产结束日期")
    private Date productEndTime;
    @ApiModelProperty(value = "原回复完工日期")
    private Date originalReplyTime;
    @ApiModelProperty(value = "新回复完工日期")
    private Date latestReplyTime;
    @ApiModelProperty(value = "备注1")
    private String remark1;
    @ApiModelProperty(value = "备注2")
    private String remark2;
    private Integer upperHalfWeek1;
    private Integer lowerHalfWeek1;
    private Integer upperHalfWeek2;
    private Integer lowerHalfWeek2;
    private Integer upperHalfWeek3;
    private Integer lowerHalfWeek3;
    private Integer upperHalfWeek4;
    private Integer lowerHalfWeek4;
    private Integer upperHalfWeek5;
    private Integer lowerHalfWeek5;
    private Integer upperHalfWeek6;
    private Integer lowerHalfWeek6;
    private Integer upperHalfWeek7;
    private Integer lowerHalfWeek7;
    private Integer upperHalfWeek8;
    private Integer lowerHalfWeek8;
    private Integer upperHalfWeek9;
    private Integer lowerHalfWeek9;
    private Integer upperHalfWeek10;
    private Integer lowerHalfWeek10;
    private Integer upperHalfWeek11;
    private Integer lowerHalfWeek11;
    private Integer upperHalfWeek12;
    private Integer lowerHalfWeek12;
    private Integer upperHalfWeek13;
    private Integer lowerHalfWeek13;
    @ApiModelProperty(value = "产品单价")
    private Number productUnitPrice;
    @ApiModelProperty(value = "订单签发完成日期")
    private Date orderSignFinishTime;
    @ApiModelProperty(value = "包装/总装")
    private String finalAssembly;
    @ApiModelProperty(value = "风险物料信息备注")
    private String riskMaterialRemark;
    @ApiModelProperty(value = "虚拟订单号")
    private String virtualOrderNo;
    @ApiModelProperty(value = "虚拟订单项次")
    private String virtualRowItem;
    @ApiModelProperty(value = "首次评审船期")
    private Date firstReviewShipTime;
    @ApiModelProperty(value = "pono")
    private String pono;
    @ApiModelProperty(value = "客户物料编号")
    private String customerMaterialNo;
    @ApiModelProperty(value = "客户顺序")
    private String customerSeq;
    @ApiModelProperty(value = "瓦数")
    private String powerNum;
    @ApiModelProperty(value = "尺寸（方形/原型/规格）")
    private String size;

    // 功能字段 不显示
    // 冻结状态0否 1是 下划线开头，前端不渲染，表示动作属性
    @ApiModelProperty(value = "冻结状态")
    @JSONField(name = "_frozenStatus")
    private Integer _frozenStatus;
    @ApiModelProperty(value = "开始排产日期")
    @JSONField(name = "_startProductPeriod")
    private LocalDate _startProductPeriod;


    @ApiModelProperty(value = "截止排产日期")
    @JSONField(name = "_endProductPeriod")
    private LocalDate _endProductPeriod;

    /**
     * 待排产数量(内存变量)
     */
    @JSONField(serialize = false)
    private int waitingOrderQty;

    /**
     * 是否客户专属产线类(内存变量)
     */
    @JSONField(serialize = false)
    private boolean isCustomerExclusive;

    /**
     * 半周排产数据(内存变量)
     */
    @JSONField(serialize = false)
    private Map<String, Integer> halfWeekScheduleDataMap = Maps.newHashMap();

    @JSONField(serialize = false)
    private String _productGroupCode;

    @JSONField(serialize = false)
    private String _productGroupName;
}