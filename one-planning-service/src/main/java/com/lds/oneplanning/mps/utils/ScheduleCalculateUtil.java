package com.lds.oneplanning.mps.utils;

import com.lds.oneplanning.basedata.entity.LineCapacity;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.wps.model.WpsRowData;
import org.apache.commons.collections.MapUtils;

import java.time.LocalDate;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

public class ScheduleCalculateUtil {

    public static int calculateWpsWaitingOrderQty(WpsRowData order, Integer reportQty) {
        if (null == order || null == order.getOrderPcsQty()) {
            return 0;
        }
        if (null == reportQty) {
            reportQty = 0;
        }
        // 订单数量(只数)
        int orderPcsQty = order.getOrderPcsQty().intValue();
        // 待排产数量
        return orderPcsQty - reportQty;
    }

    /**
     * 计算待排产数量
     *
     * @param order
     * @param reportQty
     * @return
     */
    public static int calculateWaitingOrderQty(MpsRowData order, Integer reportQty) {
        if (null == order || null == order.getOrderPcsQty()) {
            return 0;
        }
        if (null == reportQty) {
            reportQty = 0;
        }
        // 订单数量(只数)
        int orderPcsQty = order.getOrderPcsQty().intValue();
        // 待排产数量
        return orderPcsQty - reportQty;
    }

    /**
     * 计算待排产时长
     *
     * @param scheduleDate 待排产日期
     * @param lineCapacity 配置数据
     * @return
     */
    public static float calculateWaitingOrderHour(LocalDate scheduleDate, LineCapacity lineCapacity) {
        if (null == scheduleDate || null == lineCapacity ||
                null == lineCapacity.getProductHours() || null == lineCapacity.getLoadPercent()) {
            return 0F;
        }
        // 计算待排产时长=生产时长*排产比例)
        float loadPercent = lineCapacity.getLoadPercent() / 100F;
        return lineCapacity.getProductHours() * loadPercent;
    }

    /**
     * 计算待排产时长
     *
     * @param lineCapacityMap
     * @return
     */
    public static float calculateMpsWaitingOrderHour(Map<LocalDate, LineCapacity> lineCapacityMap) {
        if (MapUtils.isEmpty(lineCapacityMap)) {
            return 0F;
        }
        AtomicReference<Float> waitingOrderHour = new AtomicReference<>(0F);
        lineCapacityMap.forEach((localDate, lineCapacity) -> {
            // 待排产时长=SUM(线体类对应日期的生产时长*排产比例)
            waitingOrderHour.updateAndGet(v -> v + calculateWaitingOrderHour(localDate, lineCapacity));
        });
        return waitingOrderHour.get();
    }
}