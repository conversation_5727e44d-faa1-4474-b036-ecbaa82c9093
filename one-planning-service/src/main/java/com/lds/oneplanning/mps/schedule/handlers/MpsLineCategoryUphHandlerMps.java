package com.lds.oneplanning.mps.schedule.handlers;

import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.entity.LineCategoryRule;
import com.lds.oneplanning.basedata.service.ILineCategoryRuleService;
import com.lds.oneplanning.mps.entity.MpsFormInfo;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.schedule.MpsAutoScheduleContext;
import com.lds.oneplanning.mps.service.IMpsFormInfoService;
import com.lds.oneplanning.mps.utils.ScheduleCalculateUtil;
import com.lds.oneplanning.wps.service.facade.IWpsOrderCommonService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 填充订单可匹配的产线类
 *
 * <AUTHOR>
 */
@Component
public class MpsLineCategoryUphHandlerMps implements IMpsAutoScheduleHandler {

    @Autowired
    private IMpsFormInfoService mpsFormInfoService;

    @Autowired
    private IWpsOrderCommonService wpsOrderCommonService;

    @Autowired
    private ILineCategoryRuleService lineCategoryRuleService;

    @Override
    public void execute(MpsAutoScheduleContext context) {
        List<String> lineCategoryCodes = context.getLineCategoryCodes();
        if (CollectionUtils.isEmpty(lineCategoryCodes)) {
            return;
        }
        Map<String, Map<String, Float>> orderLineCategoryUphMap = getOrderLineCategoryUphMap(context, lineCategoryCodes);
        if (MapUtils.isNotEmpty(orderLineCategoryUphMap)) {
            context.setOrderLineCategoryUphMap(orderLineCategoryUphMap);
        }
    }

    @Override
    public int getOrder() {
        return 1;
    }

    /**
     * 获取订单可匹配的产线类
     *
     * @param context
     * @param lineCategoryCodes
     * @return Map<orderNo, Map < lineCategoryCode, uph>>
     */
    private Map<String, Map<String, Float>> getOrderLineCategoryUphMap(MpsAutoScheduleContext context, List<String> lineCategoryCodes) {
        Map<String, Map<String, Float>> orderLineCategoryUphMap = Maps.newHashMap();
        Map<String, MpsRowData> orderMap = context.getOrderMap();
        // 匹配订单和产线类
        for (MpsRowData order : orderMap.values()) {
            String orderNo = order.getOrderNo();
            MpsFormInfo mpsFormInfo = mpsFormInfoService.getByBizId(orderNo);
            if (null == mpsFormInfo) {
                continue;
            }
            // 待排产数量
            int waitingOrderQty = ScheduleCalculateUtil.calculateWaitingOrderQty(order, mpsFormInfo.getReportQty());
            order.setWaitingOrderQty(waitingOrderQty);
            if (waitingOrderQty <= 0) {
                continue;
            }
            Map<String, Float> lineCategoryUphMap = getLineCategoryUphMap(order, lineCategoryCodes);
            if (MapUtils.isEmpty(lineCategoryUphMap)) {
                continue;
            }
            orderLineCategoryUphMap.put(orderNo, lineCategoryUphMap);
        }
        return orderLineCategoryUphMap;
    }

    private Map<String, Float> getLineCategoryUphMap(MpsRowData order, List<String> lineCategoryCodes) {
        String productId = order.getProductId();
        String customerCode = order.getCustomerCode();
        // TODO 修改成产线类查询uph
        Map<String, Float> lineCategoryUphMap = Maps.newHashMap();
        // 判断客户是否有指定专线产线类
        LineCategoryRule mpsLineCategoryRule = lineCategoryRuleService.getByCustomerCodeAndProductId(customerCode, productId);
        if (null != mpsLineCategoryRule) {
            // 当前客户有指定专线产线类，则当前订单排产的产线类只有唯一一个
            String lineCategoryCode = mpsLineCategoryRule.getLineCategoryCode();
            Float uph = lineCategoryUphMap.get(lineCategoryCode);
            lineCategoryUphMap.clear();
            lineCategoryUphMap.put(lineCategoryCode, uph);
            // 设置客户专线产线类
            order.setCustomerExclusive(true);
        }
        return lineCategoryUphMap;
    }
}
