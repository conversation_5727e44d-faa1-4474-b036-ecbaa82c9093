package com.lds.oneplanning.mps.schedule.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 生产线类
 */
@Data
public class MpsProductionLine implements Serializable {

    private static final long serialVersionUID = -3828939045265400241L;

    /**
     * 产线类编码
     */
    private String lineCategoryCode;

    private String name;

    private String workshop;

    private String factoryCode;

    /**
     * 产线类待排产时长：SUM(线体类对应日期的生产时长*排产比例)
     */
    private float waitingScheduleHours;
}