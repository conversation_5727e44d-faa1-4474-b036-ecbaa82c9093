package com.lds.oneplanning.mps.schedule.handlers;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.basedata.constants.BaseDataConstant;
import com.lds.oneplanning.mps.date.HalfWeekMap;
import com.lds.oneplanning.mps.date.WeekMap;
import com.lds.oneplanning.mps.date.YearWeekMap;
import com.lds.oneplanning.basedata.entity.LineCapacity;
import com.lds.oneplanning.mps.enums.WeekTypeEnum;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.schedule.MpsAutoScheduleContext;
import com.lds.oneplanning.mps.schedule.enums.MpsOrderTypeEnum;
import com.lds.oneplanning.mps.schedule.model.MpsProductionLine;
import com.lds.oneplanning.basedata.service.ILineCapacityService;
import com.lds.oneplanning.mps.utils.MpsDateUtil;
import com.lds.oneplanning.mps.utils.ScheduleCalculateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class MpsMpsAutoScheduleOrderHandler implements IMpsAutoScheduleHandler {

    private static final String UPPER_HALF_WEEK = "upperHalfWeek";
    private static final String LOWER_HALF_WEEK = "lowerHalfWeek";

    @Autowired
    private ILineCapacityService lineCapacityService;

    @Override
    public void execute(MpsAutoScheduleContext context) {
        Date currentDate = context.getCurrentDate();
        int weeksToPush = context.getWeeksToPush();
        Map<String, MpsRowData> orderMap = context.getOrderMap();
        if (MapUtils.isEmpty(orderMap)) {
            return;
        }
        if (MapUtils.isEmpty(context.getOrderLineCategoryUphMap())) {
            return;
        }
        this.runWeekSchedule(currentDate, weeksToPush, context);
    }

    /**
     * 运行自然周排产
     *
     * @param currentDate
     * @param weeksToPush
     * @param context
     */
    private void runWeekSchedule(Date currentDate, Integer weeksToPush, MpsAutoScheduleContext context) {
        YearWeekMap yearWeekMap = MpsDateUtil.getAllDatesFromCurrentDate(currentDate, weeksToPush);
        Map<Integer, WeekMap> yearMap = yearWeekMap.getYearMap();
        int cnt = 1;
        for (Map.Entry<Integer, WeekMap> yearEntry : yearMap.entrySet()) {
            WeekMap weekMap = yearEntry.getValue();
            for (Map.Entry<Integer, HalfWeekMap> weekEntry : weekMap.getWeekMap().entrySet()) {
                HalfWeekMap halfWeekMap = weekEntry.getValue();
                for (Map.Entry<Integer, List<Date>> halfWeekEntry : halfWeekMap.getHalfWeekMap().entrySet()) {
                    // 半周期排产
                    String halfWeekKeyPrefix = WeekTypeEnum.FIRST_HALF_WEEK.getCode().equals(halfWeekEntry.getKey()) ? UPPER_HALF_WEEK : LOWER_HALF_WEEK;
                    String halfWeekKey = halfWeekKeyPrefix + cnt;
                    doHalfWeekSchedule(halfWeekKey, halfWeekEntry, context);
                }
                cnt++;
            }
        }
    }

    /**
     * 处理半周排产
     *
     * @param halfWeekKey
     * @param halfWeekEntry
     * @param context
     */
    private void doHalfWeekSchedule(String halfWeekKey, Map.Entry<Integer, List<Date>> halfWeekEntry, MpsAutoScheduleContext context) {
        Map<String, MpsRowData> orderMap = context.getOrderMap();
        List<Date> dates = halfWeekEntry.getValue();
        List<LocalDate> localDates = dates.stream().map(date -> date.toInstant()
                .atZone(MpsDateUtil.ZONE_ID).toLocalDate()).collect(Collectors.toList());
        LocalDate minDate = localDates.get(0);
        // 分类订单
        Map<MpsOrderTypeEnum, List<MpsRowData>> categorizedOrders = categorizeOrders(orderMap, minDate);
        // 处理订单
        categorizedOrders.forEach((type, orders) -> {
            if (CollectionUtils.isNotEmpty(orders)) {
                doOrdersSchedule(halfWeekKey, orders, context, type, localDates);
            }
        });
    }

    private Map<MpsOrderTypeEnum, List<MpsRowData>> categorizeOrders(Map<String, MpsRowData> orderMap, LocalDate minDate) {
        List<MpsRowData> expiredOrders = Lists.newArrayList();
        List<MpsRowData> frozenOrders = Lists.newArrayList();
        List<MpsRowData> customerExclusiveOrders = Lists.newArrayList();
        List<MpsRowData> normalOrders = Lists.newArrayList();

        orderMap.values().forEach(order -> {
            if (order.get_startProductPeriod().isBefore(minDate)) {
                expiredOrders.add(order);
            } else if (Optional.ofNullable(order.get_frozenStatus()).orElse(0) == 1) {
                frozenOrders.add(order);
            } else if (order.isCustomerExclusive()) {
                customerExclusiveOrders.add(order);
            } else {
                normalOrders.add(order);
            }
        });
        Map<MpsOrderTypeEnum, List<MpsRowData>> categorizedOrders = Maps.newHashMap();
        categorizedOrders.put(MpsOrderTypeEnum.EXPIRED, expiredOrders);
        categorizedOrders.put(MpsOrderTypeEnum.FROZEN, frozenOrders);
        categorizedOrders.put(MpsOrderTypeEnum.CUSTOMER_EXCLUSIVE, customerExclusiveOrders);
        categorizedOrders.put(MpsOrderTypeEnum.NORMAL, normalOrders);
        return categorizedOrders;
    }

    private void doOrdersSchedule(String halfWeekKey, List<MpsRowData> orders, MpsAutoScheduleContext context, MpsOrderTypeEnum mpsOrderTypeEnum, List<LocalDate> localDates) {
        Map<String, Map<String, Float>> orderLineCategoryUphMap = context.getOrderLineCategoryUphMap();
        for (MpsRowData order : orders) {
            String orderNo = order.getOrderNo();
            LocalDate startProductPeriod = order.get_startProductPeriod();
            // 正常订单：订单列表已经排过序了，如果发现订单开始生产时间晚于当前时间，则返回
            if (MpsOrderTypeEnum.NORMAL.equals(mpsOrderTypeEnum) && startProductPeriod.isAfter(localDates.get(localDates.size() - 1))) {
                return;
            }
            Optional.ofNullable(orderLineCategoryUphMap.get(orderNo)).ifPresent(lineCategoryUphMap -> {
                lineCategoryUphMap.forEach((lineCategoryCode, uph) -> {
                    // 订单待排产数量
                    int waitingOrderQty = order.getWaitingOrderQty();
                    // 订单待排产时长
                    float waitingOrderHour = (float) waitingOrderQty / uph;
                    Map<String, MpsProductionLine> halfWeekProductionLineMap = getHalfWeekProductionLineMap(context, lineCategoryCode, halfWeekKey, localDates);
                    float waitingLineHour = halfWeekProductionLineMap.get(halfWeekKey).getWaitingScheduleHours();
                    int scheduledQty;
                    if (waitingOrderHour >= waitingLineHour) {
                        scheduledQty = (int) Math.ceil(waitingLineHour * uph);
                        halfWeekProductionLineMap.get(halfWeekKey).setWaitingScheduleHours(0F);
                    } else {
                        scheduledQty = waitingOrderQty;
                        halfWeekProductionLineMap.get(halfWeekKey).setWaitingScheduleHours(waitingLineHour - waitingOrderHour);
                    }
                    order.setWaitingOrderQty(waitingOrderQty - scheduledQty);
                    order.getHalfWeekScheduleDataMap().put(halfWeekKey, scheduledQty);
                });
            });
        }
    }

    private Map<String, MpsProductionLine> getHalfWeekProductionLineMap(MpsAutoScheduleContext context, String lineCategoryCode, String halfWeekKey, List<LocalDate> localDates) {
        Map<String, Map<String, MpsProductionLine>> productionLineMap = context.getHalfWeekProductionLineMap();
        Map<String, MpsProductionLine> halfWeekProductionLineMap = productionLineMap.get(lineCategoryCode);
        if (MapUtils.isEmpty(halfWeekProductionLineMap) || null == halfWeekProductionLineMap.get(halfWeekKey)) {
            // 获取产线类排产时长
            Map<LocalDate, LineCapacity> mpsLineCategoryCapacityMap = lineCapacityService
                    .getByCodeAndDates(BaseDataConstant.CONFIG_TYPE_LINE_CATEGORY, lineCategoryCode,null, localDates);
            // 产线类待排产时长（半周）
            float waitingLineHour = ScheduleCalculateUtil.calculateMpsWaitingOrderHour(mpsLineCategoryCapacityMap);
            MpsProductionLine mpsProductionLine = new MpsProductionLine();
            mpsProductionLine.setLineCategoryCode(lineCategoryCode);
            mpsProductionLine.setWaitingScheduleHours(waitingLineHour);
            halfWeekProductionLineMap = Optional.ofNullable(halfWeekProductionLineMap).orElse(Maps.newHashMap());
            halfWeekProductionLineMap.put(halfWeekKey, mpsProductionLine);
            productionLineMap.put(lineCategoryCode, halfWeekProductionLineMap);
        }
        return halfWeekProductionLineMap;
    }

    @Override
    public int getOrder() {
        return 2;
    }
}
