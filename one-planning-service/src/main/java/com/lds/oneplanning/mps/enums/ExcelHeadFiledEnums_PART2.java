package com.lds.oneplanning.mps.enums;

/**
 * 表头枚举
 *
 */
public enum ExcelHeadFiledEnums_PART2 {
    WK1("WK1","WK1"),
    WK2("WK2","WK2"),
    WK3("WK3","WK3"),
    <PERSON><PERSON>4("WK4","WK4"),
    WK5("WK5","WK5"),
    WK6("WK6","WK6"),
    WK7("WK7","WK7"),
    WK8("WK8","WK8"),
    WK9("WK9","WK9"),
    WK10("WK10","WK10"),
    WK11("WK11","WK11"),
    WK12("WK12","WK12"),
    WK13("WK13","WK13") ;



    private String code;
    private String desc;
    private boolean editAble;

    private ExcelHeadFiledEnums_PART2(String code, String desc) {
        this.code = code;
        this.desc = desc;
        this.editAble = false;
    }

    private ExcelHeadFiledEnums_PART2(String code, String desc, boolean editAble) {
        this.code = code;
        this.desc = desc;
        this.editAble = editAble;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public boolean isEditAble() {
        return editAble;
    }

    public static ExcelHeadFiledEnums_PART2 getByCode(String code){
        for (ExcelHeadFiledEnums_PART2 enums : ExcelHeadFiledEnums_PART2.values()){
            if (enums.code.equals(code)) {
              return enums;
            }
        }
        return null;
    }
    public static String getDescByCode(String code){
        ExcelHeadFiledEnums_PART2 enumsPart1 = ExcelHeadFiledEnums_PART2.getByCode(code);
        return enumsPart1==null ? null : enumsPart1.getCode();
    }
}
