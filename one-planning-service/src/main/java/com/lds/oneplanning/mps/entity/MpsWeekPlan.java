package com.lds.oneplanning.mps.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mps_week_plan")
@ApiModel(value = "WeekPlan对象", description = "")
public class MpsWeekPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "业务id")
    private String bizId;

    @ApiModelProperty(value = "当前年份")
    private Integer currentYear;

    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @ApiModelProperty(value = "截止日期")
    private Date endDate;

    @ApiModelProperty(value = "天数")
    private Integer dayNum;

    @ApiModelProperty(value = "自然周")
    private Integer natureWeek;

    @ApiModelProperty(value = "上/下半周")
    private Integer weekType;

    @ApiModelProperty(value = "预产数量")
    private Integer prePlanQuantity;

    @ApiModelProperty(value = "产线类别编码")
    private String lineCategoryCode;

    @ApiModelProperty(value = "冻结数量")
    private Integer frozenQty;

    @ApiModelProperty(value = "创建者id")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
