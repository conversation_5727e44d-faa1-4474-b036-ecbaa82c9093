package com.lds.oneplanning.mps.model;

import com.google.common.collect.Maps;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/7 17:00
 */
@Data
public class RowSaveData {
//    private String bizId;
    private String factory;
    private String empNo;
    private String orderNo;
    private String lineUuid;
    private String lineCode;
    private String lineCategoryCode;
    private Integer _frozenStatus;
    private Long scheduleSeq;

    private String orderType;
    private Integer orderPcsQty;
    private Integer reportedPcsQty;

    private Date originalReplyTime;
    private Date latestReplyTime;
    private String remark1;
    private String remark2;

    private Map<LocalDate,Number> scheduleDataMap = Maps.newLinkedHashMap();

    private Integer upperHalfWeek1;
    private Integer lowerHalfWeek1;
    private Integer upperHalfWeek2;
    private Integer lowerHalfWeek2;
    private Integer upperHalfWeek3;
    private Integer lowerHalfWeek3;
    private Integer upperHalfWeek4;
    private Integer lowerHalfWeek4;
    private Integer upperHalfWeek5;
    private Integer lowerHalfWeek5;
    private Integer upperHalfWeek6;
    private Integer lowerHalfWeek6;
    private Integer upperHalfWeek7;
    private Integer lowerHalfWeek7;
    private Integer upperHalfWeek8;
    private Integer lowerHalfWeek8;
    private Integer upperHalfWeek9;
    private Integer lowerHalfWeek9;
    private Integer upperHalfWeek10;
    private Integer lowerHalfWeek10;
    private Integer upperHalfWeek11;
    private Integer lowerHalfWeek11;
    private Integer upperHalfWeek12;
    private Integer lowerHalfWeek12;
    private Integer upperHalfWeek13;
    private Integer lowerHalfWeek13;


    private String finalAssembly;
    private String riskMaterialRemark;
    private String customerSeq;
    private String productId;

}
