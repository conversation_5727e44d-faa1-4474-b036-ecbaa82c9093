package com.lds.oneplanning.mps.model;

import com.lds.oneplanning.mps.constans.MpsExcelConstant;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/7 10:10
 */
@Data
public class CellModel {

    private Object value;
    private String code;
    private String dateType;
    private Integer rowStart;
    private Integer rowEnd;
    private Integer columnStart;
    private Integer columnEnd;
    private boolean editAble = false;
    private boolean saveAble = false;


    private List<CellModel> subCells;

    public CellModel() {
    }

    public CellModel(Object value, String code,String dataTypeCode, Integer columnIndex,boolean editAble,boolean saveAble) {
        this.value = value;
        this.code = code;
        this.dateType = dataTypeCode;
        this.rowStart = MpsExcelConstant.HEAD_ROW_START_INDEX;
        this.rowEnd = MpsExcelConstant.HEAD_ROW_END_INDEX;
        this.columnStart = columnIndex;
        this.columnEnd = columnIndex;
        this.editAble = editAble;
        this.saveAble = saveAble;
    }


    public CellModel(Object value, String code,String dataTypeCode, Integer rowStart, Integer rowEnd,
                     Integer columnStart, Integer columnEnd,boolean editAble,boolean saveAble) {
        this.value = value;
        this.code = code;
        this.dateType = dataTypeCode;
        this.rowStart = rowStart;
        this.rowEnd = rowEnd;
        this.columnStart = columnStart;
        this.columnEnd = columnEnd;
        this.editAble = editAble;
        this.saveAble = saveAble;
    }
}
