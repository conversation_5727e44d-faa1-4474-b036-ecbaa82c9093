package com.lds.oneplanning.mps.service.impl;

import com.google.common.collect.Lists;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.mps.constans.MpsExcelConstant;
import com.lds.oneplanning.mps.date.HalfWeekMap;
import com.lds.oneplanning.mps.date.WeekMap;
import com.lds.oneplanning.mps.date.YearWeekMap;
import com.lds.oneplanning.mps.enums.ExcelDataTypeEnum;
import com.lds.oneplanning.mps.enums.ExcelHeadFiledEnums_PART1;
import com.lds.oneplanning.mps.enums.ExcelHeadFiledEnums_PART3;
import com.lds.oneplanning.mps.model.CellModel;
import com.lds.oneplanning.mps.model.MpsData;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.model.RowSaveData;
import com.lds.oneplanning.mps.service.MpsExcelService;
import com.lds.oneplanning.mps.service.facade.MpsRowDataFacadeService;
import com.lds.oneplanning.mps.utils.MpsDateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/7 10:43
 */
@Service
public class MpsExcelServiceImpl implements MpsExcelService {

    @Resource
    private MpsRowDataFacadeService mpsRowDataFacadeService;
    @Resource
    private MpsWeekPlanManager mpsWeekPlanManager;

    @Override
    public List<CellModel> getHeader(Long userId,Date startTime,Date endTime) {
        return  this.buildHeader(userId,new Date());
    }

    @Override
    public List<MpsRowData> getBody(Long userId, Date startTime,Date endTime) {
        return mpsRowDataFacadeService.listCompleteRowData(userId,startTime,endTime);
    }

    @Override
    public MpsData getData(Long userId, Date startTime,Date endTime) {
        MpsData  mpsData = new MpsData();
        mpsData.setHeader(this.buildHeader(userId,new Date()));
        List<MpsRowData> body = this.getBody(userId, startTime,endTime);
        mpsData.setBody(body);
        return mpsData;
    }

    @Override
    public void saveData(Long userId, List<RowSaveData> mpsRowDatas, Date date) {
        mpsRowDataFacadeService.saveRowData(mpsRowDatas,date);
    }

    private List<CellModel> buildHeader(Long userId, Date queryTime){
        List<CellModel> header = Lists.newArrayList();
        Integer index = MpsExcelConstant.COLUMN_OFFSET;
        // 第一部分头部 两行一列
        for (ExcelHeadFiledEnums_PART1 part1 : ExcelHeadFiledEnums_PART1.values()){
            // 两行一列
            header.add(new CellModel(part1.getDesc(),part1.getCode(),part1.getDataType(),index,part1.isEditAble(),part1.isEditAble()));
            index++;
        }
        // 第二部分，周计划部分 todo 改为接口请求
        Integer rowStartIndex = MpsExcelConstant.HEAD_ROW_START_INDEX;
        Integer  j = 1;
        String preWK = "WK";
        String preUpper = "upperHalfWeek";
        String prelower = "lowerHalfWeek";
        Integer startWeek =LocalDateTimeUtil.getWeekSeqOfYear(LocalDateTimeUtil.dateToLocalDate(queryTime));
        LocalDate queryLocalTime = LocalDateTimeUtil.dateToLocalDate(queryTime);
        // todo！ 这个三层map的结构解析起来相当费劲
        YearWeekMap  yearWeekMap = MpsDateUtil.getAllDatesFromCurrentDate(queryTime, 12);
        SimpleDateFormat sdf = new SimpleDateFormat("MM/dd");
        for (Map.Entry<Integer, WeekMap> entry : yearWeekMap.getYearMap().entrySet()){
            // 2025 或者 2026
            Integer year =entry.getKey();
            WeekMap weekMap = entry.getValue();
            for(Map.Entry<Integer, HalfWeekMap> weekMapEntry : weekMap.getWeekMap().entrySet()){
                Integer wekSeq = weekMapEntry.getKey();
                HalfWeekMap halfWeekMap = weekMapEntry.getValue();

                String upperDesc = MpsDateUtil.getStartEndDesc(halfWeekMap.getHalfWeekMap().get(0), sdf);
                String lowerDesc = MpsDateUtil.getStartEndDesc(halfWeekMap.getHalfWeekMap().get(1), sdf);
                CellModel wkCell = new CellModel(preWK+wekSeq, preWK+wekSeq, ExcelDataTypeEnum.NUMBER.getCode(), rowStartIndex, rowStartIndex, index , index + 1,true,true);
                List<CellModel> subCells = Lists.newArrayList();
                subCells.add(new CellModel(upperDesc, preUpper+j,ExcelDataTypeEnum.NUMBER.getCode(), rowStartIndex+1, rowStartIndex+1, index , index ,true,true));
                subCells.add(new CellModel(lowerDesc, prelower+j, ExcelDataTypeEnum.NUMBER.getCode(),rowStartIndex+1, rowStartIndex+1, index + 1, index + 1,true,true));
                wkCell.setSubCells(subCells);
                header.add(wkCell);
                index= index+2;
                j++;
            }
            // todo 跨年的还未处理
        }
        // 第三部分
        for (ExcelHeadFiledEnums_PART3 part3 : ExcelHeadFiledEnums_PART3.values()){
            //两行一列
            header.add(new CellModel(part3.getDesc(),part3.getCode(),part3.getDataType(),index,part3.isEditAble(),part3.isEditAble()));
            index++;
        }
        return header;
    }

}
