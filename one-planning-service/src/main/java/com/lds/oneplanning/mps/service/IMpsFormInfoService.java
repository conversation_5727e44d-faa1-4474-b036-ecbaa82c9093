package com.lds.oneplanning.mps.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lds.oneplanning.mps.entity.MpsFormInfo;
import com.lds.oneplanning.mps.model.MpsRowData;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
public interface IMpsFormInfoService extends IService<MpsFormInfo> {

    MpsFormInfo getByBizId(String bizId);
    List<MpsFormInfo> listByBizIds(List<String> bizIds);

    Long saveOrUpdateByBizId(MpsFormInfo entity);

    void saveOrUpdateBatchByBizId(List<MpsFormInfo> mpsFormInfos);

    /**
     * 批量用户ID同步报工数量
     * @param userId
     */
    void updateReportedByUserId(Long userId);

    /**
     * 同步报工数据（每天定时）
     */
    void synReportedQtyData();
}