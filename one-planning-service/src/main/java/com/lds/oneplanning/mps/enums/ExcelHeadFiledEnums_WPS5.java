package com.lds.oneplanning.mps.enums;

/**
 * 表头枚举
 *
 */
public enum ExcelHeadFiledEnums_WPS5 {

    productUnitPrice("productUnitPrice",ExcelDataTypeEnum.STRING,"产品单价"),
    orderSignFinishTime("orderSignFinishTime",ExcelDataTypeEnum.DATE,"订单签发完成日期"),
    finalAssembly("finalAssembly",ExcelDataTypeEnum.STRING,"包装/总装",true),
    riskMaterialRemark("riskMaterialRemark",ExcelDataTypeEnum.STRING,"风险物料信息备注",true),
    virtualOrderNo("virtualOrderNo",ExcelDataTypeEnum.STRING,"虚拟订单号"),
    virtualRowItem("virtualRowItem",ExcelDataTypeEnum.STRING,"虚拟订单项次"),
    firstReviewShipTime("firstReviewShipTime",ExcelDataTypeEnum.DATE,"首次评审船期"),
    pono("pono",ExcelDataTypeEnum.STRING,"PONO"),
    customerMaterialNo("customerMaterialNo",ExcelDataTypeEnum.STRING,"客户物料编号"),
    customerSeq("customerSeq",ExcelDataTypeEnum.STRING,"客人顺序",true),
    powerNum("powerNum",ExcelDataTypeEnum.NUMBER,"瓦数"),
    size("size",ExcelDataTypeEnum.STRING,"尺寸（方形/原型/规格）");


    private String code;
    private String dataType;
    private String desc;
    private boolean editAble;
    private boolean saveAble;

    private ExcelHeadFiledEnums_WPS5(String code, ExcelDataTypeEnum dataTypeEnum, String desc) {
        this.code = code;
        this.dataType = dataTypeEnum.getCode();
        this.desc = desc;
        this.editAble = false;
    }

    private ExcelHeadFiledEnums_WPS5(String code, ExcelDataTypeEnum dataTypeEnum, String desc, boolean editAble) {
        this.code = code;
        this.dataType = dataTypeEnum.getCode();
        this.desc = desc;
        this.editAble = editAble;
    }

    private ExcelHeadFiledEnums_WPS5(String code, ExcelDataTypeEnum dataTypeEnum, String desc, boolean editAble,boolean saveAble) {
        this.code = code;
        this.dataType = dataTypeEnum.getCode();
        this.desc = desc;
        this.editAble = editAble;
        this.saveAble = saveAble;
    }
    public String getCode() {
        return code;
    }

    public String getDataType() {
        return dataType;
    }

    public String getDesc() {
        return desc;
    }

    public boolean isEditAble() {
        return editAble;
    }

    public boolean isSaveAble() {
        return saveAble;
    }
    public static ExcelHeadFiledEnums_WPS5 getByCode(String code){
        for (ExcelHeadFiledEnums_WPS5 enums : ExcelHeadFiledEnums_WPS5.values()){
            if (enums.code.equals(code)) {
              return enums;
            }
        }
        return null;
    }
    public static String getDescByCode(String code){
        ExcelHeadFiledEnums_WPS5 enumsPart1 = ExcelHeadFiledEnums_WPS5.getByCode(code);
        return enumsPart1==null ? null : enumsPart1.getCode();
    }
}
