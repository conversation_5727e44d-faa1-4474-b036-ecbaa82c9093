package com.lds.oneplanning.mps.service;

import com.lds.oneplanning.mps.model.CellModel;
import com.lds.oneplanning.mps.model.MpsData;
import com.lds.oneplanning.mps.model.MpsRowData;
import com.lds.oneplanning.mps.model.RowSaveData;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: zhuang<PERSON>ayin
 * @Email: <EMAIL>
 * @Date: 2025/2/7 10:43
 */
public interface MpsExcelService {
    MpsData getData(Long userId, Date startTime,Date endTime);

    List<CellModel> getHeader(Long userId, Date startTime,Date endTime);

    List<MpsRowData> getBody(Long userId,  Date startTime,Date endTime);

    void saveData(Long userId, List<RowSaveData> mpsRowDatas, Date date);
}
