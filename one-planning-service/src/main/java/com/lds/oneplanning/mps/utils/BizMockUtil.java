package com.lds.oneplanning.mps.utils;

import com.google.common.collect.Maps;
import com.lds.oneplanning.mps.constans.MpsExcelConstant;
import com.lds.oneplanning.mps.model.CellModel;
import com.lds.oneplanning.mps.model.MpsRowData;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.compress.utils.Lists;


/**
 * @Description:
 * @Author: zhuangjiayin
 * @Email: <EMAIL>
 * @Date: 2025/2/7 14:24
 */
public class BizMockUtil {
    private BizMockUtil(){}


//    public static List<MpsRowData>  mockBody(Integer count) {
//        List<MpsRowData> resList = Lists.newArrayList();
//        for (int i = 0; i <count ; i++) {
//            MpsRowData mpsRowData = new MpsRowData();
//            mpsRowData.setMainPlan("主计划"+i);
//            mpsRowData.setDept("部门"+i);
//            mpsRowData.setCustomerGroup("客户组"+i);
//            mpsRowData.setCustomerCode("客户代码"+i);
//            mpsRowData.setSellOrderNo("销售订单号"+i);
//            mpsRowData.setRowItem("行项目"+i);
//            mpsRowData.setOmRespons("OM负责人"+i);
//            mpsRowData.setFactory("生产工厂"+i);
//            mpsRowData.setOrderType("订单类型"+i);
//            mpsRowData.setOrderNo("订单号"+i);
//            mpsRowData.setOutDeliveryNo("外向交货单"+i);
//            mpsRowData.setOutRowItem("外向行项目"+i);
//            mpsRowData.setCommodityId("商品id"+i);
//            mpsRowData.setCommodityDesc("商品描述"+i);
//            mpsRowData.setCrossPlantMaterialStatus("跨工厂物料状态"+i);
//            mpsRowData.setProductLineLevel3("三级产品线"+i);
//            mpsRowData.setProductLineLevel5("五级产品线"+i);
//            mpsRowData.setCategory("品类（大）"+i);
//            mpsRowData.setMachineModel("机型（中）"+i);
//            mpsRowData.setSpecification("规格（小）"+i);
//            mpsRowData.setProductId("产品id"+i);
//            mpsRowData.setProductDesc("产品描述"+i);
//            mpsRowData.setOrderUnitQty(i);
//            mpsRowData.setOrderPcsQty(i);
//            mpsRowData.setTransQty(i);
//            mpsRowData.setReportedPcsQty(i);
//            mpsRowData.setStockedPcsQty(i);
//            mpsRowData.setSchedulePcsQty(i);
//            mpsRowData.setBizSampleQty(i);
//            mpsRowData.setQcSampleQty(i);
//            mpsRowData.setLightComponentId("光源组件ID"+i);
//            mpsRowData.setLightDeliveryTime(new Date());
//            mpsRowData.setDriverComponentId("驱动组件ID"+i);
//            mpsRowData.setDriverDeliveryTime(new Date());
//            mpsRowData.setStructDeliveryTime(new Date());
//            mpsRowData.setPackageDeliveryTime(new Date());
//            mpsRowData.setStdWorkHours("标准工时（总装+老化+包装）"+i);
//            mpsRowData.setPackageType("包装方式"+i);
//            mpsRowData.setCapacityStruct("产能结构"+i);
//            mpsRowData.setOnlineTime(new Date());
//            mpsRowData.setOriginalOnlineTime(new Date());
//            mpsRowData.setIsInspect("是否验货"+i);
//            mpsRowData.setOriginalInspectTime(new Date());
//            mpsRowData.setLatestInspectTime(new Date());
//            mpsRowData.setOriginalLoadTime(new Date());
//            mpsRowData.setLatestLoadTime(new Date());
//            mpsRowData.setOriginalShipTime(new Date());
//            mpsRowData.setFinalShipTime(new Date());
//            mpsRowData.setEstFinishTime(new Date());
//            mpsRowData.setProductStartTime(new Date());
//            mpsRowData.setProductEndTime(new Date());
//            mpsRowData.setOriginalReplyTime(new Date());
//            mpsRowData.setLatestReplyTime(new Date());
//            mpsRowData.setRemark1("备注1"+i);
//            mpsRowData.setRemark2("备注2"+i);
//            mpsRowData.setUpperHalfWeek1(1);
//            mpsRowData.setLowerHalfWeek1(2);
//            mpsRowData.setUpperHalfWeek2(3);
//            mpsRowData.setLowerHalfWeek2(4);
//            mpsRowData.setUpperHalfWeek3(5);
//            mpsRowData.setLowerHalfWeek3(6);
//            mpsRowData.setUpperHalfWeek4(7);
//            mpsRowData.setLowerHalfWeek4(8);
//            mpsRowData.setUpperHalfWeek5(9);
//            mpsRowData.setLowerHalfWeek5(10);
//            mpsRowData.setUpperHalfWeek6(11);
//            mpsRowData.setLowerHalfWeek6(12);
//            mpsRowData.setUpperHalfWeek7(13);
//            mpsRowData.setLowerHalfWeek7(14);
//            mpsRowData.setUpperHalfWeek8(15);
//            mpsRowData.setLowerHalfWeek8(16);
//            mpsRowData.setUpperHalfWeek9(17);
//            mpsRowData.setLowerHalfWeek9(18);
//            mpsRowData.setUpperHalfWeek10(19);
//            mpsRowData.setLowerHalfWeek10(20);
//            mpsRowData.setUpperHalfWeek11(21);
//            mpsRowData.setLowerHalfWeek11(22);
//            mpsRowData.setUpperHalfWeek12(23);
//            mpsRowData.setLowerHalfWeek12(24);
//            mpsRowData.setUpperHalfWeek13(25);
//            mpsRowData.setLowerHalfWeek13(26);
//            mpsRowData.setProductUnitPrice("产品单价"+i);
//            mpsRowData.setOrderSignFinishTime(new Date());
//            mpsRowData.setFinalAssembly("包装/总装"+i);
//            mpsRowData.setRiskMaterialRemark("风险物料信息备注"+i);
//            mpsRowData.setVirtualOrderNo("虚拟订单号"+i);
//            mpsRowData.setVirtualRowItem("虚拟订单项次"+i);
//            mpsRowData.setFirstReviewShipTime(new Date());
//            mpsRowData.setPono("PONO"+i);
//            mpsRowData.setCustomerMaterialNo("客户物料编号"+i);
//            mpsRowData.setCustomerSeq("客人顺序"+i);
////            mpsRowData.setMachineModel2("机型(后)"+i);
//            mpsRowData.setPowerNum("瓦数"+i);
//            mpsRowData.setSize("尺寸（方形/原型/规格）"+i);
//            resList.add(mpsRowData);
//        }
//
//        return  resList;
//    }



/*    public static Map<Integer, List<CellModel>> buildBody(Integer count) {
        Map<Integer, List<CellModel>> resMap = Maps.newLinkedHashMap();
        Integer dataStartRowIndex = MpsExcelConstant.BODY_ROW_OFFSET;
        for (int i = 0; i <count ; i++) {
            resMap.put(dataStartRowIndex+i,buildRowData(dataStartRowIndex+i));
        }
        return resMap;
    }*/

    /*public static List<CellModel> buildRowData(Integer index){
        List<CellModel> resList = Lists.newArrayList();
        resList.add(new CellModel("mainPlan"+index,index,index,0));
        resList.add(new CellModel("dept"+index,index,index,1));
        resList.add(new CellModel("customerGroup"+index,index,index,2));
        resList.add(new CellModel("customerCode"+index,index,index,3));
        resList.add(new CellModel("sellOrderNo"+index,index,index,4));
        resList.add(new CellModel("rowItem"+index,index,index,5));
        resList.add(new CellModel("omRespons"+index,index,index,6));
        resList.add(new CellModel("factory"+index,index,index,7));
        resList.add(new CellModel("orderType"+index,index,index,8));
        resList.add(new CellModel("orderNo"+index,index,index,9));
        resList.add(new CellModel("outDeliveryNo"+index,index,index,10));
        resList.add(new CellModel("outRowItem"+index,index,index,11));
        resList.add(new CellModel("commodityId"+index,index,index,12));
        resList.add(new CellModel("commodityDesc"+index,index,index,13));
        resList.add(new CellModel("crossPlantMaterialStatus"+index,index,index,14));
        resList.add(new CellModel("productLineLevel3"+index,index,index,15));
        resList.add(new CellModel("productLineLevel5"+index,index,index,16));
        resList.add(new CellModel("category"+index,index,index,17));
        resList.add(new CellModel("machineModel"+index,index,index,18));
        resList.add(new CellModel("specification"+index,index,index,19));
        resList.add(new CellModel("productId"+index,index,index,20));
        resList.add(new CellModel("productDesc"+index,index,index,21));
        resList.add(new CellModel("orderUnitQty"+index,index,index,22));
        resList.add(new CellModel("orderPcsQty"+index,index,index,23));
        resList.add(new CellModel("transQty"+index,index,index,24));
        resList.add(new CellModel("reportedPcsQty"+index,index,index,25));
        resList.add(new CellModel("stockedPcsQty"+index,index,index,26));
        resList.add(new CellModel("schedulePcsQty"+index,index,index,27));
        resList.add(new CellModel("bizSampleQty"+index,index,index,28));
        resList.add(new CellModel("qcSampleQty"+index,index,index,29));
        resList.add(new CellModel("lightComponentId"+index,index,index,30));
        resList.add(new CellModel("lightDeliveryTime"+index,index,index,31));
        resList.add(new CellModel("driverComponentId"+index,index,index,32));
        resList.add(new CellModel("driverDeliveryTime"+index,index,index,33));
        resList.add(new CellModel("structDeliveryTime"+index,index,index,34));
        resList.add(new CellModel("packageDeliveryTime"+index,index,index,35));
        resList.add(new CellModel("stdWorkHours"+index,index,index,36));
        resList.add(new CellModel("packageType"+index,index,index,37));
        resList.add(new CellModel("capacityStruct"+index,index,index,38));
        resList.add(new CellModel("onlineTime"+index,index,index,39));
        resList.add(new CellModel("originalOnlineTime"+index,index,index,40));
        resList.add(new CellModel("isInspect"+index,index,index,41));
        resList.add(new CellModel("originalInspectTime"+index,index,index,42));
        resList.add(new CellModel("latestInspectTime"+index,index,index,43));
        resList.add(new CellModel("originalLoadTime"+index,index,index,44));
        resList.add(new CellModel("latestLoadTime"+index,index,index,45));
        resList.add(new CellModel("originalShipTime"+index,index,index,46));
        resList.add(new CellModel("finalShipTime"+index,index,index,47));
        resList.add(new CellModel("estFinishTime"+index,index,index,48));
        resList.add(new CellModel("productStartTime"+index,index,index,49));
        resList.add(new CellModel("productEndTime"+index,index,index,50));
        resList.add(new CellModel("originalReplyTime"+index,index,index,51));
        resList.add(new CellModel("latestReplyTime"+index,index,index,52));
        resList.add(new CellModel("remark1"+index,index,index,53));
        resList.add(new CellModel("remark2"+index,index,index,54));

        resList.add(new CellModel("WK1上"+index,index,index,55));
        resList.add(new CellModel("WK1下"+index,index,index,56));
        resList.add(new CellModel("WK2上"+index,index,index,57));
        resList.add(new CellModel("WK2下"+index,index,index,58));
        resList.add(new CellModel("WK3上"+index,index,index,59));
        resList.add(new CellModel("WK3下"+index,index,index,60));
        resList.add(new CellModel("WK4上"+index,index,index,61));
        resList.add(new CellModel("WK4下"+index,index,index,62));
        resList.add(new CellModel("WK5上"+index,index,index,63));
        resList.add(new CellModel("WK5下"+index,index,index,64));
        resList.add(new CellModel("WK6上"+index,index,index,65));
        resList.add(new CellModel("WK6下"+index,index,index,66));
        resList.add(new CellModel("WK7上"+index,index,index,67));
        resList.add(new CellModel("WK7下"+index,index,index,68));
        resList.add(new CellModel("WK8上"+index,index,index,69));
        resList.add(new CellModel("WK8下"+index,index,index,70));
        resList.add(new CellModel("WK9上"+index,index,index,71));
        resList.add(new CellModel("WK9下"+index,index,index,72));
        resList.add(new CellModel("WK10上"+index,index,index,73));
        resList.add(new CellModel("WK10下"+index,index,index,74));
        resList.add(new CellModel("WK11上"+index,index,index,75));
        resList.add(new CellModel("WK11下"+index,index,index,76));
        resList.add(new CellModel("WK12上"+index,index,index,77));
        resList.add(new CellModel("WK12下"+index,index,index,78));
        resList.add(new CellModel("WK13上"+index,index,index,79));
        resList.add(new CellModel("WK13下"+index,index,index,80));

        resList.add(new CellModel("productUnitPrice"+index,index,index,81));
        resList.add(new CellModel("orderSignFinishTime"+index,index,index,82));
        resList.add(new CellModel("package"+index,index,index,83));
        resList.add(new CellModel("riskMaterialRemark"+index,index,index,84));
        resList.add(new CellModel("virtualOrderNo"+index,index,index,85));
        resList.add(new CellModel("virtualRowItem"+index,index,index,86));
        resList.add(new CellModel("firstReviewShipTime"+index,index,index,87));
        resList.add(new CellModel("pono"+index,index,index,88));
        resList.add(new CellModel("customerMaterialNo"+index,index,index,89));
        resList.add(new CellModel("customerSeq"+index,index,index,90));
        resList.add(new CellModel("machineModel"+index,index,index,91));
        resList.add(new CellModel("powerNo"+index,index,index,92));
        resList.add(new CellModel("size"+index,index,index,93));

        return resList;
    }*/
}
