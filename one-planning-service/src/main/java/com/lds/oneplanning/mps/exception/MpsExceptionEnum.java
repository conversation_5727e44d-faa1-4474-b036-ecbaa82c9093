package com.lds.oneplanning.mps.exception;

import com.iot.common.exception.IBusinessException;

/**
 * <AUTHOR>
 */
public enum MpsExceptionEnum implements IBusinessException {
    CODE_EXIST(5000001, "CODE已存在！"),
    RECORD_EXIST(5000001, "记录已存在！");
    /**
     * 异常代码
     */
    private Integer code;

    /**
     * 异常描述
     */
    private String messageKey;

    /**
     * 描述：构建异常
     *
     * @param code       错误代码
     * @param messageKey 错误描述
     * @return
     * <AUTHOR>
     * @created 2017年3月21日 上午10:50:58
     * @since
     */
    MpsExceptionEnum(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }

    public static MpsExceptionEnum getByCode(Integer code) {
        for (MpsExceptionEnum businessExceptionEnum : MpsExceptionEnum.values()) {
            if (businessExceptionEnum.code.equals(code)) {
                return businessExceptionEnum;
            }
        }
        return null;
    }
}
