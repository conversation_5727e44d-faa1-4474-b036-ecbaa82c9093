package com.lds.oneplanning.mps.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iot.common.beans.BeanUtil;
import com.lds.oneplanning.mps.entity.MpsWeekPlan;
import com.lds.oneplanning.mps.mapper.MpsWeekPlanMapper;
import com.lds.oneplanning.mps.service.IMpsWeekPlanService;
import com.lds.oneplanning.mps.vo.MpsPrePlanQuantityVo;
import com.lds.oneplanning.mps.vo.MpsWeekPlanVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Service
public class MpsWeekPlanServiceImpl extends ServiceImpl<MpsWeekPlanMapper, MpsWeekPlan> implements IMpsWeekPlanService {
    @Override
    public void batchSaveByBizId(List<MpsWeekPlan> sourceList) {
        if (sourceList == null || sourceList.isEmpty()) {
            return;
        }
        Set<String> bizIds =sourceList.stream().map(MpsWeekPlan::getBizId).collect(Collectors.toSet());
        LambdaQueryWrapper<MpsWeekPlan> deleteQuery = Wrappers.<MpsWeekPlan>lambdaQuery().in(MpsWeekPlan::getBizId,bizIds);
        baseMapper.delete(deleteQuery);
        // 重新新增
        this.saveBatch(sourceList);
    }

    @Override
    public Map<String, List<MpsWeekPlanVo>> listByBizIds(List<String> bizIds) {
        Map<String, List<MpsWeekPlanVo>> mpsWeekPlanVoMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(bizIds)) {
            return mpsWeekPlanVoMap;
        }
        List<MpsWeekPlan> mpsWeekPlans = this.list(Wrappers.<MpsWeekPlan>lambdaQuery().in(MpsWeekPlan::getBizId, bizIds));
        if (CollectionUtils.isEmpty(mpsWeekPlans)) {
            return mpsWeekPlanVoMap;
        }
        // 按bizId分组
        Map<String, List<MpsWeekPlan>> mpsWeekPlanMap = mpsWeekPlans.stream().collect(Collectors.groupingBy(MpsWeekPlan::getBizId));
        // 转换为vo
        for (Map.Entry<String, List<MpsWeekPlan>> entry : mpsWeekPlanMap.entrySet()) {
            String bizId = entry.getKey();
            List<MpsWeekPlan> subMpsWeekPlans = entry.getValue();
            // 处理单个bizId下的mpsWeekPlan
            mpsWeekPlanVoMap.put(bizId, buildMpsWeekPlanVos(subMpsWeekPlans));
        }
        return mpsWeekPlanVoMap;
    }

    @Override
    public Map<String, List<MpsWeekPlan>> groupByBizId(List<String> bizIds) {
        return baseMapper.selectList(Wrappers.<MpsWeekPlan>lambdaQuery().in(MpsWeekPlan::getBizId, bizIds)).stream().collect(Collectors.groupingBy(MpsWeekPlan::getBizId));
    }

    private List<MpsWeekPlanVo> buildMpsWeekPlanVos(List<MpsWeekPlan> subMpsWeekPlans) {
        // 按自然周分组
        Map<Integer, List<MpsWeekPlan>> natureWeekMap = subMpsWeekPlans.stream().collect(Collectors.groupingBy(MpsWeekPlan::getNatureWeek));
        List<MpsWeekPlanVo> mpsWeekPlanVos = Lists.newArrayList();
        for (Map.Entry<Integer, List<MpsWeekPlan>> weekEntry : natureWeekMap.entrySet()) {
            List<MpsWeekPlan> subSubMpsWeekPlans = weekEntry.getValue();
            if (CollectionUtils.isEmpty(subSubMpsWeekPlans)) {
                continue;
            }
            mpsWeekPlanVos.add(buidMpsWeekPlanVo(weekEntry.getKey(), subSubMpsWeekPlans));
        }
        return mpsWeekPlanVos;
    }

    private MpsWeekPlanVo buidMpsWeekPlanVo(Integer natureWeek, List<MpsWeekPlan> subSubMpsWeekPlans) {
        MpsWeekPlanVo mpsWeekPlanVo = new MpsWeekPlanVo();
        mpsWeekPlanVo.setNatureWeek(natureWeek);
        // 按上/下半周排序
        subSubMpsWeekPlans.sort(Comparator.comparing(MpsWeekPlan::getWeekType));
        List<MpsPrePlanQuantityVo> mpsPrePlanQuantityVos = subSubMpsWeekPlans.stream().map(mpsWeekPlan -> {
            MpsPrePlanQuantityVo mpsPrePlanQuantityVo = new MpsPrePlanQuantityVo();
            BeanUtil.copyProperties(mpsWeekPlan, mpsPrePlanQuantityVo);
            mpsPrePlanQuantityVo.setPrePlanId(mpsWeekPlan.getId());
            return mpsPrePlanQuantityVo;
        }).collect(Collectors.toList());
        mpsWeekPlanVo.setPrePlanQuantityVos(mpsPrePlanQuantityVos);
        return mpsWeekPlanVo;
    }
}
