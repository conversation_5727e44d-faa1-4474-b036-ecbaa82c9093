package com.lds.oneplanning.mps.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.mps.date.HalfWeekMap;
import com.lds.oneplanning.mps.date.WeekMap;
import com.lds.oneplanning.mps.date.YearWeekMap;
import com.lds.oneplanning.mps.enums.WeekTypeEnum;

import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.*;

public class MpsDateUtil {


    public static final ZoneId ZONE_ID = ZoneId.systemDefault();

    /**
     * 根据当前日期和需要推的周数生成所有日期列表
     *
     * @param inputDate 当前日期
     * @param weeksToPush 往后推的周数
     * @return 所有日期列表
     */
    public static YearWeekMap getAllDatesFromCurrentDate(Date inputDate, int weeksToPush) {
        YearWeekMap yearWeekMap = new YearWeekMap();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inputDate);
        for (int weekOffset = 0; weekOffset <= weeksToPush; weekOffset++) {
            // 确保当前日期是周一
            if (calendar.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) {
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            }
            // 获取当前周的年份和周数
            int year = calendar.get(Calendar.YEAR);
            int weekNumber = calendar.get(Calendar.WEEK_OF_YEAR);
            yearWeekMap.getYearMap().putIfAbsent(year, new WeekMap());
            // 获取年份对应的自然周数 Map
            WeekMap weekMap = yearWeekMap.getYearMap().get(year);
            weekMap.getWeekMap().putIfAbsent(weekNumber, new HalfWeekMap());
            // 上半周和下半周的日期列表
            HalfWeekMap halfWeekMap = weekMap.getWeekMap().get(weekNumber);
            halfWeekMap.getHalfWeekMap().putIfAbsent(WeekTypeEnum.FIRST_HALF_WEEK.getCode(), Lists.newLinkedList());
            halfWeekMap.getHalfWeekMap().putIfAbsent(WeekTypeEnum.SECOND_HALF_WEEK.getCode(), Lists.newLinkedList());
            // 添加该周的日期（周一到周六） TODO 新版排产涉及正序，逆序排产，需要用到所有天数
            for (int day = 0; day <= 6; day++) {
                Date currentDate = calendar.getTime();
                // 对比inputDate和currentDate
                if (currentDate.before(inputDate)) {
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    continue;
                }
                //TODO 暂时把周日也放出来，用于排查线体排查时长查询
//                if (calendar.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
                    if (day < 3) { // 周一到周三为上半周
                        halfWeekMap.getHalfWeekMap().get(WeekTypeEnum.FIRST_HALF_WEEK.getCode()).add(currentDate);
                    } else { // 周四到周日为下半周
                        halfWeekMap.getHalfWeekMap().get(WeekTypeEnum.SECOND_HALF_WEEK.getCode()).add(currentDate);
                    }
//                }
                calendar.add(Calendar.DAY_OF_MONTH, 1); // 移动到下一天
                // 处理跨年情况
                if (calendar.get(Calendar.YEAR) != year) {
                    // 如果年份发生变化，重新获取年份和周数
                    year = calendar.get(Calendar.YEAR);
                    weekNumber = calendar.get(Calendar.WEEK_OF_YEAR);
                    yearWeekMap.getYearMap().putIfAbsent(year, new WeekMap());
                    weekMap = yearWeekMap.getYearMap().get(year);
                    weekMap.getWeekMap().putIfAbsent(weekNumber, new HalfWeekMap());
                    halfWeekMap = weekMap.getWeekMap().get(weekNumber);
                    halfWeekMap.getHalfWeekMap().putIfAbsent(WeekTypeEnum.FIRST_HALF_WEEK.getCode(), Lists.newLinkedList());
                    halfWeekMap.getHalfWeekMap().putIfAbsent(WeekTypeEnum.SECOND_HALF_WEEK.getCode(), Lists.newLinkedList());
                }

            }
        }
        return yearWeekMap;
    }

    public static Map<Integer, List<Integer>> getAllNatureWeeksFromCurrentDate(Date currentDate, int weeksToPush) {
        YearWeekMap yearWeekMap = getAllDatesFromCurrentDate(currentDate, weeksToPush);
        Map<Integer, List<Integer>> yearNatureWeekMap = Maps.newHashMap();
        for (Map.Entry<Integer, WeekMap> yearEntry : yearWeekMap.getYearMap().entrySet()) {
            int year = yearEntry.getKey();
            WeekMap weekMap = yearEntry.getValue();
            Set<Integer> natureWeeks = weekMap.getWeekMap().keySet();
            yearNatureWeekMap.put(year, Lists.newArrayList(natureWeeks));
        }
        return yearNatureWeekMap;
    }
    public static String getStartEndDesc( List<Date> sourceList,SimpleDateFormat sdf){
        if (sourceList == null || sourceList.isEmpty()) {
            return null;
        }
        //先进行排序
        sourceList.stream().sorted(Date::compareTo);
        // 第一个就是开始，最后一个就是结束
        List<Date> cleanList = Lists.newArrayList(sourceList.get(0),sourceList.get(sourceList.size()-1));
        return  cleanList.stream().map(sdf::format).reduce((a,b)->a+"-"+b).orElse("");
    }

}
