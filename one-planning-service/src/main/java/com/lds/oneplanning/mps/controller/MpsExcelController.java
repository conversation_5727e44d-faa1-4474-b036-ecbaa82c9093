package com.lds.oneplanning.mps.controller;


import com.lds.coral.auth.core.context.UserContextUtils;
import com.lds.oneplanning.common.utils.LocalDateTimeUtil;
import com.lds.oneplanning.mps.model.MpsData;
import com.lds.oneplanning.mps.model.RowSaveData;
import com.lds.oneplanning.mps.service.IMpsFormInfoService;
import com.lds.oneplanning.mps.service.MpsExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR> generator
 * @since 2025-01-21
 */
@Api(value = "mspExcelController", tags = "mspExcelController")
@RestController
@RequestMapping("/mps/excel")
public class MpsExcelController {

    @Resource
    private MpsExcelService mpsExcelService;
    @Resource
    private IMpsFormInfoService mpsFormInfoService;

    private static final Integer before_day = 30;


    @ApiOperation(value = "数据查询", notes = "数据查询")
    @GetMapping("/getData")
    public MpsData getData(@RequestParam(value = "startTime",required = false) Date startTime,
                           @RequestParam(value = "endTime",required = false) Date endTime){
        // 不传默认查询15天前到现在的订单
        startTime = startTime == null ? LocalDateTimeUtil.localDateToDate(LocalDate.now().minusDays(before_day)) : startTime;
        endTime = endTime == null ? new Date() : endTime;
        return  mpsExcelService.getData(UserContextUtils.getUserId(),startTime,endTime);
    }

    @ApiOperation(value = "数据保存", notes = "数据保存")
    @PostMapping("/saveData")
    public void saveData(@RequestBody List<RowSaveData> mpsRowDatas){
          mpsExcelService.saveData(UserContextUtils.getUserId(),mpsRowDatas,new Date());
    }

    @ApiOperation(value = "更新报工数据", notes = "更新报工数据")
    @GetMapping("/updateReportedNum")
    public Boolean updateReportedNum() {
        Long userId = UserContextUtils.getUserId();
        mpsFormInfoService.updateReportedByUserId(userId);
        return true;
    }
}
