package com.lds.oneplanning.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.SignStyle;
import java.time.temporal.ChronoUnit;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;

import static java.time.temporal.ChronoField.YEAR;
import static java.time.temporal.IsoFields.QUARTER_OF_YEAR;

/**
 * Local datetime utils
 *
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("all")
public final class LocalDateTimeUtil {

    public static final String ZH_CN = "zh_CN";

    private LocalDateTimeUtil() {
    }


    /**
     * YEAR_FORMAT
     */
    public static final String YEAR_FORMAT = "yyyy";
    /**
     * MONTH_FORMAT
     */
    public static final String MONTH_FORMAT = "yyyy-MM";
    /**
     * MONTH_FORMAT_SLASH
     */
    public static final String MONTH_FORMAT_SLASH = "yyyy/MM";
    /**
     * WEEK_FORMAT
     */
    public static final String WEEK_FORMAT = "YYYY-w";
    /**
     * WEEK_FORMAT_SLASH
     */
    public static final String WEEK_FORMAT_SLASH = "YYYY w";
    /**
     * DAY_FORMAT
     */
    public static final String DAY_FORMAT = "yyyy-MM-dd";
    /**
     * DAY_FORMAT_SLASH
     */
    public static final String DAY_FORMAT_SLASH = "yyyy/MM/dd";
    /**
     * HOUR_DEFAULT_FORMAT
     */
    public static final String HOUR_DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";
    /**
     * HOUR_DEFAULT_FORMAT_SLASH
     */
    public static final String HOUR_DEFAULT_FORMAT_SLASH = "yyyy/MM/dd HH:mm:ss";
    /**
     * HOUR_MINUTE_FORMAT
     */
    public static final String HOUR_MINUTE_FORMAT = "yyyy-MM-dd HH:mm";
    /**
     * HOUR_MINUTE_FORMAT_SLASH
     */
    public static final String HOUR_MINUTE_FORMAT_SLASH = "yyyy/MM/dd HH:mm";


    public static List<LocalDate> listDateByNatureWeek(int year, int natureWeekSeq) {
        // 获取当前地区的星期第一天，默认是周一
        WeekFields weekFields = WeekFields.of(DayOfWeek.MONDAY, 1);

        // 创建指定年份的第一天
        LocalDate firstDayOfYear = LocalDate.of(year, 1, 1);

        // 计算第一个属于指定自然周的日期
        LocalDate startOfFirstWeek = firstDayOfYear.with(weekFields.weekOfYear(), natureWeekSeq).with(weekFields.dayOfWeek(), 1);

        List<LocalDate> result = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            // 循环添加一周七天的日期
            result.add(startOfFirstWeek.plusDays(i));
        }

        return result;
    }
    public static int getWeekSeqOfYear(LocalDate localDate){
        if (localDate == null) {
            return  -1;
        }

        // 获取当前地区的周字段设置
        WeekFields weekFields = WeekFields.of(Locale.getDefault());

        // 获取一年中的第几周
        int res =  localDate.get(weekFields.weekOfYear());
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        //老外周天算下一周，而我们的周天是再本周里
        return  DayOfWeek.SUNDAY == dayOfWeek ?  res -1: res;
    }

    public static final DateTimeFormatter WIDGET_QUARTER_PARSER = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .appendValue(YEAR, 4, 10, SignStyle.EXCEEDS_PAD)
            .appendLiteral('-')
            .appendValue(QUARTER_OF_YEAR, 1)
            .toFormatter();

    /**
     * Gets local date *
     *
     * @param dateStr date str
     * @return the local date
     */
    public static LocalDate localDateParse(String dateStr) {
        return localDateParse(dateStr, dateStr.contains("-") ? DAY_FORMAT : DAY_FORMAT_SLASH);
    }

    /**
     * Local date parse local date
     *
     * @param dateStr   date str
     * @param formatter formatter
     * @return the local date
     */
    public static LocalDate localDateParse(String dateStr, String formatter) {
        LocalDate localDate = null;
        switch (formatter) {
            case YEAR_FORMAT:
                Year year = Year.parse(dateStr, DateTimeFormatter.ofPattern(formatter));
                localDate = LocalDate.of(year.getValue(), 1, 1);
                break;
            case MONTH_FORMAT:
            case MONTH_FORMAT_SLASH:
                YearMonth yearMonth = YearMonth.parse(dateStr, DateTimeFormatter.ofPattern(formatter));
                localDate = LocalDate.of(yearMonth.getYear(), yearMonth.getMonthValue(), 1);
                break;
            case WEEK_FORMAT:
            case WEEK_FORMAT_SLASH:
                String[] params = dateStr.contains("-") ? dateStr.split("-") : dateStr.split(" ");
                localDate = getFirstDayOfWeek(Integer.parseInt(params[0]), Integer.parseInt(params[1]));
                break;
            default:
                localDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(formatter));
                break;
        }
        return localDate;
    }

    /**
     * Local date print string
     *
     * @param date      date
     * @param formatter formatter
     * @return the string
     */
    public static String localDatePrint(LocalDate date, String formatter) {
        return date.format(DateTimeFormatter.ofPattern(formatter));
    }

    public static String getWeekYearByLanguage(String date,String language) {
        SimpleDateFormat format = new SimpleDateFormat(DAY_FORMAT);
        Date localDate = null;
        try {
            localDate = format.parse(date);
        } catch (Exception e) {
            log.error("getWeekYearByLanguage ",e);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.SUNDAY);
        if (ZH_CN.equals(language)){
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
        }
        calendar.setTime(localDate);
       return calendar.get(Calendar.YEAR)+"-"+calendar.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * Local date time parse local date
     *
     * @param dateStr date str
     * @return the local date
     */
    public static LocalDateTime localDateTimeParse(String dateStr) {
        return localDateTimeParse(dateStr, dateStr.contains("-") ? HOUR_DEFAULT_FORMAT : HOUR_DEFAULT_FORMAT_SLASH);
    }

    /**
     * Local date time parse local date time
     *
     * @param dateStr   date str
     * @param formatter formatter
     * @return the local date time
     */
    public static LocalDateTime localDateTimeParse(String dateStr, String formatter) {
        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(formatter));
    }

    /**
     * Local date time print string
     *
     * @param time      time
     * @param formatter formatter
     * @return the string
     */
    public static String localDateTimePrint(LocalDateTime time, String formatter) {
        return time.format(DateTimeFormatter.ofPattern(formatter));
    }

    /**
     * 获取指定日期所在周的星期一
     *
     * @param date date
     * @return monday monday
     */
    public static LocalDate getMonday(LocalDate date) {
        int dayOfWeek = date.getDayOfWeek().getValue();
        return date.minusDays(dayOfWeek - 1L);
    }

    /**
     * 获取指定日期所在周的星期日
     *
     * @param date date
     * @return sunday sunday
     */
    public static LocalDate getSunday(LocalDate date) {
        int dayOfWeek = date.getDayOfWeek().getValue();
        return date.plusDays(7L - dayOfWeek);
    }

    /**
     * 获取指定日期所在月的第一天
     *
     * @param date date
     * @return first day of month
     */
    public static LocalDate getFirstDayOfMonth(LocalDate date) {
        return date.with(TemporalAdjusters.firstDayOfMonth());
    }

    /**
     * 获取指定日期所在月的最后一天
     *
     * @param date date
     * @return last day of month
     */
    public static LocalDate getLastDayOfMonth(LocalDate date) {
        return date.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 获取指定日期所在季度的第一天
     *
     * @param date date
     * @return first day of quarter
     */
    public static LocalDate getFirstDayOfQuarter(LocalDate date) {
        Month firstMonthOfQuarter = date.getMonth().firstMonthOfQuarter();
        return LocalDate.of(date.getYear(), firstMonthOfQuarter.getValue(), 1);
    }

    /**
     * 获取指定日期所在季度的最后一天
     *
     * @param date date
     * @return last day of quarter
     */
    public static LocalDate getLastDayOfQuarter(LocalDate date) {
        Month firstMonthOfQuarter = date.getMonth().firstMonthOfQuarter();
        return LocalDate.of(date.getYear(), firstMonthOfQuarter.getValue() + 2, 1)
                .with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 获取指定日期所在年度的第一天
     *
     * @param date date
     * @return first day of year
     */
    public static LocalDate getFirstDayOfYear(LocalDate date) {
        return date.with(TemporalAdjusters.firstDayOfYear());
    }

    /**
     * 获取指定日期所在年度的最后一天
     *
     * @param date date
     * @return last day of year
     */
    public static LocalDate getLastDayOfYear(LocalDate date) {
        return date.with(TemporalAdjusters.lastDayOfYear());
    }

    /**
     * 获取指定日期当天开始的时间
     *
     * @param date date
     * @return first time of day
     */
    public static Date getFirstTimeOfDay(LocalDate date) {
        LocalDateTime localDateTime = date.atStartOfDay();
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取指定日期当天最后的时间
     *
     * @param date date
     * @return last time of day
     */
    public static Date getLastTimeOfDay(LocalDate date) {
        LocalDateTime localDateTime = date.atTime(23, 59, 59, 999999999);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取自然周的周一日期
     *
     * @param year year
     * @param week week
     * @return first day of week
     */
    public static LocalDate getFirstDayOfWeek(int year, int week) {
        DateTimeFormatter dateTimeFormatter = new DateTimeFormatterBuilder().appendPattern(WEEK_FORMAT)
                .parseDefaulting(WeekFields.ISO.dayOfWeek(), 1).toFormatter();
        return LocalDate.parse(year + "-" + week, dateTimeFormatter);
    }

    /**
     * 获取自然周的周日日期
     *
     * @param year year
     * @param week week
     * @return last day of week
     */
    public static LocalDate getLastDayOfWeek(int year, int week) {
        return getSunday(getFirstDayOfWeek(year, week));
    }


    /**
     * 通过日期获取自然周
     *
     * @param date date
     * @return week of year
     */
    public static String getWeekOfYear(LocalDate date) {
        return localDatePrint(date, WEEK_FORMAT);
    }

    /**
     * 通过日期获取自然周
     *
     * @param dateStr date str
     * @return week of year
     */
    public static String getWeekOfYear(String dateStr,String language) {
        return getWeekYearByLanguage(dateStr,language);
    }

    /**
     * 通过日期获取季度
     *
     * @param date date
     * @return the quarter of year
     */
    public static String getQuarterOfYear(LocalDate date) {
        return new StringBuilder()
                .append(localDatePrint(date, YEAR_FORMAT))
                .append("-")
                .append(getQuarter(date))
                .toString();
    }


    /**
     * 通过日期获取季度
     *
     * @param date date
     * @return the quarter
     */
    public static int getQuarter(LocalDate date) {
        return date.get(IsoFields.QUARTER_OF_YEAR);
    }

    /**
     * Date转LocalDate
     * @param date
     * @return
     */
    public static  LocalDate dateToLocalDate(Date date){
        if(date==null){
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        // atZone()方法返回在指定时区从此Instant生成的ZonedDateTime。
        return instant.atZone(zoneId).toLocalDate();
    }

    /**
     * localDate 转 Date
     * @param localDate
     * @return
     */
    public static Date localDateToDate(LocalDate localDate){
        if (localDate==null){
            return null;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * localDatetimeToDate
     * @param localDateTime
     * @return
     */
    public static Date localDatetimeToDate(LocalDateTime localDateTime){
        if (localDateTime==null){
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * dateToLocalDateTime
     * @param date
     * @return
     */
    public static  LocalDateTime dateToLocalDateTime(Date date){
        if(date==null){
            return null;
        }
       return  Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 判断给定日期是否今天
     * @param localDate
     * @return
     */
    public static boolean isToday(LocalDate localDate) {
        return localDate != null ? LocalDate.now().isEqual(localDate) : false;
    }

    public static LocalTime dateToLocatime(Date date){
        // 将Date转换为Instant
        Instant instant = date.toInstant();

        // 选择一个时区，例如'Asia/Shanghai'
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        // 使用ZoneId将Instant转换为ZonedDateTime
        // 然后从ZonedDateTime中提取LocalTime
        LocalTime localTime = instant.atZone(zoneId).toLocalTime();
        return localTime;
    }

    public static Integer calculateDaysBetween(LocalDate startDate, LocalDate endDate) {
        long daysLong = Optional.ofNullable(startDate)
                .flatMap(start -> Optional.ofNullable(endDate)
                        .map(end -> ChronoUnit.DAYS.between(start, end)))
                .orElse(0L);
        return Integer.valueOf(String.valueOf(daysLong));
    }
}
