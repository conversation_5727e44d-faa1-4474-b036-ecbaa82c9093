package com.lds.oneplanning.common.utils;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import java.util.concurrent.ScheduledThreadPoolExecutor;

public class ThreadPoolUtil {
	
	private ThreadPoolUtil() {}  
	
	private static ScheduledThreadPoolExecutor pool = null;

	public static ScheduledThreadPoolExecutor instance(){
		if (pool == null){
			synchronized (ThreadPoolUtil.class){
				pool = instance(20);
			}
		}
		return pool;
	}

	private static ScheduledThreadPoolExecutor instance(int poolSize){
		return new ScheduledThreadPoolExecutor(poolSize, new BasicThreadFactory.Builder().namingPattern("onePlanning-pool-%d").daemon(true).build());
	}
	
}
