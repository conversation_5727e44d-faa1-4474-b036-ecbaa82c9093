package com.lds.oneplanning.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 订单相关计算工具类
 */
public class OrderArithUtil {

    //默认除法运算精度
    private static final int DEF_DIV_SCALE = 10;


    public static float floatSubtract(float a, float b) {
        BigDecimal b1 = new BigDecimal(Float.toString(a));
        BigDecimal b2 = new BigDecimal(Float.toString(b));
        if (b1.compareTo(b2) < 0) {
            return 0F;
        }
        return b1.subtract(b2).floatValue();
    }

    public static float floatMultiply(float a, float b) {
        BigDecimal b1 = new BigDecimal(Float.toString(a));
        BigDecimal b2 = new BigDecimal(Float.toString(b));
        return b1.multiply(b2).floatValue();
    }

    public static float floatDivide(float a, float b) {
        return floatDivide(a, b, DEF_DIV_SCALE);
    }

    public static float floatDivide(float a, float b, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(Float.toString(a));
        BigDecimal b2 = new BigDecimal(Float.toString(b));
        return b1.divide(b2, scale, RoundingMode.HALF_UP).floatValue();
    }

    public static float floatAdd(float a, float b) {
        BigDecimal b1 = new BigDecimal(Float.toString(a));
        BigDecimal b2 = new BigDecimal(Float.toString(b));
        return b1.add(b2).floatValue();
    }

    public static float floatDivide(float a, int b) {
        return floatDivide(a, b, DEF_DIV_SCALE);
    }

    public static float floatDivide(float a, int b, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(Float.toString(a));
        BigDecimal b2 = new BigDecimal(Integer.toString(b));
        return b1.divide(b2, scale, RoundingMode.HALF_UP).floatValue();
    }

    public static float floatDivide(int a, int b) {
        return floatDivide(a, b, DEF_DIV_SCALE);
    }

    public static float floatDivide(int a, int b, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(Integer.toString(a));
        BigDecimal b2 = new BigDecimal(Integer.toString(b));
        return b1.divide(b2, scale, RoundingMode.HALF_UP).floatValue();
    }

    public static float floatDivide(int a, float b) {
        BigDecimal b1 = new BigDecimal(Integer.toString(a));
        BigDecimal b2 = new BigDecimal(Float.toString(b));
        return b1.divide(b2, DEF_DIV_SCALE, RoundingMode.HALF_UP).floatValue();
    }
}