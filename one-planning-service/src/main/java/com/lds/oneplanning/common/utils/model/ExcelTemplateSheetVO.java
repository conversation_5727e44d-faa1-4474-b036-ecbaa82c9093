package com.lds.oneplanning.common.utils.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * excel-sheet模板
 *
 * <AUTHOR>
 * @since 20220507
 */
@Data
public class ExcelTemplateSheetVO {

    @ApiModelProperty(value = "sheet页名称")
    private String sheetName;

    @ApiModelProperty(value = "导入说明")
    private List<String> explains;

    @ApiModelProperty(value = "表头")
    private List<ExcelTitle> titles;

    @ApiModelProperty(value = "这是行数据")
    private List<List<Object>> data;

    @ApiModelProperty(value = "哪列需要隐藏 不设置不生效")
    private Integer hiddenRowIndex;

    @ApiModelProperty(value = "哪列需要隐藏 不设置不生效")
    private List<Integer> hiddenRowIndexList;

    @Data
    public static class ExcelTitle {

        @ApiModelProperty(value = "列名")
        private String titleName;

        @ApiModelProperty(value = "是否允许自定义下拉框内容")
        private Boolean customOptions = false;

        @ApiModelProperty(value = "表头标注")
        private String remark;

        @ApiModelProperty(value = "下拉选项")
        private List<String> options;
    }
}
