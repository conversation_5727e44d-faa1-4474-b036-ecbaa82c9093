package com.lds.oneplanning.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.lds.basic.account.user.api.UserApi2;
import com.lds.basic.account.user.dto.UserDto;
import com.lds.basic.account.user.enums.UserStatusEnum;
import com.lds.oneplanning.common.service.IBasicUserService;
import com.lds.oneplanning.esb.datafetch.model.EsbGetHbData;
import com.lds.oneplanning.esb.datafetch.model.EsbGetPoGroupData;
import com.lds.oneplanning.esb.datafetch.service.IEsbDataFetchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BasicUserServiceImpl implements IBasicUserService {

    @Autowired
    private UserApi2 userApi2;

    @Autowired
    private IEsbDataFetchService esbDataFetchService;

    @Override
    public Map<String, String> batchGetLoginNamesByJobNos(Collection<String> jobNos) {
        if (CollectionUtils.isEmpty(jobNos)) {
            return Collections.emptyMap();
        }
        UserDto userDto = new UserDto();
        userDto.setJobNos(jobNos);
        userDto.setUserStatus(UserStatusEnum.NORMAL.getCode());
        List<UserDto> userDtoList = userApi2.findDto(userDto);
        if (CollectionUtils.isEmpty(userDtoList)) {
            return Collections.emptyMap();
        }
        // 过滤JobNo为空的用户,并输出异常日志
        userDtoList.stream()
                .filter(user -> user.getJobNo() == null)
                .forEach(user -> log.error("JobNo为空的用户: {}", user));
        // 过滤JobNo不为空的用户,返回Map<工号, 登录名>
        return userDtoList.stream()
                .filter(user -> user.getJobNo() != null)
                .collect(Collectors.toMap(UserDto::getJobNo, UserDto::getLoginName));
    }

    @Override
    public Map<String, String> batchGetLoginNamesByPoGroups(Collection<String> poGroups) {
        if (CollectionUtils.isEmpty(poGroups)) {
            return Collections.emptyMap();
        }
        List<EsbGetPoGroupData> esbGetPoGroupDataList = poGroups.stream()
                .map(poGroup -> {
                    EsbGetPoGroupData data = new EsbGetPoGroupData();
                    data.setEKGRP(poGroup);
                    return data;
                })
                .collect(Collectors.toList());
        List<EsbGetPoGroupData> poGroupDataRspList = esbDataFetchService.getPoGroupData(esbGetPoGroupDataList);
        if (CollectionUtils.isEmpty(poGroupDataRspList)) {
            return Collections.emptyMap();
        }
        // 获取采购组对应的工号列表,且不为空,返回Map<采购组, 工号>
        Map<String, String> poGroupJobNoMap = poGroupDataRspList.stream()
                .filter(poGroupData -> poGroupData.getEKTEL() != null)
                .collect(Collectors.toMap(EsbGetPoGroupData::getEKGRP, EsbGetPoGroupData::getEKTEL));
        if (CollUtil.isEmpty(poGroupJobNoMap)) {
            return Collections.emptyMap();
        }
        // 获取工号对应的登录名,返回Map<工号, 登录名>
        Map<String, String> jobNoLoginNameMap = batchGetLoginNamesByJobNos(poGroupJobNoMap.values());
        // 合并两个Map,返回Map<采购组, 登录名>
        log.info("poGroupJobNoMap:{}, jobNoLoginNameMap:{}", poGroupJobNoMap, jobNoLoginNameMap);
        poGroupJobNoMap.entrySet().stream()
                .filter(entry -> jobNoLoginNameMap.containsKey(entry.getValue()))
                .forEach(entry -> poGroupJobNoMap.put(entry.getKey(), jobNoLoginNameMap.get(entry.getValue())));
        return poGroupJobNoMap;
    }

    @Override
    public Map<String, String> batchGetLeaderLoginNames(Collection<String> jobNos) {
        if (CollectionUtils.isEmpty(jobNos)) {
            return Collections.emptyMap();
        }
        // 获取工号对应的上级工号
        List<EsbGetHbData> esbGetHbDataList = esbDataFetchService.getHbData(jobNos);
        if (CollectionUtils.isEmpty(esbGetHbDataList)) {
            return Collections.emptyMap();
        }
        esbGetHbDataList.stream()
                .filter(data -> data.getLeader() == null)
                .forEach(data -> log.error("工号{}没有上级领导", data.getEmpId()));
        // <工号，上级领导工号>
        Map<String, String> jobNoLeaderMap = esbGetHbDataList.stream()
                .filter(data -> MapUtils.isNotEmpty(data.getLeader()))
                .collect(Collectors.toMap(
                        EsbGetHbData::getEmpId,
                        data -> data.getLeader().keySet().iterator().next(),
                        (existing, replacement) -> existing
                ));
        // 获取上级领导工号
        Collection<String> leaderJobNos = jobNoLeaderMap.values();
        if (CollectionUtils.isEmpty(leaderJobNos)) {
            return Collections.emptyMap();
        }
        // <上级领导工号，上级领导登录名>
        Map<String, String> leaderLoginNameMap = batchGetLoginNamesByJobNos(leaderJobNos);
        if (MapUtils.isEmpty(leaderLoginNameMap)) {
            return Collections.emptyMap();
        }
        // <工号, 上级领导登录名>
        return jobNoLeaderMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> leaderLoginNameMap.get(entry.getValue())
                ));
    }

    @Override
    public Map<String, UserDto> batchGetUserInfoByJobNos(Collection<String> jobNos) {
        if (CollectionUtils.isEmpty(jobNos)) {
            return Collections.emptyMap();
        }
        UserDto userDto = new UserDto();
        userDto.setJobNos(jobNos);
        userDto.setUserStatus(UserStatusEnum.NORMAL.getCode());
        List<UserDto> userDtoList = userApi2.findDto(userDto);
        if (CollectionUtils.isEmpty(userDtoList)) {
            return Collections.emptyMap();
        }
        return userDtoList.stream().collect(Collectors.toMap(UserDto::getJobNo, user -> user));
    }
}
