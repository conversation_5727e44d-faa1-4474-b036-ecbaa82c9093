package com.lds.oneplanning.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Slf4j
public class RealIpUtils {
    private RealIpUtils() {

    }

    public static final String X_REAL_IP = "X-Real-IP";
    public static final String X_FORWARDED_FOR = "X-Forwarded-For";
    public static final String PROXY_CLIENT_IP = "Proxy-Client-IP";
    public static final String WL_PROXY_CLIENT_IP = "WL-Proxy-Client-IP";
    public static final String HTTP_CLIENT_IP = "HTTP_CLIENT_IP";
    public static final String HTTP_X_FORWARDED_FOR = "HTTP_X_FORWARDED_FOR";

    public static String fetchRealIp(HttpServletRequest request) {
        try {
            String ip = request.getHeader(X_FORWARDED_FOR);
            if (isValidIp(ip)) {
                // 处理多个代理IP的情况（第一个有效IP）
                return ip.split(",")[0].trim();
            }

            ip = request.getHeader(X_REAL_IP);
            if (isValidIp(ip)) return ip;

            ip = request.getHeader(PROXY_CLIENT_IP);
            if (isValidIp(ip)) return ip;

            ip = request.getHeader(WL_PROXY_CLIENT_IP);
            if (isValidIp(ip)) return ip;

            ip = request.getHeader(HTTP_CLIENT_IP);
            if (isValidIp(ip)) return ip;

            ip = request.getHeader(HTTP_X_FORWARDED_FOR);
            if (isValidIp(ip)) return ip;

            return request.getRemoteAddr();
        } catch (Exception e) {
            // ignore
        }
        return "";
    }

    private static boolean isValidIp(String ip) {
        return !StringUtils.isEmpty(ip) && !"unknown".equalsIgnoreCase(ip);
    }
}
