package com.lds.oneplanning.common.service.impl;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * LocalDate日期转成字符串
 */
public class LocalDateStringConverter implements Converter<LocalDate> {

    @Override
    public WriteCellData<?> convertToExcelData(LocalDate value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(Objects.isNull(value) ? "" : value.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
    }
}
