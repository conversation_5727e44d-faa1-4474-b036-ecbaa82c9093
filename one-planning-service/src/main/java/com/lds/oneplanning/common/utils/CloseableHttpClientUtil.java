package com.lds.oneplanning.common.utils;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

@SuppressWarnings("all")
/**
 * Describetion
 * <AUTHOR>
 * @Date : 2020/11/02
 */
public class CloseableHttpClientUtil {
    protected static final Log LOGGER = LogFactory.getLog(CloseableHttpClientUtil.class);

    private static final String UTF_8 = "UTF-8";
    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36";
    private static final String CONTENT_TYPE = "application/json";

//    超时时间改为60秒 改为5分钟
    private static Integer SOCKET_TIME_OUT=  300*1000;
    private static Integer CONNECT_TIME_OUT= 300*1000;

    private CloseableHttpClientUtil(){}

    /**
     * @param fullUrl
     */
    public static String  doGet(String fullUrl) {
        String res="";
        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpGet get = new HttpGet(fullUrl);

        try {
            get.addHeader("User-Agent", USER_AGENT);
            LOGGER.info(String.format("get请求 入参%s", JSON.toJSON(get)));
            HttpResponse response = client.execute(get);
            res = EntityUtils.toString(response.getEntity());
            LOGGER.info(String.format("get返回结果 %s", res));
             client.close();
             return  res;
        } catch (IOException e) {
            LOGGER.error("IO异常",e);
            res=e.getMessage();
        }
        return res;
    }

    /**
     * doGet 带头部
     * @param fullUrl
     * @param headerMap
     * @return
     */
    public static String  doGet(String fullUrl,Map<String,String> headerMap) {
        LocalDateTime start = LocalDateTime.now() ;
        String res="";
        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpGet get = new HttpGet(fullUrl);

        if (headerMap != null) {
            Iterator<Map.Entry<String, String>>  iterator=headerMap.entrySet().iterator();
            while (iterator.hasNext()){
                Map.Entry<String, String> entry= iterator.next();
                get.addHeader(entry.getKey(),entry.getValue());
            }
        }
        try {
            get.addHeader("User-Agent", USER_AGENT);
            LOGGER.info(String.format("get请求 入参%s", JSON.toJSON(get)));
            HttpResponse response = client.execute(get);
            res = EntityUtils.toString(response.getEntity());
            LOGGER.info(String.format("get返回结果 %s", res));
            client.close();
            return  res;
        } catch (IOException e) {
            LOGGER.error("IO异常",e);
            res=e.getMessage();
        }finally {
        }
        return res;
    }

    /**
     *  不带头部信息
     * @param fullUrl
     * @param paramMap
     * @return
     */
    public static String doPost(String fullUrl,Map paramMap) {
       return CloseableHttpClientUtil.doPost(fullUrl, paramMap,null,null);
    }

    /**
     *  带头部信息
     * @param fullUrl
     * @param paramMap
     * @param headerMap
     * @return
     */
    public static String doPost(String fullUrl,Object paramMap,Map<String,String> headerMap) {
        String res =  CloseableHttpClientUtil.doPost(fullUrl, paramMap,headerMap,null);
        return res;
    }


    public static String  doPost(String fullUrl,Object paramMap,Map<String,String> headerMap, CloseableHttpClient closeableHttpClient) {
        String res="";
        if (closeableHttpClient == null) {
            HttpClientBuilder httpClientBuilder=HttpClientBuilder.create();
            RequestConfig defaultRequestConfig=RequestConfig.custom().setSocketTimeout(SOCKET_TIME_OUT).setConnectTimeout(CONNECT_TIME_OUT).build();
            httpClientBuilder.setDefaultRequestConfig(defaultRequestConfig);
            closeableHttpClient = httpClientBuilder.build();
        }
        HttpPost post = new HttpPost(fullUrl);
        post.addHeader("User-Agent", USER_AGENT);
        post.addHeader("Content-Type", CONTENT_TYPE);
        try {

            if (headerMap != null) {
                Iterator<Map.Entry<String, String>>  iterator=headerMap.entrySet().iterator();
                while (iterator.hasNext()){
                    Map.Entry<String, String> entry= iterator.next();
                    post.addHeader(entry.getKey(),entry.getValue());
                }
            }
            StringEntity stringEntity=new StringEntity(JSON.toJSONString(paramMap), UTF_8);
            post.setEntity(stringEntity);
            if(MapUtils.isNotEmpty(headerMap)){
                LOGGER.info(String.format("post请求headerMap参数：%s", JSON.toJSON(headerMap)));
            }
            if(Objects.nonNull(paramMap)){
                LOGGER.info(String.format("post请求paramMap参数：%s", JSON.toJSON(paramMap)));
            }
            HttpResponse response = closeableHttpClient.execute(post);
            //返回也需要编码
             res = EntityUtils.toString(response.getEntity(),UTF_8);
            LOGGER.info(String.format("post请求 结果%s", res));
            /**
             * 统一关闭
             */
            closeableHttpClient.close();
            return res;
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("编码不支持",e);
            res=e.getMessage();
        } catch (ClientProtocolException e) {
            LOGGER.error("客户端协议出错",e);
            res=res+e.getMessage();
        } catch (IOException e) {
            LOGGER.error("IO异常",e);
            res=res+e.getMessage();
        }
        return  res;
    }

    public static String doPost(String fullUrl, Map paramMap, Map<String, String> headerMap, Integer socketTime, Integer connectTime) {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(socketTime).setConnectTimeout(connectTime).build();
        httpClientBuilder.setDefaultRequestConfig(defaultRequestConfig);
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        return doPost(fullUrl,paramMap,headerMap,closeableHttpClient);
    }

    /**
     * post 基础校验类型  提供校验功能
     * @param fullUrl
     * @param paramMap
     * @param user
     * @param password
     * @return
     */
    public static String doPostWithBasicAuth(String fullUrl,Map paramMap,String user,String password) {
        // 创建HttpClientBuilder
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        RequestConfig defaultRequestConfig=RequestConfig.custom().setSocketTimeout(SOCKET_TIME_OUT).setConnectTimeout(CONNECT_TIME_OUT).build();
        httpClientBuilder.setDefaultRequestConfig(defaultRequestConfig);
        // 设置BasicAuth
        CredentialsProvider provider = new BasicCredentialsProvider();
        AuthScope scope = new AuthScope(AuthScope.ANY_HOST, AuthScope.ANY_PORT, AuthScope.ANY_REALM);
        // Create credential pair，在此处填写用户名和密码
        UsernamePasswordCredentials credentials = new UsernamePasswordCredentials(user, password);
        provider.setCredentials(scope, credentials);
        httpClientBuilder.setDefaultCredentialsProvider(provider);
        CloseableHttpClient client = httpClientBuilder.build();

       return CloseableHttpClientUtil.doPost(fullUrl, paramMap,null,client);
    }

}
