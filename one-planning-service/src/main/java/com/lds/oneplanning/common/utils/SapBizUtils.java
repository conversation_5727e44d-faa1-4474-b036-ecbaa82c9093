package com.lds.oneplanning.common.utils;

/**
 * @Description:
 * @Author: zhua<PERSON><PERSON><PERSON><PERSON>
 * @Email: zhuang<PERSON><PERSON><PERSON>@leedarson.com
 * @Date: 2025/5/26 9:16
 */
public class SapBizUtils {

    private SapBizUtils() {
    }

    /**
     *  处理外部接口数据不统一问题
     * @param input
     * @return
     */
    public static String remove2ZeroPrefix(String input) {
        if (input != null && input.startsWith("00")) {
            return input.substring(2);
        }
        return input;
    }

    public static float roundToTwoDecimalPlaces(float value) {
        return Math.round(value * 100) / 100.0f;
    }

}
