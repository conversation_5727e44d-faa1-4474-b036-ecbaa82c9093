package com.lds.oneplanning.common.utils;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lds.oneplanning.common.utils.model.ExcelData;
import com.lds.oneplanning.common.utils.model.ExcelTemplateSheetVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.IntStream;

@Slf4j
public class ExcelUtils {

	public static final int INT_ZERO = 0;

	private ExcelUtils(){}

	public static final String XLS = "xls";
	public static final String OFFICE_EXCEL_2003_POSTFIX = XLS;
	public static final String XLSX = "xlsx";
	public static final String OFFICE_EXCEL_2010_POSTFIX = XLSX;
	public static final String EMPTY = "";
	public static final String POINT = ".";
	public static final String NOT_EXCEL_FILE = " : Not the Excel file!";
	public static final String PROCESSING = "Processing...";
	private static final int SHEET_NUM = 6;
	public static final int INT_FOUR = 4;
	public static final int INT_FORE = INT_FOUR;
	public static final String H_MM = "h:mm";
	public static final int INT_STYLE = 58;
	public static final String GENERAL = "General";
	public static final String NAME = "name";
	public static final String DEPLOY = "deploy";
	public static final String BUSINESS = "business";
	public static final String PARENT = "parent";
	public static final String SPACE_TYPE = "spaceType";
	public static final String SHEET = "sheet_";

	/**
	 * read the Excel file
	 * @param path the path of the Excel file
	 * @return
	 * @throws IOException
	 */
	public static Map<String, Object> readExcel(String path) throws IOException {
	    if (path == null || EMPTY.equals(path)) {
	        return null;
	    } else {
	        String postfix = getPostfix(path);
	        return readExcelResult(postfix, path, "");
	    }
	}

	public static Map<String, Object> readExcel(String path, String target) throws IOException {
	    if (path == null || EMPTY.equals(path)) {
	        return null;
	    } else {
        	String postfix = getPostfix(path, target);
        	return readExcelResult(postfix, path, target);
	    }
	}
	
	public static Map<String, Object> readExcelResult(String postfix, String path, String target) throws IOException {
		if (!EMPTY.equals(postfix)) {
            if (OFFICE_EXCEL_2003_POSTFIX.equals(postfix)) {
                return readXls(path, target);
            } else if (OFFICE_EXCEL_2010_POSTFIX.equals(postfix)) {
                return readXlsx(path, target);
            }
        } else {
            log.info(path + NOT_EXCEL_FILE);
        }
		return null;
	}
	
	public static String getPostfix(String path, String target){
		log.info("getPostfix ={}",target);
		try{
			BufferedInputStream bis = new BufferedInputStream(readUrlFile(path));
			if (POIFSFileSystem.hasPOIFSHeader(bis)) {  
				return OFFICE_EXCEL_2003_POSTFIX;
			} else {
				return OFFICE_EXCEL_2010_POSTFIX;
			}
		}catch(Exception e){
			log.error("getPostfix msg={}",e.getMessage(),e);
		}
		return null;
	}

	/**
	 * Read the Excel 2010
	 * @param path the path of the excel file
	 * @return
	 * @throws IOException
	 */
	public static Map<String, Object> readXlsx(String path, String target) throws IOException {
	    log.info(PROCESSING + path);
		Map<String, Object> result = Maps.newHashMap();
		XSSFWorkbook xssfWorkbook=null;
		InputStream is =null;
		try {
			is = StringUtils.isNoneBlank(target)?readUrlFile(path):new FileInputStream(path);
			xssfWorkbook = new XSSFWorkbook(is);
			// Read the Sheet
			for (int numSheet = INT_ZERO; numSheet < xssfWorkbook.getNumberOfSheets(); numSheet++) {
				XSSFSheet xssfSheet = xssfWorkbook.getSheetAt(numSheet);
				if (xssfSheet == null) {
					continue;
				}
				List<Map<String, Object>> sheetList = new ArrayList<Map<String, Object>>();
				Map<String, Object> cellData = null;
				// Read the Row
				for (int rowNum = 1; rowNum <= xssfSheet.getLastRowNum(); rowNum++) {
					XSSFRow xssfRow = xssfSheet.getRow(rowNum);
					if (xssfRow != null) {
						cellData = new HashMap<String, Object>();
						XSSFCell name = xssfRow.getCell(INT_ZERO);
						XSSFCell type = xssfRow.getCell(1);
						XSSFCell parent = xssfRow.getCell(2);

						cellData.put("name", getValue(name));
						cellData.put("type", getValue(type));
						cellData.put(PARENT, getValue(parent));

						sheetList.add(cellData);
					}
				}
				result.put(SHEET+numSheet, sheetList);
			}
		}catch (Exception e){
			throw e;
		}

	    return result;
	}

	/**
	 * Read the Excel 2003-2007
	 * @param path the path of the Excel
	 * @return
	 * @throws IOException
	 */
	public static Map<String, Object> readXls(String path, String target) throws IOException {
	    log.info(PROCESSING + path);
		Map<String, Object> result = Maps.newHashMap();
		HSSFWorkbook hssfWorkbook =null;
		try {
			InputStream is = StringUtils.isNoneBlank(target)?readUrlFile(path):new FileInputStream(path);
			hssfWorkbook = new HSSFWorkbook(is);
			// Read the Sheet
			for (int numSheet = INT_ZERO; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
				HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(numSheet);
				if (hssfSheet == null) {
					continue;
				}
				List<Map<String, Object>> sheetList = new ArrayList<Map<String, Object>>();
				Map<String, Object> cellData = null;
				// Read the Row
				for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
					HSSFRow hssfRow = hssfSheet.getRow(rowNum);
					if (hssfRow != null) {
						cellData = new HashMap<String, Object>();
						HSSFCell name = hssfRow.getCell(INT_ZERO);
						HSSFCell type = hssfRow.getCell(1);
						HSSFCell parent = hssfRow.getCell(2);

						cellData.put("name", getValue(name));
						cellData.put("type", getValue(type));
						cellData.put(PARENT, getValue(parent));

						sheetList.add(cellData);
					}
				}
				result.put(SHEET+numSheet, sheetList);
			}
		}catch (Exception e){
	    	throw e;
		}

	    return result;
	}

	public static  Workbook getWorkBook(MultipartFile file) {
		//获得文件名
		String fileName = file.getOriginalFilename();
		//创建Workbook工作薄对象，表示整个excel
		Workbook workbook = null;
		try {
			//获取excel文件的io流
			InputStream is = file.getInputStream();
			//根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象
			if(fileName.endsWith(XLS)){
				//2003
				workbook = new HSSFWorkbook(is);
			}else if(fileName.endsWith(XLSX)){
				//2007 及2007以上
				workbook = new XSSFWorkbook(is);
			}
		} catch (IOException e) {
			log.error("getWorkBook msg={}",e.getMessage(),e);
		}
		return workbook;
	}


	@SuppressWarnings("static-access")
	private static String getValue(XSSFCell xssfRow) {
	    if (xssfRow.getCellType() == INT_FOUR) {
	        return String.valueOf(xssfRow.getBooleanCellValue());
	    } else if (xssfRow.getCellType() == INT_ZERO) {
	        return String.valueOf(xssfRow.getNumericCellValue());
	    } else {
	        return String.valueOf(xssfRow.getStringCellValue());
	    }
	}

	@SuppressWarnings("static-access")
	private static String getValue(HSSFCell hssfCell) {
		if (hssfCell != null) {
			if (hssfCell.getCellType() == INT_FOUR) {
				return String.valueOf(hssfCell.getBooleanCellValue());
			} else if (hssfCell.getCellType() == INT_ZERO) {
				return String.valueOf(hssfCell.getNumericCellValue());
			} else {
				return String.valueOf(hssfCell.getStringCellValue());
			}
		} else {
			return String.valueOf("");
		}
	}

	/**
	 * get postfix of the path
	 * @param path
	 * @return
	 */
	public static String getPostfix(String path) {
	    if (path == null || EMPTY.equals(path.trim())) {
	        return EMPTY;
	    }
	    if (path.contains(POINT)) {
	        return path.substring(path.lastIndexOf(POINT) + 1, path.length());
	    }
	    return EMPTY;
	}
	
	/**
	 * 读取网络文件
	 */
	public static InputStream readUrlFile(String path) {  
        URL url = null;  
        InputStream is =null;  
        try {  
            url = new URL(path);  
			//利用HttpURLConnection对象,我们可以从网络中获取网页数据.
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setDoInput(true);  
            conn.connect();
			//得到网络返回的输入流
            is = conn.getInputStream();
              
        } catch (IOException e) {
			log.error("readUrlFile msg={}",e.getMessage(),e);
		}
        return is;  
    }

	public static void exportExcel(HttpServletResponse response, String fileName, ExcelData data) throws Exception {
		// 告诉浏览器用什么软件可以打开此文件
		response.setHeader("content-Type", "application/vnd.ms-excel");
		// 下载文件的默认名称
		response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode(fileName, "utf-8"));
		exportExcel(data, response.getOutputStream());
	}

	public static void exportExcel(ExcelData data, OutputStream out) throws Exception {
		try(HSSFWorkbook wb = new HSSFWorkbook()) {
			String sheetName = data.getName();
			if (null == sheetName) {
				sheetName = "Sheet1";
			}
			HSSFSheet sheet = wb.createSheet(sheetName);
			writeExcel(wb, sheet, data);

			wb.write(out);
		} catch(Exception e){
			log.error("exportExcel msg={}",e.getMessage(),e);
		}finally{
			//此处需要关闭 wb 变量
			out.close();
		}
	}

	private static void writeExcel(HSSFWorkbook wb, Sheet sheet, ExcelData data) {

		int rowIndex = writeTitlesToExcel(wb, sheet, data.getTitles());
		writeRowsToExcel(wb, sheet, data.getRows(), rowIndex);
		autoSizeColumns(sheet, data.getTitles().size() + 1);

	}

	private static int writeTitlesToExcel(HSSFWorkbook wb, Sheet sheet, List<String> titles) {
		int rowIndex = INT_ZERO;
		int colIndex = INT_ZERO;

		Font titleFont = wb.createFont();
		titleFont.setFontName("simsun");
		titleFont.setColor(IndexedColors.BLACK.index);

		HSSFCellStyle titleStyle = wb.createCellStyle();
		titleStyle.setAlignment(titleStyle.getAlignmentEnum());
		titleStyle.setVerticalAlignment(titleStyle.getVerticalAlignmentEnum());
		titleStyle.setFillForegroundColor(titleStyle.getFillBackgroundColor());
		titleStyle.setFillPattern(titleStyle.getFillPatternEnum());
		titleStyle.setFont(titleFont);
		setBorder(titleStyle, BorderStyle.THIN);

		Row titleRow = sheet.createRow(rowIndex);

		for (String field : titles) {
			Cell cell = titleRow.createCell(colIndex);
			cell.setCellValue(field);
			cell.setCellStyle(titleStyle);
			colIndex++;
		}

		rowIndex++;
		return rowIndex;
	}

	private static int writeRowsToExcel(HSSFWorkbook wb, Sheet sheet, List<List<Object>> rows, int rowIndex) {
		int colIndex;

		Font dataFont = wb.createFont();
		dataFont.setFontName("simsun");
		dataFont.setColor(IndexedColors.BLACK.index);

		HSSFCellStyle dataStyle = wb.createCellStyle();
		dataStyle.setAlignment(dataStyle.getAlignmentEnum());
		dataStyle.setVerticalAlignment(dataStyle.getVerticalAlignmentEnum());
		dataStyle.setFont(dataFont);
		setBorder(dataStyle, BorderStyle.THIN);

		for (List<Object> rowData : rows) {
			Row dataRow = sheet.createRow(rowIndex);
			colIndex = INT_ZERO;

			for (Object cellData : rowData) {
				Cell cell = dataRow.createCell(colIndex);
				if (cellData != null) {
					cell.setCellValue(cellData.toString());
				} else {
					cell.setCellValue("");
				}

				cell.setCellStyle(dataStyle);
				colIndex++;
			}
			rowIndex++;
		}
		return rowIndex;
	}

	private static void autoSizeColumns(Sheet sheet, int columnNumber) {

		for (int i = INT_ZERO; i < columnNumber; i++) {
			int orgWidth = sheet.getColumnWidth(i);
			sheet.autoSizeColumn(i, true);
			int newWidth = (int) (sheet.getColumnWidth(i) + 100);
			newWidth = newWidth >= 255 ? 255 : newWidth;
			if (newWidth > orgWidth) {
				sheet.setColumnWidth(i, newWidth);
			} else {
				sheet.setColumnWidth(i, orgWidth);
			}
		}
	}

	private static void setBorder(HSSFCellStyle style, BorderStyle border) {
		style.setBorderTop(border);
		style.setBorderLeft(border);
		style.setBorderRight(border);
		style.setBorderBottom(border);
	}

	/**
	 * 解析excel
	 * @param file
	 * @return
	 */
	public static List<Map<String, Object>> resolveExcelMethod(MultipartFile file) throws IOException{
		//获得Workbook工作薄对象
		Workbook workbook = getWorkBook(file);
		//创建返回对象，把每行中的值作为一个数组，所有行作为一个map返回
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		if(workbook != null){
			for(int sheetNum = INT_ZERO; sheetNum < workbook.getNumberOfSheets(); sheetNum++){
				//获得当前sheet工作表
				Sheet sheet = workbook.getSheetAt(sheetNum);
				if(sheet == null){
					continue;
				}
				//获得当前sheet的开始行
				int firstRowNum  = sheet.getFirstRowNum();
				//获得当前sheet的结束行
				int lastRowNum = sheet.getPhysicalNumberOfRows();
				//循环所有行
				for(int rowNum = firstRowNum;rowNum < lastRowNum;rowNum++){
					Map<String, Object> cellData =  Maps.newHashMap();
					//获得当前行
					Row row = sheet.getRow(rowNum);
					if(row == null){
						continue;
					}
					//获得当前行的开始列
					int firstCellNum = row.getFirstCellNum();
					//获得当前行的列数
					int lastCellNum = row.getLastCellNum();
					//循环当前行
					for(int cellNum = firstCellNum; cellNum < lastCellNum;cellNum++){
						Cell cell = row.getCell(cellNum);
						String a = getCellValue(cell);
						cellData.put(cellNum+"", a);
					}
					list.add(cellData);
				}
			}
		}
		return list;
	}

	public static String getCellValue(Cell cell){
		String cellValue = "";
		if(cell == null){
			return cellValue;
		}
		//判断数据的类型
		switch (cell.getCellTypeEnum()){
			case NUMERIC:
				cellValue = stringDateProcess(cell);
				break;
			case STRING:
				cellValue = String.valueOf(cell.getStringCellValue());
				break;
			case BOOLEAN:
				cellValue = String.valueOf(cell.getBooleanCellValue());
				break;
			case FORMULA:
				cellValue = String.valueOf(cell.getCellFormula());
				break;
			case BLANK:
				cellValue = "";
				break;
			case ERROR:
				cellValue = "非法字符";
				break;
			default:
				cellValue = "未知类型";
				break;
		}
		return cellValue;
	}

	/**
	 * 时间格式处理
	 * @return
	 * <AUTHOR> Xin Nan
	 * @data 2017年11月27日
	 */
	public static String stringDateProcess(Cell cell){
		String result ;
		if (HSSFDateUtil.isCellDateFormatted(cell)) {
			SimpleDateFormat sdf = null;
			if (cell.getCellStyle().getDataFormat() == HSSFDataFormat.getBuiltinFormat(H_MM)) {
				sdf = new SimpleDateFormat("HH:mm");
			} else {// 日期
				sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
			}
			Date date = cell.getDateCellValue();
			result = sdf.format(date);
		} else if (cell.getCellStyle().getDataFormat() == INT_STYLE) {
			// 处理自定义日期格式：m月d日(通过判断单元格的格式id解决，id的值是58)
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
			double value = cell.getNumericCellValue();
			Date date = DateUtil
					.getJavaDate(value);
			result = sdf.format(date);
		} else {
			double value = cell.getNumericCellValue();
			CellStyle style = cell.getCellStyle();
			DecimalFormat format = new DecimalFormat();
			String temp = style.getDataFormatString();
			// 单元格设置成常规
			if (GENERAL.equals(temp)) {
				format.applyPattern("#");
			}
			result = format.format(value);
		}

		return result;
	}

	public static List<String[]> resolveExcel(MultipartFile file) throws IOException{
		//获得Workbook工作薄对象
		Workbook workbook = getWorkBook(file);
		//创建返回对象，把每行中的值作为一个数组，所有行作为一个map返回
		List<String[]> list = new ArrayList<String[]>();
		if(workbook != null){
			for(int sheetNum = INT_ZERO; sheetNum < workbook.getNumberOfSheets(); sheetNum++){
				//获得当前sheet工作表
				Sheet sheet = workbook.getSheetAt(sheetNum);
				if(sheet == null){
					continue;
				}
				//获得当前sheet的开始行
				int firstRowNum  = sheet.getFirstRowNum();
				//获得当前sheet的结束行
				int lastRowNum = sheet.getPhysicalNumberOfRows();
				//循环除了第一行的所有行
				for(int rowNum = firstRowNum+1;rowNum < lastRowNum;rowNum++){
					//获得当前行
					Row row = sheet.getRow(rowNum);
					if(row == null){
						continue;
					}
					//获得当前行的开始列
					int firstCellNum = row.getFirstCellNum();
					//获得当前行的列数
					int lastCellNum = row.getLastCellNum();
					String[] cells = new String[row.getLastCellNum()];
					//循环当前行
					for(int cellNum = firstCellNum; cellNum < lastCellNum;cellNum++){
						Cell cell = row.getCell(cellNum);
						cells[cellNum] = getCellValue(cell);
					}
					list.add(cells);
				}
			}
		}
		return list;
	}

	/**
	 * 添加序号
	 * @param startIndex
	 * @param explains
	 */
	public static void addSerialNumber(int startIndex,List<String> explains){
		IntStream.range(startIndex, explains.size()).forEach(i -> explains.set(i, (i - startIndex + 1) + "、" + explains.get(i)));
	}



	/**
	 * 获取列定义
	 * @param titleName
	 * @param options
	 * @return
	 */
	public static ExcelTemplateSheetVO.ExcelTitle getColumnTitle(String titleName, List<String> options) {
		ExcelTemplateSheetVO.ExcelTitle column = new ExcelTemplateSheetVO.ExcelTitle();
		column.setTitleName(titleName);
		column.setOptions(options == null ? Lists.newArrayList() : options);
		return column;
	}


}
