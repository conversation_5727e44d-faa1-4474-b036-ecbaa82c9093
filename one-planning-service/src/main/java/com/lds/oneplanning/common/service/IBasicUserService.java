package com.lds.oneplanning.common.service;

import com.lds.basic.account.user.dto.UserDto;

import java.util.Collection;
import java.util.Map;

public interface IBasicUserService {

    /**
     * 根据工号查询登录名
     * @param jobNos 工号列表
     * @return Map<工号, 登录名>
     */
    Map<String, String> batchGetLoginNamesByJobNos(Collection<String> jobNos);

    /**
     * 根据采购组查询登录名
     * @param poGroups 采购组列表
     * @return Map<采购组, 登录名>
     */
    Map<String, String> batchGetLoginNamesByPoGroups(Collection<String> poGroups);

    /**
     * 根据工号查询上级登录名
     *
     * @param jobNos
     * @return Map<工号, 上级登录名>
     */
    Map<String, String> batchGetLeaderLoginNames(Collection<String> jobNos);

    /**
     * 根据工号查询用户ID
     *
     * @param jobNos
     * @return Map<工号, 用户信息>
     */
    Map<String, UserDto> batchGetUserInfoByJobNos(Collection<String> jobNos);
}